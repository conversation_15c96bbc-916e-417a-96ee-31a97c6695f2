package db

import (
	"time"

	"whiz/conf"

	"github.com/shopspring/decimal"
)

const (
	// CancelContractLocationID is the location ID for the cancel contract bill
	CancelContractLocationID = 100
	// CancelContractBillTermName is the term name for the cancel contract bill
	CancelContractBillTermName = "Net 30"
	// IntacctPaymentStateComplete is the state for a complete payment in Intacct
	IntacctPaymentStateComplete = "C"
	// IntacctErrorNoValidContact is the error number for no valid contact in Intacct
	IntacctErrorNoValidContact = "BL03002123"
	// IntacctErrorNoBillAlreadyExists is the error number for no bill already exists in Intacct
	IntacctErrorNoBillAlreadyExists = "BL03002185"
)

// BillItem struct for bill item in the cancel contract bill
type BillItem struct {
	XMLName                string          `xml:"APBILLITEM"`
	AccountNo              string          `xml:"ACCOUNTNO"`
	TrxAmount              decimal.Decimal `xml:"TRX_AMOUNT"`
	EntryDescription       string          `xml:"ENTRYDESCRIPTION"`
	LocationID             int             `xml:"LOCATIONID"`
	ContractID             int             `xml:"-"` // Not in XML used for fetching contract info later
	ContractCancellationID int             `xml:"-"` // Not in XML used for fetching contract info later
}

// CreateBillRequest struct for creating a bill in Intacct
type CreateBillRequest struct {
	XMLName                   string     `xml:"create"`
	WhenCreated               string     `xml:"APBILL>WHENCREATED"`
	WhenPosted                string     `xml:"APBILL>WHENPOSTED"`
	BillToPayToContactName    string     `xml:"APBILL>BILLTOPAYTOCONTACTNAME"`
	ShipToRetrunToContactName string     `xml:"APBILL>SHIPTORETURNTOCONTACTNAME"`
	RecordID                  string     `xml:"APBILL>RECORDID"`
	VendorID                  string     `xml:"APBILL>VENDORID"`
	Description               string     `xml:"APBILL>DESCRIPTION"`
	TermName                  string     `xml:"APBILL>TERMNAME"`
	APBillItems               []BillItem `xml:"APBILL>APBILLITEMS>APBILLITEM"`
}

// IntacctError struct for an error in an Intacct API call
type IntacctError struct {
	ErrorNumber  string `xml:"errorno"`
	ErrorMessage string `xml:"description2"`
	Correction   string `xml:"correction"`
}

// IntacctResult struct for the result of an Intacct API call
type IntacctResult struct {
	AuthStatus                string         `xml:"operation>authentication>status"`
	ResultStatus              string         `xml:"operation>result>status"`
	BillRecordNo              int            `xml:"operation>result>data>apbill>RECORDNO"`
	ApPaymentDetailPaymentKey int            `xml:"operation>result>data>APPYMTDETAIL>PAYMENTKEY"`
	ApPaymentCheckNumber      string         `xml:"operation>result>data>APPYMT>DOCNUMBER"`
	ApPaymentPaidDate         string         `xml:"operation>result>data>APPYMT>WHENPAID"`
	ApPaymentPaidAmount       string         `xml:"operation>result>data>APPYMT>TOTALPAID"`
	ApPaymentState            string         `xml:"operation>result>data>APPYMT>STATE"`
	Errors                    []IntacctError `xml:"operation>result>errormessage>error"`
}

// IntacctQuery struct for an Intacct query
type IntacctQuery struct {
	XMLName string   `xml:"query"`
	Object  string   `xml:"object"`
	Fields  []string `xml:"select>field"`
	Filter  struct {
		EqualTo struct {
			Field string `xml:"field"`
			Value string `xml:"value"`
		} `xml:"equalto"`
	} `xml:"filter"`
}

// IntacctPaidInfo struct for paid info in Intacct
type IntacctPaidInfo struct {
	CheckNumber      string
	PaidDate         string
	PaidAmount       string
	PaymentState     string
	PaymentRecordKey int
}

// CancelContractIntacctBill struct for a cancel contract bill in the database
type CancelContractIntacctBill struct {
	BillNumber             string          `db:"bill_number"`
	ContractID             int             `db:"contract_id"`
	BillItemMemo           string          `db:"bill_item_memo"`
	BillItemAmount         decimal.Decimal `db:"bill_item_amount"`
	ContractCancellationID int             `db:"contract_cancellation_id"`
	CreatedAt              time.Time       `db:"created_at"`
	CreatedByUserID        int             `db:"created_by_user_id"`
	IntacctBillRecordID    int             `db:"intacct_bill_record_id"`
	IntacctPaymentRecordID int             `db:"intacct_payment_record_id"`
	CheckNumber            string          `db:"check_number"`
	CheckAmount            decimal.Decimal `db:"check_amount"`
}

// GetCancelContractLenderVendorID gets the Vendor ID for the lender in the cancel contract bill
func GetCancelContractLenderVendorID() string {
	c := conf.Get()
	return c.Intacct.CancelContractLenderVendorID
}

// GetCancelContractCustomerVendorID gets the Vendor ID for the customer in the cancel contract bill
func GetCancelContractCustomerVendorID() string {
	c := conf.Get()
	return c.Intacct.CancelContractCustomerVendorID
}

// GetCancelContractLenderVendorName gets the Vendor Name for the lender in the cancel contract bill
func GetCancelContractLenderVendorName() string {
	c := conf.Get()
	return c.Intacct.CancelContractLenderVendorName
}

// GetCancelContractCustomerVendorName gets the Vendor Name for the customer in the cancel contract bill
func GetCancelContractCustomerVendorName() string {
	c := conf.Get()
	return c.Intacct.CancelContractCustomerVendorName
}
