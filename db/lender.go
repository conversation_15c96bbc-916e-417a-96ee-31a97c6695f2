package db

import (
	"context"
	"database/sql"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"
	"whiz/dms"

	"github.com/jmoiron/sqlx"
	"github.com/lib/pq"
	"github.com/pkg/errors"
	null "gopkg.in/guregu/null.v3"
)

// <PERSON><PERSON> represents the db table `lenders`
type Lender struct {
	ID                   int            `db:"id" json:"id"`
	CreatedAt            time.Time      `db:"created_at" json:"created_at"`
	CreatedByUserID      int            `db:"created_by_user_id" json:"created_by_user_id"`
	DeletedAt            pq.NullTime    `db:"deleted_at" json:"deleted_at"`
	DeletedByUserID      sql.NullInt64  `db:"deleted_by_user_id" json:"deleted_by_user_id"`
	PreviousID           sql.NullInt64  `db:"previous_id" json:"previous_id"`
	IntacctVendorID      string         `db:"intacct_vendor_id" json:"intacct_vendor_id"`
	Name                 string         `db:"name" json:"name"`
	Address              string         `db:"address" json:"address"`
	City                 string         `db:"city" json:"city"`
	StateCode            string         `db:"state_code" json:"state_code"`
	PostalCode           string         `db:"postal_code" json:"postal_code"`
	LHMCDKFinanceCode    null.String    `db:"lhm_cdk_finance_code" json:"lhm_cdk_finance_code"`
	AsburyCDKFinanceCode null.String    `db:"asbury_cdk_finance_code" json:"asbury_cdk_finance_code"`
	UCSLenderID          null.String    `db:"ucs_lender_id" json:"ucs_lender_id"`
	TekionLenderID       null.String    `db:"tekion_lender_id" json:"tekion_lender_id"`
	VTAOverallowance     bool           `db:"add_vta_overallownce" json:"add_vta_overallownce"`
	IsActive             bool           `db:"is_active" json:"is_active"`
	ESaleAPIDisplayName  null.String    `db:"e_sale_api_display_name" json:"e_sale_api_display_name"`
	CertifiableMakes     pq.StringArray `db:"certifiable_makes" json:"certifiable_makes"`
	CancellationLenderID null.Int       `db:"cancellation_lender_id" json:"cancellation_lender_id"`
	IsDigitalReserves    bool           `db:"is_digital_reserves" json:"is_digital_reserves"`
}

// DigitalReserveRule represents a digital reserve rule for a lender, including store, product type, and cancel reason.
type DigitalReserveRule struct {
	StoreID        int `json:"store_id,omitempty" db:"store_id"`
	ProductTypeID  int `json:"product_type_id" db:"product_type_id"`
	CancelReasonID int `json:"cancel_reason_id" db:"cancel_reason_id"`
}

// DigitalReserveRuleMultiselect represents a multiselect digital reserve rule for a lender
type DigitalReserveRuleMultiselect struct {
	ID              int   `json:"id,omitempty" db:"id"`
	StoreIDs        []int `json:"store_ids"`
	ProductTypeIDs  []int `json:"product_type_ids"`
	CancelReasonIDs []int `json:"cancel_reason_ids"`
}

// DigitalReserveRuleDB represents the database structure for digital reserve rules
type DigitalReserveRuleDB struct {
	ID        int       `db:"id" json:"id"`
	LenderID  int       `db:"lender_id" json:"lender_id"`
	CreatedAt time.Time `db:"created_at" json:"created_at"`
	UpdatedAt time.Time `db:"updated_at" json:"updated_at"`
}

// PDFFieldData receives a map and adds key/value pairs specific to the lender to that map
func (l Lender) PDFFieldData(payload map[string]string) {
	payload["lender_name"] = l.Name
	payload["lender_address"] = l.Address
	payload["lender_city"] = l.City
	payload["lender_state_code"] = l.StateCode
	payload["lender_postal_code"] = l.PostalCode
	payload["lender_city_state_postal"] = fmt.Sprintf("%s, %s %s", l.City, l.StateCode, l.PostalCode)
	payload["lender_all"] = fmt.Sprintf("%s, %s, %s, %s %s", l.Name, l.Address, l.City, l.StateCode, l.PostalCode)
}

// GetCancelPayeeLender gets the lender payee data for a contract cancellation
func GetCancelPayeeLender(ctx context.Context, contractID int) (ContractCancellationPayee, error) {

	q := `
	select
		source
	from contracts
	where
		id = $1`
	var source string
	err := Get().GetContext(ctx, &source, q, contractID)
	if err != nil {
		return ContractCancellationPayee{}, errors.Wrap(err, "error getting lender")
	}
	var lender ContractCancellationPayee

	if source == ContractSourceSB {

		q = `
		select
			sl.name,
			sl.address,
			sl.city,
			sl.state_code,
			sl.postal_code
		from contracts c
		join sb_sale_contracts scs on scs.contract_id = c.id
		join sb_sales ss on ss.id=scs.sb_sale_id
		join sb_lenders sl on ss.sb_lender_id = sl.id
		where
			c.id = $1`
		err = Get().GetContext(ctx, &lender, q, contractID)
		if err != nil && err != sql.ErrNoRows {
			return ContractCancellationPayee{}, errors.Wrap(err, "error getting sb lender")
		}
		if err == nil {
			return lender, nil
		}
	} else {
		// if there is cancellation_lender, get detail from that
		q = `
		select
			l.id,
			cl.name,
			cl.attention,
			cl.address,
			cl.city,
			cl.state_code,
			cl.postal_code
		from cancellation_lenders cl
		join lenders l on l.cancellation_lender_id = cl.id
		join contracts c on c.lender_id = l.id
		where
			c.id = $1`
		err = Get().GetContext(ctx, &lender, q, contractID)
		if err != nil && err != sql.ErrNoRows {
			return ContractCancellationPayee{}, errors.Wrap(err, "error getting cancellation lender from contracts")
		}
		if err == nil {
			return lender, nil
		}
		// Attempt to load the Contract's Lender first
		q = `
			select
				l.id,
				l.name,
				l.address,
				l.city,
				l.state_code,
				l.postal_code
			from lenders l
			join contracts c on c.lender_id = l.id
			where
				c.id = $1
		`
		err = Get().GetContext(ctx, &lender, q, contractID)
		if err != nil && err != sql.ErrNoRows {
			return ContractCancellationPayee{}, errors.Wrap(err, "error getting lender")
		}
		if err == nil {
			return lender, nil
		}
	}
	// get lender from cancellation_lender of sales table
	q = `
	select 
		l.id,
		cl.name,
		cl.attention,
		cl.address,
		cl.city,
		cl.state_code,
		cl.postal_code
	from contracts c 
	join sales s on s.id = c.sale_id
	join lenders l on l.id = s.lender_id
	join cancellation_lenders cl on cl.id = l.cancellation_lender_id
	where 
		c.id = $1`
	err = Get().GetContext(ctx, &lender, q, contractID)
	if err != nil && err != sql.ErrNoRows {
		return ContractCancellationPayee{}, errors.Wrap(err, "error getting cancellation lender from sales")
	}
	if err == nil {
		return lender, nil
	}

	// Try lender from sales (non-SB contracts)
	if source != ContractSourceSB {
		q = `
			select
				l.id,
				l.name,
				l.address,
				l.city,
				l.state_code,
				l.postal_code
			from lenders l
			join sales s on s.lender_id = l.id
			join contracts c on c.sale_id = s.id
			where
				c.id = $1
		`
		err = Get().GetContext(ctx, &lender, q, contractID)
		if err != nil && err != sql.ErrNoRows {
			return ContractCancellationPayee{}, errors.Wrap(err, "error getting lender")
		}
		if err == nil {
			return lender, nil
		}
	}

	return ContractCancellationPayee{}, nil
}

// GetDmsLenderByAPIDisplayName attempts to retrieve a lender by name from the database.
func GetDmsLenderByAPIDisplayName(ctx context.Context, lenderName string) (*dms.Lender, error) {
	query := `
		select
			*
		from lenders
		where 
			upper(e_sale_api_display_name) = upper($1)
			and is_active = true
	`
	var l dms.Lender
	err := Get().Unsafe().GetContext(ctx, &l, query, lenderName)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, errors.Wrapf(err, "error finding lender")
	}

	return &l, nil
}

// GetLenderDigitalReserveInfo returns if digital reserves is enabled for the lender/store, and the allowed product_type/cancel_reason combos
func GetLenderDigitalReserveInfo(ctx context.Context, lenderID int, storeID int) (bool, []DigitalReserveRule, error) {
	var rules []DigitalReserveRule

	// First check if digital reserves is enabled for this lender
	var isEnabled bool
	err := Get().GetContext(ctx, &isEnabled, `
		select is_digital_reserves from lenders where id = $1
	`, lenderID)
	if err != nil {
		return false, nil, errors.Wrap(err, "error checking if digital reserves is enabled")
	}
	if !isEnabled {
		return false, nil, nil
	}

	// Get all combinations for this lender/store from the new schema
	err = Get().SelectContext(ctx, &rules, `
		select distinct pt.product_type_id, cr.cancel_reason_id
		from digital_reserve_rules drr
		join digital_reserve_rules_stores ds on ds.digital_reserve_rule_id = drr.id
		join digital_reserve_rules_product_types pt on pt.digital_reserve_rule_id = drr.id
		join digital_reserve_rules_cancel_reasons cr on cr.digital_reserve_rule_id = drr.id
		where drr.lender_id = $1 and ds.store_id = $2
	`, lenderID, storeID)
	if err != nil {
		return false, nil, errors.Wrap(err, "error getting lender digital reserve rules")
	}
	if len(rules) > 0 {
		return true, rules, nil
	}
	return false, nil, nil
}

// GetLenderDigitalReserveRules returns all digital reserve rules for a lender in multiselect format
func GetLenderDigitalReserveRules(ctx context.Context, lenderID int) ([]DigitalReserveRuleMultiselect, error) {
	var rows []ruleRow

	// Optimized query with better performance - using CTE for cleaner structure
	err := Get().SelectContext(ctx, &rows, `
		with rule_data as (
			select
				drr.id as rule_id,
				array_agg(distinct drrs.store_id order by drrs.store_id) as store_ids,
				array_agg(distinct drrpt.product_type_id order by drrpt.product_type_id) as product_type_ids,
				array_agg(distinct drrcr.cancel_reason_id order by drrcr.cancel_reason_id) as cancel_reason_ids
			from digital_reserve_rules drr
			join digital_reserve_rules_stores drrs on drr.id = drrs.digital_reserve_rule_id
			join digital_reserve_rules_product_types drrpt on drr.id = drrpt.digital_reserve_rule_id
			join digital_reserve_rules_cancel_reasons drrcr on drr.id = drrcr.digital_reserve_rule_id
			where drr.lender_id = $1
			group by drr.id
		)
		select
			rule_id,
			array_to_string(store_ids, ',') as store_ids,
			array_to_string(product_type_ids, ',') as product_type_ids,
			array_to_string(cancel_reason_ids, ',') as cancel_reason_ids
		from rule_data
		order by rule_id
	`, lenderID)
	if err != nil {
		return nil, errors.Wrap(err, "error getting digital reserve rules")
	}

	return parseRuleRows(rows)
}

// SaveDigitalReserveRulesUpdate updates digital reserve rules during lender update
// It updates existing rules to point to the new lender ID and only creates new rules as needed
func SaveDigitalReserveRulesUpdate(ctx context.Context, tx *sqlx.Tx, oldLenderID, newLenderID int, rules []DigitalReserveRuleMultiselect) error {
	// Get existing rules for the old lender ID before making any changes
	existingRules, err := GetLenderDigitalReserveRules(ctx, oldLenderID)
	if err != nil {
		return errors.Wrap(err, "error getting existing digital reserve rules")
	}

	// If we have the same number of rules and they match, just update the lender_id and we're done
	if len(existingRules) == len(rules) && rulesMatch(existingRules, rules) {
		_, err := tx.ExecContext(ctx, `
			update digital_reserve_rules
			set lender_id = $1, updated_at = now() at time zone 'utc'
			where lender_id = $2
		`, newLenderID, oldLenderID)
		if err != nil {
			return errors.Wrap(err, "error updating digital reserve rules lender_id")
		}
		return nil
	}

	// Otherwise, clear existing rules and create new ones
	_, err = tx.ExecContext(ctx, `delete from digital_reserve_rules_stores where digital_reserve_rule_id in (select id from digital_reserve_rules where lender_id = $1)`, oldLenderID)
	if err != nil {
		return errors.Wrap(err, "error deleting old lender digital reserve rule stores")
	}
	_, err = tx.ExecContext(ctx, `delete from digital_reserve_rules_product_types where digital_reserve_rule_id in (select id from digital_reserve_rules where lender_id = $1)`, oldLenderID)
	if err != nil {
		return errors.Wrap(err, "error deleting old lender digital reserve rule product types")
	}
	_, err = tx.ExecContext(ctx, `delete from digital_reserve_rules_cancel_reasons where digital_reserve_rule_id in (select id from digital_reserve_rules where lender_id = $1)`, oldLenderID)
	if err != nil {
		return errors.Wrap(err, "error deleting old lender digital reserve rule cancel reasons")
	}
	_, err = tx.ExecContext(ctx, `delete from digital_reserve_rules where lender_id = $1`, oldLenderID)
	if err != nil {
		return errors.Wrap(err, "error deleting old lender digital reserve rules")
	}

	// Now create new rules for the new lender ID
	return SaveDigitalReserveRulesCreate(ctx, tx, newLenderID, rules)
}

// SaveDigitalReserveRulesCreate creates digital reserve rules from scratch
func SaveDigitalReserveRulesCreate(ctx context.Context, tx *sqlx.Tx, lenderID int, rules []DigitalReserveRuleMultiselect) error {
	// First, delete all existing junction table entries for this lender's rules
	_, err := tx.ExecContext(ctx, `
		delete from digital_reserve_rules_stores
		where digital_reserve_rule_id in (
			select id from digital_reserve_rules where lender_id = $1
		)
	`, lenderID)
	if err != nil {
		return errors.Wrap(err, "error deleting existing digital reserve rule stores")
	}

	_, err = tx.ExecContext(ctx, `
		delete from digital_reserve_rules_product_types
		where digital_reserve_rule_id in (
			select id from digital_reserve_rules where lender_id = $1
		)
	`, lenderID)
	if err != nil {
		return errors.Wrap(err, "error deleting existing digital reserve rule product types")
	}

	_, err = tx.ExecContext(ctx, `
		delete from digital_reserve_rules_cancel_reasons
		where digital_reserve_rule_id in (
			select id from digital_reserve_rules where lender_id = $1
		)
	`, lenderID)
	if err != nil {
		return errors.Wrap(err, "error deleting existing digital reserve rule cancel reasons")
	}

	// Now delete the main rules
	_, err = tx.ExecContext(ctx, `delete from digital_reserve_rules where lender_id = $1`, lenderID)
	if err != nil {
		return errors.Wrap(err, "error deleting existing digital reserve rules")
	}

	// Create new rules
	for _, rule := range rules {
		// Validate that rule has all required components
		if len(rule.StoreIDs) == 0 || len(rule.ProductTypeIDs) == 0 || len(rule.CancelReasonIDs) == 0 {
			return errors.New("digital reserve rules must have at least one store, product type, and cancel reason")
		}

		// Insert the main rule
		var ruleID int
		err = tx.GetContext(ctx, &ruleID, `
			insert into digital_reserve_rules (lender_id, created_at, updated_at)
			values ($1, now() at time zone 'utc', now() at time zone 'utc')
			returning id
		`, lenderID)
		if err != nil {
			return errors.Wrap(err, "error creating digital reserve rule")
		}

		// Insert stores using prepared statement for better performance
		if len(rule.StoreIDs) > 0 {
			storeStmt, err := tx.PrepareContext(ctx, `
				insert into digital_reserve_rules_stores (digital_reserve_rule_id, store_id)
				values ($1, $2)
			`)
			if err != nil {
				return errors.Wrap(err, "error preparing store insert statement")
			}
			defer storeStmt.Close()

			for _, storeID := range rule.StoreIDs {
				_, err = storeStmt.ExecContext(ctx, ruleID, storeID)
				if err != nil {
					return errors.Wrap(err, "error saving rule stores")
				}
			}
		}

		// Insert product types using prepared statement
		if len(rule.ProductTypeIDs) > 0 {
			productStmt, err := tx.PrepareContext(ctx, `
				insert into digital_reserve_rules_product_types (digital_reserve_rule_id, product_type_id)
				values ($1, $2)
			`)
			if err != nil {
				return errors.Wrap(err, "error preparing product type insert statement")
			}
			defer productStmt.Close()

			for _, productTypeID := range rule.ProductTypeIDs {
				_, err = productStmt.ExecContext(ctx, ruleID, productTypeID)
				if err != nil {
					return errors.Wrap(err, "error saving rule product types")
				}
			}
		}

		// Insert cancel reasons using prepared statement
		if len(rule.CancelReasonIDs) > 0 {
			reasonStmt, err := tx.PrepareContext(ctx, `
				insert into digital_reserve_rules_cancel_reasons (digital_reserve_rule_id, cancel_reason_id)
				values ($1, $2)
			`)
			if err != nil {
				return errors.Wrap(err, "error preparing cancel reason insert statement")
			}
			defer reasonStmt.Close()

			for _, cancelReasonID := range rule.CancelReasonIDs {
				_, err = reasonStmt.ExecContext(ctx, ruleID, cancelReasonID)
				if err != nil {
					return errors.Wrap(err, "error saving rule cancel reasons")
				}
			}
		}
	}

	return nil
}

// rulesMatch compares two sets of digital reserve rules to see if they're equivalent
func rulesMatch(existing, new []DigitalReserveRuleMultiselect) bool {
	if len(existing) != len(new) {
		return false
	}

	// Create a map of existing rules for comparison
	existingMap := make(map[string]bool)
	for _, rule := range existing {
		key := ruleToKey(rule)
		existingMap[key] = true
	}

	// Check if all new rules exist in the existing set
	for _, rule := range new {
		key := ruleToKey(rule)
		if !existingMap[key] {
			return false
		}
	}

	return true
}

// ruleToKey creates a unique string key for a digital reserve rule
func ruleToKey(rule DigitalReserveRuleMultiselect) string {
	// Sort the slices to ensure consistent comparison
	stores := make([]int, len(rule.StoreIDs))
	copy(stores, rule.StoreIDs)
	sort.Ints(stores)

	productTypes := make([]int, len(rule.ProductTypeIDs))
	copy(productTypes, rule.ProductTypeIDs)
	sort.Ints(productTypes)

	cancelReasons := make([]int, len(rule.CancelReasonIDs))
	copy(cancelReasons, rule.CancelReasonIDs)
	sort.Ints(cancelReasons)

	// Create a unique key from the sorted arrays
	return fmt.Sprintf("stores:%v|products:%v|cancels:%v", stores, productTypes, cancelReasons)
}

// ruleRow represents a row from the combined digital reserve rules query
type ruleRow struct {
	RuleID          int    `db:"rule_id"`
	StoreIDs        string `db:"store_ids"`
	ProductTypeIDs  string `db:"product_type_ids"`
	CancelReasonIDs string `db:"cancel_reason_ids"`
}

// parseRuleRows converts rule rows from the database into DigitalReserveRuleMultiselect structs
func parseRuleRows(rows []ruleRow) ([]DigitalReserveRuleMultiselect, error) {
	var result []DigitalReserveRuleMultiselect
	for _, row := range rows {
		rule := DigitalReserveRuleMultiselect{
			ID: row.RuleID,
		}

		// Parse store IDs (guaranteed to have values due to INNER JOIN)
		storeIDStrs := strings.Split(row.StoreIDs, ",")
		rule.StoreIDs = make([]int, len(storeIDStrs))
		for i, idStr := range storeIDStrs {
			id, err := strconv.Atoi(idStr)
			if err != nil {
				return nil, errors.Wrap(err, "error parsing store ID")
			}
			rule.StoreIDs[i] = id
		}

		// Parse product type IDs (guaranteed to have values due to INNER JOIN)
		productTypeIDStrs := strings.Split(row.ProductTypeIDs, ",")
		rule.ProductTypeIDs = make([]int, len(productTypeIDStrs))
		for i, idStr := range productTypeIDStrs {
			id, err := strconv.Atoi(idStr)
			if err != nil {
				return nil, errors.Wrap(err, "error parsing product type ID")
			}
			rule.ProductTypeIDs[i] = id
		}

		// Parse cancel reason IDs (guaranteed to have values due to INNER JOIN)
		cancelReasonIDStrs := strings.Split(row.CancelReasonIDs, ",")
		rule.CancelReasonIDs = make([]int, len(cancelReasonIDStrs))
		for i, idStr := range cancelReasonIDStrs {
			id, err := strconv.Atoi(idStr)
			if err != nil {
				return nil, errors.Wrap(err, "error parsing cancel reason ID")
			}
			rule.CancelReasonIDs[i] = id
		}

		result = append(result, rule)
	}

	return result, nil
}
