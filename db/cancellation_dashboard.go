package db

import (
	"time"

	"gopkg.in/guregu/null.v3"
)

// AICancellation represents a single AI cancellation request
type AICancellation struct {
	ID               int         `db:"id" json:"id"`
	Source           string      `db:"source" json:"source"`
	Status           string      `db:"status" json:"status"`
	CreatedAt        time.Time   `db:"created_at" json:"created_at"`
	UpdatedAt        time.Time   `db:"updated_at" json:"updated_at"`
	CreatedByUserID  int         `db:"created_by_user_id" json:"created_by_user_id"`
	AssignedToUserID int         `db:"assigned_to_user_id" json:"assigned_to_user_id"`
	ProcessingResult []uint8     `db:"processing_result" json:"processing_result"`
	ProcessingError  []uint8     `db:"processing_error" json:"processing_error"`
	FileID           null.Int    `db:"file_id" json:"file_id"`
	Page             null.Int    `db:"page" json:"page"`
	PageType         null.String `db:"page_type" json:"page_type"`
	Version          null.String `db:"version" json:"version"`

	Notes []AICancellationNote `db:"notes" json:"notes"`
}

// AICancellationNote represents a single note on an AI cancellation request
type AICancellationNote struct {
	ID               int       `db:"id" json:"id"`
	AICancellationID int       `db:"ai_cancellation_id" json:"ai_cancellation_id"`
	Comment          string    `db:"comment" json:"comment"`
	CreatedAt        time.Time `db:"created_at" json:"created_at"`
	CreatedBy        string    `db:"created_by" json:"created_by"`
}

// AICancellationAttachment represents a single attachment on an AI cancellation request
type AICancellationAttachment struct {
	ID                  int         `db:"id" json:"id"`
	AICancellationID    int         `db:"ai_cancellation_id" json:"ai_cancellation_id"`
	S3Bucket            string      `db:"s3_bucket" json:"s3_bucket"`
	S3FileName          string      `db:"s3_file_name" json:"s3_file_name"`
	FileName            string      `db:"file_name" json:"file_name"`
	ContentType         string      `db:"content_type" json:"content_type"`
	Description         string      `db:"description" json:"description"`
	CreatedByUserID     int         `db:"created_by_user_id" json:"created_by_user_id"`
	OriginalS3FileName  null.String `db:"original_s3_file_name" json:"original_s3_file_name"`
	OriginalFileName    null.String `db:"original_file_name" json:"original_file_name"`
	OriginalContentType null.String `db:"original_content_type" json:"original_content_type"`
}
