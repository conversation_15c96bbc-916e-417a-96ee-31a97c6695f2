package db

import (
	"time"

	"github.com/lib/pq"
	"github.com/shopspring/decimal"
	null "gopkg.in/guregu/null.v3"
)

// ContractCancellation stores a contract_cancellation record.
type ContractCancellation struct {
	ID               int             `db:"id"`
	CreatedAt        time.Time       `db:"created_at"`
	CreatedByUserID  int             `db:"created_by_user_id"`
	ContractID       int             `db:"contract_id"`
	CancelDate       time.Time       `db:"cancel_date"`
	CancelMileage    int             `db:"cancel_mileage"`
	CancelReason     string          `db:"cancel_reason"`
	CancelFee        decimal.Decimal `db:"cancel_fee"`
	CancelFactor     decimal.Decimal `db:"cancel_factor"`
	CustomerRefund   decimal.Decimal `db:"customer_refund"`
	StoreRefund      decimal.Decimal `db:"store_refund"`
	ClaimsPaidAmount decimal.Decimal `db:"claims_paid_amount"`
	UnusedVisits     int             `db:"unused_visits"`
	IsVoid           bool            `db:"is_void"`
	TransactionID    null.Int        `db:"transaction_id"`
	RefundDate       pq.NullTime     `db:"refund_date"`
	Payee            string          `db:"payee"`
	PayeeName        string          `db:"payee_name"`
	PayeeAddress     string          `db:"payee_address"`
	PayeeCity        string          `db:"payee_city"`
	PayeeState       string          `db:"payee_state"`
	PayeePostalCode  string          `db:"payee_postal_code"`
}

const (
	// RefundTypeCustomerRefund type of refund
	RefundTypeCustomerRefund = "Customer Refund"
	// RefundTypeSPPRefund type of refund
	RefundTypeSPPRefund = "SPP Refund"
	// CancelPayeeTypeLender type of Cancel Payee
	CancelPayeeTypeLender = "Lender"
	// CancelPayeeTypeCustomer type of Cancel Payee
	CancelPayeeTypeCustomer = "Customer"
	// CancelPayeeDownPayment type of Cancel Payee
	CancelPayeeDownPayment = "DownPayment"
	// CancelPayeeReserves type of Cancel Payee
	CancelPayeeReserves = "Reserves"
	// CancelPayeeStoreIssuedRefund type of Cancel Payee
	CancelPayeeStoreIssuedRefund = "Store Issued Refund"
)

// ContractCancellationPayee represents the payee of a cancellation
type ContractCancellationPayee struct {
	ID         *int   `db:"id" json:"id,omitempty"`
	Name       string `db:"name" json:"name"`
	Attention  string `db:"attention" json:"attention"`
	Address    string `db:"address" json:"address"`
	City       string `db:"city" json:"city"`
	State      string `db:"state_code" json:"state_code"`
	PostalCode string `db:"postal_code" json:"postal_code"`
	Email      string `db:"email" json:"email"`
}

const (
	// CancelStatusBillNotIssued cancel status bill not issued
	CancelStatusBillNotIssued = "Bill Not Issued"
	// CancelStatusBillCleared cancel status bill cleared
	CancelStatusBillCleared = "Bill Cleared"

	// CancelStatusCanceledPendingInvoicing cancel status pending invoicing (initiated)
	CancelStatusCanceledPendingInvoicing = "Canceled - Pending Invoicing"
	// CancelStatusInvoicedPendingCheckRequest cancel status pending check request (invoiced)
	CancelStatusInvoicedPendingCheckRequest = "Invoiced - Pending Check Request"
	// CancelStatusCheckRequestedPendingApproval cancel status pending approval (waiting for check)
	CancelStatusCheckRequestedPendingApproval = "Check Requested - Pending Approval"
	// CancelStatusCheckSentCancelComplete cancel status cancel complete (Paid for lender/customer Invoiced for other)
	CancelStatusCheckSentCancelComplete = "Check Sent - Cancel Complete"
	// CancelStatusCancelSubmittedPendingInvoice cancel status cancel submitted pending invoice
	CancelStatusCancelSubmittedPendingInvoice = "Cancel Submitted - Pending Invoice"
	// CancelStatusInvoicedCancelComplete cancel status invoiced cancel complete
	CancelStatusInvoicedCancelComplete = "Invoiced - Cancel Complete"
	// CancelStatusPaymentNotIssued cancel status payment not issued
	CancelStatusPaymentNotIssued = "Payment Not Issued"
	// CancelStatusPaymentVoided cancel status payment voided
	CancelStatusPaymentVoided = "Voided"
)

const (
	// CancelRequestPrintQuote cancel request quote, prints quote
	CancelRequestPrintQuote = "Quote"
	// CancelRequestCancel cancel request cancel, cancels contract
	CancelRequestCancel = "Cancel"
)

// ManualTaxStates is a list of states that require manual tax calculation
var ManualTaxStates = []string{
	"VA",
	"FL",
}
