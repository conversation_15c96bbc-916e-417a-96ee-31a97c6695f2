package db

import (
	"context"

	"github.com/pkg/errors"
	null "gopkg.in/guregu/null.v3"
)

const (
	// CancelReasonCustomerRequest ...
	CancelReasonCustomerRequest = "Customer Request"
	// CancelReasonLoanPaid ...
	CancelReasonLoanPaid = "Loan Paid"
	// CancelReasonTradeIn ...
	CancelReasonTradeIn = "Trade In"
	// CancelReasonRepo ...
	CancelReasonRepo = "Repo"
	// CancelReasonChargeOff ...
	CancelReasonChargeOff = "Charge Off"
	// CancelReasonUnwind ...
	CancelReasonUnwind = "Unwind"
	// CancelReasonSPPDefault ...
	CancelReasonSPPDefault = "SPP Default"
	// CancelReasonTotalLoss ...
	CancelReasonTotalLoss = "Total Loss"
	// CancelReasonLienholderRequest ...
	CancelReasonLienholderRequest = "Lienholder Request"
	// CancelReasonDownPayment ...
	CancelReasonDownPayment = "Down Payment"
	// CancelReasonSPPCustomerRequest ...
	CancelReasonSPPCustomerRequest = "SPP Customer Request"
	// CancelReasonFlatCancelRecontract ...
	CancelReasonFlatCancelRecontract = "Flat Cancel - Recontract"
	// CancelReasonFlatCancelNotPurchased ...
	CancelReasonFlatCancelNotPurchased = "Flat Cancel - Not Purchased"
	// CancelReasonFlatCancelDealershipRequest ...
	CancelReasonFlatCancelDealershipRequest = "Flat Cancel - Dealership Request"
)

// CancelReason represents a row from the cancel_reasons table
type CancelReason struct {
	ID           int         `db:"id" json:"id"`
	Name         string      `db:"name" json:"name"`
	DocumentReq  null.String `db:"document_req" json:"document_req"`
	IsFlatCancel bool        `db:"is_flat_cancel" json:"is_flat_cancel"`
	ForLenders   bool        `db:"for_lenders" json:"for_lenders"`
}

// GetCancelReasons returns all possible cancel reasons
func GetCancelReasons(ctx context.Context) ([]CancelReason, error) {
	var reasons []CancelReason

	q := `select * from cancel_reasons c order by c.sort_order`

	err := Get().Unsafe().SelectContext(ctx, &reasons, q)
	if err != nil {
		return nil, errors.Wrap(err, "get cancel_reasons failed")
	}

	return reasons, nil
}

// GetLenderCancelReasons returns all cancel reasons that are for lenders
func GetLenderCancelReasons(ctx context.Context) ([]CancelReason, error) {
	var reasons []CancelReason

	q := `select * from cancel_reasons c where c.for_lenders = true order by c.sort_order`

	err := Get().Unsafe().SelectContext(ctx, &reasons, q)
	if err != nil {
		return nil, errors.Wrap(err, "get cancel_reasons failed")
	}

	return reasons, nil
}

// GetCancelReasonByID returns the cancel reason with the specified ID
func GetCancelReasonByID(ctx context.Context, id int) (*CancelReason, error) {
	var reason CancelReason

	q := `select * from cancel_reasons where id = $1`

	err := Get().Unsafe().GetContext(ctx, &reason, q, id)
	if err != nil {
		return nil, errors.Wrapf(err, "error getting cancel reason for id %d", id)
	}

	return &reason, nil
}

// IsFlatOrUnwindCancelReason checks if the cancel reason is either a flat cancel or unwind cancel
func IsFlatOrUnwindCancelReason(reason string) bool {
	return reason == CancelReasonUnwind ||
		reason == CancelReasonFlatCancelRecontract ||
		reason == CancelReasonFlatCancelNotPurchased ||
		reason == CancelReasonFlatCancelDealershipRequest
}

// IsDownPaymentCancelReason checks if the cancel reason is a down payment cancel
func IsDownPaymentCancelReason(reason string) bool {
	return reason == CancelReasonDownPayment
}
