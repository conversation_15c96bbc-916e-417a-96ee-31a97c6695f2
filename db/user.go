package db

import (
	"context"
	"crypto/rand"
	"crypto/sha512"
	"database/sql"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"
	"time"
	"whiz/conf"
	"whiz/mstr"
	"whiz/nr"

	"github.com/jmoiron/sqlx"
	"github.com/lib/pq"
	"github.com/lib/pq/hstore"
	newrelic "github.com/newrelic/go-agent"
	"github.com/pkg/errors"
	null "gopkg.in/guregu/null.v3"

	"golang.org/x/crypto/bcrypt"
)

const (
	passwordSalt           = "82d53ee2ac9e3bd4d316f20153b377"
	passwordCost           = 14
	confirmationTokenSize  = 16
	confirmationExpiration = 10 * 24 * time.Hour // 10 Days

	// RoleAdminView is the `admin_view` role
	RoleAdminView = "admin_view"
	// RoleProductManager is the `product_manager` role
	RoleProductManager = "product_manager"
	// RoleUserProvisioning is the `user_provisioning` role
	RoleUserProvisioning = "user_provisioning"
	// RoleDealershipManagement is the `dealership_management` role
	RoleDealershipManagement = "dealership_management"
	// RoleFinanceDeal is the `finance_deal` role
	RoleFinanceDeal = "finance_deal"
	// RoleServiceRO is the `service_ro` role
	RoleServiceRO = "service_ro"
	// RoleBDCRO is the `bdc_ro` role
	RoleBDCRO = "bdc_ro"
	// RoleRemit is the `remit` role
	RoleRemit = "remit"
	// RoleCoupon is the `coupon` role
	RoleCoupon = "coupon"
	// RoleDealerViewOnly is the `dealer_view_only` role
	RoleDealerViewOnly = "dealer_view_only"
	// RoleStoreUploads is the `store_uploads` role
	RoleStoreUploads = "store_uploads"
	// RoleGAPLicenseManager is the `gap_license_manager` role
	RoleGAPLicenseManager = "gap_license_manager"
	// RoleLDCSAccounting is the `ldcs_accounting` role
	RoleLDCSAccounting = "ldcs_accounting"
	// RoleLDCSService is the `ldcs_service` role
	RoleLDCSService = "ldcs_service"
	// RoleLDCSFlatCancels is the `ldcs_flat_cancels` role
	RoleLDCSFlatCancels = "ldcs_flat_cancels"
	// RoleLDCSAllCancels is the `ldcs_all_cancels` role
	RoleLDCSAllCancels = "ldcs_all_cancels"
	// RoleLDCSFinance is the `ldcs_finance` role
	RoleLDCSFinance = "ldcs_finance"
	// RoleGAPClaims is the gap_claims role
	RoleGAPClaims = "gap_claims"
	// RoleGAPClaimsManager is the gap_claims_manager role
	RoleGAPClaimsManager = "gap_claims_manager"
	// RoleAutoClaims is the auto_claims role
	RoleAutoClaims = "auto_claims"
	// RoleAutoClaimsManager is the auto_claims_manager role
	RoleAutoClaimsManager = "auto_claims_manager"
	// RoleInspections is the inspections role
	RoleInspections = "inspections"
	// RoleRecoveryTeam is the `recovery_team` role
	RoleRecoveryTeam = "recovery_team"
	// RoleVehicleComponentManager is the `vehicle_component_manager` role
	RoleVehicleComponentManager = "vehicle_component_manager"
	// RoleAccounting is the accounting role
	RoleAccounting = "accounting"
	// RoleAccountingClaimHandler is the accounting role who will be able to post claim to batch
	RoleAccountingClaimHandler = "accounting_claim_handler"
	// RoleController is the accounting controller role
	RoleController = "controller"
	// RoleAccountRep is account rep role
	RoleAccountRep = "account_rep"
	// RoleAccountRepII is account rep role
	RoleAccountRepII = "account_rep_ii"
	// RoleAccountRepManager is account rep manager role
	RoleAccountRepManager = "account_rep_manager"
	// RoleNewsUpdatesManager is news and updates manager role
	RoleNewsUpdatesManager = "news_updates_manager"
	// RoleTestAutomation is the role used by test automation scripts. This role allows some requirements/dialogs to be bypassed during testing
	RoleTestAutomation = "test_automation"
	// RoleInternalResources is the `internal_resources` role
	RoleInternalResources = "internal_resources"
	// RoleServiceClosedStore is the service closed role
	RoleServiceClosedStore = "service_closed_store"
	// RoleViewOnlyClaims is the role for view only access to claims module
	RoleViewOnlyClaims = "view_only_claims"
	// RoleCorporateAccounting is the role for accessing all dealership invoices on a corporate level
	RoleCorporateAccounting = "corporate_accounting"
	// ResetPasswordSubject is the subject for a reset password email
	ResetPasswordSubject = "TCA App Password Reset"
	// ResetPasswordBody is the body for a reset password email. Need to use `fmt.Sprintf` with the server URL and the token
	ResetPasswordBody = `<h1>Total Care Auto App Password Reset</h1>
<p>You can reset your TCA account through the link below.</p>
<p><a href="%s/password-reset/%s">Reset Password</a></p>
<p>If you did not request a password reset you may ignore and delete this email.</p>`
	// RoleJobTitleManagement is the role for accessing job title management page
	RoleJobTitleManagement = "job_title_management"

	// Predefined System Users

	// SystemUserEmail is the email value for the SYSTEM user. This would be for system
	// processes that ocurr outside the action of a user.
	SystemUserEmail = "SYSTEM"
	// MigrationUserEmail is the email value for the Migration user. This should be used
	// for processes dealing with migrated data from SB.
	MigrationUserEmail = "<EMAIL>"
	// ScriptUserEmail is the email value for the Script user. This should be used for
	// scripts and/or console applications.
	ScriptUserEmail = "<EMAIL>"

	// RoleLenderManagement is the role for add and edit lenders
	RoleLenderManagement = "lender_management"

	//RoleNewsAndUpdatesView is the role for news update view
	RoleNewsAndUpdatesView = "news_and_updates_view"

	// RoleControllerAdmin is the accounting controller role
	RoleControllerAdmin = "controller_admin"

	// RoleAccountingCancellationHandler is the accounting role who will be able to post cancel to batch
	RoleAccountingCancellationHandler = "accounting_cancellation_handler"

	// RoleAccountingClaimAdmin is the accounting role who will be able to edit claim which is imported in intacct
	RoleAccountingClaimAdmin = "accounting_claim_admin"

	// RoleCancellationPaymentHandler is the accounting role who will be able to post cancellation payment to batch
	RoleCancellationPaymentHandler = "cancellation_payment_handler"

	// RoleCancelDashboardManager is the role for cancel dashboard manager who can cancel and reassign contracts
	RoleCancelDashboardManager = "cancel_dashboard_manager"

	// RoleDigitalReservesManagement is the role for managing digital reserves; view lenders, and edit digital reserve rules
	RoleDigitalReservesManagement = "digital_reserves_management"
)

// Roles is a list of all roles
var Roles = []string{
	RoleAdminView,
	RoleProductManager,
	RoleUserProvisioning,
	RoleDealershipManagement,
	RoleFinanceDeal,
	RoleServiceRO,
	RoleBDCRO,
	RoleRemit,
	RoleCoupon,
	RoleStoreUploads,
	RoleGAPLicenseManager,
	RoleLDCSAccounting,
	RoleLDCSService,
	RoleLDCSFinance,
	RoleGAPClaims,
	RoleGAPClaimsManager,
	RoleAutoClaims,
	RoleAutoClaimsManager,
	RoleInspections,
	RoleRecoveryTeam,
	RoleVehicleComponentManager,
	RoleAccounting,
	RoleAccountRep,
	RoleAccountRepII,
	RoleAccountRepManager,
	RoleInternalResources,
	RoleServiceClosedStore,
	RoleViewOnlyClaims,
	RoleCorporateAccounting,
	RoleJobTitleManagement,
	RoleLenderManagement,
	RoleAccountingCancellationHandler,
	RoleAccountingClaimAdmin,
	RoleCancelDashboardManager,
	RoleDigitalReservesManagement,
}

// StoreRoles is the list of roles for stores
var StoreRoles = []string{
	RoleFinanceDeal,
	RoleInspections,
	RoleRemit,
	RoleServiceRO,
	RoleBDCRO,
	RoleDealerViewOnly,
	RoleServiceClosedStore,
	RoleCorporateAccounting,
	RoleNewsAndUpdatesView,
}

// LDCSRoles is the list of roles for LDCS
var LDCSRoles = []string{
	RoleLDCSAccounting,
	RoleLDCSService,
	RoleLDCSFinance,
	RoleLDCSFlatCancels,
	RoleLDCSAllCancels,
}

// AdminRoles is the list of roles for admin
var AdminRoles = []string{
	RoleAccountRep,
	RoleAccountRepII,
	RoleAccountRepManager,
	RoleAdminView,
	RoleCoupon,
	RoleUserProvisioning,
	RoleDealershipManagement,
	RoleGAPLicenseManager,
	RoleProductManager,
	RoleStoreUploads,
	RoleVehicleComponentManager,
	RoleInternalResources,
	RoleNewsUpdatesManager,
	RoleJobTitleManagement,
	RoleLenderManagement,
	RoleCancelDashboardManager,
	RoleDigitalReservesManagement,
}

// ClaimsRoles is the list of roles for claims
var ClaimsRoles = []string{
	RoleAutoClaims,
	RoleAutoClaimsManager,
	RoleGAPClaims,
	RoleGAPClaimsManager,
	RoleRecoveryTeam,
	RoleViewOnlyClaims,
}

// AccountingRoles is the list of roles for accounting
var AccountingRoles = []string{
	RoleAccounting,
	RoleController,
	RoleControllerAdmin,
	RoleAccountingClaimHandler,
	RoleAccountingCancellationHandler,
	RoleAccountingClaimAdmin,
	RoleCancellationPaymentHandler,
}

// CurrentUser represents a user record
type CurrentUser struct {
	ID                      int           `db:"id" json:"id"`
	UserVersionID           int           `db:"user_version_id" json:"-"`
	CreatedAt               time.Time     `db:"created_at" json:"created_at"`
	UpdatedAt               time.Time     `db:"updated_at" json:"updated_at"`
	UpdatedByUserID         int           `db:"updated_by_user_id" json:"updated_by_user_id"`
	Email                   string        `db:"email" json:"email"`
	FirstName               string        `db:"first_name" json:"first_name"`
	LastName                string        `db:"last_name" json:"last_name"`
	PasswordDigest          string        `db:"password_digest" json:"-"`
	CompanyID               int           `db:"company_id" json:"company_id"`
	Active                  bool          `db:"active" json:"active"`
	Roles                   hstore.Hstore `db:"roles" json:"roles"`
	GrantAllRoles           bool          `db:"grant_all_roles" json:"grant_all_roles"`
	ConfirmationTokenSetAt  pq.NullTime   `db:"confirmation_token_set_at" json:"confirmation_token_set_at"`
	ConfirmationToken       string        `db:"confirmation_token" json:"confirmation_token"`
	ResetPasswordToken      string        `db:"reset_password_token" json:"reset_password_token"`
	ResetPasswordTokenSetAt pq.NullTime   `db:"reset_password_token_set_at" json:"reset_password_token_set_at"`
	DMSEmployeeNumber       string        `db:"dms_employee_number" json:"dms_employee_number"`
	HREmployeeNumber        string        `db:"hr_employee_number" json:"hr_employee_number"`
	PasswordLastChangedAt   time.Time     `db:"password_last_changed_at" json:"password_last_changed_at"`
	MSTRID                  null.String   `db:"mstr_id" json:"mstr_id"`
	MSTRUsername            null.String   `db:"mstr_username" json:"mstr_username"`
	LastLoggedInAt          pq.NullTime   `db:"last_logged_in_at" json:"last_logged_in_at"`
	Stores                  []struct {
		ID   int    `db:"id" json:"id"`
		Name string `db:"name" json:"name"`
		Code string `db:"code" json:"code"`
	} `db:"-" json:"stores"`
	JobTitle               null.String `db:"job_title" json:"job_title"`
	PasswordExpirationDate pq.NullTime `db:"password_expiration_date" json:"password_expiration_date"`
	PasswordExpired        bool        `db:"password_expired" json:"password_expired"`
	PasswordExpiring       bool        `db:"password_expiring" json:"password_expiring"`
	JobTitleID             null.Int    `db:"job_title_id" json:"job_title_id"`
	JobCode                null.String `db:"job_code" json:"job_code"`
	Terminated             bool        `db:"terminated" json:"terminated"`
	TerminatedAt           pq.NullTime `db:"terminated_at" json:"terminated_at"`
	LockedAt               pq.NullTime `db:"locked_at" json:"locked_at"`
	RequireSSO             bool        `db:"require_sso" json:"require_sso"`
	SSOUsername            null.String `db:"sso_username" json:"sso_username"`
	ExceptionalRole        bool        `db:"exceptional_role" json:"exceptional_role"`
}

// FullName should return a combined version of the first name and last name
func (u CurrentUser) FullName() string {
	return u.FirstName + " " + u.LastName
}

// PasswordHash will use bcrypt to generate a hash for the given password
func PasswordHash(pw string) (string, error) {
	b, err := bcrypt.GenerateFromPassword([]byte(pw+passwordSalt), passwordCost)
	if err != nil {
		return "", errors.Wrap(err, "bcrypt.GenerateFromPassword failed")
	}
	return string(b), nil
}

// PasswordMatch will use bcrypt to compare the given password with the password digest on the user
func (u *CurrentUser) PasswordMatch(pw string) bool {
	return bcrypt.CompareHashAndPassword([]byte(u.PasswordDigest), []byte(pw+passwordSalt)) == nil
}

// SetPassword will set the password digest to the hashed version of the given password
func (u *CurrentUser) SetPassword(pw string) error {
	h, err := PasswordHash(pw)
	if err != nil {
		return errors.Wrap(err, "User SetPassword failed")
	}
	u.PasswordDigest = h
	return nil
}

// GetExpirationDateString will return the password expiration date as a string
func (u *CurrentUser) GetExpirationDateString() string {
	if !u.PasswordExpirationDate.Valid {
		return ""
	}

	return u.PasswordExpirationDate.Time.Format("01-02-2006")
}

// HasRole will return true if the user has the given role
func (u *CurrentUser) HasRole(role string) bool {
	return u.Roles.Map[role].Valid
}

// HasAnyRole will return true if the user has the given role
func (u *CurrentUser) HasAnyRole(roles []string) bool {
	for _, role := range roles {
		if u.Roles.Map[role].Valid {
			return true
		}
	}
	return false
}

// HasRoleContainedIn will return true if the user has a role that's found in the given role string
func (u *CurrentUser) HasRoleContainedIn(roleStr string) bool {
	for r := range u.Roles.Map {
		if strings.Contains(r, roleStr) {
			return true
		}
	}
	return false
}

// IsConfirmationTokenActive will return true if the given time is still considered active for the confirmation token
func (u CurrentUser) IsConfirmationTokenActive(t time.Time) bool {
	return u.ConfirmationTokenSetAt.Valid && u.ConfirmationTokenSetAt.Time.Add(confirmationExpiration).After(t)
}

// PDFFieldData receives a map and adds key/value pairs specific to a salesperson to that map
func (u CurrentUser) PDFFieldData(payload map[string]string) {
	payload["salesperson_first_name"] = u.FirstName
	payload["salesperson_last_name"] = u.LastName
	payload["salesperson_name"] = u.FullName()
	payload["salesperson_email"] = u.Email
	payload["salesperson_employee_number"] = u.DMSEmployeeNumber
}

// DeactivateUser will run through the process of deactivating a user
func (u *CurrentUser) DeactivateUser(ctx context.Context, tx *sqlx.Tx, terminate bool, updatedByUserID int) error {
	oldUserVersionID := u.UserVersionID
	u.Active = false
	u.UpdatedByUserID = updatedByUserID
	u.Terminated = terminate

	query := `update user_versions set current = false where current and user_id = $1`
	_, err := tx.Exec(query, u.ID)
	if err != nil {
		return errors.Wrap(err, "error updating user_verions to remove current version")
	}
	query = `
		insert into user_versions 
		(
			user_id, 
			created_at, 
			created_by_user_id, 
			company_id, 
			first_name, 
			last_name, 
			email, 
			active, 
			roles, 
			dms_employee_number, 
			hr_employee_number, 
			current, 
			grant_all_roles, 
			job_title_id, 
			job_code, 
			terminated, 
			sso_username,
			exceptional_role
		) values (
			:id, 
			now() at time zone 'utc', 
			:updated_by_user_id, 
			:company_id, 
			:first_name, 
			:last_name,
			:email, 
			:active, 
			:roles, 
			:dms_employee_number, 
			:hr_employee_number, 
			't', 
			:grant_all_roles, 
			:job_title_id, 
			:job_code, 
			:terminated,
			:sso_username,
			:exceptional_role
		)
		returning id`
	stmt, err := tx.PrepareNamed(query)
	if err != nil {
		return errors.Wrap(err, "error preparing insert user_verions to add new version")
	}
	defer stmt.Close()
	err = stmt.Get(&u.UserVersionID, u)
	if err != nil {
		return errors.Wrap(err, "error executing insert user_verions to add new version")
	}
	if terminate {
		_, err = tx.Exec(`update users set terminated_at=now() at time zone 'utc' where id=$1`, u.ID)
		if err != nil {
			return errors.Wrap(err, "error updating users with terminated_at")
		}
	}

	storeQuery := `insert into stores_user_versions (user_version_id, store_id) select $1, store_id from stores_user_versions where user_version_id = $2`
	_, err = tx.Exec(storeQuery, u.UserVersionID, oldUserVersionID)
	if err != nil {
		return errors.Wrap(err, "error executing insert stores_user_verions to add new version's stores")
	}

	var txn newrelic.Transaction
	if ctx != nil {
		txn = newrelic.FromContext(ctx)
	}

	if u.MSTRID.Valid {
		err = u.DeleteMSTRUser(txn, tx)
		if err != nil {
			return errors.Wrap(err, "error deleting MicroStrategy user")
		}
	}

	// Deactivate user in phizz
	err = deativatePhizzUser(ctx, u.Email)
	if err != nil {
		return errors.Wrap(err, "error deactitvating user in phizz")
	}

	return nil
}

// DeleteMSTRUser will run through the process to delete the user from MicroStrategy
func (u *CurrentUser) DeleteMSTRUser(txn newrelic.Transaction, tx *sqlx.Tx) error {
	jsessionid, authToken, err := mstr.Login(txn, conf.Get().MSTR.ServerUsername, conf.Get().MSTR.ServerPassword)
	if err != nil {
		return errors.Wrap(err, "error making MSTR request to login")
	}

	err = mstr.ObjectsDelete(txn, jsessionid, authToken, mstr.ObjectTypeUser, u.MSTRID.String)
	if err != nil {
		return errors.Wrap(err, "error making MSTR request to delete object (user)")
	}

	_, err = tx.Exec("update users set mstr_id = null, mstr_username = null where id = $1", u.ID)
	if err != nil {
		return errors.Wrap(err, "error updating users")
	}
	u.MSTRID = null.String{}
	u.MSTRUsername = null.String{}
	return nil
}

// CanAccessContract returns a bool indicating if the provided user is allowed to access the specified contract.
func (u *CurrentUser) CanAccessContract(contractID int) (bool, error) {
	userCompanyAccessQ := `select count(1) from stores s join contracts c on c.store_id = s.id
			  join current_users cu on cu.company_id = s.company_id
			  where c.id = $1 and cu.id  = $2`
	var userCompanyAccess int
	err := Get().Get(&userCompanyAccess, userCompanyAccessQ, contractID, u.ID)

	if err != nil {
		return false, errors.Wrap(err, "error getting user company level authorization")
	}

	var companyIDs []int
	readableCompanyQ := `select readable_company_id from company_readable_companies crc
										where crc.company_id = $1`
	err = Get().Select(&companyIDs, readableCompanyQ, u.CompanyID)
	if err != nil {
		return false, errors.Wrap(err, "error getting readable_company_id")
	}

	var storeIDs []int
	if len(companyIDs) == 0 {
		readableStoreQ := `select readable_store_id from company_readable_stores crs
										where crs.company_id = $1`
		err = Get().Select(&storeIDs, readableStoreQ, u.CompanyID)
		if err != nil {
			return false, errors.Wrap(err, "error getting readable_store_id")
		}
	}

	var userReadStoreAccess, userReadCompanyAccess int
	if len(companyIDs) > 0 {
		userReadCompanyAccessQ := `select count(1) from contracts c join stores s on c.store_id = s.id
								where s.company_id in (select readable_company_id from company_readable_companies crc 
									where crc.company_id = $1)
								and c.id = $2`

		err = Get().Get(&userReadCompanyAccess, userReadCompanyAccessQ, u.CompanyID, contractID)

		if err != nil {
			return false, errors.Wrap(err, "error getting user company level authorization")
		}
	} else if len(storeIDs) > 0 {
		userReadStoreAccessQ := `select count(1) from contracts c join stores s on c.store_id = s.id
								where s.id in (select readable_store_id from company_readable_stores crs
									where crs.company_id = $1)
								and c.id = $2`

		err = Get().Get(&userReadCompanyAccess, userReadStoreAccessQ, u.CompanyID, contractID)

		if err != nil {
			return false, errors.Wrap(err, "error getting user store level authorization")
		}
	}

	userStoreAccessQ := `select count(1) from stores s join contracts c on c.store_id = s.id
	         join stores_user_versions suv on s.id = suv.store_id
	         join current_users cu on cu.user_version_id = suv.user_version_id
	                where c.id = $1 and cu.id  = $2`
	var userStoreAccess int
	err = Get().Get(&userStoreAccess, userStoreAccessQ, contractID, u.ID)

	if err != nil {
		return false, errors.Wrap(err, "error getting user store level authorization")
	}

	return userStoreAccess > 0 || userCompanyAccess > 0 || userReadCompanyAccess > 0 || userReadStoreAccess > 0, nil
}

// GenerateConfirmationToken will generate a new confirmation token
func GenerateConfirmationToken() (string, error) {
	b := make([]byte, confirmationTokenSize)
	_, err := rand.Read(b)
	if err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(b), nil
}

// UserGAPLicense represents a user_gap_licenses record
type UserGAPLicense struct {
	ID              int           `db:"id" json:"id"`
	CreatedAt       time.Time     `db:"created_at" json:"created_at"`
	CreatedByUserID int           `db:"created_by_user_id" json:"created_by_user_id"`
	DeletedAt       pq.NullTime   `db:"deleted_at" json:"deleted_at"`
	DeletedByUserID sql.NullInt64 `db:"deleted_by_user_id" json:"deleted_by_user_id"`
	PreviousID      sql.NullInt64 `db:"previous_id" json:"previous_id"`
	UserID          int           `db:"user_id" json:"user_id"`
	Code            string        `db:"code" json:"code"`
	StartedOn       time.Time     `db:"started_on" json:"started_on"`
	EndedOn         time.Time     `db:"ended_on" json:"ended_on"`
}

// FindActiveUserGAPLicense will get the user's active GAP license
func FindActiveUserGAPLicense(userID int, contractDate time.Time) (*UserGAPLicense, error) {
	var gapLicense UserGAPLicense
	query := `select *
		from user_gap_licenses
		where user_id = $1
		and started_on <= $2
		and ended_on >= $2
		and deleted_at is null`
	err := Get().Get(&gapLicense, query, userID, contractDate)
	if err != nil {
		if err != sql.ErrNoRows {
			return nil, errors.Wrap(err, "error finding user's active GAP license")
		}
		return nil, nil
	}
	return &gapLicense, nil
}

// GetUserByEmail returns the user with the matching email
func GetUserByEmail(ctx context.Context, email string) (CurrentUser, error) {
	var user CurrentUser

	err := Get().Unsafe().GetContext(ctx, &user, `select * from current_users where email = $1`, email)
	if err != nil {
		if err == sql.ErrNoRows {
			return CurrentUser{}, errors.Wrapf(err, "there's no user with email [%s]", email)
		}
		return CurrentUser{}, errors.Wrapf(err, "failed to load user with email [%s]", email)
	}

	return user, nil
}

func deativatePhizzUser(ctx context.Context, email string) error {
	url := conf.Get().Phizz.BaseURL + "/ext/users/" + email + "/deactivate"

	if conf.Get().Phizz.Log {
		log.Println("[Phizz-users-deactivate] request URL:", url)
	}

	phizzReq, err := http.NewRequest("PUT", url, nil)
	if err != nil {
		return errors.Wrap(err, "could not create Phizz-users-deactivate request")
	}

	phizzReq.Header.Set("Content-Type", "application/json")
	phizzReq.Header.Set("Accept", "application/json")
	salt := []byte(conf.Get().Phizz.AuthSalt)
	checksum := sha512.Sum512(salt)
	phizzReq.Header.Set("Phizz-Checksum", hex.EncodeToString(checksum[:]))

	client := http.Client{Timeout: time.Second * 30}
	var txn newrelic.Transaction
	if ctx != nil {
		txn = newrelic.FromContext(ctx)
	}

	resp, err := nr.External(txn, client, phizzReq)
	if err != nil {
		return errors.Wrap(err, "invalid response for Phizz-users-deactivate")
	}
	defer resp.Body.Close()

	bodyBytes, _, err := readResponse(resp.Body)
	if err != nil && err != io.EOF && err != io.ErrUnexpectedEOF {
		return errors.Wrap(err, "invalid response for Phizz-users-deactivate")
	}
	if conf.Get().Phizz.Log {
		log.Println("[Phizz-users-deactivate] response status:", resp.Status, ", body:", string(bodyBytes))
	}

	// Not all whiz users exist in phizz hence, statusNotFound is not an error for this function
	if (resp.StatusCode != http.StatusOK) && (resp.StatusCode != http.StatusNotFound) {
		return errors.New(fmt.Sprintf("invalid response for Phizz-users-deactivate: %d", resp.StatusCode))
	}
	return nil
}

// ActivatePhizzUser activates phizz user
func ActivatePhizzUser(ctx context.Context, email string) error {
	url := conf.Get().Phizz.BaseURL + "/ext/users/" + email + "/activate"

	if conf.Get().Phizz.Log {
		log.Println("[Phizz-users-activate] request URL:", url)
	}

	phizzReq, err := http.NewRequest("PUT", url, nil)
	if err != nil {
		return errors.Wrap(err, "could not create Phizz-users-activate request")
	}

	phizzReq.Header.Set("Content-Type", "application/json")
	phizzReq.Header.Set("Accept", "application/json")
	salt := []byte(conf.Get().Phizz.AuthSalt)
	checksum := sha512.Sum512(salt)
	phizzReq.Header.Set("Phizz-Checksum", hex.EncodeToString(checksum[:]))

	client := http.Client{Timeout: time.Second * 30}
	resp, err := nr.External(newrelic.FromContext(ctx), client, phizzReq)
	if err != nil {
		return errors.Wrap(err, "invalid response for Phizz-users-activate")
	}
	defer resp.Body.Close()

	bodyBytes, _, err := readResponse(resp.Body)
	if err != nil && err != io.EOF && err != io.ErrUnexpectedEOF {
		return errors.Wrap(err, "invalid response for Phizz-users-activate")
	}
	if conf.Get().Phizz.Log {
		log.Println("[Phizz-users-activate] response status:", resp.Status, ", body:", string(bodyBytes))
	}

	// Not all whiz users exist in phizz hence, statusNotFound is not an error for this function
	if (resp.StatusCode != http.StatusOK) && (resp.StatusCode != http.StatusNotFound) {
		return errors.New(fmt.Sprintf("invalid response for Phizz-users-activate: %d", resp.StatusCode))
	}
	return nil
}

// Lock will lock the user
func (u *CurrentUser) Lock() error {
	q := `
		update users 
		set locked_at = now() at time zone 'utc' 
		where id = $1`

	_, err := Get().Exec(q, u.ID)
	if err != nil {
		return errors.Wrapf(err, "failed to lock user with id [%d]", u.ID)
	}

	return nil
}

// Unlock will unlock the user
func (u *CurrentUser) Unlock(ctx context.Context, tx *sqlx.Tx) error {
	q := `
		update users 
		set locked_at = null 
		where id = $1`

	_, err := tx.Exec(q, u.ID)
	if err != nil {
		return errors.Wrapf(err, "failed to unlock user with id [%d]", u.ID)
	}

	return nil
}

// CheckPasswordUsed will check to see if the specified password has been used.
func (u *CurrentUser) CheckPasswordUsed(pw string) (bool, error) {
	pwHash, err := PasswordHash(pw)
	if err != nil {
		return false, errors.Wrapf(err, "failed to hash")
	}

	q := `
		select 
			count(1) 
		from user_password_digests 
		where 
			user_id = $1 
			and password_digest = $2`

	var passwordUsed int
	err = Get().Get(&passwordUsed, q, u.ID, pwHash)
	if err != nil {
		return false, errors.Wrap(err, "error checking for password previous use")
	}

	return passwordUsed > 0, nil
}

// GetCurrentUserByID will return the current user by id
func GetCurrentUserByID(id int) (*CurrentUser, error) {
	var currentUser CurrentUser
	query := `select * from current_users where id = $1 limit 1`
	err := Get().Unsafe().Get(&currentUser, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, errors.Wrapf(err, "error getting current user for user id %d", id)
	}

	return &currentUser, nil
}

// GetCurrentUsersByJobTitleID will return the current users by job title id
func GetCurrentUsersByJobTitleID(id int) ([]CurrentUser, error) {
	var currentUsers []CurrentUser
	query := `select * from current_users where job_title_id = $1`
	err := Get().Unsafe().Select(&currentUsers, query, id)
	if err != nil {
		return nil, errors.Wrapf(err, "error getting current user for job title id %d", id)
	}

	return currentUsers, nil
}

// DisableCurrentUserVersionByID will disable the current user version by id
func DisableCurrentUserVersionByID(tx *sqlx.Tx, id int) error {
	query := `update user_versions set current = false where current and user_id = $1`
	_, err := tx.Exec(query, id)
	if err != nil {
		return errors.Wrapf(err, "error disabling current user version for user id %d", id)
	}

	return nil
}

// CanAccessStore returns a bool indicating if the provided user is allowed to access the specified store.
func (u *CurrentUser) CanAccessStore(ctx context.Context, storeID int) (bool, error) {
	query := `select count(*) 
			from stores s
			join stores_user_versions suv on suv.store_id = s.id
			join current_users cu on cu.user_version_id = suv.user_version_id
			where s.id = $1 and cu.id = $2`

	var count int
	if err := Get().GetContext(ctx, &count, query, storeID, u.ID); err != nil {
		return false, errors.Wrapf(err, "error getting store level access")
	}

	return count > 0, nil
}
