package cancelquotehandlers

import (
	"strings"
	"time"

	"whiz/types" // Assuming your types package

	"github.com/go-playground/form/v4"
	"github.com/lib/pq" // For pq.NullTime
)

// formDecoder is the shared form decoder instance for this package.
var formDecoder *form.Decoder

func init() {
	formDecoder = form.NewDecoder()
	// Register a custom type function for types.JSPQDate
	formDecoder.RegisterCustomTypeFunc(decodeJSPQDateCustom, types.JSPQDate{})
}

// decodeJSPQDateCustom is a custom type decoding function for types.JSPQDate.
// It handles potential quoting issues in the input string.
func decodeJSPQDateCustom(vals []string) (interface{}, error) {
	if len(vals) == 0 || vals[0] == "" {
		// Return a zero value (invalid NullTime) for empty or missing values.
		// Adjust if an error is preferred for empty dates.
		return types.JSPQDate{NullTime: pq.NullTime{Valid: false}}, nil
	}

	val := vals[0]

	// If the input string itself is quoted (e.g., "\"2025-01-02\""), unquote it.
	// Note: Go string literals for quotes within quotes need double escaping.
	// However, the actual form value will be `""2025-01-02""`.
	if len(val) > 1 && val[0] == '"' && val[len(val)-1] == '"' {
		// This attempts to remove a single layer of quotes if present.
		// For a value like ""2025-01-02"", this results in "2025-01-02".
		val = strings.Trim(val, `"`)
	}

	t, err := time.Parse(types.JSDateFormat, val)
	if err != nil {
		return nil, err
	}

	// Return a valid types.JSPQDate.
	return types.JSPQDate{NullTime: pq.NullTime{Time: t, Valid: true}}, nil
}

// GetDecoder can be used if other parts of the application need access
// to this specific decoder instance, though it's primarily used internally
// by the unmarshal methods within this package.
func GetDecoder() *form.Decoder {
	return formDecoder
}
