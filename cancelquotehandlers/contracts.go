package cancelquotehandlers

import (
	"database/sql"
	"fmt"
	"net/http"
	"strings"
	"time"

	"whiz/db"
	"whiz/handlers"
	"whiz/types"
	"whiz/util"

	"github.com/dustin/go-humanize"
	"github.com/jmoiron/sqlx"
	"github.com/lib/pq"
	"github.com/lib/pq/hstore"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"gopkg.in/guregu/null.v3"
)

// ContractCancelReasonList returns list of available cancel reasons for lenders.
func ContractCancelReasonList(w http.ResponseWriter, req *http.Request) (int, handlers.APIResponse) {
	ctx := req.Context()
	reasons, err := db.GetLenderCancelReasons(ctx)
	if err != nil {
		err = errors.Wrap(err, "error getting cancel reasons list")
		util.ReportError(ctx, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting cancellation reasons", nil)
	}
	return http.StatusOK, map[string]interface{}{"reasons": reasons}
}

// SearchType represents the type of search to perform.
type SearchType string

const (
	// SearchTypeVin is used to indicate that the search is by VIN.
	SearchTypeVin SearchType = "vin"
	// SearchTypeContractNumber is used to indicate that the search is by contract number.
	SearchTypeContractNumber SearchType = "contractNumber"
)

// SearchResultStatus indicates the status of the search result.
type SearchResultStatus string

const (
	// SearchResultStatusSuccess indicates that the search was successful.
	SearchResultStatusSuccess SearchResultStatus = "success"

	// SearchResultStatusAmbiguous indicates that the search results are ambiguous.
	// Ambiguous search results are returned when the search criteria is not specific enough to return the desired result
	SearchResultStatusAmbiguous SearchResultStatus = "ambiguous"
)

// ContractSearchRequest is the request body for the contract search endpoint.
type ContractSearchRequest struct {
	SearchType     SearchType `form:"searchType" mod:"trim" validate:"required,oneof=vin contractNumber"`
	VIN            string     `form:"vin" mod:"trim,ucase" validate:"required_if=SearchType vin"`
	CustomerName   string     `form:"customerName" mod:"trim" validate:"required_if=SearchType vin"`
	ContractNumber string     `form:"contractNumber" mod:"trim" validate:"required_if=SearchType contractNumber"`
}

// unmarshal decodes query string parameters and stores the values in a struct.
func (input *ContractSearchRequest) unmarshal(r *http.Request) error {
	if err := r.ParseForm(); err != nil {
		return errors.Wrap(err, "could not parse form")
	}
	if err := formDecoder.Decode(&input, r.Form); err != nil {
		return errors.Wrap(err, "could not decode params")
	}
	if err := conform.Struct(r.Context(), input); err != nil {
		return errors.Wrap(err, "could not conform params")
	}
	return nil
}

// ContractSearchResult is the response body for the contract search endpoint.
type ContractSearchResult struct {
	Status    SearchResultStatus     `json:"status"`
	Message   string                 `json:"message"` // Message explaining the ambiguity and prompting for more specific criteria
	Contracts []CancelSearchContract `json:"contracts"`
}

// CancelSearchContract represents a single contract in the response body.
type CancelSearchContract struct {
	Status           string         `json:"status" db:"status"`
	ProductTypeCode  string         `json:"productTypeCode" db:"product_type_code"`
	ProductTypeName  string         `json:"productTypeName" db:"product_type_name"`
	ContractNumber   string         `json:"contractNumber" db:"contract_number"`
	IssuingDealer    string         `json:"issuingDealer" db:"issuing_dealer"`
	EffectiveDate    types.JSPQDate `json:"effectiveDate" db:"effective_date"`
	LenderName       *string        `json:"lenderName" db:"lender_name"`
	CustomerFullName string         `json:"customerFullName" db:"customer_full_name"`
	VehicleInfo      VehicleInfo    `json:"vehicleInfo" db:"vehicle_info"`
}

// VehicleInfo represents the vehicle information for a contract.
type VehicleInfo struct {
	Vin   string `json:"vin" db:"vin"`
	Make  string `json:"make" db:"make"`
	Model string `json:"model" db:"model"`
	Year  int    `json:"year" db:"year"`
}

// ContractSearch handles the contract search endpoint.
func ContractSearch(w http.ResponseWriter, req *http.Request) (int, handlers.APIResponse) {
	ctx := req.Context()

	var search ContractSearchRequest
	if err := search.unmarshal(req); err != nil {
		util.LogWarning(ctx, err)
		return http.StatusBadRequest, handlers.ErrorMessage("Could not read searchrequest parameters.", nil)
	}

	// TODO: Return a useful validation failure message
	if err := validate.Struct(search); err != nil {
		util.LogWarning(ctx, err)
		return http.StatusBadRequest, handlers.ErrorMessage("Invalid search request.", nil)
	}

	var searchResult ContractSearchResult
	searchResult.Status = SearchResultStatusSuccess

	switch search.SearchType {
	case SearchTypeContractNumber:
		query := `
			with target_vin as (
				-- get the vin of the desired contract
				select distinct vin
				from vin_records vr
				join contracts c on c.vin_record_id = vr.id
				where 
						c.code = ?
						or 
						c.original_code = ?
			)
			select 
				c.code contract_number,
				c.product_type_code,
				pt.name product_type_name,
				c.status,
				s.name issuing_dealer,
				c.effective_date,
				coalesce(l.name, sl.name) lender_name,
				
				case
					when 
						cc.is_business
						and (
							cc.first_name != ''
							or cc.last_name != ''
						)
					then cc.first_name || ' ' || cc.last_name || '/' || cc.business_name
					when 
						cc.is_business
						and cc.first_name = ''
						and cc.last_name = ''
						and ccb.first_name != ''
					then cc.business_name || '/' || ccb.first_name || ' ' || ccb.last_name
					when 
						cc.is_business
						and cc.first_name = ''
						and cc.last_name = ''
					then cc.business_name
					when 
						cc.is_business = false
						and ccb.first_name != ''
					then cc.first_name || ' ' || cc.last_name || '/' || ccb.first_name || ' ' || ccb.last_name
					else cc.first_name || ' ' || cc.last_name
				end customer_full_name,

				vr.vin as "vehicle_info.vin",
				vr.make as "vehicle_info.make",
				vr.model as "vehicle_info.model",
				vr.year as "vehicle_info.year"
			from contracts c
				join stores s on s.id = c.store_id
				join product_variants pv on pv.id = c.product_variant_id
				join products p on p.id = pv.product_id
				join product_types pt on pt.id = p.product_type_id
				join vin_records vr on vr.id = c.vin_record_id
				join target_vin tr on tr.vin = vr.vin
				join customers cc on c.customer_id = cc.id
				left join lenders l on l.id = c.lender_id
				left join sales sa on sa.id = c.sale_id
				left join lenders sl on sl.id = sa.lender_id -- sale-lender
				left join contract_cobuyers ccb on sa.contract_cobuyer_id = ccb.id
			where c.status not in (?)`

		excludeStatus := []string{
			db.ContractStatusGenerated,
			db.ContractStatusVoided,
		}
		query, args, err := sqlx.In(query, search.ContractNumber, search.ContractNumber, excludeStatus)
		if err != nil {
			err = errors.Wrap(err, "error getting related contracts")
			util.ReportError(ctx, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Failed to get related contracts", nil)
		}

		var contracts []CancelSearchContract
		query = db.Get().Rebind(query)
		err = db.Get().SelectContext(ctx, &contracts, query, args...)
		if err != nil {
			util.ReportError(ctx, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Error finding contracts", nil)
		}
		searchResult.Contracts = contracts
	case SearchTypeVin:
		query := `
			select 
				c.code contract_number,
				c.status,
				c.product_type_code,
				pv.display_name product_type_name,
				s.name issuing_dealer,
				c.effective_date,
				coalesce(l.name, sl.name) lender_name,

				case
					when 
						cc.is_business
						and (
							cc.first_name != ''
							or cc.last_name != ''
						)
					then cc.first_name || ' ' || cc.last_name || '/' || cc.business_name
					when 
						cc.is_business
						and cc.first_name = ''
						and cc.last_name = ''
						and ccb.first_name != ''
					then cc.business_name || '/' || ccb.first_name || ' ' || ccb.last_name
					when 
						cc.is_business
						and cc.first_name = ''
						and cc.last_name = ''
					then cc.business_name
					when 
						cc.is_business = false
						and ccb.first_name != ''
					then cc.first_name || ' ' || cc.last_name || '/' || ccb.first_name || ' ' || ccb.last_name
					else cc.first_name || ' ' || cc.last_name
				end customer_full_name,

				vr.vin as "vehicle_info.vin",
				vr.make as "vehicle_info.make",
				vr.model as "vehicle_info.model",
				vr.year as "vehicle_info.year"
			from contracts c
				join stores s on s.id = c.store_id
				join product_variants pv on pv.id = c.product_variant_id
				join vin_records vr on vr.id = c.vin_record_id
				join customers cc on c.customer_id = cc.id
				left join lenders l on l.id = c.lender_id
				left join sales sa on sa.id = c.sale_id
				left join lenders sl on sl.id = sa.lender_id -- sale-lender
				left join contract_cobuyers ccb on sa.contract_cobuyer_id = ccb.id
			where
				vr.vin = ?
				and c.status not in (?)
				and (
				  cc.last_name ilike ?
				  or
				  cc.first_name || ' ' || cc.last_name ilike ?
				  or 
				  cc.business_name ilike ?
				  or
				  ccb.first_name || ' ' || ccb.last_name ilike ?
				)
			`
		var customerIdentifierPattern = "%" + search.CustomerName + "%"
		excludeStatus := []string{
			db.ContractStatusGenerated,
			db.ContractStatusVoided,
		}
		query, args, err := sqlx.In(
			query,
			search.VIN,
			excludeStatus,
			customerIdentifierPattern,
			customerIdentifierPattern,
			customerIdentifierPattern,
			customerIdentifierPattern,
		)
		if err != nil {
			err = errors.Wrap(err, "error getting related contracts")
			util.ReportError(ctx, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Failed to get related contracts", nil)
		}

		query = db.Get().Rebind(query)
		var contracts []CancelSearchContract
		err = db.Get().SelectContext(ctx, &contracts, query, args...)
		if err != nil {
			util.ReportError(ctx, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Error finding contracts", nil)
		}

		// If there are multiple contracts, check if the customer full name is the same for all contracts.
		// If not, return an ambiguous result.
		if len(contracts) > 1 {
			fullName := contracts[0].CustomerFullName
			for _, contract := range contracts {
				if contract.CustomerFullName != fullName {
					searchResult.Status = SearchResultStatusAmbiguous
					searchResult.Message = "Ambiguous search results. Please provide more specific criteria."
					break
				}
			}
		}
		searchResult.Contracts = contracts
	}

	apiResponse, err := handlers.StructToAPIResponse(searchResult)
	if err != nil {
		util.ReportError(ctx, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error converting search result to API response.", nil)
	}
	return http.StatusOK, apiResponse
}

// CancelQuoteRequestPayload represents the request body for the contract cancel quotes endpoint.
type CancelQuoteRequestPayload struct {
	CancelDate      types.JSPQDate `json:"cancelDate" form:"cancelDate" validate:"required"`
	CancelMileage   *int           `json:"cancelMileage" form:"cancelMileage" validate:"required"`
	CancelReasonID  int            `json:"cancelReasonId" form:"cancelReasonId" validate:"required"`
	ContractNumbers []string       `json:"contractNumbers" form:"contractNumbers" validate:"required,min=1"` // require at least one contract number
}

func (input *CancelQuoteRequestPayload) unmarshal(r *http.Request) error {
	if err := r.ParseForm(); err != nil {
		return errors.Wrap(err, "could not parse form")
	}
	if err := formDecoder.Decode(&input, r.Form); err != nil {
		return errors.Wrap(err, "could not decode params")
	}
	return nil
}

// CancelQuoteEstimate represents a single cancel quote estimate.
type CancelQuoteEstimate struct {
	ID                 int                `json:"id"`
	Code               string             `json:"code"`
	Status             string             `json:"status"`
	ProductTypeName    string             `json:"productTypeName"`
	ProductTypeCode    string             `json:"productTypeCode"`
	ProductVariantName string             `json:"productVariantName"`
	StoreCode          string             `json:"storeCode"`
	EffectiveDate      types.JSPQDate     `json:"effectiveDate"`
	Factor             string             `json:"factor"`
	AllFactors         []string           `json:"allFactors"`
	Cancellable        bool               `json:"cancellable"`
	RuleViolations     []RuleViolation    `json:"ruleViolations"`
	Fee                decimal.Decimal    `json:"fee"`
	ClaimTotalAmount   decimal.Decimal    `json:"claimTotalAmount"`
	ClaimsDeducted     bool               `json:"claimsDeducted"`
	CustomerRefund     decimal.Decimal    `json:"customerRefund"`
	CustomerPrice      decimal.Decimal    `json:"customerPrice"`
	SalesTax           decimal.Decimal    `json:"salesTax"`
	ExpirationDate     types.JSPQNullDate `json:"expirationDate"`
	PlanTerm           string             `json:"planTerm"`
}

// APICancelQuoteResponse is the response body for the contract cancel quotes endpoint.
type APICancelQuoteResponse struct {
	Estimates []CancelQuoteEstimate `json:"estimates"`
}

// RuleViolation represents a single rule violation from a cancel quote estimate.
type RuleViolation struct {
	ID               int    `json:"id"`
	Name             string `json:"name"`
	ViolationMessage string `json:"violationMessage"`
	ViolationType    string `json:"violationType"`
}

// ContractCancelQuotes handles the contract cancel quotes endpoint.
func ContractCancelQuotes(w http.ResponseWriter, req *http.Request) (int, handlers.APIResponse) {
	ctx := req.Context()

	var cancelQuoteResponse APICancelQuoteResponse

	// Get the request parameters
	var input CancelQuoteRequestPayload
	if err := input.unmarshal(req); err != nil {
		util.LogWarning(ctx, err)
		return http.StatusBadRequest, handlers.ErrorMessage("Could not read request parameters.", nil)
	}

	// Validate the request parameters
	if err := validate.Struct(input); err != nil {
		err = errors.Wrap(err, "invalid request parameters")
		util.LogWarning(ctx, err)
		return http.StatusBadRequest, handlers.ErrorMessage("Invalid request parameters.", nil)
	}

	validateStore := false
	isAdmin := false
	isManager := false

	// Set up a user that has the user_role to mimic a cancel request from a store user
	user := db.CurrentUser{}
	user.Roles = hstore.Hstore{
		Map: map[string]sql.NullString{
			"user_role": {Valid: true, String: "1"},
		},
	}

	cancelMileage := 0
	if input.CancelMileage != nil {
		cancelMileage = *input.CancelMileage
	}

	requestInfo := handlers.EstimateRequestInfo{
		CancelContractOptions: handlers.CancelContractOptions{
			CancelDate:             input.CancelDate,
			CurrentDate:            time.Now(),
			CancelReasonID:         input.CancelReasonID,
			Mileage:                cancelMileage,
			UserID:                 0, // UserID does not matter for the quote
			CancelPayeeAttentionTo: "",
			ManualTaxRate:          decimal.Zero, // Lenders will not be able to set a manual tax rate
		},
	}

	cancelReason, err := db.GetCancelReasonByID(ctx, input.CancelReasonID)
	if err != nil {
		util.ReportError(ctx, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting cancel reason by id.", nil)
	}

	for _, contractNumber := range input.ContractNumbers {
		var contractID int
		var query = `select id from contracts where code = $1`
		err := db.Get().GetContext(ctx, &contractID, query, contractNumber)
		if err != nil {
			err = errors.Wrapf(err, "error getting contract by code %s", contractNumber)
			util.ReportError(ctx, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Error getting contract by code.", nil)
		}

		contract, err := handlers.ContractInfo(ctx, contractID, cancelReason)
		if err != nil && !errors.Is(err, handlers.ErrDMSLookUpFailed) {
			util.ReportError(ctx, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Error getting contract info.", nil)
		}

		company, err := handlers.GetCompanyForContract(ctx, contractID)
		if err != nil {
			util.ReportError(ctx, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Error getting company for contract.", nil)
		}

		// We will see if contract is of EWU type
		sql := `select count(pv.id) from products p
			join product_variants pv on p.id = pv.product_id
			join product_types pt on p.product_type_id = pt.id
			where pt.code = 'LWT'
				and (p.name ilike '%Toyota%' or p.name ilike '%Lexus%')
				and pv.id = $1`
		var isEWUToyotaOrLexus int
		err = db.Get().Get(&isEWUToyotaOrLexus, sql, contract.ProductVariantID)
		if err != nil {
			err = errors.Wrap(err, "error getting product variants")
			util.ReportError(ctx, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Error getting product variants.", nil)
		}
		contract.IsEWUToyotaOrLexus = isEWUToyotaOrLexus > 0

		if contract.IsEWUToyotaOrLexus {
			sql := `select sum(ca.cost)
				from contract_adjustments ca
				join rate_buckets rb on ca.rate_bucket_id = rb.id
				where ca.contract_id = $1
					and rb.name in ('TCA Admin', 'SPIFF')`
			err := db.Get().Get(&contract.AdminCost, sql, contract.ID)
			if err != nil {
				err = errors.Wrap(err, "error getting admin cost for product")
				util.ReportError(ctx, err)
				return http.StatusInternalServerError, handlers.ErrorMessage("Error getting product variants.", nil)
			}
		}

		requestInfo.CancelReasonName = cancelReason.Name
		cancelEstimate, err := handlers.EstimateCancellation(
			contract,
			requestInfo.CancelContractOptions,
			validateStore,
			isAdmin,
			isManager,
			user,
			&company,
		)
		if err != nil {
			util.ReportError(ctx, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Error estimating cancellation.", nil)
		}

		transformedQuote := transformCancelQuoteEstimate(contract, cancelEstimate)
		cancelQuoteResponse.Estimates = append(cancelQuoteResponse.Estimates, transformedQuote)
	}

	apiResponse, err := handlers.StructToAPIResponse(cancelQuoteResponse)
	if err != nil {
		util.ReportError(ctx, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error converting cancel quote response to API response.", nil)
	}

	return http.StatusOK, apiResponse
}

// formatTerm formats the term for a contract for display in the UI.
func formatTerm(planDuration int, planMileage null.Int) string {
	var displayMileage string
	var displayDuration string
	var term string

	if planMileage.Valid {
		if planMileage.Int64 > 0 {
			displayMileage = fmt.Sprintf("%s Miles", humanize.Comma(planMileage.Int64))
		} else {
			displayMileage = "Unlimited Miles"
		}
	}

	if planDuration > 0 {
		displayDuration = fmt.Sprintf("%d Months", planDuration)
	}

	if len(displayMileage) > 0 && len(displayDuration) > 0 {
		term = fmt.Sprintf("%s / %s", displayMileage, displayDuration)
	} else if len(displayMileage) > 0 {
		term = displayMileage
	} else if len(displayDuration) > 0 {
		term = displayDuration
	}

	return term
}

func transformCancelQuoteEstimate(contract handlers.ContractDetails, estimate handlers.CancelContractQuote) CancelQuoteEstimate {
	transformedQuote := CancelQuoteEstimate{
		ID:                 estimate.ID,
		Code:               estimate.Code,
		Status:             estimate.Status,
		ProductTypeCode:    estimate.ProductTypeCode,
		ProductTypeName:    contract.ProductTypeName,
		ProductVariantName: estimate.ProductTypeName,
		StoreCode:          estimate.StoreCode,
		EffectiveDate: types.JSPQDate{
			NullTime: pq.NullTime{
				Time:  estimate.EffectiveDate.Time,
				Valid: estimate.EffectiveDate.Valid,
			},
		},
		Factor:           estimate.Factor,
		AllFactors:       estimate.AllFactors,
		Cancellable:      estimate.Cancellable,
		Fee:              estimate.Fee,
		ClaimTotalAmount: estimate.ClaimTotalAmount,
		ClaimsDeducted:   estimate.ClaimsDeducted,
		CustomerRefund:   estimate.CustomerRefund,
		CustomerPrice:    contract.Price,
		SalesTax:         estimate.SalesTax,
		ExpirationDate:   contract.ExpirationDate,
		PlanTerm:         formatTerm(contract.PlanDuration, contract.PlanMileage),
	}

	for _, violation := range estimate.RuleViolations {
		transformedQuote.RuleViolations = append(transformedQuote.RuleViolations, RuleViolation{
			ID:               violation.ID,
			Name:             violation.Name,
			ViolationMessage: violation.ViolationMessage,
			ViolationType:    violation.ViolationType,
		})
	}
	return transformedQuote
}

// AutocompleteSuggestion represents a single autocomplete suggestion
type AutocompleteSuggestion struct {
	Suggestion string `json:"suggestion" db:"suggestion"`
	NameType   string `json:"nameType" db:"name_type"`
}

// AutocompleteResponse represents the API response
type AutocompleteResponse struct {
	Suggestions []AutocompleteSuggestion `json:"suggestions"`
}

// AutocompleteRequest represents the query parameters
type AutocompleteRequest struct {
	VIN   string `json:"vin" mod:"trim,ucase" form:"vin" binding:"required" validate:"required,len=17"`
	Query string `json:"query" mod:"trim" form:"query" binding:"required"`
}

func (input *AutocompleteRequest) unmarshal(r *http.Request) error {
	if err := r.ParseForm(); err != nil {
		return errors.Wrap(err, "could not parse form")
	}
	if err := formDecoder.Decode(&input, r.Form); err != nil {
		return errors.Wrap(err, "could not decode params")
	}

	if err := conform.Struct(r.Context(), input); err != nil {
		return errors.Wrap(err, "could not conform params")
	}
	return nil
}

const autocompleteQuery = `
    WITH name_suggestions AS (
      SELECT 
        suggestion,
        name_type
      FROM (
        -- Buyer full names
        SELECT 
          cc.first_name || ' ' || cc.last_name as suggestion,
          'buyer' as name_type
        FROM contracts c
        JOIN vin_records vr ON vr.id = c.vin_record_id
        JOIN customers cc ON c.customer_id = cc.id
        WHERE vr.vin = $1 
          AND c.status <> ANY($2)
          AND cc.first_name IS NOT NULL AND cc.first_name != ''
          AND cc.last_name IS NOT NULL AND cc.last_name != ''
        
        UNION
        
        -- Buyer last names only
        SELECT 
          cc.last_name as suggestion,
          'buyer' as name_type
        FROM contracts c
        JOIN vin_records vr ON vr.id = c.vin_record_id
        JOIN customers cc ON c.customer_id = cc.id
        WHERE vr.vin = $1 
          AND c.status <> ANY($2)
          AND (cc.first_name IS NULL OR cc.first_name = '')
          AND cc.last_name IS NOT NULL AND cc.last_name != ''
        
        UNION
        
        -- Business names
        SELECT 
          cc.business_name as suggestion,
          'business' as name_type
        FROM contracts c
        JOIN vin_records vr ON vr.id = c.vin_record_id
        JOIN customers cc ON c.customer_id = cc.id
        WHERE vr.vin = $1 
          AND c.status <> ANY($2)
          AND cc.business_name IS NOT NULL AND cc.business_name != ''
        
        UNION
        
        -- Cobuyer full names
        SELECT 
          ccb.first_name || ' ' || ccb.last_name as suggestion,
          'cobuyer' as name_type
        FROM contracts c
        JOIN vin_records vr ON vr.id = c.vin_record_id
        JOIN customers cc ON c.customer_id = cc.id
        JOIN sales sa ON sa.id = c.sale_id
        JOIN contract_cobuyers ccb ON sa.contract_cobuyer_id = ccb.id
        WHERE vr.vin = $1 
          AND c.status <> ANY($2)
          AND ccb.id IS NOT NULL
          AND ccb.first_name IS NOT NULL AND ccb.first_name != ''
          AND ccb.last_name IS NOT NULL AND ccb.last_name != ''
        
        UNION
        
        -- Cobuyer last names only
        SELECT 
          ccb.last_name as suggestion,
          'cobuyer' as name_type
        FROM contracts c
        JOIN vin_records vr ON vr.id = c.vin_record_id
        JOIN customers cc ON c.customer_id = cc.id
        JOIN sales sa ON sa.id = c.sale_id
        JOIN contract_cobuyers ccb ON sa.contract_cobuyer_id = ccb.id
        WHERE vr.vin = $1 
          AND c.status <> ANY($2)
          AND ccb.id IS NOT NULL
          AND (ccb.first_name IS NULL OR ccb.first_name = '')
          AND ccb.last_name IS NOT NULL AND ccb.last_name != ''
      ) all_suggestions
    )
    SELECT DISTINCT suggestion, name_type
    FROM name_suggestions
	{{whereClause}}
    ORDER BY suggestion, name_type
	LIMIT 50`

// GetAutocompleteNamesForVINHandler provides autocomplete suggestions for customer names based on VIN
func GetAutocompleteNamesForVINHandler(w http.ResponseWriter, req *http.Request) (int, handlers.APIResponse) {
	ctx := req.Context()

	var input AutocompleteRequest
	if err := input.unmarshal(req); err != nil {
		util.LogWarning(ctx, err)
		return http.StatusBadRequest, handlers.ErrorMessage("Could not read request parameters.", nil)
	}

	// Validate the request parameters
	if err := validate.Struct(input); err != nil {
		err = errors.Wrap(err, "invalid request parameters")
		util.LogWarning(ctx, err)
		return http.StatusBadRequest, handlers.ErrorMessage("Invalid request parameters.", nil)
	}
	excludedStatuses := pq.Array([]string{
		db.ContractStatusGenerated,
		db.ContractStatusVoided,
	})

	var params []interface{}
	params = append(params, input.VIN)
	params = append(params, excludedStatuses)

	query := strings.TrimSpace(input.Query)

	searchQuery := autocompleteQuery
	if query == "" {
		// replace whereClause in autocompleteQuery with empty string
		searchQuery = strings.Replace(searchQuery, "{{whereClause}}", "", 1)
	} else {
		searchPattern := "%" + query + "%" //
		params = append(params, searchPattern)
		// replace whereClause in autocompleteQuery with whereClause
		searchQuery = strings.Replace(searchQuery, "{{whereClause}}", "WHERE suggestion ILIKE $3", 1)
	}

	var suggestions []AutocompleteSuggestion
	err := db.Get().SelectContext(ctx, &suggestions, searchQuery, params...)
	if err != nil {
		err = errors.Wrap(err, "error getting autocomplete suggestions")
		util.ReportError(ctx, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting autocomplete suggestions.", nil)
	}

	response := AutocompleteResponse{
		Suggestions: suggestions,
	}

	apiResponse, err := handlers.StructToAPIResponse(response)
	if err != nil {
		util.ReportError(ctx, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error converting response to API response.", nil)
	}

	return http.StatusOK, apiResponse
}
