openapi: 3.0.4
info:
  version: 1.0.0
  title: TCA Connect Asbury API
  description: |
    Asbury API specification for TCA Connect
    
    The specification provides information about the request, response of the Cancellations API. The API takes a list of files to be processed in TCA Connect and the email of the user that initiated the cancellation. 
    
    It also provided information regarding the authentication required to use the API. 
    
    A sample response of the API is listed as well
    
    ---
    
    # Definitions

    - **TCA** - Total Care Auto
    - **TCA Connect** - Total Care Auto's service which will provide this API
    - **Service** - A Service that is managed by Asbury which will access this API
    
    ---

    # Authentication
    
    API authentication and authorization will rely on cryptographically generated API keys provided by TCA.
    
    ### A. Flow
    
    #### **Service Registration**
    
    Registration is a one time process for each service. If there is a need to replace an API key because of a breach, then the registration process would be repeated.
    
    1. Registration request for Service
    
        Service requests access to the TCA Connect API by contacting TCA's integration team.
        
    2. Registration Process at TCA
    
        TCA generates a cryptographically secure API key for the service, stores the key securely, and shares the API key with the Service. The API key is a 64-character hexadecimal string that provides secure access to the API.

    #### **API Request**

    1. External Service sends request to TCA Connect
        - Service includes the API key in the request header
        - Service sends request to connect with the API key in the X-API-KEY header
        
    2. Connect processing of request
        - Connect receives request, extracts the API key from the X-API-KEY header
        - Connect validates the API key against the stored keys
        - If successful, Connect generates a record of the cancellation, downloads the files listed in the request body and uploads to connects storage for processing.

    ### B. Using API Key for Authentication
     
    The API key should be included in the Authorization header of all requests to the API.

    **Example JavaScript implementation:**
    
    ```javascript
    // Example using fetch API
    async function generateCancellation(userEmail, files) {
        const apiKey = 'your-api-key-here'; // 64-character hex string provided by TCA
        
        const formData = new FormData();
        formData.append('user_email', userEmail);
        
        // Add files to form data
        files.forEach(file => {
            formData.append('files', file);
        });
        
        try {
            const response = await fetch('/asbury/cancellations', {
                method: 'POST',
                headers: {
                    'X-API-KEY': apiKey
                },
                body: formData
            });
            
            if (response.ok) {
                const result = await response.json();
                console.log('Cancellation ID:', result.id);
                return result;
            } else {
                const error = await response.json();
                console.error('Error:', error.message);
                throw new Error(error.message);
            }
        } catch (error) {
            console.error('Request failed:', error);
            throw error;
        }
    }
    
    // Usage example
    const files = [
        new File(['file content'], 'cancellation_form.pdf', { type: 'application/pdf' }),
        new File(['file content'], 'insurance_policy.pdf', { type: 'application/pdf' })
    ];
    
    generateCancellation('<EMAIL>', files)
        .then(result => console.log('Success:', result))
        .catch(error => console.error('Failed:', error));
    ```

    **Example using Axios:**
    
    ```javascript
    const axios = require('axios');
    const FormData = require('form-data');
    const fs = require('fs');
    
    async function generateCancellation(userEmail, filePaths) {
        const apiKey = 'your-api-key-here'; // 64-character hex string provided by TCA
        
        const formData = new FormData();
        formData.append('user_email', userEmail);
        
        // Add files to form data
        filePaths.forEach(filePath => {
            formData.append('files', fs.createReadStream(filePath));
        });
        
        try {
            const response = await axios.post('/asbury/cancellations', formData, {
                headers: {
                    'X-API-KEY': apiKey,
                    ...formData.getHeaders()
                }
            });
            
            console.log('Cancellation ID:', response.data.id);
            return response.data;
        } catch (error) {
            console.error('Error:', error.response?.data?.message || error.message);
            throw error;
        }
    }
    ```

    ---

paths:
  /asbury/cancellations:
    post:
        summary: Generate Cancellation
        description: Generates a cancellation with the provided files by the specified user
        requestBody:
          description: |
            The request payload should have data for the files that represent the supporting documents for the cancellation and the email of the user that initiated the cancellation. The details are given below in schema

            **Examples:**
            
            **Single File Upload:**
            - user_email: "<EMAIL>"  
            - files: [cancellation_form.pdf]
            
            **Multiple Files Upload:**
            - user_email: "<EMAIL>"
            - files: [cancellation_form.pdf, insurance_policy.pdf, vehicle_registration.pdf]
    
          required: true
          content:
            multipart/form-data:
              schema:
                $ref: '#/components/schemas/GenerateCancellationRequest'
              encoding:
                files:
                  # Specify content type for file uploads (optional)
                  contentType: application/pdf, image/jpeg, image/png
                user_email:
                  # Specify content type for text fields (optional)
                  contentType: text/plain

        responses:
          '200':
            description: Returns cancellation id of the cancellation that was generated.
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/GenerateCancellationResponse'
                example: {  
                  "id": 13512
                }

          '400':
            description: |-
               **Status Bad Request**
               
                The following `error_code` values can be returned
                - `MISSING_FILES` : No files were provided in the request body
                - `MISSING_EMAIL`: The user_email field is missing from the request body
                - `TOO_MANY_FILES`: The count of files is more than 5
                - `INVALID_FILE_TYPE`: The file type is not supported
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/Error'
            
          '401': 
            description: |-
               **Status Unauthorized**
               
                The following `error_code` values can be returned
                - `AUTH_HEADER_MISSING`: X-API-KEY header missing
                - `INVALID_API_KEY`: API key is invalid or malformed
                - `API_KEY_NOT_FOUND`: API key not found in system
                - `API_KEY_EXPIRED`: API key has expired and a new key would need to be requested
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/Error'

          '403':
            description: |-
               **Status Forbidden**
               
                The following `error_code` values can be returned
                - `NOT_AUTHORIZED`: the user isn't assigned the appropriate role to perform the action in TCA Connect
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/Error'
                  
          '404':
            description:  |-
               **Status Not found**
               
                The following `error_code` values can be returned
                - `USER_NOT_FOUND`: the user is not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/Error'
            
          '429':
            description: |-
              **Too Many Requests**
              
              Rate limit exceeded. Please retry after the specified time.
              
                The following `error_code` values can be returned
                - `RATE_LIMIT_EXCEEDED`: Too many requests from this user
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/Error'

          '500':
            description: |-
               **Internal Server Error**
               
               This is returned for any internal processing errors. e.g. database read errors
               
                The following `error_code` values can be returned
                - `UNEXPECTED_ERROR`: An unexpected error occurred
                - `COULD_NOT_PROCESS_FILES`: the processing of files from the request body failed
                - `COULD_NOT_UPLOAD_FILES` : uploading of the files in the request body to the server failed
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/Error'
             
security:
  - APIKey: []     # use the same name as under securitySchemes

components:
  securitySchemes:
    APIKey:
      type: apiKey
      in: header
      name: X-API-KEY
      description: |
        API key authentication for service-to-service communication.
        
        **API Key Requirements:**
        - Must be a 64-character hexadecimal string provided by TCA
        - Include the API key in the X-API-KEY header
        - Keep the API key secure and do not expose it in client-side code
        
        **Example Header:**
        ```
        X-API-KEY: a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef12345678
        ```
        
        **Security Notes:**
        - Store API keys securely in environment variables or secure key management systems
        - Rotate API keys periodically as per TCA's security policies
        - Never log or expose API keys in error messages or logs
  schemas:
    Error:
      type: object 
      properties:
        error_code:
          description: A code that identifies the type of error that occurred
          type: string
        message:
          description: human readable description of the error
          type: string

    GenerateCancellationRequest:
      type: object
      description: |
        Request payload for generating a cancellation.
        
        **Usage Examples:**
        - Single file: Provide one file in the files array
        - Multiple files: Provide multiple files in the files array for comprehensive documentation
      properties:
        user_email:
          description: Email address of the user that initiated the cancellation
          type: string
          example: "<EMAIL>"
        files:
          type: array
          items:
            type: string
            format: binary
          description: |
            List of files that represent the supporting documents for the cancellation. Up to 5 files can be uploaded at one time. Following file types are supported
            - PDF
            - JPG
            - JPEG
            - PNG
            - Webp
            - Tiff
            
            **Examples:**
            - Single file: `["cancellation_form.pdf"]`
            - Multiple files: `["cancellation_form.pdf", "insurance_policy.pdf", "vehicle_registration.pdf"]`
          example: ["cancellation_form.pdf", "insurance_policy.pdf"]
      required:
        - user_email
        - files
      example:
        user_email: "<EMAIL>"
        files: ["cancellation_form.pdf", "insurance_policy.pdf"]

    GenerateCancellationResponse:
      type: object
      properties:
        id:
          type: integer
          description: id of the cancellation that was generated
