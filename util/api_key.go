package util

import (
	"crypto/rand"
	"crypto/subtle"
	"encoding/hex"
	"fmt"
	"strings"
	"whiz/conf"

	"golang.org/x/crypto/argon2"
)

// Argon2 parameters - adjust based on your security/performance requirements
const (
	argonTime    = 1         // Number of iterations
	argonMemory  = 64 * 1024 // Memory usage in KB
	argonThreads = 4         // Number of threads
	argonKeyLen  = 32        // Length of derived key
	saltLength   = 32        // Length of salt
)

// hashAP<PERSON><PERSON>ey creates a secure hash of the API key using Argon2
func hashAPIKey(apiKey string, salt []byte) string {
	hash := argon2.IDKey([]byte(apiKey), salt, argonTime, argonMemory, argonThreads, argonKeyLen)
	return hex.EncodeToString(hash)
}

// GenerateAPIKey creates a cryptographically secure 64-character hexadecimal API key
func GenerateAPIKey() (string, string, error) {
	// Generate 32 random bytes (will become 64 hex characters)
	bytes := make([]byte, 32)
	_, err := rand.Read(bytes)
	if err != nil {
		return "", "", fmt.Errorf("failed to generate random bytes: %w", err)
	}
	// Convert to hexadecimal string
	apiKey := hex.EncodeToString(bytes)

	// Hash the API key
	keyHash := hashAPIKey(apiKey, []byte(conf.Get().AsburyAPI.AuthSalt))

	return apiKey, keyHash, nil
}

// ValidateAPIKey validates an API key with constant-time comparison
func ValidateAPIKey(providedKey, storedKeyHash string) error {
	// Clean the provided key
	providedKey = strings.TrimSpace(providedKey)

	// Hash the provided key
	providedKeyHash := hashAPIKey(providedKey, []byte(conf.Get().AsburyAPI.AuthSalt))

	// Use constant-time comparison to prevent timing attacks
	if subtle.ConstantTimeCompare([]byte(providedKeyHash), []byte(storedKeyHash)) != 1 {
		return fmt.Errorf("invalid API key")
	}
	return nil
}
