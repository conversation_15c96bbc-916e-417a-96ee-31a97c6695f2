package util

import (
	"net/http"
	"path/filepath"
	"regexp"
	"strings"
	"whiz/slice"
)

// excludePathPrefixes defines prefixes of paths that should be excluded from middleware
// that expects a user session.
var excludePathPrefixes = []string{
	"/favicon",
	"/api/health",
	"/static",
	"/alpha-external",
	"/ext",
}

// includeFileExtensions defines file extensions that should be included in middleware
var includeFileExtensions = []string{
	".js.map",
}

const (
	nonAlphaNumericRegex = `[^a-zA-Z0-9._-]+`
	maxFilenameLength    = 100
)

// ContinueSessionProcessing will return whether session processing should continue
// depending on the Request's url.
// cSpell: ignore favicons
func ContinueSessionProcessing(req *http.Request) bool {
	urlPath := req.URL.Path

	// The lender API routes are not session-based, so we need to exclude them from session processing.
	if strings.HasPrefix(urlPath, "/lender/api") {
		return false
	}

	fileIsIncluded := false
	for _, extension := range includeFileExtensions {
		if strings.HasSuffix(urlPath, extension) {
			fileIsIncluded = true
			break
		}
	}

	// This will communicate that session middleware functions
	// shouldn't worry about processing session logic when the
	// request is for a `favicon.ico` file or an image under a
	// `favicons` path.
	// Certain other paths are excluded as well.
	// We also want to continue with middleware processing if the urlPath is a
	// file that should be included, like the app.js.map file for example.
	return !slice.ContainsStringPrefix(excludePathPrefixes, urlPath) || fileIsIncluded
}

// SanitizeFilename takes a filename and returns a sanitized version of it
func SanitizeFilename(filename string) string {
	// Extract the extension from the filename
	ext := filepath.Ext(filename)
	// Extract thee basename (without the extension)
	name := strings.TrimSuffix(filename, ext)

	// Regex to match all characters that are not alphanumeric or a space
	re := regexp.MustCompile(nonAlphaNumericRegex)
	sanitizedName := re.ReplaceAllString(name, "_")
	sanitizedName = strings.Trim(sanitizedName, "._")

	availableLength := maxFilenameLength - len(ext)
	if len(sanitizedName) > availableLength {
		sanitizedName = sanitizedName[:availableLength]
	}

	return sanitizedName + ext
}
