package conf

// cSpell: disable
import (
	"fmt"
	"net/smtp"
	"time"

	"github.com/koding/multiconfig"
)

// cSpell: enable

// ExternalAPILogLevel controls how much logging should be done for third party interactions with connect for requests and responses
type ExternalAPILogLevel int

// Defines the different logging levels for third party interactions with connect
const (
	// No logging
	LogLevelNone ExternalAPILogLevel = iota
	// Log all request bodies and only response bodies when external interaction with connect reports an error in the request/response body
	// Does not log response bodies for successful responses.
	LogLevelRequestAndError
	// Log all request and response bodies
	LogLevelFull
)

// cSpell: Ignore Redirector LDCS MSTR

// App is the root-level configuration data for the app
type App struct {
	AppEnv             string `default:"development"`
	S3Bucket           string `required:"true"`
	CookieStoreEncKey  string `required:"true"`
	HTTPSRedirector    bool
	ReportAuthUsername string  `required:"true"`
	ReportAuthPassword string  `required:"true"`
	Server             string  `required:"true"`
	UniRestURL         string  `required:"true"`
	AuthSalt           string  `required:"true"`
	Intacct            Intacct `required:"true"`
	Leads              Leads   `required:"true"`
	Alpha              struct {
		ExternalToken string `required:"true"`
	} `required:"true"`
	AICancellation struct {
		CancellationServer string `required:"true"`
		VINDataS3Path      string `required:"true"`
	} `required:"true"`
	AWS struct {
		MaxSize   int    `default:"10485760"` // 10MiB 1024 * 1024 * 10
		PublicKey string `required:"false"`
	} `required:"false"`
	CDK struct {
		LogLevel ExternalAPILogLevel
		Host     string `required:"true"`
		Username string `required:"true"`
		Password string `required:"true"`
	} `required:"true"`
	CDKDDJ struct {
		Log      bool
		Host     string `required:"true"`
		Username string `required:"true"`
		Password string `required:"true"`
	} `required:"true"`
	CSRF struct {
		Key    string `required:"true"`
		Secure bool
		MaxAge int `default:"43200"` // 12 hours
	} `required:"true"`
	Database struct {
		ConnectionString string `required:"true"`
		Debug            bool
	} `required:"true"`
	Dataone struct {
		AccessKeyID     string `required:"true"`
		SecretAccessKey string `required:"true"`
	} `required:"true"`
	Email Email `required:"true"`
	LDCS  struct {
		URL    string `required:"true"`
		EncIV  string `required:"true"`
		EncKey string `required:"true"`
	}
	MPA struct {
		Log      bool
		BaseURL  string `required:"true"`
		Username string `required:"true"`
		Password string `required:"true"`
		AuthSalt string `required:"true"`
	} `required:"true"`
	MSTR struct {
		URL              string `required:"true"`
		ServerUsername   string `required:"true"`
		ServerPassword   string `required:"true"`
		FinanceUserGroup string `required:"true"`
		ServiceUserGroup string `required:"true"`
	}
	NewRelic struct {
		Enabled bool
		AppName string `required:"true"`
		Token   string
	}
	NSD struct {
		LogLevel ExternalAPILogLevel
		Enabled  bool   `default:"false"`
		Host     string `required:"true"`
		Password string `required:"true"`
	}
	Phizz struct {
		Log      bool
		BaseURL  string `required:"true"`
		AuthSalt string `required:"true"`
		Database string `required:"false"`
	} `required:"true"`
	EWUScp struct {
		Host          string `required:"false"`
		SSHUser       string `required:"false"`
		SSHPrivateKey string `required:"false"`
		RemotePath    string `required:"false"`
	}
	Session struct {
		Secure bool `default:"true"`
		MaxAge int  `default:"43200"` //12 hours
	}
	SPP struct {
		URL      string `required:"true"`
		Username string `required:"true"`
		Password string `required:"true"`
	} `required:"true"`
	StoneEagle struct {
		ClientCode string `required:"true"`
		PassCode   string `required:"true"`
		UserID     string `required:"true"`
	} `required:"true"`
	Tekion struct {
		LogLevel  ExternalAPILogLevel
		Host      string `required:"true"`
		AccessKey string `required:"true"`
		SecretKey string `required:"true"`
		ClientID  string `required:"true"`
	}
	WebPackDev struct {
		BaseURL string `default:"http://localhost:8080"`
	}
	BiDatabase struct {
		ConnectionString string `required:"false"`
	}
	SafeGuard struct {
		PublicKey string `required:"false"`
	}
	ESale struct {
		LenderEmail []string `required:"true"`
	}
	Pen struct {
		LogLevel ExternalAPILogLevel `required:"false"`
	}
	S3ReverseProxy struct {
		EncKey                    string        `required:"true"`
		PathPrefix                string        `required:"true"`
		DefaultLinkTimeoutMinutes time.Duration `required:"true"`
	}
	UCS struct {
		LogLevel ExternalAPILogLevel
		Host     string `required:"true"`
		Username string `required:"true"`
		Password string `required:"true"`
	}
	Okta                       Okta
	Redis                      Redis `required:"true"`
	RateLimit                  RateLimit
	EnablePriceRoundUp         bool   `default:"true"`
	EnablePriceNotRoundUpCheck bool   `default:"true"`
	PriceRoundUpEffectiveDate  string `default:"2025-03-25"`
	PriceRoundUpCutoffDays     int    `default:"90"`
	LenderQuotes               LenderQuotes
	AsburyAPI                  struct {
		AuthSalt string `required:"true"`
	}
}

// Redis contains the configuration data for connecting to a Redis server
type Redis struct {
	Address      string `required:"true"`
	TLSEnabled   bool   `default:"true"`
	Password     string
	DialTimeout  int `default:"5"` // seconds
	ReadTimeout  int `default:"5"` // seconds
	WriteTimeout int `default:"5"` // seconds
	PoolSize     int `default:"10"`
	PoolTimeout  int `default:"30"` // seconds

}

// RateLimit contains the configuration data for rate limiting
type RateLimit struct {
	Enable        bool `default:"true"`
	SlidingWindow struct {
		WindowSize  int64 `default:"10"`  // Size of the Rate Limit Window in seconds
		MaxRequests int64 `default:"100"` // Number of Maximum requests allowed in the window
	}
}

// Okta contains the configuration data for communicating with an Okta
// identification server
type Okta struct {
	ClientID           string `required:"true"`
	ClientSecret       string `required:"true"`
	OktaDomain         string `required:"true"`
	AuthServerName     string `required:"true"`
	RedirectURL        string `required:"true"`
	CookieStoreAuthKey string `required:"true"`
	CookieStoreEncKey  string `required:"true"`
	UseAuthServer      bool   `default:"false"`
	Log                bool
}

// LenderQuotes contains information about the Lender Quote Application
type LenderQuotes struct {
	ServerURL string `required:"true"`
	Okta      struct {
		IssuerURL string `required:"true"`
		Audience  string `required:"true"`
		ClientID  string `required:"true"`
	}
}

// Email contains the configuration data for sending Email
type Email struct {
	UseLog     bool   `required:"false"`
	Host       string `required:"true"`
	Port       int    `required:"true"`
	FromEmail  string `required:"true"`
	Username   string `required:"true"`
	Password   string `required:"true"`
	OverrideTo string
}

// Intacct contains the configuration for Intacct server authentication
type Intacct struct {
	Host                             string `required:"true"`
	SenderID                         string `required:"true"`
	SenderPassword                   string `required:"true"`
	UserID                           string `required:"true"`
	Password                         string `required:"true"`
	CompanyID                        string `required:"true"`
	CancelContractLenderVendorID     string `required:"true"`
	CancelContractLenderVendorName   string `required:"true"`
	CancelContractCustomerVendorID   string `required:"true"`
	CancelContractCustomerVendorName string `required:"true"`
	CancelContractGLAccountNumber    string `required:"true"`
}

// Leads contains the configuration for leads api request
type Leads struct {
	Host  string `required:"true"`
	Token string `required:"true"`
}

// Server returns the Email configuration host and port combined in one string
func (e Email) Server() string {
	return fmt.Sprintf("%s:%d", e.Host, e.Port)
}

// SMTPAuth returns a PlainAuth from the Email configuration data
func (e Email) SMTPAuth() smtp.Auth {
	return smtp.PlainAuth("", e.Username, e.Password, e.Host)
}

var app *App

// Get either returns an already-loaded configuration or loads config.toml
var Get = func() *App {
	if app == nil {
		appConf := new(App)
		confLoader := multiconfig.DefaultLoader{
			Loader: multiconfig.MultiLoader(
				&multiconfig.TagLoader{},
				&multiconfig.TOMLLoader{Path: "config.toml"},
				&multiconfig.EnvironmentLoader{},
			),
			Validator: multiconfig.MultiValidator(&multiconfig.RequiredValidator{}),
		}
		confLoader.MustLoad(appConf)
		app = appConf
	}

	return app
}
