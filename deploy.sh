#!/usr/bin/env bash

. ./deploy_config.sh

if [ -z "$sentry_deploy_token" ]
then
  echo 'Missing sentry_deploy_token. Make sure to `cp deploy_config-sample.sh deploy_config.sh`'
  exit 1
fi

if [ $golang_path ]
then
  PATH=$golang_path:$PATH
fi

if [ $nodejs_path ]
then
  PATH=$nodejs_path:$PATH
fi

start_time=`date "+%Y%m%d%H%M%S"`
GOOS=linux
GOARCH=amd64
ssh_user=landcar
shared_path=/var/www/whiz/shared
current_path=/var/www/whiz/current
release_path=/var/www/whiz/releases/$start_time
releases_path=/var/www/whiz/releases
environment=$1
local_username=`whoami`
binary_name=whiz

revision=`git log -n 1 --pretty=format:"%H"`
if [ -e "${environment}_previous_revision" ]
then 
    previous_revision=$(cat ${environment}_previous_revision)
else
    previous_revision=`git log HEAD^1 -n 1 --pretty=format:"%H"`
fi

if [ "$environment" = "production" ]; then
	hosts=("************" "*************")
	migrate_host="************"
	NODE_ENV=production
elif [ "$environment" = "stage" ]; then
	hosts=("**************")
	migrate_host="**************"
	NODE_ENV=production
elif [ "$environment" = "uat" ]; then
	hosts=("**************")
	migrate_host="**************"
	NODE_ENV=production
elif [ "$environment" = "testa" ]; then
	hosts=("**************")
	migrate_host="**************"
	NODE_ENV=production
elif [ "$environment" = "testb" ]; then
	hosts=("*************")
	migrate_host="*************"
	NODE_ENV=production
else
	echo "Invalid environment.  Specify environment {uat,testa,testb,stage,production}."
	exit 1
fi

set -e # exit on any non-zero returning command
set -x # echo on

############################ building binaries"
rm -rf deploy
mkdir -p deploy

GOOS=$GOOS GOARCH=$GOARCH go mod download

GOOS=$GOOS GOARCH=$GOARCH go build -ldflags "-X main.Revision=${revision}" -o $binary_name
mv $binary_name deploy
GOOS=$GOOS GOARCH=$GOARCH go build -o dpmigrate cmd/dpmigrate/main.go
mv dpmigrate deploy
GOOS=$GOOS GOARCH=$GOARCH go build -o clean_users cmd/clean_users/main.go
mv clean_users deploy
GOOS=$GOOS GOARCH=$GOARCH go build -o clean_quotes cmd/clean_quotes/main.go
mv clean_quotes deploy
GOOS=$GOOS GOARCH=$GOARCH go build -o create_e_contract cmd/create_e_contract/main.go
mv create_e_contract deploy
GOOS=$GOOS GOARCH=$GOARCH go build -o generate_all_products_transmittal cmd/generate_all_products_transmittal/main.go
mv generate_all_products_transmittal deploy
if [ "$environment" = "stage" ] || [ "$environment" = "uat" ] || [ "$environment" = "uat2" ] || [ "$environment" = "uat3" ]; then
    GOOS=$GOOS GOARCH=$GOARCH go build -o add_test_users cmd/add_test_users/main.go
    mv add_test_users deploy
fi
cp -p download-vin-data.sh deploy

############################ build CSS & Javascript"
yarn install
# NODE_ENV=$NODE_ENV npx webpack build --env min
# For now, test `--env prod` when deploying to UAT
npx webpack build --env prod --env min

############################ copying public dir"
cp -r public deploy/public

############################ copying migrations dir
cp -r migrations deploy/migrations

############################ copying templates dir
cp -r templates deploy/templates

############################ copying files dir"
cp -r files deploy/files

############################ copying spp/Quote.pdf
mkdir -p deploy/spp
cp spp/Quote.pdf deploy/spp/

############################ begin ssh control session
for host in "${hosts[@]}"
do
	ssh -o "ControlMaster=auto" -o "ControlPath=/tmp/%r@%h:%p" -o "ControlPersist=10m" $ssh_user@$host "exit"
done

############################ copying build
for host in "${hosts[@]}"
do
	scp -o "ControlPath=/tmp/%r@%h:%p" -r deploy $ssh_user@$host:$release_path
	ssh -o "ControlPath=/tmp/%r@%h:%p" $ssh_user@$host ln -s $shared_path/log $release_path/log
	ssh -o "ControlPath=/tmp/%r@%h:%p" $ssh_user@$host ln -s $shared_path/config.toml $release_path/config.toml
	ssh -o "ControlPath=/tmp/%r@%h:%p" $ssh_user@$host ln -s $shared_path/download-vin-data-config.sh $release_path/download-vin-data-config.sh
done

############################ finalizing / rebooting
for host in "${hosts[@]}"
do
	ssh -o "ControlPath=/tmp/%r@%h:%p" $ssh_user@$host rm -f $current_path
	ssh -o "ControlPath=/tmp/%r@%h:%p" $ssh_user@$host ln -f -s $release_path $current_path
	ssh -o "ControlPath=/tmp/%r@%h:%p" $ssh_user@$host sudo systemctl restart whiz
	sleep 4
done

############################ running migrations
ssh -o "ControlPath=/tmp/%r@%h:%p" $ssh_user@$migrate_host "cd $release_path && ./dpmigrate up"

############################ removing old releases
for host in "${hosts[@]}"
do
  ssh -o "ControlPath=/tmp/%r@%h:%p" $ssh_user@$host "cd $releases_path && ls -1tr | head -n -5 | xargs -d '\n' rm -rf --"
done

############################ end ssh control session
for host in "${hosts[@]}"
do
	ssh -O stop -o "ControlPath=/tmp/%r@%h:%p" $ssh_user@$host
done

############################ cleaning up
rm -rf deploy

############################ create release in sentry
curl https://sentry.io/api/0/organizations/landcar-inc/releases/ \
  -X POST \
  -H "Authorization: Bearer ${sentry_deploy_token}" \
  -H 'Content-Type: application/json' \
  -d "
 {
    \"version\": \"whiz-${revision}\",
    \"projects\":[\"whiz\"],
    \"refs\": [{
        \"repository\":\"totalcareauto/whiz\",
        \"commit\":\"${revision}\",
        \"previousCommit\":\"${previous_revision}\"
    }]
 }"

############################ Notify sentry that release has been deployed.
curl https://sentry.io/api/0/organizations/landcar-inc/releases/whiz-${revision}/deploys/ \
   -X POST \
   -H "Authorization: Bearer ${sentry_deploy_token}" \
   -H 'Content-Type: application/json' \
   -d "
 {
    \"environment\": \"${environment}\"
 }"

# Save the revision that was just deployed for $environment so it can be used when creating the next release
echo $revision > ${environment}_previous_revision
