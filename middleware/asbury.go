package middleware

import (
	"context"
	"database/sql"
	"fmt"
	"net/http"
	"strings"
	"time"

	"whiz/db"
	"whiz/handlers"
	"whiz/util"

	"github.com/pkg/errors"
	"gopkg.in/guregu/null.v3"
)

var errAsburyErrInvalidAPIKey = errors.New("[Asbury] Invalid API key")
var errAsburyErrMissingAuthHeader = errors.New("[Asbury] Missing Authorization header")

type asburyAuthResult struct {
	err        error
	errMsg     string
	errCode    string
	statusCode int
}

// AsburyAuthenticate is chi middleware for validating asbury request
func AsburyAuthenticate(next http.Handler) http.Handler {
	fn := func(w http.ResponseWriter, req *http.Request) {
		var asburyAuthResult asburyAuthResult
		req, asburyAuthResult = asburyAuth(req)
		if asburyAuthResult.err != nil {
			util.ReportError(req.Context(), asburyAuthResult.err)
			data := make(map[string]interface{})
			data["error_code"] = asburyAuthResult.errCode
			_ = handlers.Renderer().JSON(w, asburyAuthResult.statusCode, handlers.ErrorMessage(asburyAuthResult.errMsg, data))
			return
		}
		next.ServeHTTP(w, req)
	}
	return http.HandlerFunc(fn)
}

type asburyKey struct {
	KeyHash   string    `db:"key_hash" json:"key_hash"`
	IsActive  bool      `db:"is_active" json:"is_active"`
	CreatedAt time.Time `db:"created_at" json:"created_at"`
	ExpiresAt null.Time `db:"expires_at" json:"expires_at"`
	Client    string    `db:"client" json:"client"`
}

// validateAPIKey validates an API key with constant-time comparison
func validateAPIKey(ctx context.Context, providedKey string) error {
	// Clean the provided key
	providedKey = strings.TrimSpace(providedKey)

	asburyKey := &asburyKey{}
	err := db.Get().Unsafe().GetContext(ctx, asburyKey, `select * from api_keys where client = $1 and is_active=true`, db.APIClientNameAsbury)
	if err != nil {
		if err == sql.ErrNoRows {
			return fmt.Errorf("API key not found")
		}
		return fmt.Errorf("error checking API key: %w", err)
	}

	// Check if key is active
	if !asburyKey.IsActive {
		return fmt.Errorf("API key is inactive")
	}

	if asburyKey.ExpiresAt.Valid && asburyKey.ExpiresAt.Time.Before(time.Now()) {
		return fmt.Errorf("API key has expired")
	}

	err = util.ValidateAPIKey(providedKey, asburyKey.KeyHash)
	if err != nil {
		return fmt.Errorf("invalid API key: %w", err)
	}

	return nil
}

func asburyAuth(req *http.Request) (*http.Request, asburyAuthResult) {
	ctx := req.Context()

	apiKey := req.Header.Get("X-API-KEY")
	if apiKey == "" {
		return req, asburyAuthResult{
			err:        errAsburyErrMissingAuthHeader,
			errMsg:     "Authorization header missing",
			errCode:    db.ErrCodeAuthHeaderMissing,
			statusCode: http.StatusUnauthorized,
		}
	}

	err := validateAPIKey(ctx, apiKey)
	if err != nil {
		return req, asburyAuthResult{
			err:        errAsburyErrInvalidAPIKey,
			errMsg:     err.Error(),
			errCode:    db.ErrCodeAuthHeaderInvalid,
			statusCode: http.StatusUnauthorized,
		}
	}
	return req, asburyAuthResult{err: nil, errMsg: "", statusCode: http.StatusOK}
}
