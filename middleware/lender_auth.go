package middleware

import (
	"context"
	"log"
	"net/http"
	"strings"

	"whiz/conf"
	"whiz/handlers"
	"whiz/util"

	jwtverifier "github.com/okta/okta-jwt-verifier-golang/v2"
	"github.com/pkg/errors"
)

var lenderTokenVerifier *jwtverifier.JwtVerifier

// init initializes the lenderTokenVerifier.
func init() {
	config := conf.Get()

	if config.LenderQuotes.Okta.IssuerURL == "" {
		log.Fatalln("Error: LenderQuotes.Okta.IssuerURL is not configured. Okta Lender API JWT Verifier will not be initialized.")
		return
	}

	if config.LenderQuotes.Okta.Audience == "" {
		log.Fatalln("Error: LenderQuotes.Okta.Audience is not configured. Okta Lender API JWT Verifier will not be initialized.")
		return
	}

	if config.LenderQuotes.Okta.ClientID == "" {
		log.Fatalln("Error: LenderQuotes.Okta.ClientID is not configured. Okta Lender API JWT Verifier will not be initialized.")
		return
	}

	issuer := config.LenderQuotes.Okta.IssuerURL

	// Validate both the audience and client ID
	toValidate := map[string]string{}
	toValidate["aud"] = config.LenderQuotes.Okta.Audience
	toValidate["cid"] = config.LenderQuotes.Okta.ClientID

	jwtVerifierSetup := jwtverifier.JwtVerifier{
		Issuer:           issuer,
		ClaimsToValidate: toValidate,
	}

	verifier, err := jwtVerifierSetup.New()
	if err != nil {
		log.Fatalf("Error initializing Okta Lender API JWT Verifier: %v", err)
		return
	}
	// Allow for a clock skew of 5 minutes instead of the default 2 minutes
	verifier.SetLeeway("5m")
	lenderTokenVerifier = verifier
	log.Println("Okta Lender API JWT Verifier initialized.")

}

// ValidateLenderToken is middleware that validates an Okta Bearer token for lender API routes.
// It returns a 401 Unauthorized response if the token is invalid.
func ValidateLenderToken(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		authHeader := r.Header.Get("Authorization")
		if !strings.HasPrefix(authHeader, "Bearer ") {
			_ = handlers.Renderer().JSON(w, http.StatusUnauthorized, handlers.ErrorMessage("Bearer token required", nil))
			return
		}

		ctx := r.Context()
		token := strings.TrimPrefix(authHeader, "Bearer ")
		if !isValidLenderOktaToken(ctx, token) {
			_ = handlers.Renderer().JSON(w, http.StatusUnauthorized, handlers.ErrorMessage("Invalid token", nil))

			return
		}

		next.ServeHTTP(w, r)
	})
}

// isValidLenderOktaToken validates the token using the initialized verifier.
func isValidLenderOktaToken(ctx context.Context, tokenString string) bool {
	if lenderTokenVerifier == nil {
		err := errors.New("okta lender api jwt verifier is not initialized - this should not have happened")
		util.LogError(ctx, err)
		return false
	}

	_, err := lenderTokenVerifier.VerifyAccessToken(tokenString)
	if err != nil {
		err = errors.Wrap(err, "okta lender api token validation error")
		util.LogError(ctx, err)
		return false
	}
	return true
}
