package ratelimit

import (
	"context"
	"net/http"
	"time"
	"whiz/conf"
	"whiz/handlers"
	"whiz/util"
	"whiz/util/ratelimiters"

	"github.com/pkg/errors"
)

var (
	swLimiter *ratelimiters.SlidingWindowLimiter
)

// InitiSlidingWindowLimiter initializes the sliding window rate limiter
func InitiSlidingWindowLimiter(ctx context.Context) {
	util.LogMessage(ctx, "Initializing Sliding Window Rate Limiter")

	config := conf.Get().RateLimit.SlidingWindow

	ws := time.Duration(config.WindowSize) * time.Second
	swLimiter = ratelimiters.NewSlidingWindowLimiter(
		ws,
		config.MaxRequests,
	)

	util.LogMessage(ctx, "Sliding Window Rate Limiter initialized successfully")
	util.LogMessagef(ctx, "Sliding Window Rate Limiter window size: %s", ws)
	util.LogMessagef(ctx, "Sliding Window Rate Limiter max requests: %d", config.MaxRequests)
}

// SlidingWindowRateLimitCheck is a middleware that checks if the rate limit has been exceeded using the Sliding Window algorithm
func SlidingWindowRateLimitCheck(next http.Handler) http.Handler {

	fn := func(w http.ResponseWriter, req *http.Request) {
		ctx := req.Context()
		data := make(map[string]interface{})
		cfg := conf.Get().RateLimit

		// Do rate limiting only if it's enabled in the configuration
		if !cfg.Enable {
			util.LogMessage(ctx, "Rate Limiting is disabled")
			next.ServeHTTP(w, req)
			return
		}

		key := generateRedisKey(req)

		util.LogMessagef(ctx, "Checking rate limit for key: %s", key)

		allowed, err := swLimiter.Allow(ctx, key)
		if err != nil {
			err = errors.Wrap(err, "failed to check rate limit")
			util.ReportError(ctx, err)
			data["error_code"] = "UNEXPECTED_ERROR"
			_ = handlers.Renderer().JSON(w, http.StatusInternalServerError, handlers.ErrorMessage("Rate Limit Check error", data))
			return
		}

		if !allowed {
			remaining, err := swLimiter.GetRemainingRequests(ctx, key)
			if err != nil {
				err = errors.Wrap(err, "failed to get remaining requests")
			} else {
				err = errors.Errorf("rate limit exceeded for key [%s]", key)
			}

			data["error_code"] = "RATE_LIMIT_EXCEEDED"
			swLimiter.AddRateLimitHeaders(w, remaining)
			util.ReportError(ctx, err)
			_ = handlers.Renderer().JSON(w, http.StatusTooManyRequests, handlers.ErrorMessage("Rate Limit exceeded", data))
			return
		}

		next.ServeHTTP(w, req)
	}

	return http.HandlerFunc(fn)
}
