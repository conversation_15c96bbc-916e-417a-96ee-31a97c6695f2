# Cancellation Date Backdating Warning System

## Overview

The backdating warning system provides soft popup warnings when users enter cancellation dates in Connect that exceed established backdating rules. This helps ensure compliance with business rules while still allowing authorized users to proceed when necessary.

## Business Rules

The system enforces different backdating limits based on the type of cancellation:

- **Flat Cancel**: 30 days
- **Repo**: 180 days  
- **General Cancellations**: 90 days

## Implementation

### Shared Utility

The core functionality is implemented in `assets/js/shared/backdating-warning.js` which provides:

- `checkBackdatingWarning()` - Main function to check dates and show warnings
- `getBackdatingLimit()` - Helper to determine appropriate limits
- `getDaysFromToday()` - Calculate days difference from current date
- `BACKDATING_LIMITS` - Constants for the different limits

### Integration Points

The warning system is integrated into the following cancellation forms:

1. **Admin Cancellations Dashboard** (`assets/js/admin/cancellations_dashboard/CancellationPanel.jsx`)
2. **LCA Cancellation Modal** (`assets/js/app/lca/contracts/CancellationRequestModal.jsx`)
3. **Admin Contracts Search** (`assets/js/admin/contracts_search/Cancellation.jsx`)

### User Experience

When a user selects a cancellation date that exceeds the backdating limit:

1. A popup warning appears showing:
   - Number of days the date is in the past
   - The applicable limit for the cancellation type
   - Option to continue or cancel

2. If user clicks "OK": The date is accepted and processing continues
3. If user clicks "Cancel": The date is reset to the current date

### Example Warning Messages

- **Flat Cancel**: "Warning: The selected cancellation date is 35 days in the past, which exceeds the 30-day backdating limit for flat cancel cancellations. Please review this date before proceeding."

- **General**: "Warning: The selected cancellation date is 95 days in the past, which exceeds the 90-day backdating limit for general cancellations. Please review this date before proceeding."

- **Repo**: "Warning: The selected cancellation date is 185 days in the past, which exceeds the 180-day backdating limit for repo cancellations. Please review this date before proceeding."

## Technical Details

### Function Signature

```javascript
checkBackdatingWarning(cancelDate, cancelReasonId, cancelReasons, onReset)
```

**Parameters:**
- `cancelDate` - The cancellation date to check (moment, Date, or string)
- `cancelReasonId` - ID of the selected cancel reason
- `cancelReasons` - Array of cancel reason objects with id and name
- `onReset` - Optional callback function to reset the date

**Returns:**
- `true` - User wants to continue with the selected date
- `false` - User cancelled, date should be reset

### Reason Type Detection

The system determines the appropriate limit by checking the cancel reason name:

- Contains "flat cancel" → 30 day limit
- Contains "repo" → 180 day limit  
- All others → 90 day limit (default)

### Trigger Events

Warnings are triggered when:

1. User changes the cancellation date
2. User changes the cancel reason (if date is already set)

## Testing

Unit tests are provided in `assets/js/shared/__tests__/backdating-warning.test.js` covering:

- Limit constants
- Date calculation functions
- Warning logic for different scenarios
- User interaction handling

## Configuration

The backdating limits are defined as constants in the shared utility and can be easily modified if business rules change:

```javascript
export const BACKDATING_LIMITS = {
  FLAT_CANCEL: 30,    // 30 days for flat cancels
  GENERAL: 90,        // 90 days for general cancellations
  REPO: 180          // 180 days for repo cancellations
};
```

## Future Enhancements

Potential improvements could include:

1. **Role-based limits**: Different limits for different user roles
2. **Configuration UI**: Admin interface to modify limits
3. **Audit logging**: Track when users override warnings
4. **Enhanced messaging**: More detailed explanations for specific scenarios
5. **Integration with existing cancel stop rules**: Coordinate with hard validation rules
