package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"net/http"
	_ "net/http/pprof"
	"os"
	"time"

	"whiz/conf"
	"whiz/db"
	"whiz/handlers"
	whizMiddleware "whiz/middleware"
	whizRateLimiters "whiz/middleware/ratelimit"
	"whiz/middleware/request"
	"whiz/okta"
	"whiz/routes"
	"whiz/session"
	"whiz/tasks"
	"whiz/telemetry"

	"github.com/go-chi/chi"
	"github.com/go-chi/chi/middleware"
	_ "golang.org/x/lint" // golint from this package is used from the cli, not from code. It needs to be imported from any go file to ensure that `go tidy` doesn't remove this dependency from go.mod.
	graceful "gopkg.in/tylerb/graceful.v1"
)

var (
	// Revision used for sentry revision string
	Revision = "development"
)

func main() {
	ctx := context.Background()
	log.SetOutput(os.Stdout) // this is for when the app is run under realize
	config := conf.Get()
	telemetry.InitSentry(config.AppEnv, Revision)

	// Check for alternate tasks which don't involve launching the service
	var jobName string
	tasks.CreateJobFlag(&jobName)
	flag.Parse()
	if jobName != "" {
		tasks.Run(ctx, jobName)
		return
	}

	// Continue with normal initialization and startup
	r := chi.NewRouter()
	oktaClient := okta.NewClient(config.Okta.OktaDomain, config.Okta.AuthServerName, config.Okta.Log)
	oktaAPI := okta.NewAPI(config.Okta, oktaClient)

	// Initialize Rate Limiters
	whizRateLimiters.InitiSlidingWindowLimiter(ctx)

	// Setup Session Types (This might be able to be more configurable)
	var sessionDataTypes = []session.DataType{
		session.GetConnectSessionDataType(),
		session.GetWhizSessionDataType(),
		okta.GetSessionDataType(),
	}

	// Set the session logging flag
	session.SessionLogging = config.Okta.Log
	session.SetMaxAge(config.Session.MaxAge)
	// Initialize the Session Management
	session.InitSession(config.Okta.CookieStoreAuthKey, config.Okta.CookieStoreEncKey, sessionDataTypes)
	r.Use(middleware.RequestID) // Set reqID in context
	r.Use(request.ReqIDHeader)  // Set Request-ID in header
	r.Use(request.Request)      // Add request to context
	r.Use(middleware.RealIP)
	r.Use(session.Middleware())
	r.Use(middleware.Logger)
	r.Use(whizMiddleware.ReportRecoverer)
	r.Use(middleware.DefaultCompress)
	r.Use(whizMiddleware.SecureHeaders) // Add security headers

	routes.APIHandlers(r)
	routes.DownloadHandlers(r)
	routes.SessionHandlers(r, session.EndSessionHandler)
	routes.UserHandlers(r)
	routes.StaticHandlers(r)
	routes.AlphaHandlers(r)
	routes.ReportHandlers(r)
	routes.ExtHandlers(r)
	routes.XtkHandlers(r)
	routes.AWSHandlers(r)
	routes.LenderHandlers(r)

	oktaAPI.MountRoutes(r)

	routes.TemplateHandlers(r) // Last because of route for `/*`

	okta.OktaNonceService = okta.NewNonceService(db.Get())

	// NOTE: print routes
	//var printRoutes func(routes chi.Routes, indent string)
	//printRoutes = func(routes chi.Routes, indent string) {
	//	for _, route := range routes.Routes() {
	//		fmt.Printf("%s%s\n", indent, route.Pattern)
	//		if route.SubRoutes != nil {
	//			printRoutes(route.SubRoutes, indent+"  ")
	//		}
	//	}
	//}
	//printRoutes(r, "")

	// Pprof server.
	go func() {
		port := 6060
		log.Printf("Starting Pprof server on port %d", port)
		log.Fatal(http.ListenAndServe(fmt.Sprintf("localhost:%d", port), nil))
	}()

	if config.HTTPSRedirector {
		go (func() {
			s := &http.Server{
				Addr:         ":8081",
				Handler:      http.HandlerFunc(handlers.HTTPSRedirector),
				ReadTimeout:  2 * time.Second,
				WriteTimeout: 2 * time.Second,
				IdleTimeout:  2 * time.Second,
			}
			log.Fatal(s.ListenAndServe())
		})()
	}

	port := os.Getenv("PORT")
	if port == "" {
		port = "4000"
	}
	serverStr := ":" + port
	server := &http.Server{
		Addr:              serverStr,
		Handler:           r,
		ReadHeaderTimeout: 10 * time.Second,
		ReadTimeout:       240 * time.Second,
		WriteTimeout:      240 * time.Second,
		IdleTimeout:       10 * time.Second,
	}
	if config.AppEnv == "development" {
		log.Printf("Listening on '%s'...\n", serverStr)
		log.Fatal(func() error {
			err := server.ListenAndServe()
			if err != nil {
				okta.OktaNonceService.Shutdown()
			}
			return err
		}())
	} else {
		log.Printf("Listening on '%s' with graceful shutdown...\n", serverStr)
		log.Fatal(func() error {
			err := graceful.ListenAndServe(server, 2*time.Second)
			if err != nil {
				okta.OktaNonceService.Shutdown()
			}
			return err
		}())
	}
}
