# Create E-Contract CLI

This CLI application creates and activates e-contracts from JSON payload files without requiring HTTP request/response objects.

## Prerequisites

- Go 1.6+
- PostgreSQL database with the Whiz schema
- Valid XTK user code
- SYSTEM user must exist in the `current_users` table
- Project's `config.toml` file must be present from where the application is being executed

## Usage

```bash
./create_e_contract -input <json_file> -xtk_user_code <xtk_code> [-do_not_check_dms]
```

## Command Line Arguments

- `-input`: Path to the JSON file containing the eContract payload (required)
- `-xtk_user_code`: XTK user code for dealer platform authentication (required)
- `-do_not_check_dms`: Optional flag to skip DMS validation for deal number

## Example

```bash
./create_e_contract -input sample_econtract_payload.json -xtk_user_code "DEALER001"
```

### Skip DMS Validation

To skip DMS validation for the deal number:

```bash
./create_e_contract -input sample_econtract_payload.json -xtk_user_code "DEALER001" -do_not_check_dms
```

## JSON Payload Structure

The JSON file should contain an `EContractPayload` structure with the following fields:

### Required Fields

- `e_sale_id` (int): The ID of the e-sale record
- `employee_number` (string): Employee number for the salesperson
- `customer`: Customer information object
  - `first_name` (string): Customer's first name
  - `last_name` (string): Customer's last name (required)
  - `address` (string): Customer's address (required)
  - `city` (string): Customer's city (required)
  - `state_code` (string): Customer's state code (required)
  - `postal_code` (string): Customer's postal code (required)
  - `email` (string): Customer's email address
  - `phone` (string): Customer's phone number
  - `cellular` (string): Customer's cellular number
  - `customer_number` (string): Customer number in DMS
- `dms_number` (string): DMS deal number
- `finance_amount` (decimal): Finance amount
- `vehicle_price` (decimal): Vehicle price
- `lender`: Lender information object
  - `name` (string): Lender name
  - `address` (string): Lender address
  - `city` (string): Lender city
  - `state_code` (string): Lender state code
  - `postal_code` (string): Lender postal code
  - `finance_source` (string): Finance source (e.g., "BANK", "SPP")
- `product`: Product details object
  - `product_type_id` (int): Product type ID
  - `product_variant_id` (int): Product variant ID
  - `plan_id` (int): Plan ID
  - `option_ids` ([]int): Array of option IDs
  - `price` (decimal): Product price (required)
  - `vta_number` (string): Vehicle theft number

### Optional Fields

- `cobuyer`: Co-buyer information object (optional)
  - `first_name` (string): Co-buyer's first name
  - `last_name` (string): Co-buyer's last name
  - `address` (string): Co-buyer's address
  - `city` (string): Co-buyer's city
  - `state_code` (string): Co-buyer's state code
  - `postal_code` (string): Co-buyer's postal code
  - `email` (string): Co-buyer's email address
  - `home_phone` (string): Co-buyer's home phone
  - `alt_phone` (string): Co-buyer's alternate phone
- `finance_apr` (decimal): Finance APR (required for lease/loan deals)
- `finance_monthly_payment` (decimal): Monthly payment amount (required for lease/loan deals)
- `first_payment_date` (date): First payment date in format `2006-01-02` (required for lease/loan deals)

## Getting Product Details

To obtain the correct product details (product_type_id, product_variant_id, plan_id, option_ids), it's recommended to:

1. Navigate to **Admin → Dealer Systems → API Test** page
2. Use the **Rating** API call with appropriate parameters
3. Open the browser's developer tools (F12) and examine the response
4. Extract the product details from the API response

This ensures you have the most up-to-date and valid product information for your e-contract.

## XTK User Code Requirements

The XTK user code is used to identify the dealer platform and validate store access. This code must:

- Be a valid XTK user code that exists in the database
- Be associated with an active dealer platform
- Have access to the store specified in the e-sale record
- Be active and not expired

## Validation Process

The CLI application performs the following validation steps:

1. **Command Line Validation**: Validates all required command line arguments
2. **File Validation**: Ensures the input JSON file exists and is readable
3. **JSON Validation**: Validates the JSON structure and parses the payload
4. **Payload Validation**: Validates all required fields in the JSON payload
5. **XTK Code Validation**: Verifies the XTK code exists and is associated with an active dealer platform
6. **Store Access Validation**: Ensures the dealer platform has access to the store specified in the e-sale
7. **E-Sale Validation**: Verifies the e-sale exists and is valid
8. **Product Validation**: Validates product type, variant, plan, and options
9. **Price Validation**: Ensures the product price is within acceptable limits
10. **GAP License Validation**: Validates GAP license requirements if applicable

## Sample JSON Payload

See [sample_econtract_payload.json](./sample_econtract_payload.json) for a complete example of the JSON payload structure.

## Features

- **No HTTP Dependencies**: Uses refactored functions that don't require HTTP request/response objects
- **XTK Code Authentication**: Uses XTK user code for dealer platform identification and validation
- **Store Access Validation**: Validates that the dealer platform has access to the store
- **Automatic Contract Creation and Activation**: Creates the contract and immediately activates it
- **System User Authentication**: Uses the SYSTEM user for all operations
- **Shared Logic**: Uses the same core logic as the HTTP handlers for consistency
- **DMS Validation Control**: Optional flag to skip DMS validation for deal numbers

## Implementation Details

The CLI application uses the `CreateEContractFromPayload()` function from the `handlers` package to create an e-contract from the JSON payload, followed by the `RemitContract()` function to activate the contract. These functions encapsulate the core logic from the HTTP handlers but without HTTP dependencies.

### Shared Functions Used

- `CreateEContractFromPayload()`: Creates an e-contract from the JSON payload
- `RemitContract()`: Activates the created contract
- `GetContractByCode()`: Retrieves the created contract for activation

## Error Handling

The application will exit with a non-zero status code and display an error message if:

- Required command line arguments are missing
- The input file is not provided or cannot be read
- The JSON file cannot be parsed
- The XTK user code is invalid or the dealer platform cannot be found
- The dealer platform does not have access to the store
- The SYSTEM user cannot be found in the database
- Contract creation fails
- Contract activation fails

## Output

The CLI application will output:

- Success messages when the contract is created and activated
- Error messages if any step fails
- Contract code and details in the success message

Example output:
```
2024/01/15 10:30:00 Input Variables
2024/01/15 10:30:00 Input File: sample_econtract_payload.json
2024/01/15 10:30:00 Xtk User Code: DEALER001
2024/01/15 10:30:00 Do Not Check DMS: false
2024/01/15 10:30:01 Contract created successfully: VSC123456E (12345) for store 1
2024/01/15 10:30:02 Contract activated successfully: VSC123456E
```

## Building

To build the CLI application:

```bash
go build -o create_e_contract cmd/create_e_contract/main.go
```

## Running

After building, you can run the application:

```bash
./create_e_contract -input sample_econtract_payload.json -xtk_user_code "DEALER001"
```

### Development Environment

When working in the development environment from the root project folder, you can also run the application directly with Go:

```bash
go run cmd/create_e_contract/main.go -input sample_econtract_payload.json -xtk_user_code "DEALER001"
```

## Troubleshooting

### Common Issues

1. **"Input file is required"**: Make sure to provide the `-input` flag with a valid JSON file path
2. **"Xtk user code is required"**: Ensure you provide a valid XTK user code with the `-xtk_user_code` flag
3. **"Error reading input file"**: Check that the JSON file exists and is readable
4. **"Error parsing JSON"**: Verify that the JSON file has valid syntax and structure
5. **"Error getting system user"**: Ensure the SYSTEM user exists in the `current_users` table
6. **"Error creating contract"**: Check that all required fields in the JSON payload are provided and valid
7. **"Error activating contract"**: Verify that the XTK user code has proper permissions
