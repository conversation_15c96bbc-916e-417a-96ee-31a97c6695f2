package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"os"
	"time"

	"whiz/db"
	"whiz/handlers"
)

var (
	inputFile     string
	xtkUserCode   string
	doNotCheckDms bool
)

func init() {
	flag.StringVar(&inputFile, "input", "", "Input JSON file containing eContract payload")
	flag.StringVar(&xtkUserCode, "xtk_user_code", "", "Xtk user code")
	flag.BoolVar(&doNotCheckDms, "do_not_check_dms", false, "Do Not Check DMS for deal number")
	flag.Parse()
}

func main() {
	if inputFile == "" {
		log.Fatal("Input file is required. Use -input flag to specify the JSON file.")
	}

	if xtkUserCode == "" {
		log.Fatal("Xtk user code is required. Use -xtk_user_code flag to specify the Xtk user code.")
	}

	data, err := os.ReadFile(inputFile)
	if err != nil {
		log.Fatalf("Error reading input file: %v", err)
	}

	log.Print("Input Variables")
	log.Printf("Input File: %s", inputFile)
	log.Printf("Xtk User Code: %s", xtkUserCode)
	log.Printf("Do Not Check DMS: %t", doNotCheckDms)

	var payload handlers.EContractPayload
	if err := json.Unmarshal(data, &payload); err != nil {
		log.Fatalf("Error parsing JSON: %v", err)
	}

	payload.Clean()

	ctx := context.Background()

	var user db.CurrentUser
	err = db.Get().Unsafe().GetContext(ctx, &user, `select * from current_users where first_name='SYSTEM'`)
	if err != nil {
		log.Fatalf("Error getting system user: %v", err)
	}

	apiErr, responseData := handlers.CreateEContractFromPayload(ctx, &payload, xtkUserCode, false)
	if apiErr != nil {
		log.Fatalf("Error creating contract: %v", apiErr.Err)
	}

	contractCode := fmt.Sprintf("%v", responseData["contract_number"])

	contract, err := db.GetContractByCode(ctx, contractCode)
	if err != nil {
		log.Fatalf("Error getting contract by code: %v", err)
	}

	log.Printf("Contract created successfully: %s (%d) for store %d", contract.Code, contract.ID, contract.StoreID)

	// Remit the contract so that it'll be invoiced and activated
	remittedContract, err := handlers.RemitContract(ctx, contract.ID, payload.DMSNumber, user, true, contract.StoreID, time.Now().UTC(), !doNotCheckDms)
	if err != nil {
		log.Fatalf("Error activating contract: %v", err)
	}

	log.Printf("Contract activated successfully: %s", remittedContract.Code)
}
