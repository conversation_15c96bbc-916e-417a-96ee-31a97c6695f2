appenv="development"
s3bucket="tca-app-development"
cookiestoreenckey="5RftjY8nLRN7BbECad4MuH964PXWJji7nt4FI+SXwDk="
httpsredirector=false
reportauthusername="bi"
reportauthpassword="bipass"
server="http://localhost:3000"
uniresturl="https://stage-restuni.totalcareauto.com/LINSC/"
authsalt="b2415f485938829d5a56991d1ac09346951a1870"

[redis]
address="localhost:6379"
tlsenabled=false

[ratelimit]
enable=false # disable rate limiting for development

[alpha]
externaltoken="abc"

[aws]
maxsize=10485760

[cdk]
loglevel=1 # Log all request bodies and response bodies which contain a CDK error
username="landcar"
password="pass"
host="3pa.dmotorworks.com"

[ucs]
loglevel=2
username="replace_with_user"
password="replace_with_pwd"
host="https://b2b-test.reyrey.com"

[cdkddj]
log=true
username="lhmintegration"
password="pass"
host="api-dit.connectcdk.com"

[csrf]
key="sopurtycraigis"
secure=false
maxage=43200

[database]
connectionstring="user=whiz dbname=whiz sslmode=verify-full"

[dataone]
accesskeyid="7879"
secretaccesskey="..."

[email]
uselog=true
host="email-smtp.us-west-2.amazonaws.com"
port=587
fromemail="<EMAIL>"
username="AKIAJQJ3Z6XGDG23KK4Q"
password="replace_with_pass"

[leads]
host="https://leads.ricochet.me/api/v1/lead/create/Larry-H.-Miller"
token="<insert token value here>"

[ldcs]
url="https://ldcs.azurewebsites.net/api/auth"
enciv="..."
enckey="..."

[newrelic]
appname="whiz-dev"

[nsd]
loglevel=1 # Log all request bodies and response bodies which contain a NSD error
password=""
host="dev.nsdapi.com:6445"

[phizz]
log=true
baseurl="http://localhost:4001"
authsalt="b2415f485938829d5a56991d1ac09346951a1870"

[ewuscp]
host="************:22"
sshuser="landcar"
remotepath="/var/www/toyota_dev"
sshprivatekey=""

[session]
maxage="21600"

[spp]
url="https://gmwstest.sppinc.net/Landcar/ContractExchange.asmx"
username="sexy"
password="beast"

[stoneeagle]
clientcode="123"
passcode="secret"
userid="234"

[intacct]
host="https://api.intacct.com/ia/xml/xmlgw.phtml"
senderid = "replace_with_sender_id"
senderpassword = "replace_with_sender_password"
userid = "replace_with_user_id"
password=  "replace with_password"
companyid = "replace_with_company_id"
cancelcontractlendervendorid = "VEN_999999"
cancelcontractlendervendorname = "Cancellations - Lenders"
cancelcontractcustomervendorid = "VEN_999998"
cancelcontractcustomervendorname = "Cancellations - Customers"

[safeguard]
publickey="""-----BEGIN PGP PUBLIC KEY BLOCK-----\n\
Version: GnuPG v1.2.1 (GNU/Linux)\n\
\n\
mQGiBEG0cxERBACrwAVA9HQk1sc/p6kL5kqnIOrT+EO9bBGOAmTyS7NaAUuDYeD2\n\
YBvKgIm2OD+Z5eOjndTUFx/MEdx+lyWcGuePTO3D+b/2dbsUaQ18oKVBr8KDBV7B\n\
rbpFSbIhZxgcnkdM/cv1Ix1DLC+ZESrvASSfE97jV8rZ6yIq2vy+kqC9PwCgpVFU\n\
zK6YDu3mzFlBlyIBYHBNAQsD/1uLcBJIqIbKY0VNCsuMmMIjZb9fprjb4z5RlWxr\n\
+yKMv4ev1Z2zw5gQ1thyr5kXs/Tq9KVQ1C9F0j4eWAVHF1YvKbMXBpa/2ci+m46d\n\
L1wW9YLlM5143zzQ+cj11iBXRIZSCVh+QrvG+g6q6fQUOlJYbb6OIkvZhaJHc2qf\n\
oEiYA/97DScWgxfaLdC2Uhzi8I/xv+/bEpagngt5DWfjxY9cKDxftU7dAT+pr5VA\n\
L1MimQF4ogfWWdCTRMOFt9kMoLolrnq7OiqwA2SHjEj9F684otGsZf1KRH5ae8/E\n\
JZoijO9yBYlgCiFs3LXtVqoGtYSQPzlfWggyepQXvI9u506fEbQcc2FmZWd1YXJk\n\
IDxqZXJyeUBzZ2ludGwuY29tPohZBBMRAgAZBQJBtHMRBAsHAwIDFQIDAxYCAQIe\n\
AQIXgAAKCRBDepIGafXkqFMPAKCYxms6VkrUt3pLTH6oxln72KElAACdGJWsVHls\n\
/nGu1cEhM4i11miaQx+5Ag0EQbRzHRAIAITOQnjhsd8U+ynrxTmKyAgYN12mBukL\n\
RAeVTJpU2b/v/bVjOIwAbULHKIDtxkk7dQmrmY1HpdJzYhjnmCnd1Cj9R0L3jfEg\n\
NQUgN8EJz7XB81UJRPIGDMuJow3aNwKRt6x8TDipHnOiDm8/IoFkmkSX4eucVk7h\n\
NeoesZEdRkdiPBJknu5qvx2l84bHZx/LEHtxi+YDUK7x+ggzKeCgu05/vQw3pBb7\n\
2MpQlLYd/Eiv/AHTRIp7QRjKGzwPNIozbmn9nowbSHqFTM1eLiyH8zeEaCBjtJWU\n\
C1MAZ9UQ8Rw5z8Nme65NbA1+AQF/6pgoOzK1wPy9T7Q3qfnDllvMDpsAAwUH/iVL\n\
08+P26Pndu36sGQ22H1vW3GGYPCbl/QrPAK0vmDCwMcJ0PCN98o97JpLHx0Tupdy\n\
PEqi/6ylR2FfTiAGXtdUKoqlEIHweRD19ROmfrUG6Bcyd1mwqT7iNFvtl0yb/VFn\n\
zmPPbzqrQEYwPbde4zsBhw784uk2Sod9vT29NZHaMTpnJLD4/oKdtKbw5wk60ky6\n\
zpqmp6j1GKHVrq7vS9GCNKVb/8Mm8h3dg3/gEiZJFlT8VL+STwO8eA+bWzewUUUo\n\
QPEIfK71up3uUF39X+xy+Isp1FbXPSSHqzu4DFqeQmw9HexFFDYjluDmJ30SywdG\n\
D16h9BJjfh0bRV6qcwCIRgQYEQIABgUCQbRzHQAKCRBDepIGafXkqHv9AJ9UBxTl\n\
Ob/3354ZaWWM0DhK8Bpg6ACfWobcKX0vPtPXrtjq0NLM4Hc93Cw=\n\
=7N5P\n\
-----END PGP PUBLIC KEY BLOCK-----"""

[esale]
lenderemail=["<EMAIL>","<EMAIL>"]

[okta]
clientid="okta_client_id"
clientsecret="okta_client_secret"
oktadomain="domain"
authservername="default"
redirecturl="http://localhost:4000/authorization-code/callback"
cookiestoreauthkey="cu72LLsRSca5a++WceKU4jwN/hD7FC6RXf8AzGJv7FvWP4ZFn4BDAD8rvP6MSRGgLZBDkHy1Ygihm1lgqBPqvw=="
cookiestoreenckey="p40m7wHY5RSg7FyDFLzIKrGlzNXY5thxuRhEuAkd/b4="
useauthserver=false
log="true"

[pen]
loglevel=0 # Set log level to 0,1,2 default to no log

[s3reverseproxy]
enckey="..."
pathprefix="/api/files/download"
defaultlinktimeoutminutes=5

[aicancellation]
cancellationserver="https://3xlkt3dp1c.execute-api.us-west-2.amazonaws.com"
vinDataS3Path="AI_framework_test/VIN_Data/vin.csv"

[lenderquotes]
serverurl="http://127.0.0.1:7000"

[lenderquotes.okta]
issuerurl="https://asbury.okta.com"
audience="https://asbury.okta.com"
clientid="okta_client_id"