package tasks

import (
	"context"
	"flag"
	"log"

	"whiz/adminhandlers"
	"whiz/db"
	"whiz/handlers"
	"whiz/sbmigrate"

	"github.com/pkg/errors"
)

// CreateJobFlag defines that flag that is used to indicate that
// a task should run instead of the main service.
func CreateJobFlag(dest *string) {
	flag.StringVar(dest, "job", "", `Run a helper job

Options: 
	inspection-needed - Apply inspection surcharge to appropriate contracts.
	on-after-refresh-uat - Restore foreign keys after UAT refresh.
	import-transaction-uat - Import transactions from CSV to UAT. Specify path of the file after flag.
	expire-contracts - Set status of contracts to Expired if contract is expired by age.
	reset-expired-contracts - Re-Set expired contracts to active. 
	reset-contract-dates - Set effective & expiration date of contracts. Specify path of the file after flag.
	clear-vin-cache-records - Delete vin-cache-records older than 90 days.
	cancel-alpha-contracts - Cancel alpha contracts provided in the s3 file
	activate-e-contracts - Activate e-contracts for which generated date greater than 30 days
	update-cancel-contract-bill-payments - Update cancel contract bill payments from intacct API
	update-cancel-contract-bill-record-numbers - Update cancel contract bill record numbers from intacct API
	upload-vin-for-ai-pipeline - Upload VIN data for AI pipeline processing
`)
}

// Run invokes the specified task
func Run(ctx context.Context, jobName string) {
	user, err := db.GetUserByEmail(ctx, "SYSTEM")
	if err != nil {
		log.Println("error getting SYSTEM user")
		log.Fatalf("%+v\n", err)
	}

	if jobName == "inspection-needed" {
		err := handlers.AllSurchargeContracts(ctx, user)
		if err != nil {
			log.Println("error running inspection-needed")
			log.Fatalf("%+v\n", err)
		}
		return
	} else if jobName == "on-after-refresh-uat" {
		err := db.RestoreContractFormsCancelRuleLinks()
		if err != nil {
			log.Println("error restoring contract form links")
			log.Fatalf("%+v\n", err)
		}
		return
	} else if jobName == "import-transaction-uat" {
		if flag.NArg() == 0 {
			log.Println("error importing transactions from production")
			err := errors.New("please provide path of the CSV file to import")
			log.Fatalf("%+v\n", err)
		}

		path := flag.Args()
		err := sbmigrate.ImportTransactions(path[0])
		if err != nil {
			log.Println("error importing transactions from production")
			log.Fatalf("%+v\n", err)
		}
		return
	} else if jobName == "expire-contracts" {
		err := db.ExpireContracts()
		if err != nil {
			log.Println("error expiring contracts by age")
			log.Fatalf("%+v\n", err)
		}
	} else if jobName == "reset-expired-contracts" {
		err := db.ResetExpiredMaintenanceContracts(ctx)
		if err != nil {
			log.Println("error resetting expired contracts")
			log.Fatalf("%+v\n", err)
		}
	} else if jobName == "reset-contract-dates" {
		if flag.NArg() == 0 {
			log.Println("error resetting migrated contracts dates")
			err := errors.New("please provide path of the CSV file to import")
			log.Fatalf("%+v\n", err)
		}

		path := flag.Args()
		err := db.ResetDatesForMaintenanceContracts(ctx, path[0])
		if err != nil {
			log.Println("error resetting migrated contracts dates")
			log.Fatalf("%+v\n", err)
		}
		return
	} else if jobName == "request-leads" {
		err := handlers.RequestLeads(ctx)
		if err != nil {
			log.Println("error sending request quotes emails")
			log.Fatalf("%+v\n", err)
		}
	} else if jobName == "clear-vin-cache-records" {
		err := handlers.ClearVinCacheRecords(ctx)
		if err != nil {
			log.Println("error clearing vin-cache-records")
			log.Fatalf("%+v\n", err)
		}
	} else if jobName == "cancel-alpha-contracts" {
		err := handlers.CancelAlphaContracts(ctx)
		if err != nil {
			log.Println("error canceling alpha contracts")
			log.Fatalf("%+v\n", err)
		}
	} else if jobName == "activate-e-contracts" {
		err := handlers.AutoActivateEContracts(ctx)
		if err != nil {
			log.Println("error activating e contracts")
			log.Fatalf("%+v\n", err)
		}
	} else if jobName == "update-cancel-contract-bill-payments" {
		err := adminhandlers.UpdateCancelContractBillPayments(ctx)
		if err != nil {
			log.Println("error updating cancel contract bill payments")
			log.Fatalf("%+v\n", err)
		}
	} else if jobName == "upload-vin-for-ai-pipeline" {
		err := adminhandlers.UploadVinForAIPipeline(ctx)
		if err != nil {
			log.Println("error uploading VIN data for AI pipeline")
			log.Fatalf("%+v\n", err)
		}
	} else if jobName == "update-cancel-contract-bill-record-numbers" {
		err := adminhandlers.UpdateCancelContractBillRecordNumbers(ctx)
		if err != nil {
			log.Println("error updating cancel contract bill record numbers")
			log.Fatalf("%+v\n", err)
		}
	} else if jobName != "" {
		log.Fatalf("Unrecognized job: %s", jobName)
	}
}
