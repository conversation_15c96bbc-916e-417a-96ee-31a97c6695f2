package dmsfactory

import (
	"context"
	"whiz/cdk"
	"whiz/conf"
	"whiz/db"
	"whiz/dms"
	"whiz/tekion"
	"whiz/ucs"

	"github.com/pkg/errors"
)

type notFoundError interface {
	error
	IsNotFound() bool
}

type userError interface {
	error
	UserErrorMessage() string
}

// DealOnly will look for a deal given the store and the deal number.
func DealOnly(ctx context.Context, store *db.Store, dealNumber string) (*dms.Deal, error) {
	var deal *dms.Deal
	var err error
	switch store.DMSProvider {
	case db.DMSProviderCDK:
		deal, err = cdk.DealOnly(ctx, store.DMSProviderParameters.Map["dealer_id"].String, dealNumber)
	case db.DMSProviderUCS:
		deal, err = ucs.GetDeal(ctx, store.DMSProviderParameters.Map["dealer_number"].String, store.DMSProviderParameters.Map["store_number"].String, store.DMSProviderParameters.Map["area_number"].String, dealNumber)
	case db.DMSProviderTekion:
		deal, err = tekion.GetDeal(ctx, store.DMSProviderParameters.Map["dealer_id"].String, dealNumber)
	default:
		return nil, errors.New("unsupported DMS provider")
	}

	if err != nil {
		if nfErr, ok := err.(notFoundError); ok && nfErr.IsNotFound() {
			return deal, errors.WithMessagef(err, "deal number not found for %s deal", dealNumber)
		}
		if userErr, ok := err.(userError); ok {
			return deal, errors.WithMessagef(err, "%s error for %s deal", userErr.UserErrorMessage(), dealNumber)
		}
		return deal, errors.WithMessagef(err, "deal Number not found for %s deal", dealNumber)
	}
	return deal, nil
}

// Deal will look for a deal given the store and the deal number.
func Deal(ctx context.Context, store *db.Store, dealNumber string, lenderOptionName string) (*dms.Deal, error) {
	var deal *dms.Deal
	var err error
	switch store.DMSProvider {
	case db.DMSProviderCDK:
		deal, err = cdk.Deal(ctx, store, store.DMSProviderParameters.Map["dealer_id"].String, dealNumber, lenderOptionName)
	case db.DMSProviderUCS:
		deal, err = ucs.GetDeal(ctx, store.DMSProviderParameters.Map["dealer_number"].String, store.DMSProviderParameters.Map["store_number"].String, store.DMSProviderParameters.Map["area_number"].String, dealNumber)
	case db.DMSProviderTekion:
		deal, err = tekion.GetDeal(ctx, store.DMSProviderParameters.Map["dealer_id"].String, dealNumber)
	default:
		return nil, errors.New("unsupported DMS provider")
	}

	if err != nil {
		if nfErr, ok := err.(notFoundError); ok && nfErr.IsNotFound() {
			return deal, errors.WithMessagef(err, "deal number not found for %s deal", dealNumber)
		}
		if userErr, ok := err.(userError); ok {
			return deal, errors.WithMessagef(err, "%s error for %s deal", userErr.UserErrorMessage(), dealNumber)
		}
		return deal, errors.WithMessagef(err, "deal Number not found for %s deal", dealNumber)
	}
	if conf.Get().AppEnv != "production" {
		anonimizeDeal(deal)
	}
	return deal, nil
}

// RO will look for an RO given the store and the RO number.
func RO(ctx context.Context, store *db.Store, roNumber string) (*dms.RO, error) {
	var ro *dms.RO
	var err error
	switch store.DMSProvider {
	case db.DMSProviderCDK:
		ro, err = cdk.RO(ctx, store.DMSProviderParameters.Map["dealer_id"].String, roNumber)
	case db.DMSProviderUCS:
		ro, err = ucs.GetRO(ctx, store.DMSProviderParameters.Map["dealer_number"].String, store.DMSProviderParameters.Map["store_number"].String, store.DMSProviderParameters.Map["area_number"].String, roNumber)
	case db.DMSProviderTekion:
		ro, err = tekion.GetRO(ctx, store.DMSProviderParameters.Map["dealer_id"].String, roNumber)
	default:
		return nil, errors.New("unsupported DMS provider")
	}

	if err != nil {
		if nfErr, ok := err.(notFoundError); ok && nfErr.IsNotFound() {
			return ro, errors.WithMessagef(err, "ro number not found for %s ro", roNumber)
		}
		if userErr, ok := err.(userError); ok {
			return ro, errors.WithMessage(err, userErr.UserErrorMessage())
		}
		return ro, errors.WithMessagef(err, "ro number not found for %s ro", roNumber)
	}
	if conf.Get().AppEnv != "production" {
		anonimizeRO(ro)
	}
	return ro, nil
}

// GetROMaintInfo will look for an ROMaintInfo given the store and the RO number.
func GetROMaintInfo(ctx context.Context, store *db.Store, roNumber string) (*dms.ROMaintInfo, error) {
	var roMaintInfo *dms.ROMaintInfo
	var err error
	switch store.DMSProvider {
	case db.DMSProviderCDK:
		roMaintInfo, err = cdk.GetROMaintInfo(ctx, store.DMSProviderParameters.Map["dealer_id"].String, roNumber)
	case db.DMSProviderUCS:
		roMaintInfo, err = ucs.GetMaintInfo(ctx, store.DMSProviderParameters.Map["dealer_number"].String, store.DMSProviderParameters.Map["store_number"].String, store.DMSProviderParameters.Map["area_number"].String, roNumber)
	case db.DMSProviderTekion:
		roMaintInfo, err = tekion.GetMaintInfo(ctx, store.DMSProviderParameters.Map["dealer_id"].String, roNumber)
	default:
		return nil, errors.New("unsupported DMS provider")
	}

	if err != nil {
		if nfErr, ok := err.(notFoundError); ok && nfErr.IsNotFound() {
			return roMaintInfo, errors.WithMessagef(err, "ro number not found for %s ro", roNumber)
		}
		if userErr, ok := err.(userError); ok {
			return roMaintInfo, errors.WithMessage(err, userErr.UserErrorMessage())
		}
		return roMaintInfo, errors.WithMessagef(err, "ro number not found for %s ro", roNumber)
	}

	return roMaintInfo, nil
}

func anonimizeRO(ro *dms.RO) {
	if ro.Customer.BusinessName != "" {
		ro.Customer.BusinessName = "Test Biz"
	}
	if ro.Customer.FirstName != "" {
		ro.Customer.FirstName = "Test"
	}
	if ro.Customer.LastName != "" {
		ro.Customer.LastName = "Example"
	}
	if ro.Customer.Address != "" {
		ro.Customer.Address = "123 Fake St"
	}
	if ro.Customer.City != "" {
		ro.Customer.City = "Draper"
	}
	if ro.Customer.State != "" {
		ro.Customer.State = "UT"
	}
	if ro.Customer.PostalCode != "" {
		ro.Customer.PostalCode = "84020"
	}
	if ro.Customer.Email != "" {
		ro.Customer.Email = "<EMAIL>"
	}
	if ro.Customer.Phone != "" {
		ro.Customer.Phone = "************"
	}
}

func anonimizeDeal(deal *dms.Deal) {
	businessName := ""
	if deal.Customer.IsBusiness {
		businessName = "Test Biz"
	}
	deal.Customer = dms.Customer{
		DMSCustomerNumber: "123",
		IsBusiness:        deal.Customer.IsBusiness,
		BusinessName:      businessName,
		FirstName:         "Test",
		LastName:          "Example",
		Address:           "123 Fake St",
		City:              "Draper",
		State:             "UT",
		PostalCode:        "84020",
		Phone:             "************",
		Email:             "<EMAIL>",
		Cellular:          "0123456789",
	}

	if (deal.CoBuyer != dms.Customer{}) {
		deal.CoBuyer = dms.Customer{
			DMSCustomerNumber: "123",
			IsBusiness:        deal.Customer.IsBusiness,
			BusinessName:      businessName,
			FirstName:         "Co-Test",
			LastName:          "Example",
			Address:           "123 Fake St",
			City:              "Draper",
			State:             "UT",
			PostalCode:        "84020",
			Phone:             "************",
			Email:             "<EMAIL>",
		}
	}
}
