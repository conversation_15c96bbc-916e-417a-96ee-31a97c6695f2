-- Add is_digital_reserves column to lenders table
ALTER TABLE lenders
ADD COLUMN is_digital_reserves BOOLEAN NOT NULL DEFAULT FALSE;

-- Create the main digital_reserve_rules table
CREATE TABLE digital_reserve_rules (
    id SERIAL PRIMARY KEY,
    lender_id INT NOT NULL REFERENCES lenders(id) ON UPDATE CASCADE ON DELETE RESTRICT,
    created_at TIMESTAMP NOT NULL DEFAULT (now() at time zone 'utc'),
    updated_at TIMESTAMP NOT NULL DEFAULT (now() at time zone 'utc')
);

-- Create junction tables for many-to-many relationships
CREATE TABLE digital_reserve_rules_stores (
    digital_reserve_rule_id INT NOT NULL REFERENCES digital_reserve_rules(id) ON UPDATE CASCADE ON DELETE RESTRICT,
    store_id INT NOT NULL REFERENCES stores(id) ON UPDATE CASCADE ON DELETE RESTRICT,
    PRIMARY KEY (digital_reserve_rule_id, store_id)
);

CREATE TABLE digital_reserve_rules_product_types (
    digital_reserve_rule_id INT NOT NULL REFERENCES digital_reserve_rules(id) ON UPDATE CASCADE ON DELETE RESTRICT,
    product_type_id INT NOT NULL REFERENCES product_types(id) ON UPDATE CASCADE ON DELETE RESTRICT,
    PRIMARY KEY (digital_reserve_rule_id, product_type_id)
);

CREATE TABLE digital_reserve_rules_cancel_reasons (
   digital_reserve_rule_id INT NOT NULL REFERENCES digital_reserve_rules(id) ON UPDATE CASCADE ON DELETE RESTRICT,
   cancel_reason_id INT NOT NULL REFERENCES cancel_reasons(id) ON UPDATE CASCADE ON DELETE RESTRICT,
   PRIMARY KEY (digital_reserve_rule_id, cancel_reason_id)
);

-- Create indexes for better performance
CREATE INDEX idx_digital_reserve_rules_lender_id ON digital_reserve_rules (lender_id);
CREATE INDEX idx_drr_stores_rule_id ON digital_reserve_rules_stores (digital_reserve_rule_id);
CREATE INDEX idx_drr_stores_store_id ON digital_reserve_rules_stores (store_id);
CREATE INDEX idx_drr_product_types_rule_id ON digital_reserve_rules_product_types (digital_reserve_rule_id);
CREATE INDEX idx_drr_product_types_product_type_id ON digital_reserve_rules_product_types (product_type_id);
CREATE INDEX idx_drr_cancel_reasons_rule_id ON digital_reserve_rules_cancel_reasons (digital_reserve_rule_id);
CREATE INDEX idx_drr_cancel_reasons_cancel_reason_id ON digital_reserve_rules_cancel_reasons (cancel_reason_id);


-- Insert digital_reserves_management role
INSERT INTO roles
    (role_category_id, created_at, created_by_user_id, updated_at, updated_by_user_id, role, description)
VALUES
    (
        (SELECT id FROM role_categories WHERE category_name = 'Admin Roles' LIMIT 1),
        NOW() AT TIME ZONE 'utc',
        (SELECT id FROM current_users WHERE email = 'SYSTEM' LIMIT 1),
        NOW() AT TIME ZONE 'utc',
        (SELECT id FROM current_users WHERE email = 'SYSTEM' LIMIT 1),
        'digital_reserves_management',
        'View lenders and edit digital reserve rules'
    )
;

-- Update existing roles to use underscore instead of space
UPDATE roles SET role = 'controller_admin' WHERE role = 'controller admin';
UPDATE roles SET role = 'accounting_claim_admin' WHERE role = 'accounting claim admin';
UPDATE roles SET role = 'accounting_cancellation_handler' WHERE role = 'accounting cancellation handler';
UPDATE roles SET role = 'cancellation_payment_handler' WHERE role = 'cancellation payment handler';

-- TS-14762: For old Lender's show Cancellation Payment Details
ALTER TABLE contract_cancellations ADD COLUMN check_applicable BOOLEAN NOT NULL DEFAULT FALSE;
UPDATE contract_cancellations SET check_applicable = TRUE WHERE payee_type = 'Lender' OR payee_type = 'Customer';