-- Remove the is_digital_reserves column from lenders table
ALTER TABLE lenders
DROP COLUMN IF EXISTS is_digital_reserves;

-- Drop the new tables in reverse order
DROP INDEX IF EXISTS idx_drr_cancel_reasons_cancel_reason_id;
DROP INDEX IF EXISTS idx_drr_cancel_reasons_rule_id;
DROP INDEX IF EXISTS idx_drr_product_types_product_type_id;
DROP INDEX IF EXISTS idx_drr_product_types_rule_id;
DROP INDEX IF EXISTS idx_drr_stores_store_id;
DROP INDEX IF EXISTS idx_drr_stores_rule_id;
DROP INDEX IF EXISTS idx_digital_reserve_rules_lender_id;

DROP TABLE IF EXISTS digital_reserve_rules_cancel_reasons;
DROP TABLE IF EXISTS digital_reserve_rules_product_types;
DROP TABLE IF EXISTS digital_reserve_rules_stores;
DROP TABLE IF EXISTS digital_reserve_rules;

-- Remove digital_reserves_management role
DELETE FROM roles WHERE role = 'digital_reserves_management';

-- Revert existing roles to use space instead of underscore
UPDATE roles SET role = 'controller admin' WHERE role = 'controller_admin';
UPDATE roles SET role = 'accounting claim admin' WHERE role = 'accounting_claim_admin';
UPDATE roles SET role = 'accounting cancellation handler' WHERE role = 'accounting_cancellation_handler';
UPDATE roles SET role = 'cancellation payment handler' WHERE role = 'cancellation_payment_handler';

-- Revert - TS-14762: For old Lender's show Cancellation Payment Details
ALTER TABLE contract_cancellations DROP COLUMN check_applicable;