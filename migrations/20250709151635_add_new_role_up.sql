-- Insert new roles
insert into roles 
    (role_category_id, created_at, created_by_user_id, updated_at, updated_by_user_id, role, description)
VALUES
    (
        (select id from role_categories where category_name = 'Admin Roles' limit 1),
        now() at time zone 'utc',
        (select id from current_users where email = 'SYSTEM' limit 1),
        now() at time zone 'utc',
        (select id from current_users where email = 'SYSTEM' limit 1),
        'cancel_dashboard_manager',
        'Cancel Dashboard Manager'
    )
;

alter table ai_cancellations
    add column assigned_to_user_id int references users(id) on update cascade on delete restrict,
    add column delete_note varchar(255);
create index ai_cancellations_assigned_to_user_id_idx on ai_cancellations using btree (assigned_to_user_id);

update ai_cancellations
set assigned_to_user_id = created_by_user_id;

alter table ai_cancellations
    alter column assigned_to_user_id set not null;