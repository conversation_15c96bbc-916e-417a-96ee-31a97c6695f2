diff --git a/assets/js/admin/contracts/Edit.jsx b/assets/js/admin/contracts/Edit.jsx
index 61cea3be..74e23375 100644
--- a/assets/js/admin/contracts/Edit.jsx
+++ b/assets/js/admin/contracts/Edit.jsx
@@ -281,19 +281,17 @@ export default class ContractEdit extends React.Component {
           + `&id=${this.props.contract.id}`;
         this.context.router.push(this.props.location.query.return_route || defaultReturnRoute);
 
-        if (this.props.contract.product_type_code === 'VSC') {
-          const endorsementData = {
-            product_variant_display_name: this.props.contract.product_variant_display_name,
-            plan_name: this.props.contract.plan_name,
-            expiration_mileage: this.props.contract.expiration_mileage,
-            effective_date: this.props.contract.effective_date,
-            expiration_date: this.props.contract.expiration_date,
-            deductible: (this.props.contract.options.find(o => o.name.toLowerCase().includes('deductible'))).name,
-          };
-          const endorsementURL = `/api/admin/contracts/${this.props.contract.code}/edit/endorsement-form?q=${encodeURIComponent(JSON.stringify(endorsementData))}`;
-          window.open(endorsementURL,'_blank');
-          window.location.reload();
-        }
+        const endorsementData = {
+          product_variant_display_name: this.props.contract.product_variant_display_name,
+          plan_name: this.props.contract.plan_name,
+          expiration_mileage: this.props.contract.expiration_mileage,
+          effective_date: this.props.contract.effective_date,
+          expiration_date: this.props.contract.expiration_date,
+          deductible: (this.props.contract.options.find(o => o.name.toLowerCase().includes('deductible'))).name,
+        };
+        const endorsementURL = `/api/admin/contracts/${this.props.contract.code}/edit/endorsement-form?q=${encodeURIComponent(JSON.stringify(endorsementData))}`;
+        window.open(endorsementURL,'_blank');
+        window.location.reload();
       } else {
         Alert.error(`Error updating contract: ${results.data.message}`);
         this.state.form.cursor('errors').update(() =>
