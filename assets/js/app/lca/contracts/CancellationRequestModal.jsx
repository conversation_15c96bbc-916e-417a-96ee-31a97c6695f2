import React from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import accounting from 'accounting';
import Alert from 'react-s-alert';
import $ from 'jquery';
import hstore from 'shared/hstore';

import {jsonPromise as ajax} from 'shared/ajax';
import Modal from 'shared/components/Modal';
import Table from 'shared/components/Table';
import dateFormat from 'shared/date-format';
import ModalDatePicker from 'shared/components/ModalDatePicker';
import * as Roles from 'shared/roles';
import Attachments, { Status } from 'shared/components/Attachments';
import { ViolationTypes, GetDisplayClassForViolationType } from 'shared/cancel-rules';
import CancellationHelpModal from "admin/contracts_search/CancellationHelpModal";

const QUOTES_TAB = "quotes";
const CANCELLATION_TAB = "cancellation";
const API_DATE_FORMAT = "YYYY-MM-DD";
const CANCELLED_STATUS = "C"; //Unidata status for cancelled contracts
const CANCEL_REASON_FLAT_CANCEL = "flat cancel";

// Backdating warning constants
const BACKDATING_LIMITS = {
  FLAT_CANCEL: 30,    // 30 days for flat cancels
  GENERAL: 90,        // 90 days for general cancellations
  REPO: 180          // 180 days for repo cancellations
};

export default class CancellationRequestModal extends React.Component {
  static contextTypes = {
    sessionLoaded: PropTypes.bool.isRequired,
    user: PropTypes.shape({
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }),
    }),
    store: PropTypes.shape({
      id: PropTypes.number.isRequired,
      name: PropTypes.string.isRequired
    }).isRequired
  };

  static propTypes = {
    visible: PropTypes.bool,
    onCloseModal: PropTypes.func,
    contractId: PropTypes.number,
    contractList: PropTypes.array,
    loadContracts: PropTypes.func,
    contractType: PropTypes.string
  };

  constructor(props){
    super(props);

    this.state = {
      cancelDate: moment(),
      mileage: '',
      reason: '',
      cancellationReasons: [],
      productOrder: void 0,
      cancellationEstimate: {
        estimates: []
      },
      cancellationQuoteEstimate: {
        estimates: []
      },
      activeTab: CANCELLATION_TAB,
      quoteRequested: false,
      checkedClaim: false, 
      loading: false,
      attachmentTypes: [],
      attachments: [],
      attachmentsStatus: Status.Ready,
      helpModal: false,
    };
  }

  componentDidMount(){
    $('[data-toggle="popover"]').popover({
      container: '.popover-container'
    }).on('mouseleave', function () {
      //Hide the popover tooltip as it is blocking tab switch
      setTimeout(function () {
        if (!$('[data-toggle="popover"]:hover').length) {
          $('.popover').hide();
        }
      }, 300);
    });

    let activeTab = QUOTES_TAB;

    if(this.context && hstore.has(this.context.user.roles, "ldcs_accounting")){
      activeTab = CANCELLATION_TAB;
    }

    this.setState({
      activeTab
    });

    this.loadSupportingData();
  }

  showHelpModal = () => {
    this.setState({ helpModal: true });
  }

  closeHelpButton = () => {
    this.setState({ helpModal: false });
  }

  componentDidUpdate(){
    $('[data-toggle="popover"]').popover({
      container: '.popover-container'
    }).on('mouseleave', function () {
      //Hide the popover tooltip as it is blocking tab switch
      setTimeout(function () {
        if (!$('[data-toggle="popover"]:hover').length) {
          $('.popover').hide();
        }
      }, 300);
    });
  }

  loadSupportingData = () => {
    const url = `/api/contracts/cancellation/quote-supporting-data`;

    this.setState({
      loading: true,
      showLoader: true
    }, () => {
      ajax(url, {}, {}).then((results) => {
        if(results.status === 200){
          let productOrder = {};
          results.data.product_types.forEach((productType) => {
            productOrder[productType.code] = productType.position;
          });
          this.setState({
            attachmentTypes: results.data.attachment_types,
            cancellationReasons: results.data.cancel_reasons,
            productOrder: productOrder
          });
        } else{
          Alert.error("Error loading supporting data");
        }
        this.setState({
          showLoader: false,
          loading: false
        });
      }, () => {
        Alert.error("Error loading supporting data");
        this.setState({
          loading: false,
          showLoader: false,
        });
      });
    });
  }

  loadProductOrder = () => {
    this.setState({ showLoader: true }, () => {
      ajax(`/api/product-types`, {}, {}).then((response) => {
        if (response.status === 200) {
          let productOrder = {};
          response.data.product_types.forEach((productType) => {
            productOrder[productType.code] = productType.position;
          });
          this.setState({
            showLoader: false,
            productOrder
          }, this.loadContracts);
        } else if (response.status === 404) {
          this.setState({ showLoader: false }, () => {
            Alert.error("Product Types Not Found");
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  sortByProductOrder = (contractA, contractB) => {
    const { productOrder } = this.state;
    if(productOrder) {
      return productOrder[contractA.product_type_code] - productOrder[contractB.product_type_code];
    }
    return 0;
  };
  
  handleChange = (key, event) => {
    const state = this.state;
    state[key] = event.target.value;

    // Check backdating warning when reason changes (if date is already set)
    if (key === 'reason' && this.state.cancelDate) {
      const shouldContinue = this.checkBackdatingWarning(this.state.cancelDate, event.target.value);
      if (!shouldContinue) {
        return; // User cancelled, don't update the reason
      }
    }

    this.setState(state);

    // Clear the estimates if the mileage or cancel reason changes.
    if (key === 'mileage' || key === 'reason') {
      this.resetCancellationQuotes();
    }
  };
  
  handleChecked = (event) => {
    this.setState((prevState) => ({
      checkedClaim: !prevState.checkedClaim
    }));
  };

  checkBackdatingWarning = (cancelDate, cancelReasonId) => {
    if (!cancelDate || !cancelReasonId) return true;

    const selectedReason = this.state.cancellationReasons.find(reason => reason.id === cancelReasonId);
    if (!selectedReason) return true;

    const daysDifference = moment().diff(moment(cancelDate), 'days');
    if (daysDifference <= 0) return true; // Future date or today, no warning needed

    let warningLimit = BACKDATING_LIMITS.GENERAL; // Default 90 days
    let reasonType = 'general';

    // Determine the appropriate limit based on cancel reason
    const reasonName = selectedReason.name.toLowerCase();
    if (reasonName.includes('flat cancel')) {
      warningLimit = BACKDATING_LIMITS.FLAT_CANCEL; // 30 days
      reasonType = 'flat cancel';
    } else if (reasonName.includes('repo')) {
      warningLimit = BACKDATING_LIMITS.REPO; // 180 days
      reasonType = 'repo';
    }

    // Show warning if exceeding the limit
    if (daysDifference > warningLimit) {
      const message = `Warning: The selected cancellation date is ${daysDifference} days in the past, which exceeds the ${warningLimit}-day backdating limit for ${reasonType} cancellations. Please review this date before proceeding.`;

      // Show a soft warning popup
      if (window.confirm(message + '\n\nDo you want to continue with this date?')) {
        // User confirmed, continue with the selected date
        return true;
      } else {
        // User cancelled, reset to current date
        this.setState({
          cancelDate: moment()
        });
        return false;
      }
    }
    return true;
  };

  handleDateChange = (date) => {
    if(date){
      if(!date.isValid()){
        Alert.error("Enter correct cancel date");
        return;
      }
    } else{
      Alert.error("Enter correct cancel date");
      return;
    }

    // Check for backdating warning
    const shouldContinue = this.checkBackdatingWarning(date, this.state.reason);
    if (!shouldContinue) {
      return; // User cancelled, don't update the date
    }

    this.setState({
      cancelDate: date
    });

    this.resetCancellationQuotes();
  };

  resetCancellationQuotes = () => {
    this.setState({
      cancellationEstimate: [],
      cancelStores: [],
      quoteRequested: false,
    });
  }

  handleGetQuote = () => {
    const urlQuotes = `/api/contracts/${this.props.contractId}/cancellation/estimate/quote?cancel_date=${moment(this.state.cancelDate).format(API_DATE_FORMAT)}&mileage=${this.state.mileage}&cancel_reason_id=${this.state.reason}&store_id=${this.context.store.id}`;
    
    this.setState({
      quoteRequested: true,
      loading: true
    }, () => {
      ajax(urlQuotes, {}, {}).then((response) => {
        if (response.status === 200) {
          response.data.estimates.sort(this.sortByProductOrder);

          if (hstore.has(this.context.user.roles, "ldcs_accounting")) {
            this.setState({
              cancellationQuoteEstimate: JSON.parse(JSON.stringify(response.data)),
              cancellationEstimate: JSON.parse(JSON.stringify(response.data))
            });
          } else {
            this.setState({
              cancellationQuoteEstimate: JSON.parse(JSON.stringify(response.data))
            });
          }
        } else {
          if(response.data.message){
            Alert.error(`Error: ${response.data.message}`);
          } else {
            Alert.error("Error loading cancellation estimate"); 
          }
        }
        this.setState({
          loading: false
        });
      },
      (reason) => {
        if(reason.data.message){
          Alert.error(`Error: ${reason.data.message}`);
        } else {
          Alert.error("Error loading cancellation estimate");
        }
        this.setState({
          loading: false
        });
      });
    });
  };

  handleCancelContract = () => {
    const { cancelDate, mileage, reason, cancellationEstimate, checkedClaim } = this.state;
    let arrSelectedCancelContract = [];

    for(const estimate of cancellationEstimate.estimates){
      if(estimate.selected &&
          estimate.contract_status !== CANCELLED_STATUS &&
          estimate.store_code === this.context.store.code){
        arrSelectedCancelContract.push(estimate.id);
      }
    }

    const cancelContractUrl = `/api/contracts/cancel`;
    const cancelRequest = {
      "cancel_date" : moment(cancelDate).format(API_DATE_FORMAT),
      "mileage" : parseInt(mileage, 10),
      "cancel_reason_id" : parseInt(reason, 10),
      "store_id" : this.context.store.id,
      "contracts" : arrSelectedCancelContract,
      "has_claim_requested": checkedClaim,
      "apply_fee": true
    };

    const submit = () => {
      this.setState({
        loading: true
      }, () => {
        ajax(cancelContractUrl, cancelRequest, {method: "PUT"}).then(
          (results) => {
            if (results.status === 200) {
              if (results.data.warnings && results.data.warnings.length > 0) {
                Alert.warning(results.data.warnings.join(", "));
              }
              if (results.data.count === 0) {
                Alert.warning("Something went wrong, No contract was canceled");
              } else {
                Alert.success(results.data.count + " Contracts " + results.data.message);
                // once a cancel request is success, close the modal.
                this.props.onCloseModal();
              }
            } else {
              if (results.data.message) {
                Alert.error(`Error: ${results.data.message}`);
              } else {
                Alert.error("Error updating contract cancellations");
              }
            }
            this.setState({
              loading: false
            });
          },
          (reason) => {
            if (reason.data.message) {
              Alert.error(`Error: ${reason.data.message}`);
            } else {
              Alert.error("Error updating contract cancellations");
            }
            this.setState({
              loading: false
            });
          }
        );
      });
    };

    const { attachments, attachmentTypes } = this.state;
    const attachmentType = attachmentTypes.find(at => at.name === 'Cancel Quote');
    if (attachments && attachments.length > 0) {
      cancelRequest.attachments = [];
      let promises = [];
      // read all attachments then submit the request.
      for (let i = 0; i < attachments.length; i++) {
        const file = attachments[i];
        let att = {
          content_type: file.type,
          name: file.name,
          description: 'Cancel Contract',
          attachment_type_id: attachmentType.id,
        };
        cancelRequest.attachments.push(att);
        
        // Read each file content asynchronously
        let readerPromise = new Promise(resolve => {
          const reader = new FileReader();
          reader.onload = (e) => {
            att.file_content = btoa(e.target.result);
            resolve();
          };
          reader.readAsBinaryString(file);
        });
        promises.push(readerPromise);
      }

      // Wait for all files to be read, then submit
      Promise.all(promises).then(() => {
        submit();
      });
    } else if (this.isFlatCancel() || hstore.has(this.context.user.roles, Roles.TestAutomation)) {
      submit();
    }
  };

  setActive = (activeTab) => {
    this.setState({
      activeTab
    });
  };

  getAllSelectedForCancellation = (estimates) => {
    let allSelected = true;
    let selectableCount = 0;

    for(const estimate of estimates){
      if((this.state.activeTab === QUOTES_TAB || estimate.store_code === this.context.store.code) &&
          estimate.cancellable) {
        selectableCount++;
        allSelected = allSelected && !!estimate.selected;
        if (!allSelected) {
          return false;
        }
      }
    }

    if(selectableCount === 0){
      return false;
    }

    return allSelected;
  };

  getIfOneSelectedCancellation = (estimates) => {
    let oneSelected = false;

    for(const estimate of estimates){
      oneSelected = oneSelected || !!estimate.selected;
      if(oneSelected){
        return true;
      }
    }

    return oneSelected;
  };

  hasCancellableContract = (estimates) => {
    for(const estimate of estimates){
      if(!!estimate.selected && estimate.contract_status !== CANCELLED_STATUS){
        return true;
      }
    }

    return false;
  };

  isIssuingDealerSame = (estimates) => {
    for(const estimate of estimates){
      if(estimate.store_code === this.context.store.code){
        return true;
      }
    }

    return false;
  };

  selectAllCancellation = () => {
    const {activeTab, cancellationEstimate, cancellationQuoteEstimate} = this.state;

    let estimates = [];

    if(activeTab === CANCELLATION_TAB){
      estimates = cancellationEstimate.estimates ? cancellationEstimate.estimates : [];
    } else if(activeTab === QUOTES_TAB){
      estimates = cancellationQuoteEstimate.estimates ? cancellationQuoteEstimate.estimates : [];
    }

    const allSelected = this.getAllSelectedForCancellation(estimates);

    for(const estimate of estimates) {
      if((this.state.activeTab === QUOTES_TAB || estimate.store_code === this.context.store.code) &&
          estimate.cancellable) {
        estimate.selected = !allSelected;
      }
    }

    if(activeTab === CANCELLATION_TAB) {
      this.setState({
        cancellationEstimate
      });
    } else if(activeTab === QUOTES_TAB){
      this.setState({
        cancellationQuoteEstimate
      });
    }
  };

  selectCancellationContract = (index) => {
    const { cancellationEstimate, cancellationQuoteEstimate, activeTab } = this.state;

    let estimates = [];

    if(activeTab === CANCELLATION_TAB) {
      estimates = cancellationEstimate.estimates ? cancellationEstimate.estimates : [];
    } else if(activeTab === QUOTES_TAB) {
      estimates = cancellationQuoteEstimate.estimates ? cancellationQuoteEstimate.estimates : [];
    }

    estimates[index].selected = !estimates[index].selected;

    if(activeTab === CANCELLATION_TAB) {
      this.setState({
        cancellationEstimate
      });
    } else if(activeTab === QUOTES_TAB){
      this.setState({
        cancellationQuoteEstimate
      });
    }
  };

  handleUpdateFiles = (files) => {
    this.setState({
      attachments: files
    });
  }

  renderTableHeader(estimates){

    if(estimates.length === 0){
      return;
    }

    const isIssuingDealerSame = this.isIssuingDealerSame(estimates);
    const accountingPermission = hstore.has(this.context.user.roles, "ldcs_accounting");
    const showStoreRefund = isIssuingDealerSame && accountingPermission;

    return (
      <thead>
        <tr className="table-header-row">
          <th>
            <input type="checkbox"
              className="form-check-inline"
              checked={ estimates.length > 0 && this.getAllSelectedForCancellation(estimates) }
              onChange={ this.selectAllCancellation }/>
          </th>
          <th>Contract</th>
          <th>Type</th>
          <th>Issuing Dealer</th>
          <th>Effective Date</th>
          <th>Factor</th>
          <th>Fee</th>
          <th>Total Claims</th>
          <th className="text-right">Total Claim Amount Paid</th>
          <th className="text-right">Customer Refund</th>
          {
            showStoreRefund &&
              <th className="text-right">Store Refund ({this.context.store.code})</th>
          }
          <th className="text-right">Sales Tax</th>
        </tr>
      </thead>
    );
  }

  renderTableBody(estimates){
    const isIssuingDealerSame = this.isIssuingDealerSame(estimates);
    const accountingPermission = hstore.has(this.context.user.roles, "ldcs_accounting");
    const showStoreRefund = isIssuingDealerSame && accountingPermission;

    if(estimates.length === 0){
      return;
    }

    return (
      <tbody>
        {
          estimates.map((estimate, index) => {
            const {
              all_factors,
              factor,
              rule_violations,
            } = estimate;
            
            let factorToDisplay;
            if (all_factors) {
              const remaining = all_factors.filter((f) => f != factor).map((f) => f.replace(" used", ""));
              factorToDisplay = remaining.join("/");
            }

            let violationDisplayMessage = ViolationTypes.AdditionalStepsDisplay;
            let violationType = ViolationTypes.AdditionalSteps;
            (rule_violations || []).forEach(rv => {
              if (rv.violation_type === ViolationTypes.NotCancellable) {
                violationType = ViolationTypes.NotCancellable;
                violationDisplayMessage = ViolationTypes.NotCancellableDisplay;
              }
            });


            if (!estimate.cancellable) {
              return (
                <tr key={estimate.id}>
                  <td>
                  </td>
                  <td>
                    {estimate.original_code || estimate.code}
                  </td>
                  <td>
                    {`${estimate.product_type_name}*`}
                  </td>
                  <td>
                    {estimate.store_code}
                  </td>
                  <td>
                    {moment.utc(estimate.effective_date).format(dateFormat)}
                  </td>
                  <td>
                    {
                      factorToDisplay
                        ? (
                          <React.Fragment>
                            {`${factorToDisplay}/`}<h6>{factor}</h6>
                          </React.Fragment>
                        ): factor
                    }
                  </td>
                  <td colSpan={5}>
                    <span data-toggle="popover"
                      data-placement="top"
                      data-trigger="hover focus"
                      data-delay={100}
                      className="popover-container"
                      data-html="true"
                      data-content=
                        {`<ul>${estimate.rule_violations.map((violation, i) => {
                          const msgElement = violation.violation_message.indexOf('Cancellation contact information') !== -1 ? `<b>${violation.violation_message}</b>` : violation.violation_message;
                          return `<li>${msgElement}</li>`;
                        }).join("")
                        }</ul>`}
                      title={`<span class="${GetDisplayClassForViolationType(violationType)}"><i class="fa fa-exclamation-triangle"/> ${violationDisplayMessage} </span>`}
                    >
                      <span className={`${GetDisplayClassForViolationType(violationType)}`}>
                        <i className="fa fa-exclamation-triangle"/> {violationDisplayMessage}
                      </span>
                    </span>
                  </td>
                  <td>
                  </td>
                </tr>
              );
            } else {
              return (
                <tr key={estimate.id}>
                  <td>
                    {
                      (this.state.activeTab === QUOTES_TAB || estimate.store_code === this.context.store.code) &&
                        <input type="checkbox"
                          className="form-check-inline"
                          checked={ !!estimate.selected }
                          onChange={ this.selectCancellationContract.bind(this, index) }/>
                    }
                  </td>
                  <td>
                    {estimate.original_code || estimate.code}
                  </td>
                  <td>
                    {estimate.cancellable ? estimate.product_type_name : `${estimate.product_type_name}*`}
                  </td>
                  <td>
                    {estimate.store_code}
                  </td>
                  <td>
                    {moment.utc(estimate.effective_date).format(dateFormat)}
                  </td>
                  <td>
                    {
                      factorToDisplay
                        ? (
                          <React.Fragment>
                            {`${factorToDisplay}/`}<h6>{factor}</h6>
                          </React.Fragment>
                        ): factor
                    }
                  </td>
                  <td>
                    {accounting.formatMoney(estimate.fee)}
                  </td>
                  <td>
                    {estimate.claim_count}
                  </td>
                  <td className="text-right">
                    {accounting.formatMoney(estimate.claim_total_amount)}
                  </td>
                  <td className="text-right">
                    {accounting.formatMoney(estimate.customer_refund)}
                  </td>
                  {
                    showStoreRefund &&
                    (estimate.store_code === this.context.store.code ?
                      <td className="text-right">
                        {!!estimate.selected && accounting.formatMoney(estimate.store_refund)}
                      </td> :
                      <td></td>)
                  }
                  <td className="text-right">
                    {accounting.formatMoney(estimate.sales_tax)}
                  </td>
                </tr>
              );
            }
          })
        }
        {this.renderFooter(estimates)}
      </tbody>
    );
  }

  renderFooter(estimates) {
    if(!this.context.sessionLoaded){
      return;
    }

    const isIssuingDealerSame = this.isIssuingDealerSame(estimates);
    const accountingPermission = hstore.has(this.context.user.roles, "ldcs_accounting");
    const showStoreRefund = isIssuingDealerSame && accountingPermission;

    let customerRefundTotal = 0;
    let storeRefundTotal = 0;
    let salesTaxTotal = 0;
    let customerRefundAmount = false;
    let storeRefundAmount = false;
    let salesTaxAmount = false;

    for (const estimate of estimates) {
      if (estimate.selected && estimate.cancellable) {
        if (estimate.customer_refund) {
          customerRefundAmount = true;
          customerRefundTotal += accounting.unformat(estimate.customer_refund);
        }
        if (estimate.store_refund) {
          storeRefundAmount = true;
          storeRefundTotal += accounting.unformat(estimate.store_refund);
        }
        if (estimate.sales_tax) {
          salesTaxAmount = true;
          salesTaxTotal += accounting.unformat(estimate.sales_tax);
        }
      }
    }
    return (<tr className='footer'>
      <td colSpan={9} style={{ textAlign: "right" }} className='total'><strong>Grand Total</strong></td>
      <td style={{ textAlign: "right" }}><strong>{customerRefundAmount ? accounting.formatMoney(customerRefundTotal) : null}</strong></td>
      {showStoreRefund && <td style={{ textAlign: "right" }}><strong>{storeRefundAmount ? accounting.formatMoney(storeRefundTotal) : null}</strong></td>}
      <td style={{ textAlign: "right"}}><strong>{salesTaxAmount ? accounting.formatMoney(salesTaxTotal) : null}</strong></td>
    </tr>);
  }

  handleUpdateAttachments(attachments) {
    this.setState({ attachments: attachments });
  }

  handleAttachmentsStatusChange(newStatus) {
    this.setState({
      attachmentStatus: newStatus
    });
  }

  renderHelpButton() {
    return(
      <button type="button" className="btn btn-secondary btn-sm"
        onClick={ this.showHelpModal.bind(this) }
      >
        <i className='fa fa-question-circle'></i> Help
      </button>);
  }

  renderAttachment() {
    const { attachments, cancellationEstimate } = this.state;
    return (<div>
      <div className="col form-group">
        {
          attachments.length === 0 &&
          <span className="font-weight-bold text-danger">{`* Please upload document in order to cancel contract${this.numberOfContractSelectedToCancel() > 1 && 's' || ''}`}</span>
        }
      </div>
      <div className="col-6 form-group">
        <Attachments id="attachment-files"
          allowMultiple={true}
          maxFiles={cancellationEstimate.estimates.length * 2}
          acceptedFileTypes = {[
            'image/*',
            'application/pdf', // *.pdf
            'text/csv',  // *.csv
            'text/plain',   // *.txt
            'application/msword', // *.doc
            'application/vnd.ms-excel', // *.xls
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // *.docx
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' // *.xlsx
          ]}
          onStatusChange={this.handleAttachmentsStatusChange.bind(this)}
          onUpdateAttachments={this.handleUpdateAttachments.bind(this)}
        />
      </div>
    </div>);
  }

  renderCancelTabOptions() {
    return (
      <div>
        <div className="col form-group">
          {this.renderHelpButton()}
        </div>
        <div className="col form-group">
          { !this.isFlatCancel() && this.renderAttachment() }
        </div>
      </div>
    );
  }

  renderDataTab(estimates) {
    const { activeTab, loading, checkedClaim } = this.state;

    let id = "quotes-tab";

    let tipText = "* Some products may not be cancellable or may not provide a quote for cancellation.";

    if(activeTab === CANCELLATION_TAB) {
      id = "cancellation-tab";
      tipText = `* Some products may not be cancellable, and only contracts issued by your current store (${this.context.store.code} - ${this.context.store.name}) may be cancelled.`;
    }

    return (
      <div role="tabpanel" className="tab-pane in active py-3" id={id}>
        {
          loading &&
              <div>
                <i className='fa fa-spin fa-refresh' /> Loading
              </div>
        }
        {
          !loading &&
              <Table>
                {this.renderTableHeader(estimates)}
                {this.renderTableBody(estimates)}
              </Table>
        }
        {
          !loading && activeTab === CANCELLATION_TAB &&
          this.renderCancelTabOptions()
        }
        {
          activeTab === CANCELLATION_TAB &&
          <div>
            <input type="checkbox"
              onChange={this.handleChecked}
              checked={checkedClaim}
              id="claim-checkbox"/>
            <label htmlFor="claim-checkbox" className="ml-2">
              Have claims been requested on ANY contracts for this customer within the last 60 days? Please confirm with
              customer before selecting.
            </label>
          </div>
        }
        <small className="tip">{tipText}</small>
      </div>
    );
  }

  renderTab(estimates){
    const accountingPermission = hstore.has(this.context.user.roles, "ldcs_accounting");
    const financePermission = hstore.hasAny(this.context.user.roles, ["ldcs_finance", "ldcs_service"]);

    return (
      <div className="col-12">
        <ul className="nav nav-tabs" role="tablist">
          {
            (financePermission || accountingPermission) &&
              <li className="nav-item" key={ QUOTES_TAB }>
                <a className={ `nav-link ${this.state.activeTab === QUOTES_TAB ? "active" : ""} ${this.state.activeTab === QUOTES_TAB ? "" : "text-primary"}` }
                  data-toggle="tab"
                  role="tab"
                  accessKey={ QUOTES_TAB }
                  aria-controls={ QUOTES_TAB }
                  onClick={ this.setActive.bind(this, QUOTES_TAB) }>
                    Quotes
                </a>
              </li>
          }
          {
            accountingPermission &&
              <li className="nav-item" key={CANCELLATION_TAB}>
                <a className={`nav-link ${this.state.activeTab === CANCELLATION_TAB ? "active" : ""} ${this.state.activeTab === CANCELLATION_TAB ? "" : "text-primary"}`}
                  data-toggle="tab"
                  role="tab"
                  accessKey={CANCELLATION_TAB}
                  aria-controls={CANCELLATION_TAB}
                  onClick={this.setActive.bind(this, CANCELLATION_TAB)}>
                    Cancellation
                </a>
              </li>
          }
        </ul>
        <div className="tab-content">
          {this.renderDataTab(estimates)}
        </div>
      </div>
    );
  }

  isFlatCancel = () => {
    const { reason, cancellationReasons } = this.state;
    if (!!reason && !!cancellationReasons) {
      const cancellationReason = cancellationReasons.find(cancelReason => cancelReason.id == reason);
      return cancellationReason.name.toLowerCase().includes(CANCEL_REASON_FLAT_CANCEL);
    }
    return false;
  }
  disableGetQuote() {
    const {
      reason,
      mileage,
    } = this.state;

    if (!reason) {
      return true;
    }
    if (this.isFlatCancel()) {
      return false;
    }
    if (!mileage) {
      return true;
    }
    return false;
  }

  render(){
    const { visible, onCloseModal, contractList } = this.props;

    const {
      cancelDate,
      mileage,
      reason,
      cancellationReasons,
      quoteRequested,
      cancellationEstimate,
      cancellationQuoteEstimate,
      activeTab,
      helpModal,
      loading,
    } = this.state;

    let estimates = [];

    if(activeTab === CANCELLATION_TAB) {
      estimates = cancellationEstimate.estimates ? cancellationEstimate.estimates : [];
    } else if(activeTab === QUOTES_TAB) {
      estimates = cancellationQuoteEstimate.estimates ? cancellationQuoteEstimate.estimates : [];
    }

    const anyContractSelected = this.getIfOneSelectedCancellation(estimates);

    let customerName = "";
    let vin = "";

    if(contractList.length > 0){
      customerName = contractList[0].customer_name;
      vin = contractList[0].vin;
    }

    let arrSelectedCancelContract = [];
    for(const estimate of estimates){
      if(estimate.selected){
        arrSelectedCancelContract.push(estimate.id);
      }
    }

    const data = {
      "cancel_date": moment(cancelDate).format(API_DATE_FORMAT),
      "mileage": parseInt(mileage, 10),
      "cancel_reason_id": parseInt(reason, 10),
      "store_id": this.context.store.id,
      "contracts": arrSelectedCancelContract
    };
    
    const cancelContractPdfUrl = `/api/contracts/cancellation/estimate/pdf?q=${encodeURIComponent(JSON.stringify(data))}`;
    
    const customerQuotePdfUrl = `/api/contracts/cancellation/estimate/quote/pdf?q=${encodeURIComponent(JSON.stringify(data))}`;
    
    const cancelRequestPdfUrl = `/api/contracts/${this.props.contractId}/cancellation/request-form?q=${encodeURIComponent(JSON.stringify(data))}`;

    const { attachments, attachmentStatus } = this.state;
    let flatUnwindOnly = true;
    // if the User has the AdminView role or has the LDCS All Cancels, then they can cancel any contract regardless of the reason.
    if (hstore.hasAny(this.context.user.roles, [Roles.AdminView, Roles.LDCSAllCancels])) {
      flatUnwindOnly = false;
    }
    const reasonName = data.cancel_reason_id > 0 && cancellationReasons.length > 0 && cancellationReasons.find((r) => r.id === data.cancel_reason_id).name;

    let nonFlatUnwindReason = reasonName &&
    !reasonName.toLowerCase().startsWith(CANCEL_REASON_FLAT_CANCEL);

    let disableCancel = loading || !anyContractSelected ||
      (!this.isFlatCancel() && (attachments.length === 0 || attachmentStatus !== Status.READY)) ||
      (flatUnwindOnly && nonFlatUnwindReason);

    if (hstore.has(this.context.user.roles, Roles.TestAutomation) && anyContractSelected) {
      disableCancel = false;
    }
    
    return (
      <div>
        {helpModal ?
          <Modal
            visible={helpModal}
            close={this.closeHelpButton.bind(this)}
          >
            <CancellationHelpModal cancellationReasons={cancellationReasons}/>
          </Modal> :
          <Modal visible={visible} close={onCloseModal} size="large">
            <h4 className="border-bottom border-light">Cancellation Request Estimate</h4>
            <div className="container">
              <div className="row py-2">
                <div className="col-4 text-break">
                  <h4>{customerName}<br/>{vin}</h4>
                </div>
                <div className="col-2">
                  <label>Cancel Reason</label>
                  <br/>
                  <select className="form-control"
                    value={reason}
                    onChange={this.handleChange.bind(this, "reason")}>
                    <option key={-1} value="">Select reason...</option>
                    {
                      cancellationReasons.map((reason, i) =>
                        <option key={reason.id} value={reason.id}>{reason.name}</option>
                      )
                    }
                  </select>
                </div>
                <div className="col-2">
                  <label>Cancel Date</label>
                  <br/>
                  <ModalDatePicker className="form-control"
                    id="cancelDate"
                    selected={!this.isFlatCancel()&&cancelDate}
                    maxDate={moment()}
                    onChange={this.handleDateChange}
                    disabled={this.isFlatCancel()}
                  />
                </div>
                <div className="col-2">
                  <label>Mileage</label>
                  <br/>
                  <input type='number'
                    className='form-control'
                    value={mileage}
                    onChange={this.handleChange.bind(this, "mileage")}
                    disabled={this.isFlatCancel()}
                  />
                </div>
                <div className="col-2">
                  <label/>
                  <br/>
                  {
                    !quoteRequested &&
                    <button className="btn btn-primary mt-2"
                      disabled={this.disableGetQuote()}
                      onClick={this.handleGetQuote}>
                      Get Quotes
                    </button>
                  }
                </div>
              </div>
              <div className="row justify-content-end py-2">
                <div style={{color:'#ff9900'}}>
                  {quoteRequested && `NOTE: Making any changes to the above inputs will clear the current quotes and require getting new quotes.`}
                </div>
              </div>
              <div className="row py-2 border-bottom border-light">
                {
                  quoteRequested &&
                  this.renderTab(estimates)
                }
              </div>
              {
                activeTab === CANCELLATION_TAB &&
                <div className="row float-right py-2">
                  <a className={`btn btn-primary ${anyContractSelected ? "" : "disabled"}`}
                    target='_blank'
                    rel="noopener noreferrer"
                    href={anyContractSelected ? cancelContractPdfUrl : ""}>
                    <i className="fa fa-print"/> Print Quotes
                  </a>
                  &nbsp;
                  <button className="btn btn-primary"
                    disabled={disableCancel}
                    onClick={this.handleCancelContract}>
                    Cancel Contracts
                  </button>
                  &nbsp;
                  <button className="btn btn-primary"
                    onClick={onCloseModal}>
                    <i className="fa fa-close"/> Close
                  </button>
                </div>
              }
              {
                activeTab === QUOTES_TAB &&
                <div className="row float-right py-2">
                  <a className={`btn btn-primary ${anyContractSelected ? "" : "disabled"}`}
                    target='_blank'
                    rel="noopener noreferrer"
                    href={anyContractSelected ? customerQuotePdfUrl : ""}>
                    <i className="fa fa-print"/> Print Customer Quote
                  </a>
                  &nbsp;
                  <a className={`btn btn-primary ${anyContractSelected ? "" : "disabled"}`}
                    target='_blank'
                    rel="noopener noreferrer"
                    href={anyContractSelected ? cancelRequestPdfUrl : ""}>
                    <i className="fa fa-print"/> Print Cancellation Request Form
                  </a>
                  &nbsp;
                  <button className="btn btn-primary"
                    onClick={onCloseModal}>
                    <i className="fa fa-close"/> Close
                  </button>
                </div>
              }
            </div>
          </Modal>
        }
      </div>
    );
  }

  /**
   * Function which returns number of contract selected for cancellation process.
   */
  numberOfContractSelectedToCancel = () => {
    const { cancellationEstimate: { estimates } } = this.state;
    return estimates && estimates.filter( estimates => estimates.selected ).length || 0;
  }
}
