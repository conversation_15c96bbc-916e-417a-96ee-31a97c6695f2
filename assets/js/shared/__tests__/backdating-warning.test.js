import moment from 'moment';
import { checkBackdatingWarning, BACKDATING_LIMITS, getBackdatingLimit, getDaysFromToday } from '../backdating-warning';

// Mock window.confirm
global.window = {
  confirm: jest.fn()
};

describe('backdating-warning', () => {
  const mockCancelReasons = [
    { id: 1, name: 'Flat Cancel - Recontract' },
    { id: 2, name: '<PERSON><PERSON>' },
    { id: 3, name: 'Customer Request' },
    { id: 4, name: 'Flat Cancel - Not Purchased' }
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('BACKDATING_LIMITS', () => {
    it('should have correct limit values', () => {
      expect(BACKDATING_LIMITS.FLAT_CANCEL).toBe(30);
      expect(BACKDATING_LIMITS.GENERAL).toBe(90);
      expect(BACKDATING_LIMITS.REPO).toBe(180);
    });
  });

  describe('getDaysFromToday', () => {
    it('should return positive number for past dates', () => {
      const pastDate = moment().subtract(5, 'days');
      expect(getDaysFromToday(pastDate)).toBe(5);
    });

    it('should return 0 for today', () => {
      const today = moment();
      expect(getDaysFromToday(today)).toBe(0);
    });

    it('should return negative number for future dates', () => {
      const futureDate = moment().add(5, 'days');
      expect(getDaysFromToday(futureDate)).toBe(-5);
    });
  });

  describe('getBackdatingLimit', () => {
    it('should return flat cancel limit for flat cancel reasons', () => {
      const result = getBackdatingLimit({ name: 'Flat Cancel - Recontract' });
      expect(result.limit).toBe(30);
      expect(result.type).toBe('flat cancel');
    });

    it('should return repo limit for repo reasons', () => {
      const result = getBackdatingLimit({ name: 'Repo' });
      expect(result.limit).toBe(180);
      expect(result.type).toBe('repo');
    });

    it('should return general limit for other reasons', () => {
      const result = getBackdatingLimit({ name: 'Customer Request' });
      expect(result.limit).toBe(90);
      expect(result.type).toBe('general');
    });

    it('should return general limit for null/undefined reason', () => {
      const result = getBackdatingLimit(null);
      expect(result.limit).toBe(90);
      expect(result.type).toBe('general');
    });
  });

  describe('checkBackdatingWarning', () => {
    it('should return true for future dates', () => {
      const futureDate = moment().add(1, 'day');
      const result = checkBackdatingWarning(futureDate, 1, mockCancelReasons);
      expect(result).toBe(true);
      expect(window.confirm).not.toHaveBeenCalled();
    });

    it('should return true for today', () => {
      const today = moment();
      const result = checkBackdatingWarning(today, 1, mockCancelReasons);
      expect(result).toBe(true);
      expect(window.confirm).not.toHaveBeenCalled();
    });

    it('should return true for dates within flat cancel limit', () => {
      const dateWithinLimit = moment().subtract(25, 'days'); // Within 30 day limit
      const result = checkBackdatingWarning(dateWithinLimit, 1, mockCancelReasons); // Flat cancel reason
      expect(result).toBe(true);
      expect(window.confirm).not.toHaveBeenCalled();
    });

    it('should show warning for dates exceeding flat cancel limit', () => {
      window.confirm.mockReturnValue(true);
      const dateExceedingLimit = moment().subtract(35, 'days'); // Exceeds 30 day limit
      const result = checkBackdatingWarning(dateExceedingLimit, 1, mockCancelReasons); // Flat cancel reason
      
      expect(window.confirm).toHaveBeenCalledWith(
        expect.stringContaining('35 days in the past, which exceeds the 30-day backdating limit for flat cancel cancellations')
      );
      expect(result).toBe(true);
    });

    it('should show warning for dates exceeding repo limit', () => {
      window.confirm.mockReturnValue(true);
      const dateExceedingLimit = moment().subtract(185, 'days'); // Exceeds 180 day limit
      const result = checkBackdatingWarning(dateExceedingLimit, 2, mockCancelReasons); // Repo reason
      
      expect(window.confirm).toHaveBeenCalledWith(
        expect.stringContaining('185 days in the past, which exceeds the 180-day backdating limit for repo cancellations')
      );
      expect(result).toBe(true);
    });

    it('should show warning for dates exceeding general limit', () => {
      window.confirm.mockReturnValue(true);
      const dateExceedingLimit = moment().subtract(95, 'days'); // Exceeds 90 day limit
      const result = checkBackdatingWarning(dateExceedingLimit, 3, mockCancelReasons); // General reason
      
      expect(window.confirm).toHaveBeenCalledWith(
        expect.stringContaining('95 days in the past, which exceeds the 90-day backdating limit for general cancellations')
      );
      expect(result).toBe(true);
    });

    it('should call onReset when user cancels', () => {
      window.confirm.mockReturnValue(false);
      const onReset = jest.fn();
      const dateExceedingLimit = moment().subtract(35, 'days');
      
      const result = checkBackdatingWarning(dateExceedingLimit, 1, mockCancelReasons, onReset);
      
      expect(window.confirm).toHaveBeenCalled();
      expect(onReset).toHaveBeenCalled();
      expect(result).toBe(false);
    });

    it('should return true for missing parameters', () => {
      expect(checkBackdatingWarning(null, 1, mockCancelReasons)).toBe(true);
      expect(checkBackdatingWarning(moment(), null, mockCancelReasons)).toBe(true);
      expect(checkBackdatingWarning(moment(), 1, null)).toBe(true);
    });
  });
});
