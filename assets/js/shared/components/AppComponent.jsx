var React = require('react');
var PropTypes = require('prop-types');
var Alert = require('react-s-alert').default;

var ajax = require('shared/ajax');
var hstore = require('shared/hstore');
var VersionChecker = require('shared/components/VersionChecker');

var pt = PropTypes;
var ErrorBoundary = require("../../ErrorBoundary").default;
var FIS_BASE_PATH = require("../../app/fis_menu/routes").FIS_BASE_PATH;
var classNames = require('classnames');

class App extends React.Component {
  static childContextTypes = {
    sessionLoaded: pt.bool.isRequired,
    user: pt.shape({
      id: pt.number.isRequired,
      first_name: pt.string.isRequired,
      last_name: pt.string.isRequired,
      email: pt.string.isRequired,
      roles: pt.shape({
        Map: pt.object.isRequired,
      }).isRequired,
      stores: pt.arrayOf(
        pt.shape({
          id: pt.number.isRequired,
          code: pt.string.isRequired,
          name: pt.string.isRequired,
          has_ro_integration: pt.bool.isRequired,
          has_deal_integration: pt.bool.isRequired,
          time_zone: pt.string.isRequired,
        })
      ).isRequired,
    }),
    store: pt.shape({
      id: pt.number.isRequired,
      code: pt.string.isRequired,
      name: pt.string.isRequired,
      has_ro_integration: pt.bool.isRequired,
      has_deal_integration: pt.bool.isRequired,
      time_zone: pt.string.isRequired,
    }),
  };

  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  static propTypes = {
    children: PropTypes.shape({}).isRequired,
    location: PropTypes.shape({
      pathname: PropTypes.string.isRequired,
    }).isRequired,
    route: PropTypes.shape({
      headerComponent: PropTypes.func.isRequired,
      userLoaded: PropTypes.func,
    }).isRequired,
    routes: PropTypes.arrayOf(PropTypes.shape({
      noAuth: PropTypes.bool,
      noHeader: PropTypes.bool,
      roles: PropTypes.arrayOf(PropTypes.string.isRequired),
    }).isRequired).isRequired,
  };

  constructor(props) {
    super(props);

    this.state = {
      sessionLoading: true,
      sessionLoaded: false,
      user: undefined,
      store: undefined,
      userLoggingOut: false,
      routeUnlisten: undefined,
      hasNewVersion: false,
      forbidden: false,
    };
  }


  getChildContext() {
    return {
      sessionLoaded: this.state.sessionLoaded,
      user: this.state.user,
      store: this.state.store,
    };
  }

  componentWillUnmount() {
    VersionChecker.stop();
    if (this.state.routeUnlisten) {
      this.state.routeUnlisten();
    }
  }

  componentDidMount() {
    VersionChecker.start(this.versionDiffed);
    ajax.needAuth = function() {
      if (!this.isCurrentPathNoAuth()) {
        this.setState({sessionLoaded: false, user: undefined});
        const return_uri = `${this.props.location.basename ? this.props.location.basename : ''}${this.props.location.pathname}`;
        this.context.router.replace({pathname: '/login', query: {return_uri: return_uri}});
      }
    }.bind(this);
    if (!this.isCurrentPathNoAuth()) {
      this.loadUser(() => {
        if (this.props.route.userLoaded) {
          if (this.props.route.userLoaded.call(this, this.state.user)) {
            this.testForbidden();
          }
        } else {
          this.testForbidden();
        }
      });
    }
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    this.testForbidden(nextProps);
  }

  testForbidden = (props) => {
    if (this.isCurrentPathNoAuth(props)) {
      this.setState({forbidden: false});
      return;
    }
    var roles = this.currentRoles(props);
    if (!roles || roles.length === 0) {
      this.setState({forbidden: false});
      return;
    }
    if (!this.state.sessionLoaded) {
      this.setState({forbidden: true});
      return;
    }
    if (hstore.hasAny(this.state.user.roles, roles)) {
      this.setState({forbidden: false});
    } else {
      this.setState({forbidden: true});
    }
  };

  isCurrentPathNoAuth = (props) => {
    if (!props) {
      props = this.props;
    }
    return props.routes.findIndex(function(route) {
      return route.noAuth;
    }) !== -1;
  };

  isCurrentPathNoHeader = () => {
    return this.props.routes.findIndex(function(route) {
      return route.noHeader;
    }) !== -1;
  };

  currentRoles = (props) => {
    if (!props) {
      props = this.props;
    }
    return props.routes[props.routes.length - 1].roles;
  };

  versionDiffed = () => {
    this.setState({hasNewVersion: true});
  };

  loadUser(callback) {
    this.setState({sessionLoading: true}, function() {
      ajax.json(
        '/api/session',
        {},
        {},
        function(data, status, xhr) {
          if (status === 200) {
            this.setState({sessionLoaded: true, user: data.User, store: data.CurrentStore});
            if (callback) {
              callback();
            }
          } else {
            this.setState({sessionLoaded: false});
          }
          this.setState({sessionLoading: false});
        }.bind(this)
      );
    });
  }

  logoutUser = () => {
    this.setState({userLoggingOut: true}, function() {
      ajax.json(
        '/session',
        {},
        {method: "DELETE"},
        function(data, status, xhr) {
          if (status === 200) {
            const return_uri = `${this.props.location.basename ? this.props.location.basename : ''}${this.props.location.pathname}`;
            this.context.router.replace({pathname: '/login', query: {return_uri: return_uri}});
            this.setState({sessionLoaded: false, user: undefined});
          }
          this.setState({userLoggingOut: false});
        }.bind(this)
      );
    });
  };

  render() {
    const isFISRoute = (this.props.location.pathname.includes(FIS_BASE_PATH));
    const isCancellationPage = this.props.location.pathname.startsWith('/cancellations-dashboard/cancellation/');

    const containerWrapperClasses = classNames('pt-2', {
      'container': !isFISRoute && !isCancellationPage,
      'fis-menu-container': isFISRoute,
      'cancellation-page': isCancellationPage,
    });
    return (
      <div>
        { this.state.user && this.renderHeader() }
        <div className={containerWrapperClasses}>
          <ErrorBoundary>
            <Alert stack={{limit: 3}} offset={30} position={'top-right'} effect={'slide'} timeout={5000} />
            { this.renderNewVersion() }
            { this.renderContent() }
          </ErrorBoundary>
        </div>
      </div>
    );
  }

  renderHeader = () => {
    if (!this.isCurrentPathNoHeader()) {
      return React.createElement(this.props.route.headerComponent, {
        sessionLoaded: this.state.sessionLoaded,
        user: this.state.user,
        logoutUser: this.logoutUser,
      });
    }
  };

  renderNewVersion = () => {
    var click = (e) => {
      e.preventDefault();
      window.location.reload(true);
    };
    if (this.state.hasNewVersion) {
      return (
        <div className="alert alert-warning">
          <div className="row align-items-center">
            <div className="col">TCA Connect has an update. Please refresh the page to update.</div>
            <div className="col-auto">
              <button type="button" onClick={ click } className="btn btn-secondary btn-sm">Refresh</button>
            </div>
          </div>
        </div>
      );
    }
  };

  renderContent = () => {
    if (this.state.userLoggingOut) {
      return <div>Logging out...</div>;
    } else if (this.isCurrentPathNoAuth()) {
      return React.cloneElement(this.props.children, {key: this.props.location.pathname, loadUser: this.loadUser.bind(this)});
    } else if (this.state.forbidden) {
      return (
        <div>
          <i className="fa fa-warning" /> Forbidden. Not allowed to go to this page.
        </div>
      );
    } else if (this.state.sessionLoaded) {
      return React.cloneElement(this.props.children, {key: this.props.location.pathname, store: this.state.store});
    } else if (this.state.sessionLoading) {
      return (
        <div>
          <i className="fa fa-refresh fa-spin" /> Loading app...
        </div>
      );
    } else {
      return <div>Problem loading app. Please try again.</div>;
    }
  };
}

module.exports = App;
