var React = require("react");
var PropTypes = require('prop-types');

var Modal = require("shared/components/Modal");

class ConfirmModal extends React.Component {
  static propTypes = {
    visible: PropTypes.bool.isRequired,
    close: PropTypes.func.isRequired,
    confirm: PropTypes.func.isRequired,
    children: PropTypes.node,
    proceedLabel: PropTypes.string,
    cancelLabel: PropTypes.string,
    disabled: PropTypes.bool,
  };

  onCancel = (e) => {
    this.props.close();
  };

  onConfirm = (e) => {
    this.props.confirm();
  };

  render() {
    return (
      <Modal visible={ this.props.visible } close={ this.props.close }>
        { this.props.children }
        <div className="text-right">
          <button type="button" id="confirm-modal-cancel" className="btn btn-secondary btn-sm" onClick={ this.onCancel }>
            <i className="fa fa-ban" /> { this.props.cancelLabel || 'Cancel'}
          </button>
          &nbsp;
          <button type="button" id="confirm-modal-ok" className="btn btn-primary btn-sm" onClick={ this.onConfirm } disabled={ this.props.disabled }>
            <i className="fa fa-check" /> { this.props.proceedLabel || 'OK'}
          </button>
        </div>
      </Modal>
    );
  }
}

module.exports = ConfirmModal;
