import React from 'react';
import accounting from 'accounting';
import PropTypes from 'prop-types';
import moment from 'moment';
import dateFormat from 'shared/date-format';
import { getTimezoneWiseDate } from 'shared/display-date';
import Claims from './Claims';
import ContractVinEditModal from './ContractVinEditModal';
import ContractMileageEditModal from './ContractMileageEditModal';
import hstore from 'shared/hstore';
import isEmpty from 'lodash/isEmpty';
import {jsonPromise as ajax} from 'shared/ajax';
import {PAYMENT_TYPE_MAP, SPP_LENDERS} from './constant';
import * as Roles from './../../roles';
import Alert from "react-s-alert";
import Modal from 'shared/components/Modal.jsx';
import FieldError from 'shared/components/FieldError';
import ErrorSummary from 'shared/components/ErrorSummary';
import * as roles from 'shared/roles';
import * as Context from 'shared/context';
import ContractAttachmentModal from './ContractAttachmentModal';
import {PRODUCT_CODE_MAP} from 'shared/components/contracts/constant';
import CancelPaymentUpdate from '../../../admin/accounting/cancel-contract/CancelPaymentUpdate';


const OWNER = "owner";
const CO_OWNER = "co-owner";

export default class ContractDetails extends React.Component {

  static contextTypes = {
    router: Context.Router.isRequired,
    store: PropTypes.shape({
      id: PropTypes.number.isRequired,
      code: PropTypes.string.isRequired,
    }).isRequired,
    user: PropTypes.shape({
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }),
      stores: PropTypes.arrayOf(PropTypes.shape({
        company_group: PropTypes.string,
      })),
    }),
  };

  static propTypes = {
    id: PropTypes.number,
    contractData: PropTypes.shape({
      id: PropTypes.number.isRequired,
      code: PropTypes.string.isRequired,
      product_code: PropTypes.string.isRequired,
      deal_number: PropTypes.string.isRequired,
      store_code: PropTypes.string.isRequired,
      store_number: PropTypes.string.isRequired,
      status: PropTypes.string.isRequired,
      edit_with_roles: PropTypes.arrayOf(PropTypes.string).isRequired,
      source: PropTypes.string.isRequired,
      vin: PropTypes.string.isRequired,
    }).isRequired,
    redirectToPrevious: PropTypes.func,
    activeTab: PropTypes.string,
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired
    }),
    setActive: PropTypes.func,
    isServiceUser: PropTypes.bool,
    contractStatusMap: PropTypes.object,
    productCodeMap: PropTypes.object,
    showContractLoader: PropTypes.bool,
    contractDetails: PropTypes.object,
    contractList: PropTypes.arrayOf(PropTypes.object).isRequired,
    cancelContract: PropTypes.object,
    loadContractDetails: PropTypes.func,
    updateNotes: PropTypes.func,
    isAdminView: PropTypes.bool,
    isEditView: PropTypes.bool,
    onChange: PropTypes.func,
    handleSave: PropTypes.func,
    onContractEdit: PropTypes.func,
    noteDetails: PropTypes.array,
    addNote: PropTypes.func,
    expireContract: PropTypes.func,
    unExpireContract: PropTypes.func,
    location: PropTypes.object,
    handleAddAttachment: PropTypes.func,
    canShowFinancedByDropdown: PropTypes.bool,
    isPaymentTypeLoanOrLease: PropTypes.func,
    cancelWaitingPayment: PropTypes.bool,
  };
  
  constructor(props, context){
    super(props);
    
    this.state = {
      supportingData: {},
      newNote: "",
      newModalNote: "",
      loader: false,
      showModal: false,
      showUnexpireModal: false,
      showFlagsModal: false,
      showEditVINModal: false,
      showEditMileageModal: false,
      showAdjustmentModal: false,
      showCancelUpdatePaymentModal: false,
      validationErrors: {},
      selectedFlag: "",
      allFlags: [],
      submitting: false,
      adjustmentAmount: 0,
      selectedAdjustment: '',
      contractVINs: [],
      surchargesAndAdjustments: [],
      adjustmentNote: '',
    };
  }
  
  componentDidMount(){
    if(this.props.isEditView) {
      this.loadSupportingData();
    }
    this.loadFlagsData();
    this.loadSurchargeData();
    this.loadRelatedVINs();
  }

  componentDidUpdate(prevProps) {
    if (prevProps.id !== this.props.id) {
      this.loadFlagsData();
      this.loadSurchargeData();
      this.loadRelatedVINs();
    }
  }

  loadFlagsData = () => {
    this.setState({
      loader: true
    }, () => {
      ajax(`/api/admin/contracts/${this.props.id}/flags`, {}, {}).then((response) => {
        if(response.status === 200){
          this.setState({
            allFlags: (response.data.contract_flags && response.data.contract_flags.filter(i => i.flag_reason != "ClaimFromCancelRefund")) || []
          });
        } else {
          Alert.error("Error loading flags data");
        }
        this.setState({
          loader: false
        });
      });
    });
  }

  loadSurchargeData = () => {
    this.setState({
      loader: true
    }, () => {
      ajax(`/api/admin/contracts/${this.props.id}/surcharges-and-adjustments`, {}, {}).then((response) => {
        if(response.status === 200){
          const adjustments = (response.data.manual_adjustments || [] ).map(adj => {
            return {
              menu_id: `ADJUSTMENT:${adj.adjustment_id}`,
              type: 'ADJUSTMENT',
              id: adj.adjustment_id,
              name: adj.name,
              default_amount: adj.default_amount,
              note_prefix: adj.note_prefix
            };
          });

          const surcharges = (response.data.surcharges || [] ).map(sch => {
            return {
              menu_id: `SURCHARGE:${sch.id}`,
              type: 'SURCHARGE',
              id: sch.id,
              name: sch.name.String,
              default_amount: sch.cost,
              note_prefix: `${sch.name.String}: `
            };
          });

          const surchargesAndAdjustments = [ ...adjustments, ...surcharges];

          this.setState({
            surchargesAndAdjustments: surchargesAndAdjustments || [],
          });
        } else {
          Alert.error("Error loading surcharge data");
        }
        this.setState({
          loader: false
        });
      });
    });
  }

  loadSupportingData = () => {
    this.setState({
      loader: true
    }, () => {
      ajax(`/api/admin/contracts/update-supporting-data`, {}, {}).then((response) => {
        if(response.status === 200){
          this.setState({
            supportingData: response.data
          });
        } else {
          Alert.error("Error loading supporting data");
        }
        this.setState({
          loader: false
        });
      });
    });
  };

  OnChange = (key, event) => {
    const state = this.state;
    state[key] = event.target.value;
    this.setState(state);
  };
  
  isContractsTabActive = () => {
    return this.props.activeTab === "contracts";
  };

  isVehicleTabActive = () => {
    return this.props.activeTab === "vehicle";
  };

  isCustomerTabActive = () => {
    return this.props.activeTab === "customer";
  };

  isFinancingTabActive = () => {
    return this.props.activeTab === "financing";
  };
  
  isEventsTabActive = () => {
    return this.props.activeTab === "events";
  };

  isNotesTabActive = () => {
    return this.props.activeTab === "notes";
  };
  
  isIssuingDealerSame = () => {
    if(this.props.contractDetails.contract.issuing_dealer === this.context.store.code){
      return true;
    }
    return false;
  };
  
  showModal = (event) => {
    event.preventDefault();
    this.setState({
      showModal: true
    });
  };

  showCancelUpdatePaymentModal = (event) => {
    event.preventDefault();
    this.setState({
      showCancelUpdatePaymentModal: true
    });
  };

  showUnexpireModal = (event) => {
    event.preventDefault();
    this.setState({
      showUnexpireModal: true
    });
  };
  
  renderEditNote(key){
    const note = this.state[key];
    if(hstore.has(this.context.user.roles, Roles.AccountRep) 
    || hstore.has(this.context.user.roles, Roles.AccountRepII)
    || hstore.has(this.context.user.roles, Roles.ViewOnlyClaims)){
      return(
        <div className="col-12 py-2">
          <textarea type="text"
            className="form-control mt-2"
            rows="5"
            value={note}
            onChange={this.OnChange.bind(null, key)}/>
        </div>
      );
    }
  }

  /**
   * getContractStatusBlock() function returns status block for contract
   * @returns status span component
   */
  getContractStatusBlock() {
    /**
     * Status -"A": Active, "C" : Cancelled, "X" : Expired, "P" : Pending
     * */
    const {
      productCodeMap,
      contractStatusMap
    } = this.props;
    let contractStatus = this.props.contractDetails.contract.status;
    let contractStatusBlock = "";
    if (contractStatus === contractStatusMap.Active || contractStatus === contractStatusMap.Remitted) {
      contractStatusBlock = <span className="badge badge-success">{`Active`}</span>;
    } else if (contractStatus === contractStatusMap.Pending) {
      if(this.props.contractData.product_code === productCodeMap.gap){
        contractStatusBlock = <span className="badge badge-warning">{`Paid`}</span>;
      } else {
        contractStatusBlock = <span className="badge badge-warning">{`Pending`}</span>;
      }
    } else if (contractStatus === contractStatusMap.Canceled) {
      contractStatusBlock = <span className="badge badge-danger">{`Cancelled`}</span>;
    } else if (contractStatus === contractStatusMap.Expired) {
      contractStatusBlock = <span className="badge badge-danger">{`Expired`}</span>;
    } else if (contractStatus === contractStatusMap.Generated) {
      contractStatusBlock = <span className="badge badge-success">{`New`}</span>;
    }
    return contractStatusBlock;
  }

  /**
   * getContractTransferBadge() function returns transfer status for contract
   * @returns transfer status span component
   */
  getContractTransferBadge() {
    const {
      contractDetails: {
        transfers,
        contract: {
          status,
        }
      },
      contractStatusMap,
    } = this.props;
    let contractTransferStatusBlock = "";

    if (status === contractStatusMap.Expired) {
      return contractTransferStatusBlock;
    }
    if (transfers && transfers.length > 0) {
      contractTransferStatusBlock = <span className="badge badge-warning">{`Transferred`}</span>;
    } 
    return contractTransferStatusBlock;
  }

  /**
   * getContractReInstateBadge() function returns transfer status for contract
   * @returns reinstate status span component
   */
  getContractReInstateBadge() {
    const {
      contractDetails : {
        reinstate,
        contract: {
          status,
        }
      },
      contractStatusMap,
    } = this.props;
    let contractReinstateStatusBlock = "";

    if (status === contractStatusMap.Expired) {
      return contractReinstateStatusBlock;
    }

    if (reinstate && reinstate.reinstate_date && !reinstate.is_void) {
      contractReinstateStatusBlock = <span className="badge badge-success">{`Reinstated`}</span>;
    } 
    return contractReinstateStatusBlock;
  }

  contractTypeSwitch(contractDetails) {
    const {
      productCodeMap
    } = this.props;
    
    switch (this.props.contractData.product_code) {
    case productCodeMap.service:
      return this.renderContractSCTable(contractDetails);
    case productCodeMap.maintenance:
      return this.renderContractMCTable(contractDetails);
    case productCodeMap.appearanceProtection:
      return this.renderContractCPTable(contractDetails);
    case productCodeMap.gap:
      return this.renderContractGPTable(contractDetails);
    case productCodeMap.toyotaTireWheel:
    case productCodeMap.toyotaGap:
      return this.renderContractTTAndTGTable(contractDetails);
    default:
      return this.renderContractTable(contractDetails);
    }
  }

  renderContractsTabContent(contractDetails) {
    const {
      cancelContract,
      contractDetails : {
        reinstate,
      },
    } = this.props;
    let olderCancellation = false;
    if (reinstate && reinstate.reinstate_date && cancelContract && cancelContract.cancel_date) {
      // we are comparing keeping .local(false) for cancel date because we dont have time stamp and only date.
      olderCancellation = moment.utc(reinstate.reinstate_date).local(false).isAfter(moment.utc(cancelContract.cancel_date).local(true));
    }

    let renderer = (
      <div className="container py-2">
        <div>{ this.contractTypeSwitch(contractDetails)}</div>
        {this.renderCancellationDetails()}
        {this.renderReInstateDetails()}
        {this.renderClaims()}
      </div>
    );
    if (olderCancellation) {
      renderer = ( 
        <div className="container py-2">
          <div>{ this.contractTypeSwitch(contractDetails)}</div>
          {this.renderReInstateDetails()}
          {this.renderCancellationDetails()}
          { this.renderClaims()}
        </div>
      );
    }
    return (
      <div className="clearfix">
        {this.renderFlagsDetails(this.state.allFlags)}
        { this.renderEditContractButton() }
        {renderer}
      </div>
    );
  }

  /**
   * Method reloads the page and keeps the current product type tab focused
   */
  reloadWindowAndKeepFocus = () => {
    const {
      id: contractId,
      product_code: productCode,
      code: contractCode
    } = this.props.contractData;
    const query = {
      product_code: productCode,
      id: contractId,
    };
    const route = { pathname: `/contracts-search/${contractCode}`, query: query};
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    } else {
      window.location.reload();
    }
  }  

  /**
   * Function to render flag details.
   *
   * @memberof ContractDetails
   */
  renderFlagsDetails = ({ length }) => length && (
    <div className="mt-2 text-truncate d-inline-block w-50 font-weight-bold text-danger">
      This contract currently has {length} flag{length > 1 && 's'}.
    </div>
  ) || null;

  renderEditContractButton() {
    const {
      contractDetails,
      contractData,
      contractStatusMap,
      onContractEdit,
      cancelWaitingPayment,
      isAdminView,
    } = this.props;
    // Only display the edit button in the admin view
    const showContractEdit = isAdminView;
    const showEditMileage = ['VSC', 'MNT'].includes(contractDetails.contract.product_type_code);
    // Only a user with the role specified by the contractData is allowed to
    // make changes to the terms of the contract.
    // Only contracts that were created in Connect can be changed.
    const editWithRoles = contractData.edit_with_roles;
    const editDisabled = !editWithRoles.some(
      editRole => hstore.has(this.context.user.roles, editRole)
    ) || !contractDetails.contract.editable;
    const expireDisabled = !editWithRoles.some(
      editRole => hstore.has(this.context.user.roles, editRole)
    ) && 
    !hstore.has(this.context.user.roles, Roles.AccountRepII)
    || contractDetails.contract.status === contractStatusMap.Canceled;
    return (
      <div className="float-right">
        { 
          showContractEdit &&
          <div className="row">
            {hstore.has(this.context.user.roles, roles.AccountRepManager) && showEditMileage &&this.renderEditMileageButton()}
            {hstore.has(this.context.user.roles, roles.AccountRepManager) && this.renderEditVINButton()}
            {hstore.has(this.context.user.roles, roles.AccountRepManager) && this.renderAdjustmentsButton()}
            &nbsp; &nbsp;
            {this.renderFlagsButton()}
            {
              (
                contractDetails.contract.status === contractStatusMap.Active ||
                contractDetails.contract.status === contractStatusMap.Canceled
              ) &&
              (
                <div className="m-2">
                  <button className="btn btn-secondary" id="expire-contract"
                    onClick={this.showModal}
                    disabled={this.props.cancelWaitingPayment || expireDisabled}>
                    <i className="fa fa-close"/> Expire
                  </button>
                </div>
              )
            }
            {
              (
                hstore.has(this.context.user.roles, roles.AccountRepManager) &&
                contractDetails.contract.is_manually_expired &&
                contractDetails.contract.status === contractStatusMap.Expired
              ) &&
              (
                <div className="m-2">
                  <button className="btn btn-secondary" id="expire-contract"
                    onClick={this.showUnexpireModal}
                    disabled={expireDisabled}>
                    <i className="fa fa-close"/> Unexpire
                  </button>
                </div>
              )
            }
            <div className="my-2">
              <button className="btn btn-secondary" id="edit-contract" 
                onClick={onContractEdit}
                disabled={editDisabled || cancelWaitingPayment}
              >
                <i className="fa fa-edit"/> Edit
              </button>
            </div>
          </div>
        }
      </div>
    );
  }

  /**
   * Method to render the flags button on the main UI.
   */
  renderFlagsButton = () => {
    return (
      <div className="my-2">
        <button className="btn btn-secondary" id="flags-contract"
          onClick={this.showFlagsModal}
          disabled={this.props.cancelWaitingPayment}>
          <i className="fa fa-flag"/> Manage Flags
        </button>
      </div>
    );
  }

  /** 
   * Method to set the state to indicate that flags modal has to render
   */
  showFlagsModal = (event) => {
    event.preventDefault();
    this.setState({showFlagsModal: true});
  };

  
  /**
   * Method to render the edit VIN button on the main UI.
   */
  renderEditVINButton = () => {
    return (
      <div className="my-2">
        <button className="btn btn-secondary" id="edit-vin-button"
          onClick={this.showEditVINModal}
          disabled={this.props.cancelWaitingPayment}>
          <i className="fa fa-edit"/> Edit VIN
        </button>
        &nbsp; &nbsp;
      </div>
    );
  }

  /**
  * Method to render the edit Mileage button on the main UI.
  */
  renderEditMileageButton = () => {
    return (
      <div className="my-2">
        <button className="btn btn-secondary" id="edit-vin-button"
          onClick={this.showEditMileageModal}
          disabled={this.props.cancelWaitingPayment}>
          <i className="fa fa-edit"/> Edit Mileage
        </button>
        &nbsp; &nbsp;
      </div>
    );
  }

  /** 
   * Method to set the state to indicate that Edit VIN modal has to render
   */
  showEditVINModal = (e) => {
    e.preventDefault();
    this.setState({ showEditVINModal: true });
  };

  /**
   * Method closes Edit VIN modal
   */
  closeEditVINModal = (e) => {
    e && e.preventDefault();
    this.setState({ showEditVINModal: false });
    this.reloadWindowAndKeepFocus();
  }

  /** 
   * Method to set the state to indicate that Edit Mileage modal has to render
   */
  showEditMileageModal = (e) => {
    e.preventDefault();
    this.setState({ showEditMileageModal: true });
  };

  /**
   * Method closes Edit Mileage modal and reloads the page
   */
  closeEditMileageModal = (e) => {
    e && e.preventDefault();
    this.setState({ showEditMileageModal: false });
    this.reloadWindowAndKeepFocus();
  }

  /**
   * Method closes Edit Mileage modal
   */
  cancelEditMileageModal = (e) => {
    e && e.preventDefault();
    this.setState({ showEditMileageModal: false });
  }

  /**
   * Method gets information about the contracts and their VINs on the current Contract Details page.
   * This information may be used to edit the VIN of one or more of these contracts.
   */
  loadRelatedVINs = () => {
    let contractVINs = this.props.contractList.map(contract => {
      return {
        code: contract.code,
        original_code: contract.original_code,
        contract_id: contract.id,
        product_type_code: contract.product_code,
        status: contract.status,
        vin: contract.vin,
        store_code: contract.store_code,
        is_selected: false,
      };
    });
    this.setState({
      contractVINs: contractVINs,
      currentContractVIN: this.props.contractData.vin,
    });
  };

  /**
   * Method to render the add adjustment button on the main UI.
   */
  renderAdjustmentsButton = () => {
    return (
      <div className="my-2">
        <button className="btn btn-secondary" id="adjustments-contract"
          onClick={this.showAdjustmentModal}
          disabled={this.props.cancelWaitingPayment}>
          <i className="fa fa-money" aria-hidden="true" /> Create Adjustment
        </button>
      </div>
    );
  }

  /** 
   * Method to set the state to indicate that adjustment modal has to render
   */
  showAdjustmentModal = (event) => {
    event.preventDefault();
    this.setState({ showAdjustmentModal: true });
  };

  /**
   * Method closes adjustment cancel modal
   */
  closeAdjustmentModal = (e) => {
    e && e.preventDefault();
    this.setState({
      showAdjustmentModal: false,
      selectedAdjustment: '',
      adjustmentAmount: 0,
      adjustmentNote: '',
    });
  }

  /**
   * Method will render the modal for the adjustment to add.
   */
  renderAdjustmentModal = () => {
    const { validationErrors } = this.state;
    return (
      <Modal visible={true} close={this.closeAdjustmentModal}>
        <div className="mt-4">
          <ErrorSummary errors={validationErrors} />
          <br />
          {this.renderAdjustmentControl(validationErrors)}
          {this.adjustmentAmount(validationErrors)}
          <br/>
          <div className="clearfix text-right">
            {this.renderAdjustmentModalButton()}
          </div>
        </div>
      </Modal>
    );
  }

  /**
   * This method will render the drop down list for the adjustments.
   */
  renderAdjustmentControl = (e) => {
    return (
      <div className="row">
        <div className="col-12">
          <div className='form-group'>
            <label htmlFor='f-adjustment'>*Select Adjustment to Add</label>
            {this.generateAdjustmentOptions()}
            <FieldError err={e.adjustment} />
          </div>
        </div>
      </div>
    );
  }

  /**
   * This method will render the adjustment amount text on modal
   * @param {e denotes validation errors on the field} 
   */
  adjustmentAmount(e){
    const { 
      adjustmentAmount,
      adjustmentNote
    } = this.state;
    if(hstore.has(this.context.user.roles, Roles.AccountRep)){
      return(
        <div className="row">
          <div className="col-12">
            <div className='form-group'>
              <label htmlFor='f-adjustment-amount'>*Adjustment Amount</label>
              <input type="text"
                className="form-control"
                value={adjustmentAmount}
                onChange={this.adjustmentAmountChange}/>
              <FieldError err={e.adjustment_amount} />
            </div>
          </div>

          <div className="col-12">
            <div className='form-group'>
              <label htmlFor='f-adjustment-note'>*Adjustment Note</label>
              <input type="text"
                className="form-control"
                value={adjustmentNote}
                onChange={this.adjustmentNoteChange}/>
              <FieldError err={e.adjustment_note} />
            </div>
          </div>
        </div>
      );
    }
  }

  /**
   * This method will set a state value to the field value of the adjustment amount control.
   */
  adjustmentAmountChange = (event) => {
    this.setState({ adjustmentAmount : event.target.value });
  }

  /**
   * This method will set a state value to the field value of the adjustment note control.
   */
  adjustmentNoteChange = (event) => {
    this.setState({ adjustmentNote : event.target.value });
  }

  /**
   * Method will generate the all available adjustments and surcharges in drop down list
   */
  generateAdjustmentOptions = () => {
    const {
      surchargesAndAdjustments,
      selectedAdjustment,
    } = this.state;
    if (surchargesAndAdjustments) {
      return (
        <select id='f-adjustment'
          className='form-control
          form-control-sm'
          disabled={false}
          value={selectedAdjustment}
          required={true}
          onChange={this.handleAdjustmentChange}
        >
          <option value=''>&mdash; Select &mdash;</option>
          {
            surchargesAndAdjustments.map((value,index) => {
              return (<option
                key={value.menu_id}
                value={value.menu_id}>
                {value.name}
              </option>);
            })
          }
        </select>
      );
    }
  }

  /**
   * Method will handle adjustment change in drop down list.
   */
  handleAdjustmentChange = (e) => {
    const { 
      surchargesAndAdjustments,
    } = this.state;

    const selectedAdjustment = e.target.value;
    const adjustmentType = selectedAdjustment.split(':')[0];
    const adjustment = surchargesAndAdjustments.find(a => a.menu_id === selectedAdjustment);
    let adjustmentAmount = parseInt(adjustment.default_amount) || 0;
    const adjustmentNote = adjustment.note_prefix;

    // The amount can only be multiplied by -1 for the legacy 'No Inspection' surcharge.
    if ((adjustmentAmount < 0) && (adjustmentType === 'SURCHARGE') && (adjustment.name === 'No Inspection')) {
      adjustmentAmount = adjustmentAmount * -1;
    }
    this.setState({ 
      selectedAdjustment,
      adjustmentAmount,
      adjustmentNote
    });
  }

  /**
   * This method renders buttons on the modal
   */
  renderAdjustmentModalButton() {
    const {
      submitting,
      selectedAdjustment,
      adjustmentAmount,
      adjustmentNote,
    } = this.state;
    if (submitting) {
      return (
        <div>
          <button type="button" disabled={true} className="btn btn-primary" onClick={this.handleAdjustmentSubmitForm}>
            <i className="fa fa-refresh fa-spin" /> Submitting
          </button>
          <button className="btn btn-secondary" disabled={true} id="close-flags" onClick={this.closeAdjustmentModal}>
            <i className="fa fa-refresh fa-spin" /> Cancel
          </button>
        </div>
      );
    }

    const isDisabled = !selectedAdjustment || !adjustmentAmount || !adjustmentNote;
    return (
      <div>
        <button type="button" disabled={isDisabled} className="btn btn-primary"  onClick={this.handleAdjustmentSubmitForm}>
          <i className="fa fa-check" /> Submit
        </button>
        {' '}
        <button className="btn btn-secondary" disabled={false} id="close-flags" onClick={this.closeAdjustmentModal}>
          <i className="fa fa-check" /> Cancel
        </button>
      </div>
    );
  }

  /**
   * Handles the form submit and calls the api 
   */
  handleAdjustmentSubmitForm = (e) => {
    e.preventDefault();
    const {
      selectedAdjustment,
      adjustmentAmount,
      adjustmentNote,
    } = this.state;

    const adjustmentType = selectedAdjustment.split(':')[0];
    const adjustmentId = parseInt(selectedAdjustment.split(':')[1]);
    const data = {
      adjustment_type: adjustmentType,
      id: adjustmentId,
      adjustment_amount: adjustmentAmount,
      adjustment_note: adjustmentNote,
    };

    const url = `/api/admin/contracts/${this.props.id}/surcharges-and-adjustments`;
    const method = 'POST';

    this.setState({ submitting: true, validationErrors: {}, isUploadError: false}, () => {
      ajax(url, data, { method: method }).then(results => {
        if (results.status === 200) {
          Alert.success('Surcharge added successfully.');
          this.loadSurchargeData();
          this.setState({ submitting: false });
          this.closeAdjustmentModal();
        } else {
          Alert.error(`Error adding surcharge: ${results.data.message}`);
          this.setState({ submitting: false, validationErrors: results.data.validationErrors || {}, isUploadError: true });
        }
      }).catch(reason => {
        Alert.error('Error adding surcharge');
        this.setState({ submitting: false, isUploadError: true });
      });
    });
  }

  /**
   * Method will render the modal for the flags to cancel.
   */
  renderFlagsModal = () => {
    const { validationErrors } = this.state;
    return (
      <Modal visible={true} close={this.closeFlagsModal}>
        <div className="mt-4">
          <ErrorSummary errors={validationErrors} />
          {this.renderFlagsControl(validationErrors)}
          <div>*Add Notes</div>
          {this.renderFlagsNote("newModalNote")}
          <br/>
          <div className="clearfix text-right">
            {this.renderFlagsModalButton()}
          </div>
        </div>
      </Modal>
    );
  }

  /**
   * Handles the form submit and calls the api 
   */
  handleFlagsSubmitForm = (e) => {
    e.preventDefault();
    const {
      newModalNote,
      selectedFlag,
      allFlags,
    } = this.state;

    const data = {
      notes_text: newModalNote,
      id: parseInt(selectedFlag),
      flag_reason: allFlags.find((value,index) => value.id === parseInt(selectedFlag)).flag_reason,
    };

    const url = `/api/admin/contracts/${this.props.id}/flags`;
    const method = 'DELETE';

    this.setState({ submitting: true, validationErrors: {}, isUploadError: false}, () => {
      ajax(url, data, { method: method }).then(results => {
        if (results.status === 200) {
          Alert.success('Contract flag removed successfully.');
          this.loadFlagsData();
          this.setState({ submitting: false });
          this.closeFlagsModal();
          this.props.updateNotes({
            id: results.data.note.NoteID,
            notes_text: results.data.note.NoteText,
            created_at: results.data.note.CreatedAt,
            user_name: `${this.context.user.first_name} ${this.context.user.last_name}`,
          });
        } else {
          Alert.error(`Error removing flag: ${results.data.message}`);
          this.setState({ submitting: false, validationErrors: results.data.validationErrors || {}, isUploadError: true });
        }
      }).catch(reason => {
        Alert.error('Error removing flag');
        this.setState({ submitting: false, isUploadError: true });
      });
    });
  }



  /**
   * This method renders buttons on the modal
   */
  renderFlagsModalButton() {
    const {
      submitting,
      selectedFlag,
      newModalNote,
    } = this.state;
    if (submitting) {
      return (
        <div>
          <button type="button" disabled={true} className="btn btn-primary" onClick={this.handleFlagsSubmitForm}>
            <i className="fa fa-refresh fa-spin" /> Submitting
          </button>
          <button className="btn btn-secondary" disabled={true} id="close-flags" onClick={this.closeFlagsModal}>
            <i className="fa fa-refresh fa-spin" /> Cancel
          </button>
        </div>
      );
    }
    const isDisabled = !selectedFlag || !newModalNote;
    return (
      <div>
        <button type="button" disabled={isDisabled} className="btn btn-primary"  onClick={this.handleFlagsSubmitForm}>
          <i className="fa fa-check" /> Submit
        </button>
        {' '}
        <button className="btn btn-secondary" disabled={false} id="close-flags" onClick={this.closeFlagsModal}>
          <i className="fa fa-check" /> Cancel
        </button>
      </div>
    );
  }

  /**
   * Method closes flag cancel modal
   */
  closeFlagsModal = (e) => {
    e && e.preventDefault();
    this.setState({showFlagsModal: false, selectedFlag: "", newModalNote: ""});
  }

  /**
   * This method will render the drop down list for the flags.
   */
  renderFlagsControl = (e) => {
    return (
      <div className="row">
        <div className="col-12">
          <div className='form-group'>
            <label htmlFor='f-flags'>*Select Flag to be Removed</label>
            {this.genrateFlagsOptions()}
            <FieldError err={e.attachmentType} />
          </div>
        </div>
      </div>
    );
  }

  /**
   * Method will genrate the all availbale flags in drop down lost
   */
  genrateFlagsOptions = () => {
    const {
      allFlags,
      selectedFlag,
    } = this.state;
    if (allFlags) {
      return (
        <select id='f-flags' className='form-control form-control-sm' disabled={false} value={selectedFlag} required={true} onChange={this.handleFlagChange}>
          <option value=''>&mdash; Select &mdash;</option>
          {
            allFlags.map((value,index) => 
              <option
                key={value.id}
                value={value.id}>
                {value.contract_flag_description}
              </option>
            )
          }
        </select>
      );
    }
  }

  /**
   * Method will handle flag change in drop down list.
   */
  handleFlagChange = (e) => {
    this.setState({ selectedFlag: e.target.value });
  }

  /**
   * This method will render the notes text area on modal
   * @param {Key denotes which property we are accesing} key 
   */
  renderFlagsNote(key){
    const note = this.state[key];
    if(hstore.has(this.context.user.roles, Roles.AccountRep)){
      return(
        <div className="row">
          <div className="col-12">
            <textarea type="text"
              className="form-control"
              rows="5"
              value={note}
              onChange={this.OnFlagNoteChange.bind(null, key)}/>
          </div>
        </div>
      );
    }
  }

  /**
   * This method will set a state value to the field value of the notes control.
   */
  OnFlagNoteChange = (key, event) => {
    const state = this.state;
    state[key] = event.target.value;
    this.setState(state);
  }

  renderReInstateDetails() {
    const {
      contractDetails : {
        reinstate,
      },
    } = this.props;

    if (reinstate && reinstate.reinstate_date) {
      return (
        <div id="reinstate-detail">
          <h5>Reinstate Details</h5>
          <table className="table table-striped text-wrap no-gutters">
            <tbody>
              <tr className="row">
                <td className="col-2"><span>Reinstate Date</span></td>
                <td className="col-4"><span><strong>{reinstate.reinstate_date ? moment.utc(reinstate.reinstate_date).local(false).format(dateFormat): ""}</strong></span></td>
              </tr>
              <tr className="row">
                <td className="col-2"><span>Invoice Date</span></td>
                <td className="col-4"><span><strong>{reinstate.invoice_date ? moment.utc(reinstate.invoice_date).local(false).format(dateFormat): ""}</strong></span></td>
                <td className="col-2"><span>Invoice Number</span></td>
                <td className="col-4"><span><strong>{reinstate.invoice_number}</strong></span></td>
              </tr>
            </tbody>
          </table>
        </div>
      );
    }
  }

  renderCancellationDetails() {
    const {
      cancelContract,
      contractDetails,
      contractDetails : {
        reinstate,
      },
    } = this.props;
    const financingDetails = contractDetails.financing_details;
    const isReinstatement = reinstate && reinstate.reinstate_date;
    const updateCancelPaymentEnabled = (this.props.contractDetails.contract.status === this.props.contractStatusMap.Canceled) &&
      hstore.hasAny(this.context.user.roles, [roles.CancellationPaymentHandler]);

    if ((this.props.contractDetails.contract.status === this.props.contractStatusMap.Canceled || isReinstatement) && cancelContract) {
      //Removed odometer, percent_of_contract_used and check_number, Added cancel factor 
      return (
        <>
          <div id="cancellation-detail">
            <div className="row">
              <div className="my-2">
                <h5>Cancellation Details</h5>
              </div>
              &nbsp;&nbsp;
              {
                this.props.isAdminView &&
                <div className="my-2">
                  <button className="btn btn-secondary"
                    onClick={this.showCancelUpdatePaymentModal}
                    disabled={!updateCancelPaymentEnabled}>
                    <i className="fa fa-edit"/>Update Cancel Payment
                  </button>
                &nbsp;&nbsp;
                </div>
              }
            </div>
            <table className="table table-striped text-wrap no-gutters">
              <tbody>
                <tr className="row">
                  <td className="col-2"><span>Cancel Status</span></td>
                  <td className="col-4"><span><strong>{cancelContract.cancel_status}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Cancel Date</span></td>
                  <td className="col-4">
                    <span><strong>{cancelContract.cancel_date ? moment.utc(cancelContract.cancel_date).format(dateFormat) : ""}</strong></span>
                  </td>
                  <td className="col-2"><span>Cancel Mileage</span></td>
                  <td className="col-4">
                    <span><strong>{cancelContract.cancel_mileage ? accounting.formatNumber(cancelContract.cancel_mileage) : ""}</strong></span>
                  </td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Cancel Reason</span></td>
                  <td className="col-4"><span><strong>{cancelContract.cancel_reason}</strong></span></td>
                  <td className="col-2"><span>Customer Refund</span></td>
                  <td className="col-4">
                    <span><strong>{cancelContract.customer_refund ? accounting.formatMoney(cancelContract.customer_refund) : ""}</strong></span>
                  </td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Cancel Fee</span></td>
                  <td className="col-4">
                    <span><strong>{cancelContract.cancel_fee ? accounting.formatMoney(cancelContract.cancel_fee) : ""}</strong></span>
                  </td>
                  <td className="col-2"><span>Store Refund</span></td>
                  <td className="col-4">
                    <span>
                      <strong>
                        {
                          (this.props.isAdminView || this.isIssuingDealerSame()) ?
                            (cancelContract.store_refund ? accounting.formatMoney(cancelContract.store_refund) : "") :
                            "(Available to Issuing Dealer Only)"
                        }
                      </strong>
                    </span>
                  </td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Contract Used</span></td>
                  <td className="col-4"><span><strong>{`${cancelContract.cancel_factor_used}%`}</strong></span></td>
                  <td className="col-2"><span>Third Party Remit Refund</span></td>
                  <td className="col-4">
                    <span><strong>{`${accounting.formatMoney(cancelContract.third_party_remit)}`}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Days Used</span></td>
                  <td className="col-4"><span><strong>{`${cancelContract.days_used}%`}</strong></span></td>
                  <td className="col-2"><span>Miles Used</span></td>
                  <td className="col-4"><span><strong>{`${cancelContract.miles_used}%`}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2">Sales Tax</td>
                  <td className="col-4">
                    <span><strong>{`${accounting.formatMoney(cancelContract.sales_tax)}`}</strong></span></td>

                  <td className="col-2"><span>RSA Refund</span></td>
                  <td className="col-4">
                    <span><strong>{`${accounting.formatMoney(cancelContract.rsa_refund)}`}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Sales Tax Percent</span></td>
                  <td className="col-4"><span><strong>{cancelContract.sales_tax_rate}%</strong></span></td>
                  <td className="col-2"><span>Total Claim Amount Deducted</span></td>
                  <td className="col-4">
                    <span><strong>{accounting.formatMoney(cancelContract.claims_deducted_amount)}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Claims Deducted</span></td>
                  <td className="col-4"><span><strong>{cancelContract.claims_deducted ? `Yes` : `No`}</strong></span></td>
                </tr>
                {
                  financingDetails.payment_type === PAYMENT_TYPE_MAP.SPP &&
                <tr className="row">
                  <td className="col-2"><span>SPP Amount Paid</span></td>
                  <td className="col-4">
                    <span><strong>{accounting.formatMoney(cancelContract.spp_customer_paid)}</strong></span></td>
                  <td className="col-2"><span>SPP Balance</span></td>
                  <td className="col-4">
                    <span><strong>{accounting.formatMoney(cancelContract.spp_balance)}</strong></span></td>
                </tr>
                }
                {
                  financingDetails.payment_type === PAYMENT_TYPE_MAP.SPP &&
                <tr className="row">
                  <td className="col-2"><span>Adjusted Customer Refund</span></td>
                  <td className="col-4">
                    <span><strong>{accounting.formatMoney(cancelContract.adjusted_customer_refund)}</strong></span></td>
                  <td className="col-2"><span>Store Chargeback</span></td>
                  <td className="col-4"><span><strong>{accounting.formatMoney(cancelContract.store_chargeback)}</strong></span>
                  </td>
                </tr>
                }
                {
                  financingDetails.payment_type === PAYMENT_TYPE_MAP.SPP &&
                <tr className="row">
                  <td className="col-2"><span>SPP Refund</span></td>
                  <td className="col-4">
                    <span><strong>{accounting.formatMoney(cancelContract.spp_refund)}</strong></span></td>
                </tr>
                }
                <tr className="row">
                  <td className="col-2"><span>Invoice Date</span></td>
                  <td className="col-4">
                    <span><strong>{cancelContract.invoiced_at ? moment(cancelContract.invoiced_at).format(dateFormat) : ""}</strong></span>
                  </td>
                  <td className="col-2"><span>Invoice Number</span></td>
                  <td className="col-4"><span><strong>{cancelContract.invoice_number}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Refund Payable To</span></td>
                  <td className="col-4"><span><strong>{cancelContract.payee_type}</strong></span></td>
                  <td className="col-2"><span>Payee Name</span></td>
                  <td className="col-4"><span><strong>{cancelContract.payee_name}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Payee Attention To</span></td>
                  <td className="col-4"><span><strong>{cancelContract.payee_attention_to}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Address</span></td>
                  <td className="col-4"><span><strong>{cancelContract.payee_address}</strong></span></td>
                  <td className="col-2"><span>City</span></td>
                  <td className="col-4"><span><strong>{cancelContract.payee_city}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>State</span></td>
                  <td className="col-4"><span><strong>{cancelContract.payee_state}</strong></span></td>
                  <td className="col-2"><span>Zip</span></td>
                  <td className="col-4"><span><strong>{cancelContract.payee_postal_code}</strong></span></td>
                </tr>
              </tbody>
            </table>
          </div>
          {
            (cancelContract.payee_type === 'Lender' || cancelContract.payee_type === 'Customer') && (
              <div id="cancellation-detail">
                <h5>Cancellation Payment Details</h5>
                <table className="table table-striped text-wrap no-gutters">
                  <tbody>
                    <tr className="row">
                      <td className="col-2"><span>Check Type</span></td>
                      <td className="col-4">
                        <span>
                          <strong>
                            {cancelContract.check_applicable
                              ? (cancelContract.is_electronic_check ? 'Electronic Check' : 'Paper Check')
                              : ''}
                          </strong>
                        </span>
                      </td>
                      <td className="col-2"><span>Email Address</span></td>
                      <td className="col-4">
                        <span>
                          <strong>
                            {cancelContract.check_applicable ? cancelContract.email : ''}
                          </strong>
                        </span>
                      </td>
                    </tr>
                    {
                      (cancelContract.cancel_status === 'Cancel Complete' || cancelContract.cancel_status === 'Bill Not Issued'
                      || cancelContract.check_number) && (
                        <>
                          <tr className="row">
                            <td className="col-2"><span>Bill Number</span></td>
                            <td className="col-4"><span><strong>{cancelContract.bill_number}</strong></span></td>
                          </tr>
                          <tr className="row">
                            <td className="col-2"><span>Paid Date</span></td>
                            <td className="col-4">
                              <span><strong>{cancelContract.paid_date}</strong></span></td>
                          </tr>
                          <tr className="row">
                            <td className="col-2"><span>Check Number</span></td>
                            <td className="col-4"><span><strong>{cancelContract.check_number}</strong></span></td>
                            <td className="col-2"><span>Check Amount</span></td>
                            <td className="col-4">
                              <span><strong>{accounting.formatMoney(cancelContract.check_amount)}</strong></span>
                            </td>
                          </tr>
                          {
                            cancelContract.manual_update_notes.length > 0 &&
                          <tr className="row">
                            <td className="col-2"><span>Manual Update Notes</span></td>
                            <td className="col-4"><span><strong>{cancelContract.manual_update_notes}</strong></span></td>
                          </tr>
                          }
                        </>
                      )
                    }
                  </tbody>
                </table>
              </div>
            )
          }
        </>
      );
    }
  }

  renderClaims() {
    const {
      isServiceUser,
      contractDetails,
      contractData,
      loadContractDetails,
      isAdminView,
    } = this.props;
    
    return (
      <Claims isAdminView={isAdminView}
        productCodeMap={this.props.productCodeMap}
        isServiceUser={isServiceUser}
        claims={ contractDetails.claims }
        contractInfo={ contractDetails.contract }
        vehicleInfo={ contractDetails.vehicle_details }
        productCode={ contractData.product_code }
        status={ contractDetails.contract.status }
        loadContract={ loadContractDetails }
        location={this.props.location}
        customerName={ `${contractDetails.customer_details.first_name} ${contractDetails.customer_details.last_name}`}
      />
    );
  }
  
  renderOptionsAndSurcharges(option, surcharge){
    const options = option.options;
    let optionIndex = option.optionIndex;
    const optionLength = option.optionLength;
    const surcharges = surcharge.surcharges;
    let surchargeIndex = surcharge.surchargeIndex;
    const surchargeLength = surcharge.surchargeLength;
    const length = (optionLength - optionIndex) + (surchargeLength - surchargeIndex);
    let rows = [];
    
    for(let index = 0; index < length; index++){
      rows.push(
        <tr className="row" key={index}>
          <td className="col-2"></td>
          <td className="col-4"></td>
          <td className="col-4">
            <span>
              {
                optionIndex < optionLength ?
                  options[optionIndex].name || options[optionIndex].code :
                  surchargeIndex < surchargeLength ?
                    surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
              }
            </span>
          </td>
          <td className="col-2">
            <span>
              <strong>
                {
                  optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                    "Yes" : ""
                }
              </strong>
            </span>
          </td>
        </tr>
      );
    }
    return rows;
  }

  displayCost = (contractDetails) => {
    if((hstore.hasAny(this.context.user.roles, [roles.LDCSAccounting, roles.LDCSFinance]) && !this.props.isAdminView) || this.props.isAdminView) {
      if (this.props.isAdminView || this.isIssuingDealerSame()) {
        if (contractDetails.cost) {
          return accounting.formatMoney(contractDetails.cost);
        } else {
          return "";
        }
      } else {
        return "(Available to Issuing Dealer Only)";
      }
    } else {
      return "";
    }
  }

  renderContractSCTable(contractDetails) {
    const options = contractDetails.options;
    const surcharges = contractDetails.surcharges;
    let optionIndex = 0;
    let surchargeIndex = 0;
    
    const optionLength = options ? options.length : 0;
    const surchargeLength = surcharges ? surcharges.length : 0;
    return (
      <table className="table table-striped text-nowrap no-gutters">
        <tbody>
          <tr className="row">
            <td className="col-2"><span>Contract Number</span></td>
            <td className="col-4"><span><strong>{contractDetails.original_code || contractDetails.code}</strong>
              &nbsp;&nbsp;&nbsp;{this.getContractStatusBlock()}&nbsp;&nbsp;{this.getContractTransferBadge()}
              {this.props.contractDetails.transfers && this.props.contractDetails.transfers.length > 0 ? <span>&nbsp;&nbsp;</span> : ''}
              {this.getContractReInstateBadge()}</span></td>
            <td className="col-6"><span><strong>
              {
                (contractDetails.options && contractDetails.options.length) || surchargeLength ?
                  "Included Options" : ""
              }
            </strong></span></td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Issuing Dealer</span></td>
            <td className="col-4"><span><strong>{contractDetails.issuing_dealer}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Issuing Dealer #</span></td>
            <td className="col-4"><span><strong>{contractDetails.issuing_dealer_number}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Plan</span></td>
            <td className="col-4"><span><strong>{contractDetails.plan_name}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ? 
                    options[optionIndex].name || options[optionIndex].code : 
                    surchargeIndex < surchargeLength ? 
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Product</span></td>
            <td className="col-4"><span><strong>{contractDetails.product_variant_display_name}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Dealer PV Name</span></td>
            <td className="col-4"><span><strong>{contractDetails.dealer_pv_display_name}</strong></span></td>
            <td className="col-4"></td>
            <td className="col-2"></td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Effective Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.effective_date ? moment.utc(contractDetails.effective_date).format(dateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Expiration Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.expiration_date ? moment.utc(contractDetails.expiration_date).format(dateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Effective Mileage</span></td>
            <td className="col-4"><span><strong>{accounting.formatNumber(contractDetails.effective_mileage)}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Expiration Mileage</span></td>
            <td className="col-4"><span><strong>{contractDetails.expiration_mileage ? accounting.formatNumber(contractDetails.expiration_mileage) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Price</span></td>
            <td className="col-4"><span><strong>{contractDetails.price ? accounting.formatMoney(contractDetails.price) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Cost</span></td>
            <td className="col-4">
              <span>
                <strong>
                  {
                    this.displayCost(contractDetails)
                  }
                </strong>
              </span>
            </td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          {
            contractDetails.surcharge_cost != 0 &&
            <tr className="row">
              <td className="col-2"><span>Inspection Level</span></td>
              <td className="col-4">
                <span><strong>{contractDetails.eligibility_level ? contractDetails.eligibility_level : (contractDetails.inspection_liability.String ? contractDetails.inspection_liability.String + ' Liability' : 'Not Required')}</strong></span>
              </td>
            </tr>
          }
          {
            contractDetails.surcharge_cost != 0 &&
            <tr className="row">
              <td className="col-2"><span>Inspection Charge</span></td>
              <td className="col-4"><span><strong>{contractDetails.surcharge_cost ? accounting.formatMoney(contractDetails.surcharge_cost) : ""}</strong></span></td>
            </tr>
          }
          <tr className="row">
            <td className="col-2"><span>Invoice Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.invoiced_at ? moment(contractDetails.invoiced_at).format(dateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Invoice Number</span></td>
            <td className="col-4"><span><strong>{contractDetails.invoice_number}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Book Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.book_date ? moment.utc(contractDetails.book_date).format(dateFormat) : ""}</strong></span></td>
          </tr>
          {
            this.props.isAdminView &&
            <tr className="row">
              <td className="col-2"><span>Third Party Remit</span></td>
              <td className="col-4"><span><strong>{contractDetails.remit_amount ? accounting.formatMoney(contractDetails.remit_amount) : ""}</strong></span></td>
            </tr>
          }
          {
            this.props.isAdminView &&
            <tr className="row">
              <td className="col-2"><span>Source</span></td>
              <td className="col-4"><span><strong>{contractDetails.source}</strong></span></td>
            </tr>
          }
          {
            this.props.isAdminView &&
            <tr className="row">
              <td className="col-2"><span>Dealer Platform</span></td>
              <td className="col-4"><span><strong>{contractDetails.dealer_platform && contractDetails.dealer_platform.name}</strong></span></td>
            </tr>
          }
          {
            this.props.isAdminView &&
            <tr className="row">
              <td className="col-2"><span>Last updated At</span></td>
              <td className="col-4">
                <span><strong>{contractDetails.updated_at ? moment.utc(contractDetails.updated_at).format(dateFormat) : ""}</strong></span>
              </td>
            </tr>
          }
        </tbody>
      </table>
    );
  }

  renderContractMCTable(contractDetails) {
    const options = contractDetails.options;
    const surcharges = contractDetails.surcharges;
    let optionIndex = 0;
    let surchargeIndex = 0;

    const optionLength = options ? options.length : 0;
    const surchargeLength = surcharges ? surcharges.length : 0;
    
    return (
      <table className="table table-striped text-nowrap no-gutters">
        <tbody>
          <tr className="row">
            <td className="col-2"><span>Contract Number</span></td>
            <td className="col-4"><span><strong>{contractDetails.original_code || contractDetails.code}</strong>
              &nbsp;&nbsp;&nbsp;{this.getContractStatusBlock()}&nbsp;&nbsp;{this.getContractTransferBadge()}
              {this.props.contractDetails.transfers && this.props.contractDetails.transfers.length > 0 ? <span>&nbsp;&nbsp;</span> : ''}
              {this.getContractReInstateBadge()}</span></td>
            <td className="col-6"><span><strong>Included Options</strong></span></td>{/*Showing everytime as Major Coupons and Minor Coupons*/}
          </tr>
          <tr className="row">
            <td className="col-2"><span>Issuing Dealer</span></td>
            <td className="col-4"><span><strong>{contractDetails.issuing_dealer}</strong></span></td>
            <td className="col-2"><span>Major Coupons</span></td>
            <td className="col-4"><span><strong>{contractDetails.maintenance_visits}</strong></span></td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Issuing Dealer #</span></td>
            <td className="col-4"><span><strong>{contractDetails.issuing_dealer_number}</strong></span></td>
            <td className="col-2"><span>Minor Coupons</span></td>
            <td className="col-4"><span><strong>{contractDetails.minor_coupons_purchased || 0}</strong></span></td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Plan</span></td>
            <td className="col-4"><span><strong>{contractDetails.plan_name}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Product</span></td>
            <td className="col-4"><span><strong>{contractDetails.product_variant_display_name}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Dealer PV Name</span></td>
            <td className="col-4"><span><strong>{contractDetails.dealer_pv_display_name}</strong></span></td>
            <td className="col-4"></td>
            <td className="col-2"></td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Effective Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.effective_date ? moment.utc(contractDetails.effective_date).format(dateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Expiration Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.expiration_date ? moment.utc(contractDetails.expiration_date).format(dateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Effective Mileage</span></td>
            <td className="col-4"><span><strong>{accounting.formatNumber(contractDetails.effective_mileage)}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Expiration Mileage</span></td>
            <td className="col-4"><span><strong>{contractDetails.expiration_mileage ? accounting.formatNumber(contractDetails.expiration_mileage) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Price</span></td>
            <td className="col-4"><span><strong>{contractDetails.price ? accounting.formatMoney(contractDetails.price) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Cost</span></td>
            <td className="col-4">
              <span>
                <strong>
                  {
                    this.displayCost(contractDetails)
                  }
                </strong>
              </span>
            </td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Invoice Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.invoiced_at ? moment(contractDetails.invoiced_at).format(dateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Invoice Number</span></td>
            <td className="col-4"><span><strong>{contractDetails.invoice_number}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Book Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.book_date ? moment.utc(contractDetails.book_date).format(dateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          {
            this.props.isAdminView &&
            <tr className="row">
              <td className="col-2"><span>Third Party Remit</span></td>
              <td className="col-4"><span><strong>{contractDetails.remit_amount ? accounting.formatMoney(contractDetails.remit_amount) : ""}</strong></span></td>
              <td className="col-4">
                <span>
                  {
                    optionIndex < optionLength ?
                      options[optionIndex].name || options[optionIndex].code :
                      surchargeIndex < surchargeLength ?
                        surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                  }
                </span>
              </td>
              <td className="col-2">
                <span>
                  <strong>
                    {
                      optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                        "Yes" : ""
                    }
                  </strong>
                </span>
              </td>
            </tr>
          }
          {
            this.props.isAdminView &&
            <tr className="row">
              <td className="col-2"><span>Source</span></td>
              <td className="col-4"><span><strong>{contractDetails.source}</strong></span></td>
            </tr>
          }
          {
            this.props.isAdminView &&
            <tr className="row">
              <td className="col-2"><span>Dealer Platform</span></td>
              <td className="col-4"><span><strong>{contractDetails.dealer_platform && contractDetails.dealer_platform.name}</strong></span></td>
            </tr>
          }
          {
            this.props.isAdminView &&
            <tr className="row">
              <td className="col-2"><span>Last updated At</span></td>
              <td className="col-4">
                <span><strong>{contractDetails.updated_at ? moment.utc(contractDetails.updated_at).format(dateFormat) : ""}</strong></span>
              </td>
            </tr>
          }
          {this.renderOptionsAndSurcharges({options, optionIndex, optionLength},{surcharges, surchargeIndex, surchargeLength})}
        </tbody>
      </table>
    );
  }

  renderVTANumber(contractDetails){
    if(contractDetails.product_type_code === PRODUCT_CODE_MAP.theftRegistration){
      return (
        <tr className="row">
          <td className="col-2"><span>VTA Number</span></td>
          <td className="col-4"><span><strong>{contractDetails.vehicle_theft_number}</strong></span></td>
        </tr>
      );
    }
    else{
      return (
        <div></div>
      );
    }    
  }

  renderContractCPTable(contractDetails) {
    const options = contractDetails.options;
    const surcharges = contractDetails.surcharges;
    let optionIndex = 0;
    let surchargeIndex = 0;

    const optionLength = options ? options.length : 0;
    const surchargeLength = surcharges ? surcharges.length : 0;
    
    return (
      <table className="table table-striped text-nowrap no-gutters">
        <tbody>
          <tr className="row">
            <td className="col-2"><span>Contract Number</span></td>
            <td className="col-4"><span><strong>{contractDetails.original_code || contractDetails.code}</strong>
              &nbsp;&nbsp;&nbsp;{this.getContractStatusBlock()}&nbsp;&nbsp;{this.getContractTransferBadge()}
              {this.props.contractDetails.transfers && this.props.contractDetails.transfers.length > 0 ? <span>&nbsp;&nbsp;</span> : ''}
              {this.getContractReInstateBadge()}</span></td>
            <td className="col-6"><span><strong>Included Options</strong></span></td>{/*Showing every time for paint, fabric etc*/}
          </tr>
          <tr className="row">
            <td className="col-2"><span>Issuing Dealer</span></td>
            <td className="col-4"><span><strong>{contractDetails.issuing_dealer}</strong></span></td>
            <td className="col-2"><span>Paint</span></td>
            <td className="col-4"><span><strong>{contractDetails.paint !== null ? (contractDetails.paint ? "Yes" : "No" ): ""}</strong></span></td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Issuing Dealer #</span></td>
            <td className="col-4"><span><strong>{contractDetails.issuing_dealer_number}</strong></span></td>
            <td className="col-2"><span>Fabric</span></td>
            <td className="col-4"><span><strong>{contractDetails.fabric !== null ? (contractDetails.fabric ? "Yes" : "No" ): ""}</strong></span></td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Effective Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.effective_date ? moment.utc(contractDetails.effective_date).format(dateFormat) : ""}</strong></span></td>
            <td className="col-2"><span>Leather/Vinyl</span></td>
            <td className="col-4"><span><strong>{contractDetails.leather_or_vinyl !== null ? (contractDetails.leather_or_vinyl !== null ? "Yes" : "No") : ""}</strong></span></td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Expiration Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.expiration_date ? moment.utc(contractDetails.expiration_date).format(dateFormat) : ""}</strong></span></td>
            <td className="col-2"><span>Dent and Ding</span></td>
            <td className="col-4"><span><strong>{contractDetails.dent_and_ding !== null ? (contractDetails.dent_and_ding !== null ? "Yes" : "No") : ""}</strong></span></td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Price</span></td>
            <td className="col-4"><span><strong>{contractDetails.price ? accounting.formatMoney(contractDetails.price) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Cost</span></td>
            <td className="col-4">
              <span>
                <strong>
                  {
                    this.displayCost(contractDetails)
                  }
                </strong>
              </span>
            </td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Invoice Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.invoiced_at ? moment(contractDetails.invoiced_at).format(dateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Invoice Number</span></td>
            <td className="col-4"><span><strong>{contractDetails.invoice_number}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Book Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.book_date ? moment.utc(contractDetails.book_date).format(dateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          {
            this.props.isAdminView &&
            <tr className="row">
              <td className="col-2"><span>Third Party Remit</span></td>
              <td className="col-4"><span><strong>{contractDetails.remit_amount ? accounting.formatMoney(contractDetails.remit_amount) : ""}</strong></span></td>
              <td className="col-4">
                <span>
                  {
                    optionIndex < optionLength ?
                      options[optionIndex].name || options[optionIndex].code :
                      surchargeIndex < surchargeLength ?
                        surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                  }
                </span>
              </td>
              <td className="col-2">
                <span>
                  <strong>
                    {
                      optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                        "Yes" : ""
                    }
                  </strong>
                </span>
              </td>
            </tr>
          }
          {
            this.props.isAdminView &&
            <tr className="row">
              <td className="col-2"><span>Source</span></td>
              <td className="col-4"><span><strong>{contractDetails.source}</strong></span></td>
            </tr>
          }
          {
            this.props.isAdminView &&
            <tr className="row">
              <td className="col-2"><span>Dealer Platform</span></td>
              <td className="col-4"><span><strong>{contractDetails.dealer_platform && contractDetails.dealer_platform.name}</strong></span></td>
            </tr>
          }
          {
            this.props.isAdminView &&
            <tr className="row">
              <td className="col-2"><span>Last updated At</span></td>
              <td className="col-4">
                <span><strong>{contractDetails.updated_at ? moment.utc(contractDetails.updated_at).format(dateFormat) : ""}</strong></span>
              </td>
            </tr>
          }
          {this.renderOptionsAndSurcharges({options, optionIndex, optionLength},{surcharges, surchargeIndex, surchargeLength})}
        </tbody>
      </table>
    );
  }

  renderContractGPTable(contractDetails) {
    const options = contractDetails.options;
    const surcharges = contractDetails.surcharges;
    let optionIndex = 0;
    let surchargeIndex = 0;

    const optionLength = options ? options.length : 0;
    const surchargeLength = surcharges ? surcharges.length : 0;
    
    return (
      <table className="table table-striped text-nowrap no-gutters">
        <tbody>
          <tr className="row">
            <td className="col-2"><span>Contract Number</span></td>
            <td className="col-4"><span><strong>{contractDetails.original_code || contractDetails.code}</strong>
              &nbsp;&nbsp;&nbsp;{this.getContractStatusBlock()}&nbsp;&nbsp;{this.getContractTransferBadge()}
              {this.props.contractDetails.transfers && this.props.contractDetails.transfers.length > 0 ? <span>&nbsp;&nbsp;</span> : ''}
              {this.getContractReInstateBadge()}</span></td>
            <td className="col-6"><span><strong>
              {
                (contractDetails.options && contractDetails.options.length) || surchargeLength ?
                  "Included Options" : ""
              }
            </strong></span></td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Issuing Dealer</span></td>
            <td className="col-4"><span><strong>{contractDetails.issuing_dealer}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Issuing Dealer #</span></td>
            <td className="col-4"><span><strong>{contractDetails.issuing_dealer_number}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Effective Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.effective_date ? moment.utc(contractDetails.effective_date).format(dateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Expiration Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.expiration_date ? moment.utc(contractDetails.expiration_date).format(dateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Plan</span></td>
            <td className="col-4"><span><strong>{contractDetails.plan_name}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Price</span></td>
            <td className="col-4"><span><strong>{contractDetails.price ? accounting.formatMoney(contractDetails.price) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Cost</span></td>
            <td className="col-4">
              <span>
                <strong>
                  {
                    this.displayCost(contractDetails)
                  }
                </strong>
              </span>
            </td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          {this.renderAdditionalSurcharges(contractDetails)}
          <tr className="row">
            <td className="col-2"><span>Invoice Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.invoiced_at ? moment(contractDetails.invoiced_at).format(dateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Invoice Number</span></td>
            <td className="col-4"><span><strong>{contractDetails.invoice_number}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Book Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.book_date ? moment.utc(contractDetails.book_date).format(dateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          {
            this.props.isAdminView &&
            <tr className="row">
              <td className="col-2"><span>Third Party Remit</span></td>
              <td className="col-4"><span><strong>{contractDetails.remit_amount ? accounting.formatMoney(contractDetails.remit_amount) : ""}</strong></span></td>
              <td className="col-4">
                <span>
                  {
                    optionIndex < optionLength ?
                      options[optionIndex].name || options[optionIndex].code :
                      surchargeIndex < surchargeLength ?
                        surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                  }
                </span>
              </td>
              <td className="col-2">
                <span>
                  <strong>
                    {
                      optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                        "Yes" : ""
                    }
                  </strong>
                </span>
              </td>
            </tr>
          }
          {
            this.props.isAdminView &&
            <tr className="row">
              <td className="col-2"><span>Source</span></td>
              <td className="col-4"><span><strong>{contractDetails.source}</strong></span></td>
            </tr>
          }
          {
            this.props.isAdminView &&
            <tr className="row">
              <td className="col-2"><span>Dealer Platform</span></td>
              <td className="col-4"><span><strong>{contractDetails.dealer_platform && contractDetails.dealer_platform.name}</strong></span></td>
            </tr>
          }
          {
            this.props.isAdminView &&
            <tr className="row">
              <td className="col-2"><span>Last updated At</span></td>
              <td className="col-4">
                <span><strong>{contractDetails.updated_at ? moment.utc(contractDetails.updated_at).format(dateFormat) : ""}</strong></span>
              </td>
            </tr>
          }
          {this.renderOptionsAndSurcharges({options, optionIndex, optionLength},{surcharges, surchargeIndex, surchargeLength})}
        </tbody>
      </table>
    );
  }

  renderContractTTAndTGTable(contractDetails) {
    const options = contractDetails.options;
    const surcharges = contractDetails.surcharges;
    let optionIndex = 0;
    let surchargeIndex = 0;

    const optionLength = options ? options.length : 0;
    const surchargeLength = surcharges ? surcharges.length : 0;
    
    return (
      <table className="table table-striped text-nowrap no-gutters">
        <tbody>
          <tr className="row">
            <td className="col-2"><span>Contract Number</span></td>
            <td className="col-4"><span><strong>{contractDetails.original_code || contractDetails.code}</strong>
              &nbsp;&nbsp;&nbsp;{this.getContractStatusBlock()}&nbsp;&nbsp;{this.getContractTransferBadge()}
              {this.props.contractDetails.transfers && this.props.contractDetails.transfers.length > 0 ? <span>&nbsp;&nbsp;</span> : ''}
              {this.getContractReInstateBadge()}</span></td>
            <td className="col-6"><span><strong>
              {
                (contractDetails.options && contractDetails.options.length) || surchargeLength ?
                  "Included Options" : ""
              }
            </strong></span></td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Issuing Dealer</span></td>
            <td className="col-4"><span><strong>{contractDetails.issuing_dealer}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Issuing Dealer #</span></td>
            <td className="col-4"><span><strong>{contractDetails.issuing_dealer_number}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Effective Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.effective_date ? moment.utc(contractDetails.effective_date).format(dateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Expiration Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.expiration_date ? moment.utc(contractDetails.expiration_date).format(dateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Price</span></td>
            <td className="col-4"><span><strong>{contractDetails.price ? accounting.formatMoney(contractDetails.price) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Cost</span></td>
            <td className="col-4">
              <span>
                <strong>
                  {
                    this.displayCost(contractDetails)
                  }
                </strong>
              </span>
            </td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          {this.renderAdditionalSurcharges(contractDetails)}
          {
            this.props.isAdminView &&
            <tr className="row">
              <td className="col-2"><span>Created Using</span></td>
              <td className="col-4"><span><strong>{contractDetails.source}</strong></span></td>
            </tr>
          }
          {
            this.props.isAdminView &&
            <tr className="row">
              <td className="col-2"><span>Last updated At</span></td>
              <td className="col-4">
                <span><strong>{contractDetails.updated_at ? moment.utc(contractDetails.updated_at).format(dateFormat) : ""}</strong></span>
              </td>
            </tr>
          }
          {this.renderOptionsAndSurcharges({options, optionIndex, optionLength},{surcharges, surchargeIndex, surchargeLength})}
        </tbody>
      </table>
    );
  }

  renderContractTable(contractDetails) {
    const options = contractDetails.options;
    const surcharges = contractDetails.surcharges;
    let optionIndex = 0;
    let surchargeIndex = 0;

    const optionLength = options ? options.length : 0;
    const surchargeLength = surcharges ? surcharges.length : 0;
    
    return (
      <table className="table table-striped text-nowrap no-gutters">
        <tbody>
          <tr className="row">
            <td className="col-2"><span>Contract Number</span></td>
            <td className="col-4"><span><strong>{contractDetails.original_code || contractDetails.code}</strong>
              &nbsp;&nbsp;&nbsp;{this.getContractStatusBlock()}&nbsp;&nbsp;{this.getContractTransferBadge()}
              {this.props.contractDetails.transfers && this.props.contractDetails.transfers.length > 0 ? <span>&nbsp;&nbsp;</span> : ''}
              {this.getContractReInstateBadge()}</span></td>
            <td className="col-6"><span><strong>
              {
                (contractDetails.options && contractDetails.options.length) || surchargeLength ?
                  "Included Options" : ""
              }
            </strong></span></td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Issuing Dealer</span></td>
            <td className="col-4"><span><strong>{contractDetails.issuing_dealer}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Issuing Dealer #</span></td>
            <td className="col-4"><span><strong>{contractDetails.issuing_dealer_number}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          {this.renderVTANumber(contractDetails)}
          <tr className="row">
            <td className="col-2"><span>Effective Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.effective_date ? moment.utc(contractDetails.effective_date).format(dateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Expiration Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.expiration_date ? moment.utc(contractDetails.expiration_date).format(dateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Price</span></td>
            <td className="col-4"><span><strong>{contractDetails.price ? accounting.formatMoney(contractDetails.price) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Cost</span></td>
            <td className="col-4">
              <span>
                <strong>
                  {
                    this.displayCost(contractDetails)
                  }
                </strong>
              </span>
            </td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          {this.renderAdditionalSurcharges(contractDetails)}
          <tr className="row">
            <td className="col-2"><span>Invoice Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.invoiced_at ? moment(contractDetails.invoiced_at).format(dateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Invoice Number</span></td>
            <td className="col-4"><span><strong>{contractDetails.invoice_number}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Book Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.book_date ? moment.utc(contractDetails.book_date).format(dateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          {
            this.props.isAdminView &&
            <tr className="row">
              <td className="col-2"><span>Third Party Remit</span></td>
              <td className="col-4">
                <span><strong>{contractDetails.remit_amount ? accounting.formatMoney(contractDetails.remit_amount) : ""}</strong></span>
              </td>
              <td className="col-4">
                <span>
                  {
                    optionIndex < optionLength ?
                      options[optionIndex].name || options[optionIndex].code :
                      surchargeIndex < surchargeLength ?
                        surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                  }
                </span>
              </td>
              <td className="col-2">
                <span>
                  <strong>
                    {
                      optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                        "Yes" : ""
                    }
                  </strong>
                </span>
              </td>
            </tr>
          }
          {
            this.props.isAdminView &&
            <tr className="row">
              <td className="col-2"><span>Source</span></td>
              <td className="col-4"><span><strong>{contractDetails.source}</strong></span></td>
            </tr>
          }
          {
            this.props.isAdminView &&
            <tr className="row">
              <td className="col-2"><span>Dealer Platform</span></td>
              <td className="col-4"><span><strong>{contractDetails.dealer_platform && contractDetails.dealer_platform.name}</strong></span></td>
            </tr>
          }
          {
            this.props.isAdminView &&
            <tr className="row">
              <td className="col-2"><span>Last updated At</span></td>
              <td className="col-4">
                <span><strong>{contractDetails.updated_at ? moment.utc(contractDetails.updated_at).format(dateFormat) : ""}</strong></span>
              </td>
            </tr>
          }
          {this.renderOptionsAndSurcharges({options, optionIndex, optionLength},{surcharges, surchargeIndex, surchargeLength})}
        </tbody>
      </table>
    );
  }

  renderVehicleTabContent(vehicleDetails) {
    let newOrUsedVehicle = "";
    if (vehicleDetails.is_new) {
      newOrUsedVehicle = "New";
    } else {
      newOrUsedVehicle = "Used";
    }
    return (
      <div className="container py-2">
        <div className="row">
          <div className="col-12">
            <table className="table table-striped text-nowrap no-gutters">
              <tbody>
                <tr className="row">
                  <td className="col-2"><span>VIN</span></td>
                  <td className="col-4"><span><strong>{vehicleDetails.vin}</strong></span></td>
                  <td className="col-2"><span>Condition</span></td>
                  <td className="col-4"><span><strong>{newOrUsedVehicle}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Year</span></td>
                  <td className="col-4"><span><strong>{vehicleDetails.year}</strong></span></td>
                  <td className="col-2"><span>Beginning Miles</span></td>
                  <td className="col-4"><span><strong>{vehicleDetails.odometer !== "" ? accounting.formatNumber(vehicleDetails.odometer) : ""}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Make</span></td>
                  <td className="col-4"><span><strong>{vehicleDetails.make}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Model</span></td>
                  <td className="col-4"><span><strong>{vehicleDetails.model}</strong></span></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    );
  }

  renderPaymentOptions() {
    return [
      <option key={PAYMENT_TYPE_MAP.CASH} value={PAYMENT_TYPE_MAP.CASH}>{PAYMENT_TYPE_MAP.CASH}</option>,
      <option key={PAYMENT_TYPE_MAP.SPP} value={PAYMENT_TYPE_MAP.SPP}>{PAYMENT_TYPE_MAP.SPP}</option>,
      <option key={PAYMENT_TYPE_MAP.LOAN} value={PAYMENT_TYPE_MAP.LOAN}>{PAYMENT_TYPE_MAP.LOAN}</option>,
      <option key={PAYMENT_TYPE_MAP.LEASE} value={PAYMENT_TYPE_MAP.LEASE}>{PAYMENT_TYPE_MAP.LEASE}</option>
    ];
  }

  renderLenderNames() {
    if (!this.state.showContractLoader && this.state.supportingData.lenders) {
      return this.state.supportingData.lenders.map((lender) => {
        return <option key={lender.id} value={lender.id}>{lender.name}</option>;
      });
    }
  }
  
  getLenderAddress(financingDetails) {
    const lender = financingDetails.lender_id ?
      this.state.supportingData.lenders?.find((lender) =>
        lender.id === parseInt(financingDetails.lender_id) && lender) : null;

    let lenderAddress = lender ?
      lender.address :
      financingDetails.lender_address ?
        financingDetails.lender_address : "";
    if (lenderAddress) {
      lenderAddress += lender ?
        ` ${lender.postal_code}` :
        financingDetails.lender_zip ?
          ` ${financingDetails.lender_zip}` : "";
    }
    
    return lenderAddress;
  }

  renderFinancingTabContent(contractDetails) {
    
    const {
      isEditView,
      onChange,
    } = this.props;
    
    const financingDetails = contractDetails.financing_details;
    const isPaymentTypeLoanOrLease = this.props.isPaymentTypeLoanOrLease(financingDetails.payment_type);
    const canShowLender = contractDetails.contract.is_e_contract ?
      (financingDetails.lender_name === SPP_LENDERS.LenderSPP || 
      financingDetails.lender_name === SPP_LENDERS.LenderSPPFull) ? true : isPaymentTypeLoanOrLease :
      isPaymentTypeLoanOrLease;
    const lenderAddress = this.getLenderAddress(financingDetails);
   
    return (
      <div className="container py-2">
        <div className="row">
          <div className="col-12">
            <table className="table table-striped text-nowrap no-gutters">
              <tbody>
                <tr className="row">
                  <td className="col-2"><span>Deal Type</span></td>
                  <td className="col-4"><span><strong>{financingDetails.sale_type}</strong></span></td>
                  <td className="col-2"><span>Financed By</span></td>
                  {!hstore.hasAny(this.context.user.roles, [roles.AccountRep]) || 
                  !this.props.canShowFinancedByDropdown ?
                    <td className="col-4"><span><strong>{canShowLender && financingDetails.lender_name}</strong></span></td> :
                    <td className="col-4">
                      <span>
                        <strong>
                          {
                            isEditView ?
                              <select id="financed-by"
                                required={this.props.canShowFinancedByDropdown}
                                className="form-control form-control-sm"
                                value={financingDetails.lender_id || ""}
                                onChange={onChange.bind(null, "lender_id")}>
                                <option></option>
                                {this.renderLenderNames()}
                              </select> :
                              financingDetails.lender_name
                          }
                        </strong>
                      </span>
                    </td>
                  }
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Deal #</span></td>
                  <td className="col-4"><span><strong>{this.props.contractData.deal_number}</strong></span></td>
                  <td className="col-2"><span>Address</span></td>
                  <td className="col-4"><span><strong>{canShowLender && lenderAddress}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Date</span></td>
                  <td className="col-4"><span><strong>{financingDetails.contract_date ? moment.utc(financingDetails.contract_date).format(dateFormat) : ""}</strong></span></td>
                  <td className="col-2"><span>Phone/Fax</span></td>
                  <td className="col-4"><span><strong>{financingDetails.lender_phone}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Status</span></td>
                  <td className="col-4"><span><strong>{contractDetails.contract.status}</strong></span></td>
                  <td className="col-2"><span>MSRP</span></td>
                  <td className="col-4"><span><strong>{financingDetails.msrp ? accounting.formatMoney(financingDetails.msrp) : ""}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Payment Type</span></td>
                  <td className="col-4">
                    <span>
                      <strong>
                        {
                          isEditView && hstore.hasAny(this.context.user.roles, [roles.AccountRep]) ?
                            <select id="financ-payment-type"
                              required={true}
                              className="form-control form-control-sm"
                              value={financingDetails.payment_type}
                              onChange={onChange.bind(null, "payment_type")}>
                              {this.renderPaymentOptions()}
                            </select> :
                            financingDetails.payment_type
                        }
                      </strong>
                    </span>
                  </td>
                  <td className="col-2"><span>Vehicle Price</span></td>
                  <td className="col-4"><span><strong>{financingDetails.vehicle_price ? accounting.formatMoney(financingDetails.vehicle_price) : ""}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Store #</span></td>
                  <td className="col-4"><span><strong>{this.props.contractData.store_code} - {this.props.contractData.store_number}</strong></span></td>
                  <td className="col-2"><span>Financed</span></td>
                  <td className="col-4"><span><strong>{financingDetails.finance_amount ? accounting.formatMoney(financingDetails.finance_amount) : ""}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>{`F&I Manager`}</span></td>
                  <td className="col-4"><span><strong>{financingDetails.sales_man}</strong></span></td>
                  <td className="col-2"><span>First Payment</span></td>
                  <td className="col-4"><span><strong>{financingDetails.first_payment_date ? moment.utc(financingDetails.first_payment_date).format(dateFormat) : ""}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Term</span></td>
                  <td className="col-4"><span><strong>{financingDetails.term}</strong></span></td>
                  <td className="col-2"><span>Payment</span></td>
                  <td className="col-4"><span><strong>{financingDetails.finance_monthly_payment ? accounting.formatMoney(financingDetails.finance_monthly_payment) : ""}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Buy Rate</span></td>
                  <td className="col-4"><span><strong>{`${financingDetails.finance_apr ? `${financingDetails.finance_apr}%` : ""}`}</strong></span></td>
                  <td className="col-2"><span>E-Rate Financed</span></td>
                  <td className="col-4"><span><strong>{financingDetails.e_rating_finance_amount ? accounting.formatMoney(financingDetails.e_rating_finance_amount) : ""}</strong></span></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    );
  }

  renderCoOwner(customerData) {
    return (
      <div>
        <h5>Co-Buyer</h5>
        <div className="row">{this.renderCustomerTable(customerData, CO_OWNER)}</div>
      </div>
    );
  }

  renderCustomerTabContent(owner, coOwner) {
    return (
      <div className="container py-2">
        <div className="row">{this.renderCustomerTable(owner, OWNER)}</div>
        {
          this.renderCoOwner(coOwner)
        }
      </div>
    );
  }

  renderStateOptions() {
    let states = this.state.supportingData.states;
    let options = Object.keys(states).map(function(code, idx){
      return <option key={idx} value={code}>{code} - {states[code]}</option>;
    });
    options.unshift(<option key='' value=''>&mdash; Select &mdash;</option>);
    return options;
  }

  /**
   * Function to render CUDL charges with contract detail page.
   * Currently we are using it for GAP and VTA/default contract view.
   *
   * @memberof ContractDetails
   */
  renderAdditionalSurcharges = ({ surcharges }) => {
    const vtaOverAllowance = surcharges && surcharges.find(surcharge => surcharge.rate_bucket_name === "VTA Overallowance") || false;
    if (vtaOverAllowance) {
      return (
        <tr className="row">
          <td className="col-2"><span>{vtaOverAllowance.name}</span></td>
          <td className="col-4">
            <span>
              <strong>
                {accounting.formatMoney(vtaOverAllowance.cost)}
              </strong>
            </span>
          </td>
        </tr>
      );
    }

    return null;
  }
  
  onKeyPress = (event) => {
    if(event.key === "Enter"){
      event.preventDefault();
    }
  };
  
  renderCustomerTable (customer, prefix) {
    const {
      isEditView,
      onChange,
    } = this.props;
    
    const required = prefix === OWNER;
    
    if(isEditView){
      return (
        <div className="col-12">
          <table className="table table-striped text-nowrap no-gutters">
            <tbody>
              <tr className="row">
                <td className="col-2"><span>First Name</span></td>
                <td className="col-4">
                  <span>
                    <strong>
                      <input id={`${prefix}_first_name`} 
                        className="form-control form-control-sm" 
                        value={customer.first_name}
                        required={prefix === OWNER && !customer.is_business}
                        onKeyPress={this.onKeyPress}
                        onChange={onChange.bind(null, `${prefix}_first_name`)}/>
                    </strong>
                  </span>
                </td>
                <td className="col-2"><span>Phone</span></td>
                <td className="col-4">
                  <strong>
                    <input id={`${prefix}_phone`} 
                      className="form-control form-control-sm"
                      value={prefix === CO_OWNER ? customer.home_phone : customer.phone}
                      required={required}
                      onKeyPress={this.onKeyPress}
                      onChange={onChange.bind(null, `${prefix}_phone`)}/>
                  </strong>
                </td>
              </tr>
              <tr className="row">
                <td className="col-2"><span>Last Name</span></td>
                <td className="col-4">
                  <span>
                    <strong>
                      <input id={`${prefix}_last_name`}
                        className="form-control form-control-sm"
                        value={customer.last_name}
                        required={prefix === OWNER && !customer.is_business}
                        onKeyPress={this.onKeyPress}
                        onChange={onChange.bind(null, `${prefix}_last_name`)}/>
                    </strong>
                  </span>
                </td>
                <td className="col-2"><span>Alt Phone</span></td>
                <td className="col-4"><strong>
                  {
                    prefix === CO_OWNER ?
                      (<input id={`${prefix}_alt_phone`}
                        className="form-control form-control-sm"
                        value={customer.alt_phone}
                        onKeyPress={this.onKeyPress}
                        onChange={onChange.bind(null, `${prefix}_alt_phone`)}/>):
                      (customer.alt_phone)
                  }
                </strong>
                </td>
              </tr>
              { prefix == OWNER &&
              <tr className="row">
                <td className="col-2"><span>Business Name</span></td>
                <td className="col-4">
                  <span>
                    <strong>
                      <input id={`${prefix}_business_name`}
                        className="form-control form-control-sm"
                        value={customer.business_name}
                        required={customer.is_business}
                        onKeyPress={this.onKeyPress}
                        onChange={onChange.bind(null, `${prefix}_business_name`)}/>
                    </strong>
                  </span>
                </td>
              </tr>}
              <tr className="row">
                <td className="col-2"><span>Address</span></td>
                <td className="col-4">
                  <strong>
                    <input id={`${prefix}_address`}
                      className="form-control form-control-sm"
                      value={customer.address}
                      required={required}
                      onKeyPress={this.onKeyPress}
                      onChange={onChange.bind(null, `${prefix}_address`)}/>
                  </strong>
                </td>
                <td className="col-2"><span>Email</span></td>
                <td className="col-4">
                  <strong>
                    <input id={`${prefix}_email`}
                      type="email"
                      className="form-control form-control-sm"
                      value={customer.email}
                      onKeyPress={this.onKeyPress}
                      onChange={onChange.bind(null, `${prefix}_email`)}/>
                  </strong>
                </td>
              </tr>
              <tr className="row">
                <td className="col-2"><span>City</span></td>
                <td className="col-4">
                  <strong>
                    <input id={`${prefix}_city`}
                      className="form-control form-control-sm"
                      value={customer.city}
                      required={required}
                      onKeyPress={this.onKeyPress}
                      onChange={onChange.bind(null, `${prefix}_city`)}/>
                  </strong>
                </td>
                <td className="col-2"><span>Best Contact</span></td>
                <td className="col-4"><strong>{customer.best_contact_method}</strong></td>
              </tr>
              <tr className="row">
                <td className="col-2"><span>State</span></td>
                <td className="col-4">
                  <strong>
                    <select id={`${prefix}_state_code`}
                      required={required}
                      className="form-control form-control-sm"
                      value={customer.state_code}
                      onKeyPress={this.onKeyPress}
                      onChange={onChange.bind(null, `${prefix}_state_code`)}>
                      {this.renderStateOptions()}
                    </select>
                  </strong>
                </td>
              </tr>
              <tr className="row">
                <td className="col-2"><span>Zip</span></td>
                <td className="col-4">
                  <strong>
                    <input  id={`${prefix}_postal_code`}
                      className="form-control form-control-sm"
                      value={customer.postal_code}
                      required={required}
                      onKeyPress={this.onKeyPress}
                      onChange={onChange.bind(null, `${prefix}_postal_code`)}/>
                  </strong>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      );
    } else {
      return (
        <div className="col-12">
          <table className="table table-striped text-nowrap no-gutters">
            <tbody>
              <tr className="row">
                <td className="col-2"><span>First Name</span></td>
                <td className="col-4"><span><strong>{customer.first_name}</strong></span></td>
                <td className="col-2"><span>Phone</span></td>
                <td className="col-4"><strong>{prefix === CO_OWNER ? customer.home_phone : customer.phone}</strong></td>
              </tr>
              <tr className="row">
                <td className="col-2"><span>Last Name</span></td>
                <td className="col-4"><span><strong>{customer.last_name}</strong></span></td>
                <td className="col-2"><span>Alt Phone</span></td>
                <td className="col-4"><strong>{customer.alt_phone}</strong></td>
              </tr>
              { 
                prefix === OWNER ?
                  <tr className="row">
                    <td className="col-2"><span>Business Name</span></td>
                    <td className="col-4"><strong>{customer.business_name}</strong></td>
                  </tr> : ''}
              <tr className="row">
                <td className="col-2"><span>Address</span></td>
                <td className="col-4"><strong>{customer.address}</strong></td>
                <td className="col-2"><span>Email</span></td>
                <td className="col-4"><strong>{customer.email}</strong></td>
              </tr>
              <tr className="row">
                <td className="col-2"><span>City</span></td>
                <td className="col-4"><strong>{customer.city}</strong></td>
                <td className="col-2"><span>Best Contact</span></td>
                <td className="col-4"><strong>{customer.best_contact_method}</strong></td>
              </tr>
              <tr className="row">
                <td className="col-2"><span>State</span></td>
                <td className="col-4"><strong>{customer.state_code}</strong></td>
              </tr>
              <tr className="row">
                <td className="col-2"><span>Zip</span></td>
                <td className="col-4"><strong>{customer.postal_code}</strong></td>
              </tr>
            </tbody>
          </table>
        </div>
      );
    }
  }

  renderEventsTabContent(eventDetails){
    const { contractDetails: { contract } } = this.props;

    if(eventDetails) {
      return (
        <div className="container py-2">
          <div className="row">
            <div className="col-12">
              <table className="table table-striped text-nowrap no-gutters">
                <thead>
                  <tr>
                    <th>Date</th>
                    <th>User</th>
                    <th>Description</th>
                  </tr>
                </thead>
                <tbody>
                  {
                    eventDetails.map((event) => {
                      return (
                        <tr key={event.id}>
                          <td><span>{event.created_at && getTimezoneWiseDate(contract.source, event.created_at, dateFormat) || ""}</span></td>
                          <td><span>{event.created_by_name}</span></td>
                          <td><span>{event.description}</span></td>
                        </tr>
                      );
                    })
                  }
                </tbody>
              </table>
            </div>
          </div>
        </div>
      );
    } else{
      return (
        <div className="container py-2">
          No events data available
        </div>
      );
    }
  }
  
  addNote =(event) => {
    this.props.addNote(this.state.newNote, this.props.id, event, () => {
      this.setState({
        newNote: ""
      });
    });
  };
  
  renderNotesTabContent(notesDetails){
    return (
      <div className="container py-2">
        <div className="row">
          {this.renderEditNote("newNote")}
          <div className="col-12 py-2">
            <button className="btn btn-primary float-right mt-3"
              id="add-note"
              disabled={!this.state.newNote}
              onClick={this.addNote}>
            Save Note
            </button>
          </div>
          <div className="col-12">
            {
              notesDetails && notesDetails.length > 0 ?
                <table className="table table-striped no-gutters">
                  <thead>
                    <tr>
                      <th>Date</th>
                      <th>User</th>
                      <th>Note</th>
                    </tr>
                  </thead>
                  <tbody>
                    {
                      notesDetails.map((note) => {
                        return (
                          <tr key={note.id}>
                            <td><div>{note.created_at ? moment(note.created_at).format(dateFormat) : ""}</div></td>
                            <td><div>{note.user_name}</div></td>
                            <td><div className="expando-texto" style={{maxWidth: 550}}>{note.notes_text}</div></td>
                          </tr>
                        );
                      })
                    }
                  </tbody>
                </table> :
                <span>No notes data available</span>
            }
          </div>
        </div>
      </div>
    );
  }
  
  renderContractsTab(contractDetails) {
    if (this.isContractsTabActive()) {
      return (<div role="tabpanel" className="tab-pane in active" id="contracts">
        {this.renderContractsTabContent(contractDetails)}
      </div>);
    }
  }

  renderVehicleTab(vehicleDetails) {
    if (this.isVehicleTabActive()) {
      return (<div role="tabpanel" className="tab-pane in active" id="vehicle">
        {this.renderVehicleTabContent(vehicleDetails)}
      </div>);
    }
  }

  renderCustomerTab(owner, coOwner) {
    if (this.isCustomerTabActive()) {
      return (<div role="tabpanel" className="tab-pane in active" id="customer">
        {this.renderCustomerTabContent(owner, coOwner)}
      </div>);
    }
  }

  renderFinancingTab(contractDetails) {
    if (this.isFinancingTabActive()) {
      return (<div role="tabpanel" className="tab-pane in active" id="financing">
        {this.renderFinancingTabContent(contractDetails)}
      </div>);
    }
  }

  renderEventsTab(eventDetails) {
    if (this.isEventsTabActive()) {
      return (<div role="tabpanel" className="tab-pane in active" id="events">
        {this.renderEventsTabContent(eventDetails)}
      </div>);
    }
  }

  renderNotesTab(notesDetails) {
    if (this.isNotesTabActive()) {
      return (<div role="tabpanel" className="tab-pane in active" id="notes">
        {this.renderNotesTabContent(notesDetails)}
      </div>);
    }
  }
  
  renderNoDataMessage(showNoData) {
    if (showNoData && !this.props.showContractLoader) {
      return (
        <div className="text-center">
          <span>No results available for the search criteria</span>
        </div>
      );
    }
  }

  handleUploadSuccess = (isUploadError, note, closeButtonCall) => {
    if (closeButtonCall) {
      this.setState({ showModal: !closeButtonCall });
      return;
    }
    this.setState({ attachmentUploaded: !isUploadError, showModal: false, newModalNote: note }, () => this.props.expireContract(this.state.newModalNote, this.props.id, this.props.handleAddAttachment));
  }

  handleUpdateContract = () => {
    Alert.success('Attachment uploaded successfully');
  }

  /**
   * This renders the upload document modal with respective parameters and props
   */
  renderExpireModal = (show) => {
    return (
      <ContractAttachmentModal
        visible={show}
        contractId={this.props.id}
        onDone={this.handleUploadSuccess}
        title={`Expire Contract`}
        buttonText={`Expire`}
        selectedAttachmentType={null}
        showOverWriteOption={false}
        isAdminView={true}
        updateContract={this.handleUpdateContract}
        defaultDescription={'Expire Contract'}
        descriptionRequired={true}
      />
    );
  }

  handleUploadUnExpireSuccess = (isUploadError, note, closeButtonCall) => {
    if (closeButtonCall) {
      this.setState({ showUnexpireModal: !closeButtonCall });
      return;
    }
    this.setState({ 
      attachmentUploaded: !isUploadError,
      showUnexpireModal: false,
      newModalNote: note 
    }, () => this.props.unExpireContract(this.state.newModalNote, this.props.id, this.props.handleAddAttachment));
  }
  /**
   * This renders the upload document modal with respective parameters and props
   */
    renderUnexpireModal = (show) => {
      return (
        <ContractAttachmentModal
          visible={show}
          contractId={this.props.id}
          onDone={this.handleUploadUnExpireSuccess}
          title={`Unexpire Contract`}
          buttonText={`Unexpire`}
          selectedAttachmentType={null}
          showOverWriteOption={false}
          isAdminView={true}
          updateContract={this.handleUpdateContract}
          defaultDescription={'Unexpire Contract'}
          descriptionRequired={true}
        />
      );
    }
  
  onSubmit = (e) => {
    e.preventDefault();
  };

 closeCancelUpdatePaymentModal = (e) => {
   e && e.preventDefault();
   this.setState({ showCancelUpdatePaymentModal: false });
   this.reloadWindowAndKeepFocus();
 };

 render() {
   const {
     contractDetails,
     noteDetails,
     isAdminView,
     showContractLoader,
     setActive,
   } = this.props;

   const {
     showFlagsModal,
     showAdjustmentModal,
     showEditVINModal,
     showEditMileageModal,
     loader,
   } = this.state;

   const {
     user,
   } = this.context;

   if (showContractLoader || loader) {
     return <h4><i className="fa fa-refresh fa-spin" /> Loading ...</h4>;
   } else if(isEmpty(contractDetails)){
     return this.renderNoDataMessage(isEmpty(contractDetails));
   } else {
     let userHasCompanyGroup = false;
     // if issuing dealer store does not have a company group, don't show customer tab in LCA view
     if (!contractDetails.contract.issuing_dealer_company_group) {
       userHasCompanyGroup = false;
     } else {
       userHasCompanyGroup = user.stores.some(store =>{
         if(store.company_group && store.company_group === contractDetails.contract.issuing_dealer_company_group) {
           return true;
         }});
     }
     
     const getCancellationEmail = () => {
       if (contractDetails.cancellation.is_electronic_check) {
         return contractDetails.cancellation.email;
       }
       return contractDetails.cancellation?.payee_type === 'Customer' ? contractDetails.customer_details.email : '';
     };

     return <section className="row px-2">
       <section className="col-12 justify-content-start">
         <div className="clearfix"></div>
         <div>
           <ul className="nav nav-tabs pt-3" role="tablist" id="contractsTabPanel">
             <li className="nav-item">
               <a className={`nav-link ${this.isContractsTabActive() ? "active" : "text-primary"}`}
                 data-toggle="tab"
                 id="contracts-tab"
                 role="tab" aria-controls="contracts" onClick={setActive}>
                  Contracts
               </a>
             </li>
             <li className="nav-item">
               <a className={`nav-link ${this.isVehicleTabActive() ? "active" : "text-primary"}`}
                 data-toggle="tab"
                 id="vehicle-tab"
                 role="tab" aria-controls="vehicle" onClick={setActive}>
                  Vehicle
               </a>
             </li>
             {
               (hstore.has(this.context.user.roles, Roles.AdminView) || userHasCompanyGroup) &&
              <li className="nav-item">
                <a className={`nav-link ${this.isCustomerTabActive() ? "active" : "text-primary"}`}
                  data-toggle="tab"
                  id="customer-tab"
                  role="tab" aria-controls="customer" onClick={setActive}>
                  Customer
                </a>
              </li>
             }
             <li className="nav-item">
               <a className={`nav-link ${this.isFinancingTabActive() ? "active" : "text-primary"}`}
                 data-toggle="tab"
                 id="financing-tab"
                 role="tab" aria-controls="financing" onClick={setActive}>
                  Financing
               </a>
             </li>
             {
               isAdminView &&
                <li className="nav-item">
                  <a className={`nav-link ${this.isEventsTabActive() ? "active" : "text-primary"}`}
                    data-toggle="tab"
                    id="events-tab"
                    role="tab" aria-controls="events" onClick={setActive}>
                    Events
                  </a>
                </li>
             }
             {
               isAdminView &&
                <li className="nav-item">
                  <a className={`nav-link ${this.isNotesTabActive() ? "active" : "text-primary"}`}
                    data-toggle="tab"
                    id="notes-tab"
                    role="tab" aria-controls="notes" onClick={setActive}>
                    Notes
                  </a>
                </li>
             }
             {(this.props.isAdminView || this.isIssuingDealerSame()) &&
                (<li className="nav-item">
                  <a className={`nav-link text-primary"}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    href={`/api/contracts/${contractDetails.contract.id}/download`}
                    aria-controls="download">
                  Download Contract
                  </a>
                </li>)}
           </ul>
           <div className="tab-content">
             {this.renderContractsTab(contractDetails.contract)}
             {this.renderVehicleTab(contractDetails.vehicle_details)}
             {this.renderCustomerTab(contractDetails.customer_details, contractDetails.cobuyer)}
             {this.renderFinancingTab(contractDetails)}
             {isAdminView && this.renderEventsTab(contractDetails.contract_events)}
             {isAdminView && this.renderNotesTab(noteDetails)}
           </div>
           {this.renderExpireModal(this.state.showModal)}
           {this.renderUnexpireModal(this.state.showUnexpireModal)}
           {showFlagsModal && this.renderFlagsModal()}
           {showAdjustmentModal && this.renderAdjustmentModal()}
           <ContractVinEditModal 
             currentVIN={this.props.contractData.vin}
             contractVINInfo={this.state.contractVINs}
             onDone={this.closeEditVINModal}
             visible={showEditVINModal}
           />
           <ContractMileageEditModal 
             contractId={this.props.contractData.id}
             contractCode={this.props.contractData.code}
             contractStatus={this.props.contractData.status}
             productTypeName={contractDetails.contract.product_type_name}
             effectiveMileage={contractDetails.contract.effective_mileage}
             expirationMileage={contractDetails.contract.expiration_mileage}
             additiveMileage={contractDetails.contract.additive_mileage}
             vehicleDetails={contractDetails.vehicle_details}
             onDone={this.closeEditMileageModal}
             onCancel={this.cancelEditMileageModal}
             visible={showEditMileageModal}
           />
           <CancelPaymentUpdate
             contractCancellationID = {this.props.cancelContract.id}
             cancelStatus = {this.props.cancelContract.cancel_status}
             billNumber = {this.props.cancelContract.bill_number}
             show = {this.state.showCancelUpdatePaymentModal}
             closeFunc = {this.closeCancelUpdatePaymentModal}
             email = {getCancellationEmail()}
             isElectronicCheck={contractDetails.cancellation.is_electronic_check}
           />
         </div>
       </section>
     </section>;
   }
 }
}
