import moment from 'moment';

// Backdating warning constants
export const BACKDATING_LIMITS = {
  FLAT_CANCEL: 30,    // 30 days for flat cancels
  GENERAL: 90,        // 90 days for general cancellations
  REPO: 180          // 180 days for repo cancellations
};

/**
 * Checks if a cancellation date exceeds backdating limits and shows a warning popup
 * @param {moment|Date|string} cancelDate - The cancellation date to check
 * @param {number} cancelReasonId - The ID of the cancel reason
 * @param {Array} cancelReasons - Array of cancel reason objects with id and name
 * @param {Function} onReset - Callback function to reset the date (optional)
 * @returns {boolean} - True if user wants to continue, false if they cancelled
 */
export const checkBackdatingWarning = (cancelDate, cancelReasonId, cancelReasons, onReset) => {
  if (!cancelDate || !cancelReasonId || !cancelReasons) return true;

  const selectedReason = cancelReasons.find(reason => reason.id == cancelReasonId);
  if (!selectedReason) return true;

  const daysDifference = moment().diff(moment(cancelDate), 'days');
  if (daysDifference <= 0) return true; // Future date or today, no warning needed

  let warningLimit = BACKDATING_LIMITS.GENERAL; // Default 90 days
  let reasonType = 'general';

  // Determine the appropriate limit based on cancel reason
  const reasonName = selectedReason.name.toLowerCase();
  if (reasonName.includes('flat cancel')) {
    warningLimit = BACKDATING_LIMITS.FLAT_CANCEL; // 30 days
    reasonType = 'flat cancel';
  } else if (reasonName.includes('repo')) {
    warningLimit = BACKDATING_LIMITS.REPO; // 180 days
    reasonType = 'repo';
  }

  // Show warning if exceeding the limit
  if (daysDifference > warningLimit) {
    const message = `Warning: The selected cancellation date is ${daysDifference} days in the past, which exceeds the ${warningLimit}-day backdating limit for ${reasonType} cancellations. Please review this date before proceeding.`;
    
    // Show a soft warning popup
    if (window.confirm(message + '\n\nDo you want to continue with this date?')) {
      // User confirmed, continue with the selected date
      return true;
    } else {
      // User cancelled, reset to current date if callback provided
      if (onReset && typeof onReset === 'function') {
        onReset();
      }
      return false;
    }
  }
  return true;
};

/**
 * Gets the appropriate backdating limit for a given cancel reason
 * @param {Object} cancelReason - Cancel reason object with name property
 * @returns {Object} - Object with limit (number) and type (string)
 */
export const getBackdatingLimit = (cancelReason) => {
  if (!cancelReason || !cancelReason.name) {
    return { limit: BACKDATING_LIMITS.GENERAL, type: 'general' };
  }

  const reasonName = cancelReason.name.toLowerCase();
  if (reasonName.includes('flat cancel')) {
    return { limit: BACKDATING_LIMITS.FLAT_CANCEL, type: 'flat cancel' };
  } else if (reasonName.includes('repo')) {
    return { limit: BACKDATING_LIMITS.REPO, type: 'repo' };
  }
  
  return { limit: BACKDATING_LIMITS.GENERAL, type: 'general' };
};

/**
 * Calculates the number of days between current date and a given date
 * @param {moment|Date|string} date - The date to compare with current date
 * @returns {number} - Number of days (positive if date is in the past)
 */
export const getDaysFromToday = (date) => {
  return moment().diff(moment(date), 'days');
};
