import hstore from "./hstore";

export const DealerViewOnly = 'dealer_view_only';
export const FinanceDeal = 'finance_deal';
export const Inspections = 'inspections';
export const Remit = 'remit';
export const ServiceRO = 'service_ro';
export const BDCRO = 'bdc_ro';
export const LDCSAccounting = 'ldcs_accounting';
export const LDCSAllCancels = "ldcs_all_cancels";
export const LDCSFlatCancels = "ldcs_flat_cancels";
export const LDCSService = 'ldcs_service';
export const LDCSFinance = 'ldcs_finance';
export const AccountRep = 'account_rep';
export const AccountRepII = 'account_rep_ii';
export const AccountRepManager = 'account_rep_manager';
export const AdminView = 'admin_view';
export const Coupon = 'coupon';
export const UserProvisioning = 'user_provisioning';
export const DealershipManagement = 'dealership_management';
export const GAPLicenseManager = 'gap_license_manager';
export const ProductManager = 'product_manager';
export const StoreUploads = 'store_uploads';
export const VehicleComponentManager = 'vehicle_component_manager';
export const AutoClaims = 'auto_claims';
export const AutoClaimsManager = 'auto_claims_manager';
export const GAPClaims = 'gap_claims';
export const GAPClaimsManager = 'gap_claims_manager';
export const RecoveryTeam = 'recovery_team';
export const Accounting = 'accounting';
export const Controller = 'controller'; // accounting controller
export const ControllerAdmin = 'controller_admin';
export const TestAutomation = "test_automation";
export const ServiceClosedStore = 'service_closed_store';
export const ViewOnlyClaims = "view_only_claims";
export const NewsUpdatesManager = "news_updates_manager";
export const NewsUpdatesView = "news_and_updates_view";
export const CorporateAccounting = "corporate_accounting";
export const JobTitleManagementRole = "job_title_management";
export const LenderManagement = "lender_management";
export const NewsAndUpdatesView = 'news_and_updates_view';
export const AccountingCancellationHandler = 'accounting_cancellation_handler';
export const CancellationPaymentHandler = 'cancellation_payment_handler';
export const CancelDashboardManager = 'cancel_dashboard_manager';
export const DigitalReservesManagement = 'digital_reserves_management';

const storeRoles = [
  FinanceDeal,
  Inspections,
  Remit,
  ServiceRO,
  BDCRO,
  DealerViewOnly,
  ServiceClosedStore,
  CorporateAccounting,
  NewsAndUpdatesView,
];

const ldcsRoles = [
  LDCSAccounting,
  LDCSService,
  LDCSFinance,
];

export const IsDealerViewOnlyRoleUser = (roles, hstore) => {

  // Get all assigned roles and filter through store and ldcs roles to get only store roles for the user
  const assignedStoreRoles = hstore.toArray(roles).filter(obj => {
    if ([...storeRoles, ...ldcsRoles].includes(obj)) {
      return true;
    }
    return false;
  });

  // If more than one role it means user has other roles assinged to them so old code will take care of it 
  // We check if its the only role assinged to user if thats the case we return true otherwise false
  return hstore.has(roles, DealerViewOnly) && assignedStoreRoles.length <= 1;
};

export const IsBDCROViewOnlyRoleUser = (roles, hstore) => {

  // Get all assigned roles and filter through store and ldcs roles to get only store roles for the user
  const assignedStoreRoles = hstore.toArray(roles).filter(obj => {
    if ([...storeRoles, ...ldcsRoles].includes(obj)) {
      return true;
    }
    return false;
  });

  // If more than one role it means user has other roles assinged to them so old code will take care of it 
  // We check if its the only role assinged to user if thats the case we return true otherwise false
  return hstore.has(roles, BDCRO) && assignedStoreRoles.length <= 1;
};

export const UserHasAnyRole = (context, roles) => {
  return hstore.hasAny(context?.user?.roles || {}, roles);
};