import React from 'react';
import PropTypes from 'prop-types';
import <PERSON><PERSON> from "react-s-alert";
import moment from "moment/moment";

import {jsonPromise as ajax} from 'shared/ajax';
import hstore from 'shared/hstore';
import CancellationView from './CancellationView.jsx';
import { Status } from 'shared/components/Attachments';
import * as Roles from 'shared/roles';

const QUOTES_TAB = "quotes";
const CANCELLATION_TAB = "cancellation";
const API_DATE_FORMAT = "YYYY-MM-DD";
import {CONSTANTS} from './constants';

// Backdating warning constants
const BACKDATING_LIMITS = {
  FLAT_CANCEL: 30,    // 30 days for flat cancels
  GENERAL: 90,        // 90 days for general cancellations
  REPO: 180          // 180 days for repo cancellations
};

export default class CancellationPage extends React.Component {

  static contextTypes = {
    router: PropTypes.shape({
      push: PropTypes.func.isRequired,
    }).isRequired,
    user: PropTypes.shape({
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }),
    }),
  };
  
  static propTypes = {
    params: PropTypes.shape({
      contract_code: PropTypes.string.isRequired
    }).isRequired,
    location: PropTypes.shape({
      query: PropTypes.shape({
        customer_name: PropTypes.string,
        vin: PropTypes.string,
        contract_id: PropTypes.string,
        contract_product_code: PropTypes.string,
        payment_type: PropTypes.string,
      })
    }),
  };

  constructor(props) {
    super(props);

    this.state = {
      cancelDate: null,
      mileage: '',
      reason: '',
      sppAmountPaid: '',
      sppBalance: '',
      nsdKeyClaims: '',
      nsdTireAndWheelClaims: '',
      nsdVTAClaims: '',
      cancellationReasons: [],
      attachmentTypes: [],
      cancellationEstimate: {
        estimates: []
      },
      cancellationQuoteEstimate: {
        estimates: []
      },
      productOrder: void 0,
      activeTab: CANCELLATION_TAB,
      quoteRequested: false,
      loading: false,
      fileName: "",
      attachments: [],
      attachmentsStatus: Status.READY,
      payee: '',
      cancellationPayees: [],
      cancelStores: [],
      cancelStoreID: 0,
      payeeName: '',
      payeeAttentionTo: '',
      payeeAddress: '',
      payeeCity: '',
      payeeState: '',
      payeePostalCode: '',
      payeeCustomer: null,
      payeeLender: null,
      states: [],
      manualTax: '',
      skipBill: false,
      applyFee: true,
      isElectronicCheck: false,
      email: ''
    };
  }

  componentDidMount(){
    var cancelDate = this.state.cancelDate;
    if (!hstore.has(this.context.user.roles, Roles.TestAutomation)) {
      cancelDate = moment();
    }
    this.setState({
      activeTab: CANCELLATION_TAB,
      cancelDate: cancelDate
    });

    this.loadSupportingData();
  }

  loadSupportingData = () => {
    const url = `/api/contracts/cancellation/quote-supporting-data?contract_id=${this.props.location.query.contract_id}`;

    this.setState({
      loading: true
    }, () => {
      ajax(url, {}, {}).then((results) => {
        if(results.status === 200){
          let productOrder = {};
          results.data.product_types.forEach((productType) => {
            productOrder[productType.code] = productType.position;
          });
          this.setState({
            attachmentTypes: results.data.attachment_types,
            cancellationReasons: results.data.cancel_reasons,
            cancellationPayees: results.data.cancel_payees,
            productOrder: productOrder,
            cancelStores: results.data.cancel_stores,
            payeePostalCode: results.data.cancel_payee_postal_code,
            payeeCustomer: results.data.payee_customer,
            payeeLender: results.data.payee_lender,
            cancelStoreID: results.data.payee_store_id,
            contractStoreState: results.data.contract_store_state,
            states: results.data.states,
            manualTaxStates: results.data.manual_tax_states,
          });
        } else{
          Alert.error("Error loading supporting data");
        }
        this.setState({
          loading: false
        });
      }, () => {
        Alert.error("Error loading supporting data");
        this.setState({
          loading: false
        });
      });
    });
  }

  canSelectFutureDate = () => {
    return this.isFlatCancel();
  };

  isFlatCancel = () => {
    const { cancellationReasons, reason } = this.state;
    const cancellationReason = cancellationReasons.find(cancelReason => cancelReason.id == reason);
    return Boolean(cancellationReason && cancellationReason.name.toLowerCase().includes('flat cancel') || false);
  };
  
  handleChange = (key, event) => {
    const state = this.state;
    state[key] = event.target.value;

    if (key === 'reason') {
      // Check backdating warning when reason changes (if date is already set)
      if (this.state.cancelDate) {
        const shouldContinue = this.checkBackdatingWarning(this.state.cancelDate, event.target.value);
        if (!shouldContinue) {
          return; // User cancelled, don't update the reason
        }
      }

      if (!this.canSelectFutureDate() && moment(state.cancelDate).isAfter(moment())) {
        state.cancelDate = moment();
      }

      // Clear the manual tax if the reason changes.
      state.manualTax = '';
    }

    let setStoreInfo = false;
    if (key === 'payee') {
      if (state[key] === "Customer") {
        state.payeeName = state.payeeCustomer.name;
        state.payeeAddress = state.payeeCustomer.address;
        state.payeeCity = state.payeeCustomer.city;
        state.payeeState = state.payeeCustomer.state_code;
        state.payeePostalCode = state.payeeCustomer.postal_code;
        state.email = state.payeeCustomer.email;
      } else if (state[key] === "Lender" || state[key] === "Reserves") {
        state.payeeName = state.payeeLender.name;
        state.payeeAttentionTo = state.payeeLender.attention;
        state.payeeAddress = state.payeeLender.address;
        state.payeeCity = state.payeeLender.city;
        state.payeeState = state.payeeLender.state_code;
        state.payeePostalCode = state.payeeLender.postal_code;
      } else if (state[key] === "DownPayment" || state[key] ==="Store Issued Refund") {
        setStoreInfo = true;
      } else {
        state.payeeName = '';
        state.payeeAddress = '';
        state.payeeCity = '';
        state.payeeState = '';
        state.payeePostalCode = '';
      }
      state.isElectronicCheck = false;
      state.email = '';
    }
    
    if (key === 'store') {
      state.cancelStoreID = state[key];
      setStoreInfo = true;
    }

    if (setStoreInfo) {
      const store = state.cancelStores.find(s => s.id == state.cancelStoreID);
      if (store) {
        state.payeeName = store.name;
        state.payeeAddress = store.address;
        state.payeeCity = store.city;
        state.payeeState = store.state_code;
        state.payeePostalCode = store.postal_code;
      } else {
        state.payeeName = '';
        state.payeeAddress = '';
        state.payeeCity = '';
        state.payeeState = '';
        state.payeePostalCode = '';
      }
    }

    this.setState(state);

    // Clear the cancel estimates if the data changing isn't the payee or store changing
    if (this.isInputChanging(key)) {
      this.resetCancellationQuotes();
    }
  };

  isInputChanging(key) {
    let changing = false;

    switch(key) {
    case 'mileage':
    case 'reason':
    case 'nsdKeyClaims':
    case 'nsdTireAndWheelClaims':
    case 'nsdVTAClaims':
    case 'sppAmountPaid':
    case 'sppBalance':
      changing = true;
    }

    return changing;
  }

  handleTypeChange = (key, value, event) => {
    const state = this.state;
    state[key] = value;
    this.setState(state);
    this.resetCancellationQuotes();
  }

  checkBackdatingWarning = (cancelDate, cancelReasonId) => {
    if (!cancelDate || !cancelReasonId) return true;

    const selectedReason = this.state.cancellationReasons.find(reason => reason.id == cancelReasonId);
    if (!selectedReason) return true;

    const daysDifference = moment().diff(moment(cancelDate), 'days');
    if (daysDifference <= 0) return true; // Future date or today, no warning needed

    let warningLimit = BACKDATING_LIMITS.GENERAL; // Default 90 days
    let reasonType = 'general';

    // Determine the appropriate limit based on cancel reason
    const reasonName = selectedReason.name.toLowerCase();
    if (reasonName.includes('flat cancel')) {
      warningLimit = BACKDATING_LIMITS.FLAT_CANCEL; // 30 days
      reasonType = 'flat cancel';
    } else if (reasonName.includes('repo')) {
      warningLimit = BACKDATING_LIMITS.REPO; // 180 days
      reasonType = 'repo';
    }

    // Show warning if exceeding the limit
    if (daysDifference > warningLimit) {
      const message = `Warning: The selected cancellation date is ${daysDifference} days in the past, which exceeds the ${warningLimit}-day backdating limit for ${reasonType} cancellations. Please review this date before proceeding.`;

      // Show a soft warning popup
      if (window.confirm(message + '\n\nDo you want to continue with this date?')) {
        // User confirmed, continue with the selected date
        return true;
      } else {
        // User cancelled, reset to current date
        this.setState({
          cancelDate: moment()
        });
        return false;
      }
    }
    return true;
  };

  handleDateChange = (date) => {
    this.resetCancellationQuotes();
    if(date){
      if(!date.isValid()){
        Alert.error("Enter correct cancel date");
        return;
      }
    } else{
      Alert.error("Enter correct cancel date");
      return;
    }

    // Check for backdating warning
    const shouldContinue = this.checkBackdatingWarning(date, this.state.reason);
    if (!shouldContinue) {
      return; // User cancelled, don't update the date
    }

    this.setState({
      cancelDate: date
    });
  };

  handleTaxChange = (e) => {
    let tax = e.target.value ? parseFloat(e.target.value) : '';
    this.resetCancellationQuotes();
    if ( tax > CONSTANTS.MAX_SALES_TAX) {
      Alert.error(`Tax cannot be greater than ${CONSTANTS.MAX_SALES_TAX}%`);
      return;
    }
    this.setState({
      manualTax: tax
    });
  }

  resetCancellationQuotes = () => {
    this.setState({
      cancellationEstimate: [],
      cancellationQuoteEstimate: [],
      quoteRequested: false,
      payee: '',
      payeeName: '',
      payeeAttentionTo: '',
      payeeAddress: '',
      payeeCity: '',
      payeeState: '',
      payeePostalCode: '',
    });
  }

  handleGetQuote = () => {
    const urlQuotes = `/api/admin/contracts/${this.props.location.query.contract_id}/cancellation/estimate/quote?cancel_date=${moment(this.state.cancelDate).format(API_DATE_FORMAT)}&mileage=${this.state.mileage}&cancel_reason_id=${this.state.reason}&spp_customer_paid=${this.state.sppAmountPaid || 0}&spp_balance=${this.state.sppBalance || 0}&nsd_key_claims=${this.state.nsdKeyClaims || 0}&nsd_tire_and_wheel_claims=${this.state.nsdTireAndWheelClaims || 0}&nsd_vta_claims=${this.state.nsdVTAClaims || 0}&manual_tax=${this.state.manualTax}`;
    
    this.setState({
      quoteRequested: true,
      loading: true
    }, () => {
      ajax(urlQuotes, {}, {}).then((response) => {
        if (response.status === 200) {
          response.data.estimates.sort(this.sortByProductOrder);
          this.setState({
            cancellationQuoteEstimate: JSON.parse(JSON.stringify(response.data)),
            cancellationEstimate: JSON.parse(JSON.stringify(response.data))
          });
        } else {
          if(response.data.message){
            Alert.error(response.data.message);
          } else {
            Alert.error("Error loading cancellation estimate"); 
          }
        }
        this.setState({
          loading: false
        });
      },
      (reason) => {
        Alert.error("Error loading cancellation estimate");
        this.setState({
          loading: false
        });
      });
    });
  };

  handleCancelContract = (cancelRequest) => {
    const cancelContractUrl = `/api/admin/contracts/cancel`;
    const { attachments, attachmentTypes } = this.state;
    const attachmentType = attachmentTypes && attachmentTypes.find(at => at.name === 'Cancel Quote');

    const submit = () => {
      this.setState({
        loading: true
      }, () => {
        ajax(cancelContractUrl, cancelRequest, {method: "PUT"}).then(
          (results) => {
            this.setState({
              loading: false
            });
            if (results.status === 200) {
              if (results.data.warnings && results.data.warnings.length > 0) {
                Alert.warning(results.data.warnings.join(", "));
              }
              if (results.data.count === 0) {
                Alert.warning("Something went wrong, No contract was canceled");
              } else {
                Alert.success(results.data.count + " Contracts " + results.data.message);
                this.context.router.push(`/contracts-search/${this.props.params.contract_code}?product_code=${this.props.location.query.contract_product_code}&id=${this.props.location.query.contract_id}`);
              }
            } else {
              // If there was a message in the response, then show that and if not, then show the generic error message.
              if (results.data.message) {
                Alert.error("Error: " + results.data.message);
              } else {
                Alert.error("Error updating contract cancellations");
              }
            }
          },
          (reason) => {
            // If there was a message in the response, then show that and if not, then show the generic error message.
            if (reason.data && reason.data.message) {
              Alert.error("Error: " + reason.data.message);
            } else {
              Alert.error("Error updating contract cancellations");
            }
            this.setState({
              loading: false
            });
          }
        );
      });
    };

    if (attachments && attachments.length > 0 && attachmentType) {
      cancelRequest.attachments = [];
      let promises = [];
      // read all attachments then submit the request.
      for (let i = 0; i < attachments.length; i++) {
        const file = attachments[i];
        let att = {
          content_type: file.type,
          name: file.name,
          description: 'Cancel Contract',
          attachment_type_id: attachmentType.id,
        };
        cancelRequest.attachments.push(att);
        
        // Read each file content asynchronously
        let readerPromise = new Promise(resolve => {
          const reader = new FileReader();
          reader.onload = (e) => {
            att.file_content = btoa(e.target.result);
            resolve();
          };
          reader.readAsBinaryString(file);
        });
        promises.push(readerPromise);
      }

      // Wait for all files to be read, then submit
      Promise.all(promises).then(() => {
        submit();
      });
    } else if (this.isFlatCancel() || hstore.has(this.context.user.roles, Roles.TestAutomation)) {
      submit();
    }
  };
  

  handleUpdateFiles = (files) => {
    this.setState({
      attachments: files
    });
  }
  
  sortByProductOrder = (contractA, contractB) => {
    const { productOrder } = this.state;
    if(productOrder) {
      return productOrder[contractA.product_type_code] - productOrder[contractB.product_type_code];
    }
    return 0;
  };
  
  setActive = (activeTab) => {
    this.setState({
      activeTab
    });
  };

  getAllSelectedForCancellation = (estimates) => {
    let allSelected = true;
    let selectableCount = 0;

    for(const estimate of estimates){
      if(estimate.cancellable && estimate.message_type !== "error") {
        selectableCount++;
        allSelected = allSelected && !!estimate.selected;
        if (!allSelected) {
          return false;
        }
      }
    }

    if(selectableCount === 0){
      return false;
    }

    return allSelected;
  };

  getIfOneSelectedCancellation = (estimates) => {
    let oneSelected = false;

    for(const estimate of estimates){
      oneSelected = oneSelected || !!estimate.selected;
      if(oneSelected){
        return true;
      }
    }

    return oneSelected;
  };

  selectAllCancellation = (checked) => {
    const {activeTab, cancellationEstimate, cancellationQuoteEstimate} = this.state;

    let estimates = [];

    if(activeTab === CANCELLATION_TAB){
      estimates = cancellationEstimate.estimates ? cancellationEstimate.estimates : [];
    } else if(activeTab === QUOTES_TAB){
      estimates = cancellationQuoteEstimate.estimates ? cancellationQuoteEstimate.estimates : [];
    }

    for(const estimate of estimates) {
      if(estimate.cancellable && estimate.message_type !== "error") {
        estimate.selected = checked;
      }
    }

    if(activeTab === CANCELLATION_TAB) {
      this.setState({
        cancellationEstimate
      });
    } else if(activeTab === QUOTES_TAB){
      this.setState({
        cancellationQuoteEstimate
      });
    }
  };

  selectCancellationContract = (index) => {
    const { cancellationEstimate, cancellationQuoteEstimate, activeTab } = this.state;

    let estimates = [];

    if(activeTab === CANCELLATION_TAB) {
      estimates = cancellationEstimate.estimates ? cancellationEstimate.estimates : [];
    } else if(activeTab === QUOTES_TAB) {
      estimates = cancellationQuoteEstimate.estimates ? cancellationQuoteEstimate.estimates : [];
    }

    estimates[index].selected = !estimates[index].selected;

    if(activeTab === CANCELLATION_TAB) {
      this.setState({
        cancellationEstimate
      });
    } else if(activeTab === QUOTES_TAB){
      this.setState({
        cancellationQuoteEstimate
      });
    }
  };

  handleSkipBill = () => {
    this.setState({skipBill: !this.state.skipBill});
  }

  handleApplyFee = () => {
    this.setState({applyFee: !this.state.applyFee});
  }
  
  handleCheckPreference = (isElectronicCheck) => {
    const payee = this.state.payee;
    this.setState({
      isElectronicCheck: isElectronicCheck,
      email: payee === 'Customer' ? this.state.payeeCustomer.email : ''
    });
  }

  handleEmailChange = (value) => {
    this.setState({email: value.trim()});
  }

  render(){
    const {
      activeTab,
      loading,
      cancelDate,
      mileage,
      reason,
      sppAmountPaid,
      sppBalance,
      nsdKeyClaims,
      nsdTireAndWheelClaims,
      nsdVTAClaims,
      cancellationReasons,
      cancellationEstimate,
      cancellationQuoteEstimate,
      quoteRequested,
      payee,
      cancellationPayees,
      cancelStores,
      cancelStoreID,
      contractStoreState,
      manualTaxStates,
      payeeName,
      payeeAttentionTo,
      payeeAddress,
      payeeCity,
      payeeState,
      payeePostalCode,
      states
    } = this.state;
    let estimates = [];
    if(activeTab === CANCELLATION_TAB) {
      estimates = cancellationEstimate.estimates ? cancellationEstimate.estimates : [];
    } else if(activeTab === QUOTES_TAB) {
      estimates = cancellationQuoteEstimate.estimates ? cancellationQuoteEstimate.estimates : [];
    }

    const sppContract = (estimate) => estimate.payment_type === "SPP";
    const isSPPContractInEstimate = estimates.some(sppContract);
    
    return (
      <CancellationView contractCode={this.props.params.contract_code}
        customerName={`${this.props.location.query.customer_name}`}
        paymentType={this.props.location.query.payment_type}
        vin={this.props.location.query.vin}
        contractId={Number(this.props.location.query.contract_id)}
        contractProductCode={this.props.location.query.contract_product_code}
        activeTab={activeTab}
        estimates={estimates}
        loading={loading}
        cancelDate={cancelDate}
        mileage={mileage}
        reason={reason}
        sppAmountPaid={sppAmountPaid}
        sppBalance={sppBalance}
        nsdKeyClaims={nsdKeyClaims}
        nsdTireAndWheelClaims={nsdTireAndWheelClaims}
        nsdVTAClaims={nsdVTAClaims}
        cancellationReasons={cancellationReasons}
        quoteRequested={quoteRequested}
        getAllSelectedForCancellation={this.getAllSelectedForCancellation}
        selectAllCancellation={this.selectAllCancellation}
        selectCancellationContract={this.selectCancellationContract}
        getIfOneSelectedCancellation={this.getIfOneSelectedCancellation}
        handleDateChange={this.handleDateChange}
        handleChange={this.handleChange}
        handleTaxChange={this.handleTaxChange}
        handleTypeChange={this.handleTypeChange}
        handleGetQuote={this.handleGetQuote}
        setActive={this.setActive}
        attachmentTypes={this.state.attachmentTypes}
        handleCancelContract={this.handleCancelContract}
        onUpdateAttachments={this.handleUpdateFiles}
        canSelectFutureDate={this.canSelectFutureDate()}
        isSPPContractInEstimate={isSPPContractInEstimate}
        cancellationPayees={cancellationPayees}
        payee={payee}
        payeeName={payeeName}
        payeeAttentionTo={payeeAttentionTo}
        payeeAddress={payeeAddress}
        payeeCity={payeeCity}
        payeeState={payeeState}
        payeePostalCode={payeePostalCode}
        cancelStores={cancelStores}
        cancelStoreID={cancelStoreID}
        contractStoreState={contractStoreState}
        manualTaxStates={manualTaxStates}
        states={states}
        manualTax={this.state.manualTax}
        skipBill={this.state.skipBill}
        handleSkipBill={this.handleSkipBill}
        applyFee={this.state.applyFee}
        handleApplyFee={this.handleApplyFee}
        isElectronicCheck={this.state.isElectronicCheck}
        email={this.state.email}
        handleCheckPreference={this.handleCheckPreference}
        handleEmailChange={this.handleEmailChange}
      />
    );
  }
}
