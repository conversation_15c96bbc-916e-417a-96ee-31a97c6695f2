import React from 'react';
import PropTypes from 'prop-types';
import $ from 'jquery';
import moment from 'moment/moment';
import accounting from 'accounting';

import ModalDatePicker from 'shared/components/ModalDatePicker';
import Attachments, { Status } from 'shared/components/Attachments';
import dateFormat from 'shared/date-format';
import hstore from 'shared/hstore';
import * as Roles from 'shared/roles';
import Modal from 'shared/components/Modal';
import ConfirmModal from 'shared/components/ConfirmModal';
import { ViolationTypes, GetDisplayClassForViolationType } from 'shared/cancel-rules';
import {CONTRACT_STATUS_MAP, PRODUCT_CODE_MAP} from 'shared/components/contracts/constant';
import CancellationHelpModal from "./CancellationHelpModal";
import {CONSTANTS} from './constants.js';

const QUOTES_TAB = "quotes";
const CANCELLATION_TAB = "cancellation";
const API_DATE_FORMAT = "YYYY-MM-DD";

const PAYEE_LENDER = 'Lender';
const PAYEE_RESERVES = 'Reserves';
const PAYEE_CUSTOMER = 'Customer';
const PAYEE_DOWN_PAYMENT = 'DownPayment';
const PAYEE_STORE_REFUND = 'Store Issued Refund';

export default class CancellationView extends React.Component {
  
  static contextTypes = {
    user: PropTypes.shape({
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }),
    }),
    store: PropTypes.shape({
      id: PropTypes.number.isRequired,
    }).isRequired,
  };


  static propTypes = {
    contractCode: PropTypes.string,
    customerName: PropTypes.string,
    vin: PropTypes.string,
    contractId: PropTypes.number,
    contractProductCode: PropTypes.string,
    activeTab: PropTypes.string,
    estimates: PropTypes.array,
    loading: PropTypes.bool,
    cancelDate: PropTypes.object,
    mileage: PropTypes.string,
    handleTypeChange: PropTypes.func,
    reason: PropTypes.string,
    sppAmountPaid: PropTypes.string,
    sppBalance: PropTypes.string,
    nsdKeyClaims: PropTypes.string,
    nsdTireAndWheelClaims: PropTypes.string,
    nsdVTAClaims: PropTypes.string,
    cancellationReasons: PropTypes.array,
    quoteRequested: PropTypes.bool,
    getAllSelectedForCancellation: PropTypes.func,
    selectAllCancellation: PropTypes.func,
    selectCancellationContract: PropTypes.func,
    getIfOneSelectedCancellation: PropTypes.func,
    handleDateChange: PropTypes.func,
    handleChange: PropTypes.func,
    handleTaxChange: PropTypes.func,
    handleGetQuote: PropTypes.func,
    setActive: PropTypes.func,
    handleCancelContract: PropTypes.func,
    canSelectFutureDate: PropTypes.bool,
    paymentType: PropTypes.string,
    isSPPContractInEstimate: PropTypes.bool,
    attachmentTypes: PropTypes.arrayOf(PropTypes.object),
    onUpdateAttachments: PropTypes.func,
    payee: PropTypes.string,
    cancellationPayees: PropTypes.array,
    cancelStores: PropTypes.array,
    cancelStoreID: PropTypes.number,
    contractStoreState: PropTypes.string,
    manualTaxStates: PropTypes.arrayOf(PropTypes.string),
    payeeName: PropTypes.string,
    payeeAttentionTo: PropTypes.string,
    payeeAddress: PropTypes.string,
    payeeCity: PropTypes.string,
    payeeState: PropTypes.string,
    payeePostalCode: PropTypes.string,
    states: PropTypes.array,
    manualTax: PropTypes.number,
    skipBill: PropTypes.bool,
    handleSkipBill: PropTypes.func,
    applyFee: PropTypes.bool,
    handleApplyFee: PropTypes.func,
    isElectronicCheck: PropTypes.bool,
    email: PropTypes.string,
    handleCheckPreference: PropTypes.func,
    handleEmailChange: PropTypes.func,
  };

  constructor(props) {
    super(props);

    this.state = {
      showSPPConfirmation: false,
      showExpiredConfirmation: false,
      expiredContractsExists: false,
      sppContracts: [],
      attachments: [],
      attachmentStatus: Status.READY,
      helpModal: false,
      showManualTaxModal: false,
      refundPayableToSelections: {},
      refundPayeeFields: {},
      contractSupportingData: {},
      reservesChangeModalContractId: null,
      reservesChangeModalNewPayee: '',
      reservesChangeModalNote: '',
      reservesChangeNotes: {},
      reservesSetBySystem: {},
      cancelRequestForSubmission: null,
    };

    // Track in-flight supporting data requests to avoid duplicate fetches
    this._inFlightSupportingDataRequests = new Set();
    this._prevSelectedContractIds = [];
    this._lastPrefillContext = null;
    this._prevIsElectronicCheck = null;
  }

  componentDidMount(){
    this.removePopOver();
    if (!this._inFlightSupportingDataRequests) {
      this._inFlightSupportingDataRequests = new Set();
    }
    // Track previous selected contract IDs
    this._prevSelectedContractIds = this.getSelectedContractIds();
    this.getSelectedContractIds().forEach(id => {
      if (!this._inFlightSupportingDataRequests.has(id) && !this.state.contractSupportingData[id]) {
        this._inFlightSupportingDataRequests.add(id);
        this.fetchSupportingDataForContract(id);
      }
    });
    // Prefill email on initial mount if needed
    this.prefillEmailIfNeeded();
  }
  
  componentDidUpdate(prevProps){
    this.removePopOver();
    if (!this._inFlightSupportingDataRequests) {
      this._inFlightSupportingDataRequests = new Set();
    }
    const prevSelectedIds = this._prevSelectedContractIds || [];
    const currentSelectedIds = this.getSelectedContractIds();
    const selectedChanged = prevSelectedIds.length !== currentSelectedIds.length || prevSelectedIds.some((id, i) => id !== currentSelectedIds[i]);
    const estimatesChanged = this.props.estimates !== prevProps.estimates;
    const reasonChanged = this.props.reason !== prevProps.reason;
    if (estimatesChanged) {
      this._inFlightSupportingDataRequests = new Set(); // Reset in-flight tracker!
      this.setState({
        refundPayableToSelections: {},
        refundPayeeFields: {},
        contractSupportingData: {},
        reservesSetBySystem: {},
      }, () => {
        currentSelectedIds.forEach(id => {
          this._inFlightSupportingDataRequests.add(id);
          this.fetchSupportingDataForContract(id);
        });
      });
    } else if (selectedChanged) {
      // Only selection changed, not estimates
      currentSelectedIds.forEach(id => {
        if (!this._inFlightSupportingDataRequests.has(id) && !this.state.contractSupportingData[id]) {
          this._inFlightSupportingDataRequests.add(id);
          this.fetchSupportingDataForContract(id);
        }
      });
    }
    // Clear reservesSetBySystem if reason changed and digital reserves rule is not applicable
    if (reasonChanged) {
      const newReason = this.props.reason;
      const estimates = this.props.estimates || [];
      let reservesSetBySystem = { ...this.state.reservesSetBySystem };
      let changed = false;
      estimates.forEach(estimate => {
        const contractId = estimate.id;
        const data = this.state.contractSupportingData[contractId];
        if (reservesSetBySystem[contractId]) {
          const isDigital = data?.payee_lender_is_digital_reserves;
          const digitalReserveRules = data?.payee_lender_digital_reserve_rules || [];
          const hasRule = isDigital && estimate.product_type_id && newReason && digitalReserveRules.some(
            rule => rule.product_type_id === estimate.product_type_id && rule.cancel_reason_id === Number(newReason)
          );
          if (!hasRule) {
            reservesSetBySystem[contractId] = false;
            changed = true;
          }
        }
      });
      if (changed) {
        this.setState({ reservesSetBySystem });
      }
    }
    this._prevSelectedContractIds = currentSelectedIds;
    // Prefill email on update if needed
    this.prefillEmailIfNeeded();
  }
  
  removePopOver(){
    $('[data-toggle="popover"]').popover({
      container: '.popover-container'
    }).on('mouseleave', function () {
      //Hide the popover tooltip as it is blocking tab switch
      setTimeout(function () {
        if (!$('[data-toggle="popover"]:hover').length) {
          $('.popover').hide();
        }
      }, 300);
    });
  }

  getSelectedContractIds() {
    return (this.props.estimates || []).filter(e => e.selected).map(e => e.id).sort();
  }

  fetchSupportingDataForContract(contractId) {
    fetch(`/api/contracts/cancellation/quote-supporting-data?contract_id=${contractId}`)
      .then(res => res.json())
      .then(data => {
        this.setState(prevState => ({
          contractSupportingData: {
            ...prevState.contractSupportingData,
            [contractId]: data
          }
        }), () => {
          setTimeout(() => this.applyDigitalReservesDefaultIfEligible(contractId), 0);
        });
      });
  }

  getContractStatusBlock(estimate) {
    let contractStatus = estimate.status;
    let contractStatusMap = CONTRACT_STATUS_MAP;
    let contractStatusBlock = "";
    if (contractStatus === contractStatusMap.Active || contractStatus === contractStatusMap.Remitted) {
      contractStatusBlock = <span className="badge badge-success">{`Active`}</span>;
    } else if (contractStatus === contractStatusMap.Pending) {
      if(estimate.product_type_code === PRODUCT_CODE_MAP.gap){
        contractStatusBlock = <span className="badge badge-warning">{`Paid`}</span>;
      } else {
        contractStatusBlock = <span className="badge badge-warning">{`Pending`}</span>;
      }
    } else if (contractStatus === contractStatusMap.Canceled) {
      contractStatusBlock = <span className="badge badge-danger">{`Cancelled`}</span>;
    } else if (contractStatus === contractStatusMap.Expired) {
      contractStatusBlock = <span className="badge badge-danger">{`Expired`}</span>;
    } else if (contractStatus === contractStatusMap.Generated) {
      contractStatusBlock = <span className="badge badge-success">{`New`}</span>;
    }
    return contractStatusBlock;
  }
  

  renderTableHeader(estimates){

    if(estimates.length === 0){
      return;
    }

    const {
      getAllSelectedForCancellation,
      selectAllCancellation
    } = this.props;
    return (
      <thead>
        <tr className="table-header-row">
          <th>
            <input type="checkbox"
              className="form-check-inline"
              checked={ estimates.length > 0 && getAllSelectedForCancellation(estimates) }
              onChange={e => selectAllCancellation(e.target.checked)}/>
          </th>
          <th>Contract</th>
          <th>Contract Status</th>
          <th>Type</th>
          <th>Issuing Dealer</th>
          <th>Effective Date</th>
          <th>Factor</th>
          <th>Fee</th>
          <th>Total Claims</th>
          <th className="text-right">Total Claim Amount Paid</th>
          <th className="text-right">Customer Refund</th>
          <th className="text-right">Store Refund</th>
          <th className="text-right">Sales Tax</th>
        </tr>
      </thead>
    );
  }

  renderTableBody(estimates){
    if(estimates.length === 0){
      return;
    }
    
    const selectCancellationContract = this.handleSelectCancellationContract;
    return (
      <>
        {
          estimates.map((estimate, index) => {
            const {
              all_factors,
              factor,
              rule_violations,
            } = estimate;
            
            let factorToDisplay;
            if (all_factors) {
              const remaining = all_factors.filter((f) => f != factor).map((f) => f.replace(" used", ""));
              factorToDisplay = remaining.join("/");
            }

            let violationDisplayMessage = ViolationTypes.AdditionalStepsDisplay;
            let violationType = ViolationTypes.AdditionalSteps;
            (rule_violations || []).forEach(rv => {
              if (rv.violation_type === ViolationTypes.NotCancellable) {
                violationType = ViolationTypes.NotCancellable;
                violationDisplayMessage = ViolationTypes.NotCancellableDisplay;
              }
            });

            const rowClass = index % 2 === 0 ? 'table-row-odd' : 'table-row-even';

            if (!estimate.cancellable) {
              return (
                <tbody key={estimate.id} className={rowClass}>
                  <tr>
                    <td>
                    </td>
                    <td>
                      {estimate.original_code || estimate.code}
                    </td>
                    <td>
                      {this.getContractStatusBlock(estimate)}
                    </td>
                    <td>
                      {`${estimate.product_type_name}*`}
                    </td>
                    <td>
                      {estimate.store_code}
                    </td>
                    <td>
                      {moment.utc(estimate.effective_date).format(dateFormat)}
                    </td>
                    <td>
                      {
                        factorToDisplay
                          ? (
                            <React.Fragment>
                              {`${factorToDisplay}/`}<h6>{factor}</h6>
                            </React.Fragment>
                          ): factor
                      }
                    </td>
                    <td colSpan={5}>
                      <span data-toggle="popover"
                        data-placement="top"
                        data-trigger="hover focus"
                        data-delay={100}
                        className="popover-container"
                        data-html="true"
                        data-content=
                          {`<ul>${estimate.rule_violations.map((rule, i) => {
                            const msgElement = rule.violation_message.indexOf('Cancellation contact information') !== -1 ? `<b>${rule.violation_message}</b>` : rule.violation_message;
                            return `<li>${msgElement}</li>`;
                          }).join("")
                          }</ul>`}
                        title={`<span class="${GetDisplayClassForViolationType(violationType)}"><i class="fa fa-exclamation-triangle"/> ${violationDisplayMessage} </span>`}
                      >
                        <span className={`${GetDisplayClassForViolationType(violationType)}`}>
                          <i className="fa fa-exclamation-triangle"/> {violationDisplayMessage}
                        </span>
                      </span>
                    </td>
                    <td>
                    </td>
                  </tr>
                </tbody>
              );
            } else {
              return (
                <tbody key={estimate.id} className={rowClass}>
                  <tr>
                    <td>
                      <input type="checkbox"
                        className="form-check-inline"
                        checked={ !!estimate.selected }
                        onChange={ selectCancellationContract.bind(this, index) }/>
                    </td>
                    <td>
                      {estimate.original_code || estimate.code}
                    </td>
                    <td>
                      {this.getContractStatusBlock(estimate)}
                    </td>
                    <td>
                      {estimate.cancellable ? estimate.product_type_name : `${estimate.product_type_name}*`}
                    </td>
                    <td>
                      {estimate.store_code}
                    </td>
                    <td>
                      {moment.utc(estimate.effective_date).format(dateFormat)}
                    </td>
                    <td>
                      {
                        factorToDisplay
                          ? (
                            <React.Fragment>
                              {`${factorToDisplay}/`}<h6>{factor}</h6>
                            </React.Fragment>
                          ): factor
                      }
                    </td>
                    <td>
                      {accounting.formatMoney(estimate.fee)}
                    </td>
                    <td>
                      {estimate.claim_count}
                    </td>
                    <td className="text-right">
                      {accounting.formatMoney(estimate.claim_total_amount)}
                    </td>
                    <td className="text-right">
                      {accounting.formatMoney(estimate.customer_refund)}
                    </td>
                    {
                      <td className="text-right">
                        {!!estimate.selected && accounting.formatMoney(estimate.store_refund)}
                      </td>
                    }
                    <td className="text-right">
                      {accounting.formatMoney(estimate.sales_tax)}
                    </td>
                  </tr>
                  {/* SPP/extra details row if present */}
                  {(() => {
                    if (estimate.cancellable && (
                      (Number(estimate.adjusted_customer_refund) !== Number(estimate.customer_refund))
                        || Number(estimate.store_chargeback)
                        || Number(estimate.nsd_refund)
                        || Number(estimate.spp_refund)
                    )) {
                      return (
                        <tr>
                          <td colSpan="100%">
                            <div className="container">
                              <div className="row mb-1">
                                <div className="col font-weight-bold">Adjusted Customer Refund</div>
                                <div className="col font-weight-bold">Store Chargeback</div>
                                <div className="col font-weight-bold">SPP Refund</div>
                                <div className="col font-weight-bold">NSD Refund</div>
                              </div>
                              <div className="row">
                                <div className="col">{accounting.formatMoney(estimate.adjusted_customer_refund)}</div>
                                <div className="col">{accounting.formatMoney(estimate.store_chargeback)}</div>
                                <div className="col">{accounting.formatMoney(estimate.spp_refund)}</div>
                                <div className="col">{accounting.formatMoney(estimate.nsd_refund)}</div>
                              </div>
                            </div>
                          </td>
                        </tr>
                      );
                    }
                    return null;
                  })()}
                  {/* SPP note row if present */}
                  {estimate.payment_type === "SPP" && (
                    <tr>
                      <td colSpan="100%">
                        <div style={{color:'red'}}>
                          {`NOTE: This contract is an SPP payment type.`}
                        </div>
                      </td>
                    </tr>
                  )}
                  {/* Refund Payable To subrow only if selected and payee input is enabled */}
                  {this.props.activeTab === "cancellation" && estimate.selected && !this.disablePayeeInput() && (
                    <tr>
                      <td colSpan="100%" style={{ borderBottom: '1px solid #e3e3e3', padding: 0 }}>
                        <div style={{ padding: '10px 16px 10px 80px' }}>
                          {this.renderRefundPayableToDropdown(estimate)}
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              );
            }
          })
        }
      </>
    );
  }

  renderTable(estimates) {
    return (
      <table className="table table-hover quotes-table">
        {this.renderTableHeader(estimates)}
        {this.renderTableBody(estimates)}
        <tfoot>
          {this.renderFooter(estimates)}
        </tfoot>
      </table>
    );
  }

  renderFooter(estimates) {
    if(estimates.length === 0){
      return;
    }

    let customerRefundTotal = 0;
    let storeRefundTotal = 0;
    let salesTaxTotal = 0;
    let customerRefundAmount = false;
    let storeRefundAmount = false;
    let salesTaxAmount = false;

    for (const estimate of estimates) {
      if (estimate.selected && estimate.cancellable) {
        if (estimate.customer_refund) {
          customerRefundAmount = true;
          customerRefundTotal += accounting.unformat(estimate.customer_refund);
        }
        if (estimate.store_refund) {
          storeRefundAmount = true;
          storeRefundTotal += accounting.unformat(estimate.store_refund);
        }
        if (estimate.sales_tax) {
          salesTaxAmount = true;
          salesTaxTotal += accounting.unformat(estimate.sales_tax);
        }
      }
    }
    return (<tr className='footer'>
      <td colSpan={10} style={{ textAlign: "right" }} className='total'><strong>Grand Total</strong></td>
      <td style={{ textAlign: "right" }}><strong>{customerRefundAmount ? accounting.formatMoney(customerRefundTotal) : null}</strong></td>
      <td style={{ textAlign: "right" }}><strong>{storeRefundAmount ? accounting.formatMoney(storeRefundTotal) : null}</strong></td>
      <td style={{ textAlign: "right"}}><strong>{salesTaxAmount ? accounting.formatMoney(salesTaxTotal) : null}</strong></td>
    </tr>);
  }

  handleAttachmentsStatusChange(newStatus) {
    this.setState({
      attachmentStatus: newStatus
    });
  }

  handleUpdateAttachments(attachments) {
    this.setState({ attachments: attachments });
    this.props.onUpdateAttachments(attachments);
  }

  showHelpModal = () => {
    this.setState({ helpModal: true });
  }

  closeHelpButton = () => {
    this.setState({ helpModal: false });
  }

  renderHelpButton() {
    return (
      <div className="col form-group">
        <button type="button" className="btn btn-secondary btn-sm"
          onClick={ this.showHelpModal.bind(this) }
        >
          <i className='fa fa-question-circle'></i> Help
        </button>
      </div>);
  }

  renderAttachment() {
    const { estimates } = this.props;
    const { attachments } = this.state;
    return (
      <div>
        <div className="col form-group">
          {
            attachments.length === 0 &&
              <span className="font-weight-bold text-danger">{`* Please upload document in order to cancel contract${this.numberOfContractSelectedToCancel() > 1 && 's' || ''}`}</span>
          }
          {/* Additional error for refund payable to info */}
          {(() => {
            let showRefundPayeeMsg = false;
            for (const estimate of estimates) {
              if (estimate.selected) {
                const payeeFields = this.state.refundPayeeFields[estimate.id];
                if (!payeeFields || !payeeFields.payee || !payeeFields.payeeName || !payeeFields.payeeAddress || !payeeFields.payeeCity || !payeeFields.payeeState || !payeeFields.payeePostalCode) {
                  showRefundPayeeMsg = true;
                  break;
                }
              }
            }
            return (!this.disablePayeeInput() && showRefundPayeeMsg) ? (
              <>
                <br/>
                <span className="font-weight-bold text-danger">* Please select refund payable to for all contracts to cancel.</span>
              </>
            ) : null;
          })()}
        </div>
        <div className="col-6 form-group">
          <Attachments id="attachment-files"
            allowMultiple={true}
            maxFiles={estimates.length * 2}
            acceptedFileTypes = {[
              'image/*',
              'application/pdf', // *.pdf
              'text/csv',  // *.csv
              'text/plain',   // *.txt
              'application/msword', // *.doc
              'application/vnd.ms-excel', // *.xls
              'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // *.docx
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' // *.xlsx
            ]}
            onStatusChange={this.handleAttachmentsStatusChange.bind(this)}
            onUpdateAttachments={this.handleUpdateAttachments.bind(this)}
          />
        </div>
      </div>
    );
  }


  renderCancelTabUI(){
    const {
      skipBill,
      handleSkipBill,
      applyFee,
      handleApplyFee,
      isElectronicCheck,
      email,
      handleCheckPreference,
      handleEmailChange
    } = this.props;

    // Show check options if any selected contract has payee type Lender or Customer
    const anySelectedCustomer = Object.entries(this.state.refundPayeeFields)
      .filter(([id, fields]) => {
        const estimate = (this.props.estimates || []).find(e => e.id == id && e.selected);
        return estimate && fields && fields.payee === PAYEE_CUSTOMER;
      })
      .length > 0;

    return (
      <div>
        <div className="col form-group">
          {this.renderHelpButton()}
        </div>
        <div className="col form-group">
          {!this.isFlatCancel() && this.renderAttachment()}
        </div>
        <div className="col-6 form-group">
          <input
            type="checkbox"
            className="form-check-inline"
            checked={skipBill}
            onChange={handleSkipBill}
          />
          <label className="form-check-label"><strong>Don&apos;t Include Bill</strong></label>
        </div>
        <div className="col-6 form-group">
          <input
            type="checkbox"
            className="form-check-inline"
            checked={applyFee}
            onChange={handleApplyFee}
          />
          <label className="form-check-label"><strong>Apply Processing Fee</strong></label>
        </div>
        {
          anySelectedCustomer && (
            <div className="check-options-box">
              <div style={{ fontSize: '0.95em', color: '#6c757d', marginBottom: '8px' }}>
                Applicable for Customer only
              </div>
              <div className="form-group mb-2 d-flex align-items-center" style={{ gap: '24px' }}>
                <div className="form-check form-check-inline mb-0">
                  <input
                    type='radio'
                    name="check_preferance"
                    className="form-check-input"
                    checked={isElectronicCheck}
                    onChange={() => handleCheckPreference(true)}
                    radioGroup="check_preferance"
                    id="electronic-check-radio"
                  />
                  <label className="form-check-label" htmlFor="electronic-check-radio"><strong>Electronic Check</strong></label>
                </div>
                <div className="form-check form-check-inline mb-0">
                  <input
                    type='radio'
                    name="check_preferance"
                    className="form-check-input"
                    checked={!isElectronicCheck}
                    onChange={() => handleCheckPreference(false)}
                    radioGroup="check_preferance"
                    id="paper-check-radio"
                  />
                  <label className="form-check-label" htmlFor="paper-check-radio"><strong>Paper Check</strong></label>
                </div>
              </div>
              {
                isElectronicCheck && (
                  <div className="form-group mt-1">
                    <label>Email Address*</label>
                    <input
                      type="email"
                      id="email"
                      className="form-control"
                      style={{ maxWidth: '450px' }}
                      value={email}
                      onChange={(e) => handleEmailChange(e.target.value)}
                    />
                  </div>
                )
              }
            </div>
          )
        }
      </div>
    );
  }

  isFlatCancel() {
    const {
      reason,
      cancellationReasons,
    } = this.props;

    if (!!reason && !!cancellationReasons) {
      const cancellationReason = cancellationReasons.find(cancelReason => cancelReason.id == reason);
      return cancellationReason.name.toLowerCase().includes('flat cancel');
    }
    return false;
  }

  disablePayeeInput() {
    const {
      reason,
      cancellationReasons,
    } = this.props;

    const cancellationReason = cancellationReasons.find(cancelReason => cancelReason.id == reason);
    return (this.isFlatCancel() ||
      cancellationReason.name === 'Unwind' ||
      cancellationReason.name === 'SPP Default' ||
      cancellationReason.name === 'SPP Customer Request');
  }

  renderDataTab(estimates) {
    const { activeTab, loading } = this.props;

    let id = "quotes-tab";

    let tipText = "* Some products may not be cancellable or may not provide a quote for cancellation.";

    if(activeTab === CANCELLATION_TAB) {
      id = "cancellation-tab";
    }

    return (
      <div role="tabpanel" className="tab-pane in active py-3" id={id}>
        {
          loading &&
          <div>
            <i className='fa fa-spin fa-refresh' /> Loading
          </div>
        }
        {
          !loading &&
          this.renderTable(estimates)
        }
        {
          !loading && activeTab === CANCELLATION_TAB &&
            <div className="row">
              <div className="col">
                {this.renderCancelTabUI()}
              </div>
            </div>
        }
        <small className="tip">{tipText}</small>
      </div>
    );
  }

  renderTab(estimates){
    const {
      activeTab,
      setActive,
    } = this.props;
    return (
      <div className="col-12">
        <ul className="nav nav-tabs" role="tablist">
          {
            <li className="nav-item" key={ QUOTES_TAB }>
              <a className={ `nav-link ${activeTab === QUOTES_TAB ? "active" : "text-primary"}` }
                data-toggle="tab"
                role="tab"
                accessKey={ QUOTES_TAB }
                aria-controls={ QUOTES_TAB }
                onClick={ setActive.bind(this, QUOTES_TAB) }>
                Quotes
              </a>
            </li>
          }
          {
            <li className="nav-item" key={CANCELLATION_TAB}>
              <a className={`nav-link ${activeTab === CANCELLATION_TAB ? "active" : "text-primary"}`}
                data-toggle="tab"
                role="tab"
                accessKey={CANCELLATION_TAB}
                aria-controls={CANCELLATION_TAB}
                onClick={ setActive.bind(this, CANCELLATION_TAB) }>
                Cancellation
              </a>
            </li>
          }
        </ul>
        <div className="tab-content">
          {this.renderDataTab(estimates)}
        </div>
      </div>
    );
  }

  showManualTaxModal = () => {
    this.setState({ showManualTaxModal: true });
  }
  closeConfirmManualTaxModal = () => {
    this.setState({ showManualTaxModal: false });
  }
  handleManualTaxConfirm = () => {
    this.setState({
      showManualTaxModal: false
    },
    () => {
      this.handleCancellation();
    });
  }

  renderConfirmManualTaxModal() {
    const {
      contractStoreState
    } = this.props;

    if (!this.state.showManualTaxModal) {
      return null;
    }
    return (
      <ConfirmModal
        visible={this.state.showManualTaxModal}
        close={this.closeConfirmManualTaxModal}
        confirm={this.handleManualTaxConfirm}
        proceedLabel="Yes"
        cancelLabel="No"
      >
        <h4><i className="fa fa-warning"></i> Confirm Tax Rate for {contractStoreState} State</h4>
        <p>{contractStoreState} - Must enter tax manually,<br/>
            Verify Tax with Qlik Report and cancel separately for different tax rates.<br/>
            Are you sure you want to proceed?</p>
      </ConfirmModal>
    );
  }

  handleCancellation = () => {
    const {
      estimates,
      handleCancelContract,
    } = this.props;

    let arrSelectedCancelContract = [];
    let sppContracts = [];
    let expiredContractsExists = false;
    for (const estimate of estimates) {
      if (estimate.selected) {
        arrSelectedCancelContract.push(estimate.id);
        if (estimate.payment_type === 'SPP') {
          sppContracts.push(estimate);
        }
        if (estimate.status === CONTRACT_STATUS_MAP.Expired) {
          expiredContractsExists = true;
        }
      }
    }

    const contractPayees = this.disablePayeeInput() ? {} : this.buildContractPayeesPayload(arrSelectedCancelContract);
    // Add contract_notes
    const contract_notes = {};
    arrSelectedCancelContract.forEach(id => {
      if (this.state.reservesChangeNotes[id]) {
        contract_notes[id] = `Refund Payable change from Reserves: ${this.state.reservesChangeNotes[id]}`;
      }
    });

    const cancelRequest = {
      cancel_date: this.props.cancelDate ? moment(this.props.cancelDate).format(API_DATE_FORMAT) : this.props.cancelDate,
      mileage: parseInt(this.props.mileage, 10),
      cancel_reason_id: parseInt(this.props.reason, 10),
      spp_customer_paid: parseFloat(this.props.sppAmountPaid || 0),
      spp_balance: parseFloat(this.props.sppBalance || 0),
      manual_tax_rate: parseFloat(this.props.manualTax || 0),
      nsd_key_claims: parseFloat(this.props.nsdKeyClaims || 0),
      nsd_tire_and_wheel_claims: parseFloat(this.props.nsdTireAndWheelClaims || 0),
      nsd_vta_claims: parseFloat(this.props.nsdVTAClaims || 0),
      contracts: arrSelectedCancelContract,
      contract_payees: contractPayees,
      contract_notes,
    };
    // Conditionally add fields only when applicable
    if (this.props.applyFee) {
      cancelRequest.apply_fee = this.props.applyFee;
    }
    if (this.props.skipBill) {
      cancelRequest.skip_bill = this.props.skipBill;
    }
    if (this.props.isElectronicCheck) {
      cancelRequest.is_electronic_check = this.props.isElectronicCheck;
      if (this.props.email) {
        cancelRequest.email = this.props.email;
      }
    }

    this.setState({ cancelRequestForSubmission: cancelRequest }, () => {
      // Show SPP confirmation if any SPP contracts selected
      if (sppContracts.length > 0) {
        this.setState({ showSPPConfirmation: true, sppContracts, expiredContractsExists });
        return;
      }
      // Show expired confirmation if any expired contracts selected
      if (expiredContractsExists) {
        this.showExpiredCancel();
        return;
      }

      handleCancelContract(this.state.cancelRequestForSubmission);
    });
  }

  showSPPCancel = (sppContracts) => {
    this.setState({showSPPConfirmation: true, sppContracts: sppContracts});
  }

  closeSPPCancel = () => {
    this.setState({showSPPConfirmation: false, sppContracts: []});
  }

  onCancelSppContract = (e) => {
    e.preventDefault();
    this.setState({
      showSPPConfirmation: false,
      sppContracts: []
    }, 
    () => {
      if (this.state.expiredContractsExists) {
        this.showExpiredCancel();
      } else {
        this.props.handleCancelContract(this.state.cancelRequestForSubmission);
      }
    });
  }

  onCancelExpiredContract = (e) => {
    e.preventDefault();
    this.setState({
      showExpiredConfirmation: false
    }, 
    () => {
      this.props.handleCancelContract(this.state.cancelRequestForSubmission);
    });
  }

  showExpiredCancel = () => {
    this.setState({showExpiredConfirmation: true});
  }

  closeExpiredCancel = () => {
    this.setState({showExpiredConfirmation: false});
  }

  renderExpiredCancel() {
    return (
      <Modal visible={this.state.showExpiredConfirmation} manualOverflow={true}>
        <form onSubmit={this.onCancelExpiredContract}>
          <h3>{`Expired Cancel Confirmation`}</h3>
          <hr/>
          <div className='form-group'>
            <label><h5>Confirm: </h5></label>
            <br />
            {`You are canceling one or more expired contracts. Are you sure you want to proceed?`}
          </div>
          <div className='clearfix'>
            <div className='float-right'>
              <button type='submit' className='btn btn-primary'>
                <i className='fa fa-check' /> Yes
              </button>
              {' '}
              <button type='button' className='btn btn-secondary' onClick={this.closeExpiredCancel}>
                <i className='fa fa-ban' /> No
              </button>
            </div>
          </div>
        </form>
      </Modal>
    );
  }

  renderSPPCancel() {
    const {
      sppContracts,
    } = this.state;
    return (
      <Modal visible={this.state.showSPPConfirmation} manualOverflow={true}>
        <form onSubmit={this.onCancelSppContract}>
          <h3>{`SPP Cancel Confirmation`}</h3>
          <hr/>
          <div className='form-group'>
            <label><h5>Confirm: </h5></label>
            <br />
            {`Do you want to cancel ${sppContracts.length} spp contract ?`}
          </div>
          <div className='clearfix'>
            <div className='float-right'>
              <button type='submit' className='btn btn-primary'>
                <i className='fa fa-check' /> Yes
              </button>
              {' '}
              <button type='button' className='btn btn-secondary' onClick={this.closeSPPCancel}>
                <i className='fa fa-ban' /> Cancel
              </button>
            </div>
          </div>
        </form>
      </Modal>
    );
  }

  disableGetQuote() {
    const {
      reason,
      mileage
    } = this.props;

    if (!reason) {
      return true;
    }
    if (this.isFlatCancel()) {
      return false;
    }
    if (!mileage) {
      return true;
    }
    return false;
  }

  render(){

    const {
      contractCode,
      contractId,
      contractProductCode,
      cancelDate,
      mileage,
      reason,
      sppAmountPaid,
      sppBalance,
      nsdKeyClaims,
      nsdTireAndWheelClaims,
      nsdVTAClaims,
      customerName,
      vin,
      cancellationReasons,
      quoteRequested,
      estimates,
      activeTab,
      getIfOneSelectedCancellation,
      handleDateChange,
      handleChange,
      handleTaxChange,
      handleGetQuote,
      canSelectFutureDate,
      handleTypeChange,
      isSPPContractInEstimate,
      contractStoreState,
      manualTaxStates,
      manualTax,
      loading
    } = this.props;

    // Filter out cancelled contracts before rendering
    const filteredEstimates = estimates.filter(
      est => est.status !== CONTRACT_STATUS_MAP.Canceled
    );
    const anyContractSelected = getIfOneSelectedCancellation(filteredEstimates);
    
    let arrSelectedCancelContract = [];
    for(const estimate of filteredEstimates){
      if(estimate.selected){
        arrSelectedCancelContract.push(estimate.id);
      }
    }
    

    const contractPayees = this.buildContractPayeesPayload(arrSelectedCancelContract);

    const cancelRequest = {
      cancel_date: cancelDate ? moment(cancelDate).format(API_DATE_FORMAT): cancelDate,
      mileage: parseInt(mileage, 10),
      cancel_reason_id: parseInt(reason, 10),
      spp_customer_paid: parseFloat(sppAmountPaid || 0),
      spp_balance: parseFloat(sppBalance || 0),
      manual_tax_rate: parseFloat(manualTax || 0),
      nsd_key_claims: parseFloat(nsdKeyClaims || 0),
      nsd_tire_and_wheel_claims: parseFloat(nsdTireAndWheelClaims || 0),
      nsd_vta_claims: parseFloat(nsdVTAClaims || 0),
      contracts: arrSelectedCancelContract,
      contract_payees: contractPayees,
    };

    const cancelContractPdfUrl = `/api/admin/contracts/cancellation/estimate/pdf?q=${encodeURIComponent(JSON.stringify(cancelRequest))}`;

    const customerQuotePdfUrl = `/api/admin/contracts/cancellation/estimate/quote/pdf?q=${encodeURIComponent(JSON.stringify(cancelRequest))}`;

    const cancelRequestPdfUrl = `/api/admin/contracts/${contractId}/cancellation/request-form?q=${encodeURIComponent(JSON.stringify(cancelRequest))}`;

    const checkRequestPdfUrl = `/api/admin/contracts/cancellation/check-request/pdf?q=${encodeURIComponent(JSON.stringify(cancelRequest))}`;

    const { attachmentStatus, refundPayeeFields } = this.state;
    let disableCancel = loading || !anyContractSelected ||
    (!this.isFlatCancel() &&
      (attachmentStatus !== Status.READY || this.state.attachments.length === 0)
    );

    // New validation: For each selected contract, require payee info
    if (!disableCancel && !this.disablePayeeInput()) {
      for (const estimate of estimates) {
        if (estimate.selected) {
          const payeeFields = refundPayeeFields[estimate.id];
          // Required fields: payee, payeeName, payeeAddress, payeeCity, payeeState, payeePostalCode
          if (!payeeFields || !payeeFields.payee || !payeeFields.payeeName || !payeeFields.payeeAddress || !payeeFields.payeeCity || !payeeFields.payeeState || !payeeFields.payeePostalCode) {
            disableCancel = true;
            break;
          }
        }
      }
    }

    if (hstore.has(this.context.user.roles, Roles.TestAutomation) && anyContractSelected) {
      disableCancel = false;
    }

    let isManualTaxEnabled = false;
    if (Roles.UserHasAnyRole(this.context, [Roles.AccountRepII, Roles.AccountRepManager, Roles.AccountRep])) {
      isManualTaxEnabled = true;
    }
    let taxTipText = "* This rate will be use if only the CDK Deal Lookup fails";

    const sppRefundType = (
      <div className="col-4">
        <label>SPP Refund Type </label>
        <br/>
        <input type="radio"
          name="type"
          id="refund_type-radio"
          onChange={handleTypeChange.bind(this, "refundType", "SPP Refund")}
          checked={isSPPContractInEstimate}/>
        {` `}
        <label htmlFor="spp_refund-radio">SPP Refund</label>
          &nbsp;&nbsp;&nbsp;
        <input type="radio"
          name="type"
          id="refund_type-radio"
          onChange={handleTypeChange.bind(this, "refundType", "Customer Refund")}
          checked={!isSPPContractInEstimate}/>
        {` `}
        <label htmlFor="cust_refund-radio">Customer Refund</label>
      </div>
    );

    return (
      <div>
        <Modal 
          visible={ this.state.helpModal } 
          close={ this.closeHelpButton.bind(this) }
        >
          <CancellationHelpModal cancellationReasons={cancellationReasons} />
        </Modal>
        {this.renderSPPCancel()}
        {this.renderExpiredCancel()}
        {this.renderReservesChangeModal()}
        <h4 className="border-bottom border-light">Cancellation Request Estimate</h4>
        <div className="container">
          <div className="row py-2">
            <div className="col-4">
              <h4>{customerName} {vin}</h4>
            </div>
          </div>
          <div className="row py-2">
            <a className="btn btn-primary mt-2"
              href={`/admin/contracts-search/${contractCode}?product_code=${contractProductCode}&id=${contractId}`}>
              Contract View
            </a>
          </div>
          <div className="row py-2">
            <div className="col-4">
              <label>Cancel Reason*</label>
              <br/>
              <select className="form-control"
                id="reason"
                value={reason}
                onChange={handleChange.bind(null, "reason")}>
                <option key={-1} value="">Select reason...</option>
                {
                  cancellationReasons.map((reason, i) =>
                    <option key={reason.id} value={reason.id}>{reason.name}</option>
                  )
                }
              </select>
            </div>
            <div className="col-4">
              <label>Cancel Date*</label>
              <br/>
              <ModalDatePicker className="form-control"
                id="cancel-date"
                selected={!this.isFlatCancel() && cancelDate}
                {...canSelectFutureDate && {} || { maxDate: moment() }}
                onChange={handleDateChange}
                disabled={this.isFlatCancel()}
              />
            </div>
            <div className="col-4">
              <label>Mileage*</label>
              <br/>
              <input type="number"
                id="mileage"
                className="form-control"
                value={!this.isFlatCancel() && mileage}
                onChange={handleChange.bind(null, "mileage")}
                disabled={this.isFlatCancel()}
              />
            </div>

          </div>
          <div className="row py-2">
            <div className="col-4">
              <label>NSD Key Claims</label>
              <br/>
              <input type="number"
                id="nsd-key-claims"
                className="form-control"
                value={nsdKeyClaims}
                onChange={handleChange.bind(null, "nsdKeyClaims")}
              />
            </div>
            <div className="col-4">
              <label>NSD Tire &amp; Wheel Claims</label>
              <br/>
              <input type="number"
                id="nsd-tire-and-wheel-claims"
                className="form-control"
                value={nsdTireAndWheelClaims}
                onChange={handleChange.bind(null, "nsdTireAndWheelClaims")}
              />
            </div>
            <div className="col-4">
              <label>NSD VTA Claims</label>
              <br/>
              <input type="number"
                id="nsd-vta-claims"
                className="form-control"
                value={nsdVTAClaims}
                onChange={handleChange.bind(null, "nsdVTAClaims")}
              />
            </div>
          </div>
          <div className="row py-2">
            <div className="col-4">
              <label>SPP Amount Paid</label>
              <br/>
              <input type="number"
                id="spp-customer-refund"
                className="form-control"
                value={sppAmountPaid}
                onChange={handleChange.bind(null, "sppAmountPaid")}
              />
            </div>
            <div className="col-4">
              <label>SPP Balance</label>
              <br/>
              <input type="number"
                id="spp-balance"
                className="form-control"
                value={sppBalance}
                onChange={handleChange.bind(null, "sppBalance")}
              />
            </div>
            <div className="col-4">
              <label>Tax Rate</label>
              <br/>
              <input type="number"
                id="manual_tax"
                min="1" max={CONSTANTS.MAX_SALES_TAX} step="0.01"
                className="form-control"
                value={manualTax}
                disabled={!isManualTaxEnabled}
                onChange={handleTaxChange.bind(null)}
              />
              <small className="tip">{taxTipText}</small>
            </div>
            {isSPPContractInEstimate && sppRefundType}
          </div>
          <div className="row py-2">
            <div className="col-5" style={{color:'red'}}>
              {isSPPContractInEstimate && `NOTE: There is one or more SPP contract in cancel list.`}
            </div>
          </div>
          <div className="row py-2">
            <div className="col-12" style={{color:'#ff9900'}}>
              {quoteRequested && !loading && `NOTE: Making any changes to the above inputs will clear the current quotes and require getting new quotes.`}
            </div>
          </div>
          <div className="row py-2">
            <div className="col-2">
              <label/>
              <br/>
              {
                !quoteRequested && 
                  <button className="btn btn-primary mt-2"
                    disabled={this.disableGetQuote()}
                    onClick={handleGetQuote}>
                    Get Quotes
                  </button>
              }
            </div>
          </div>
          <div className="row py-2 border-bottom border-light">
            {
              quoteRequested && 
              (loading || (!loading &&
              filteredEstimates &&
              filteredEstimates.length > 0)) &&
              this.renderTab(filteredEstimates)
            }
          </div>
          {
            quoteRequested && activeTab === CANCELLATION_TAB &&
            <div className="row float-right py-2">
              <a className={`btn btn-primary ${anyContractSelected ? "" : "disabled"}`}
                target='_blank'
                rel="noopener noreferrer"
                href={anyContractSelected ? checkRequestPdfUrl : ""}>
                <i className="fa fa-print"/> Print Check Request
              </a>
              &nbsp;
              <a className={`btn btn-primary ${anyContractSelected ? "" : "disabled"}`}
                target='_blank'
                rel="noopener noreferrer"
                href={anyContractSelected ? cancelContractPdfUrl : ""}>
                <i className="fa fa-print"/> Print Quotes
              </a>
              &nbsp;
              <button className="btn btn-primary"
                disabled={disableCancel}
                onClick={this.handleCancellation}>
                Cancel Contracts
              </button>
            </div>
          }
          {
            quoteRequested && activeTab === QUOTES_TAB &&
            <div className="row float-right py-2">
              <a className={`btn btn-primary ${anyContractSelected ? "" : "disabled"}`}
                target='_blank'
                rel="noopener noreferrer"
                href={anyContractSelected ? checkRequestPdfUrl : ""}>
                <i className="fa fa-print"/> Print Check Request
              </a>
              &nbsp;
              <a className={`btn btn-primary ${anyContractSelected ? "" : "disabled"}`}
                target='_blank'
                rel="noopener noreferrer"
                href={anyContractSelected ? customerQuotePdfUrl : ""}>
                <i className="fa fa-print"/> Print Customer Quote
              </a>
              &nbsp;
              <a className={`btn btn-primary ${anyContractSelected ? "" : "disabled"}`}
                target='_blank'
                rel="noopener noreferrer"
                href={anyContractSelected ? cancelRequestPdfUrl : ""}>
                <i className="fa fa-print"/> Print Cancellation Request Form
              </a>      
            </div>
          }
          {manualTaxStates && manualTaxStates.includes(contractStoreState) && this.renderConfirmManualTaxModal()}
        </div>
      </div>
    );
  }

  /**
   * Function which returns number of contract selected for cancellation process.
   */
  numberOfContractSelectedToCancel = () => {
    const { estimates } = this.props;
    return estimates && estimates.filter( estimates => estimates.selected ).length || 0;
  }

  handleRefundPayableToChange = (id, value) => {
    this.setState((prevState) => {
      const prevPayee = prevState.refundPayableToSelections[id] || '';
      const reservesSetBySystem = { ...prevState.reservesSetBySystem };
      // Only show modal if system set reserves and user is changing away from it
      if (
        prevPayee === PAYEE_RESERVES &&
        value !== PAYEE_RESERVES &&
        prevPayee !== value &&
        reservesSetBySystem[id]
      ) {
        return {
          showReservesChangeModal: true,
          reservesChangeModalContractId: id,
          reservesChangeModalNewPayee: value,
          reservesChangeModalNote: '',
        };
      }
      // If user changes away from reserves, clear the system flag
      if (prevPayee === PAYEE_RESERVES && value !== PAYEE_RESERVES) {
        reservesSetBySystem[id] = false;
      }
      // If user manually selects reserves, do NOT set reservesSetBySystem to true
      if (value === PAYEE_RESERVES && !reservesSetBySystem[id]) {
        reservesSetBySystem[id] = false;
      }
      // Normal change
      const refundPayableToSelections = { ...prevState.refundPayableToSelections, [id]: value };
      const refundPayeeFields = { ...prevState.refundPayeeFields };
      const supportingData = prevState.contractSupportingData[id] || {};
      let payeeData = {};
      let cancelStoreID = '';

      if (value === PAYEE_LENDER && supportingData.payee_lender) {
        payeeData = supportingData.payee_lender;
      } else if (value === PAYEE_CUSTOMER && supportingData.payee_customer) {
        payeeData = supportingData.payee_customer;
      } else if (
        (value === PAYEE_DOWN_PAYMENT || value === PAYEE_STORE_REFUND) &&
        supportingData.cancel_stores
      ) {
        cancelStoreID = refundPayeeFields[id]?.cancelStoreID || supportingData.payee_store_id || '';
        const storeObj = supportingData.cancel_stores.find(s => s.id == cancelStoreID);
        if (storeObj) {
          payeeData = storeObj;
        }
      } else if (value === PAYEE_RESERVES && supportingData.payee_lender) {
        payeeData = supportingData.payee_lender;
      }

      refundPayeeFields[id] = {
        payee: value,
        payeeName: payeeData.name || '',
        payeeAttentionTo: payeeData.attention || '',
        payeeAddress: payeeData.address || '',
        payeeCity: payeeData.city || '',
        payeeState: payeeData.state_code || '',
        payeePostalCode: payeeData.postal_code || '',
        cancelStoreID: cancelStoreID,
      };
      return {
        refundPayableToSelections,
        refundPayeeFields,
        reservesSetBySystem,
      };
    });
  }

  handleRefundPayeeFieldChange = (id, field, value) => {
    this.setState((prevState) => {
      const refundPayeeFields = { ...prevState.refundPayeeFields };
      let updatedFields = { ...refundPayeeFields[id], [field]: value };

      // If the store is changed for DownPayment or Store Issued Refund, update all fields
      const payee = updatedFields.payee;
      const supportingData = prevState.contractSupportingData[id] || {};
      if (
        (payee === PAYEE_DOWN_PAYMENT || payee === PAYEE_STORE_REFUND) &&
        field === 'cancelStoreID' &&
        supportingData.cancel_stores
      ) {
        const storeObj = supportingData.cancel_stores.find(s => s.id == value);
        if (storeObj) {
          updatedFields = {
            ...updatedFields,
            payeeName: storeObj.name || '',
            payeeAttentionTo: '',
            payeeAddress: storeObj.address || '',
            payeeCity: storeObj.city || '',
            payeeState: storeObj.state_code || '',
            payeePostalCode: storeObj.postal_code || '',
          };
        }
      }

      refundPayeeFields[id] = updatedFields;
      return { refundPayeeFields };
    });
  }

  renderRefundPayableToDropdown(estimate) {
    const { refundPayableToSelections, refundPayeeFields } = this.state;
    const payee = refundPayableToSelections[estimate.id] || '';
    let payeeFields = refundPayeeFields[estimate.id];
    // If the contract is selected and payeeFields is not initialized, initialize it to empty
    if (estimate.selected && !payeeFields) {
      this.setState(prevState => ({
        refundPayeeFields: {
          ...prevState.refundPayeeFields,
          [estimate.id]: {
            payee: '',
            payeeName: '',
            payeeAttentionTo: '',
            payeeAddress: '',
            payeeCity: '',
            payeeState: '',
            payeePostalCode: '',
            cancelStoreID: '',
          }
        }
      }));
      return null;
    }
    // Use supporting data for dropdowns
    const supportingData = this.state.contractSupportingData[estimate.id] || {};
    const payeeOptions = supportingData.cancel_payees || [];
    const storeOptions = supportingData.cancel_stores || [];
    const stateOptions = supportingData.states || {};
    let disablePayeeName = true;
    if (hstore.has(this.context.user.roles, Roles.AccountRepManager) && (payee === PAYEE_LENDER || payee === PAYEE_RESERVES)) {
      disablePayeeName = false;
    }

    return (
      <div style={{ width: '100%' }}>
        {/* Row 1: Payee Selection and Name Fields */}
        <div className="row mb-2" style={{ alignItems: 'center' }}>
          <div className="col" style={{maxWidth: '240px', minWidth: '180px'}}>
            <label style={{ marginRight: 8, minWidth: 120}}><strong>Refund Payable To*</strong></label>
            <select
              className="form-control"
              style={{ minWidth: 140 }}
              value={payee}
              onChange={e => this.handleRefundPayableToChange(estimate.id, e.target.value)}
              disabled={this.disablePayeeInput()}
            >
              <option key="" value="">Select payee...</option>
              {payeeOptions.map(opt => (
                <option key={opt} value={opt}>{opt}{opt === "Reserves" && " - no refund"}</option>
              ))}
            </select>
          </div>
          {payee && (
            <>
              {/* For DownPayment, show Store dropdown before Store Name */}
              {payee === PAYEE_DOWN_PAYMENT && (
                <div className="col" style={{maxWidth: '350px'}}>
                  <label>Store</label>
                  <select className="form-control"
                    value={payeeFields.cancelStoreID || ''}
                    onChange={e => this.handleRefundPayeeFieldChange(estimate.id, 'cancelStoreID', e.target.value)}>
                    <option key="" value="">Select store...</option>
                    {storeOptions.map(store => (
                      <option key={store.id} value={store.id}>{store.name} - {store.code}</option>
                    ))}
                  </select>
                </div>
              )}
              <div className="col" style={{maxWidth: '300px'}}>
                <label>{ payee === PAYEE_CUSTOMER && PAYEE_CUSTOMER}{ (payee === PAYEE_LENDER || payee === PAYEE_RESERVES) && PAYEE_LENDER}{ (payee === PAYEE_DOWN_PAYMENT || payee === PAYEE_STORE_REFUND) && 'Store'} Name*</label>
                <input type="string"
                  className="form-control"
                  value={payeeFields.payeeName || ''}
                  onChange={e => this.handleRefundPayeeFieldChange(estimate.id, 'payeeName', e.target.value)}
                  disabled={disablePayeeName}
                />
              </div>
              {/* For Store Issued Refund, do not show Store dropdown */}
              { payee === PAYEE_LENDER && (
                <div className="col" style={{maxWidth: '250px'}}>
                  <label>Attention To</label>
                  <input type="string"
                    className="form-control"
                    value={payeeFields.payeeAttentionTo || ''}
                    onChange={e => this.handleRefundPayeeFieldChange(estimate.id, 'payeeAttentionTo', e.target.value)}
                  />
                </div>
              )}
            </>
          )}
        </div>

        {/* Row 2: Address Fields */}
        {payee && (
          <div className="row" style={{ alignItems: 'center' }}>
            <div className="col" style={{maxWidth: '350px'}}>
              <label>Address*</label>
              <input type="string"
                className="form-control"
                value={payeeFields.payeeAddress || ''}
                onChange={e => this.handleRefundPayeeFieldChange(estimate.id, 'payeeAddress', e.target.value)}
              />
            </div>
            <div className="col" style={{maxWidth: '200px'}}>
              <label>City*</label>
              <input type="string"
                className="form-control"
                value={payeeFields.payeeCity || ''}
                onChange={e => this.handleRefundPayeeFieldChange(estimate.id, 'payeeCity', e.target.value)}
              />
            </div>
            <div className="col" style={{maxWidth: '200px'}}>
              <label>State*</label>
              <select className="form-control"
                value={payeeFields.payeeState || ''}
                onChange={e => this.handleRefundPayeeFieldChange(estimate.id, 'payeeState', e.target.value)}>
                <option key="" value="">Select state...</option>
                {Object.keys(stateOptions).map(code => (
                  <option key={code} value={code}>{code} - {stateOptions[code]}</option>
                ))}
              </select>
            </div>
            <div className="col" style={{maxWidth: '150px'}}>
              <label style={{whiteSpace: 'nowrap'}}>Postal Code*</label>
              <input type="string"
                className="form-control"
                value={payeeFields.payeePostalCode || ''}
                onChange={e => this.handleRefundPayeeFieldChange(estimate.id, 'payeePostalCode', e.target.value)}
              />
            </div>
          </div>
        )}
      </div>
    );
  }

  // Add a wrapper for selectCancellationContract to handle reset on uncheck
  handleSelectCancellationContract = (index, ...args) => {
    const estimate = this.props.estimates[index];
    const wasSelected = estimate.selected;
    this.props.selectCancellationContract(index, ...args);
    setTimeout(() => {
      const updatedEstimate = this.props.estimates[index];
      if (!wasSelected && updatedEstimate.selected) {
        // Contract was just checked, fetch supporting data if not already present
        if (!this.state.contractSupportingData[estimate.id]) {
          this.fetchSupportingDataForContract(estimate.id);
        } else {
          // Apply digital reserves default if eligible
          this.applyDigitalReservesDefaultIfEligible(estimate.id);
        }
      }
      if (wasSelected && !updatedEstimate.selected) {
        this.setState(prevState => {
          const refundPayableToSelections = { ...prevState.refundPayableToSelections };
          const refundPayeeFields = { ...prevState.refundPayeeFields };
          delete refundPayableToSelections[estimate.id];
          delete refundPayeeFields[estimate.id];
          return { refundPayableToSelections, refundPayeeFields };
        });
      }
    }, 0);
  }

  // Add a wrapper for selectAllCancellation to handle reset on uncheck
  handleSelectAllCancellation = (checked) => {
    this.props.selectAllCancellation(checked);
    this.setState({ refundPayableToSelections: {}, refundPayeeFields: {} });
  }

  applyDigitalReservesDefaultIfEligible(contractId) {
    const data = this.state.contractSupportingData[contractId];
    if (!data) return;
    // Find the estimate for this contract
    const estimate = (this.props.estimates || []).find(e => e.id === contractId);
    const productTypeId = estimate?.product_type_id;
    const cancelReasonId = this.props.reason;
    const isDigital = data.payee_lender_is_digital_reserves;
    const digitalReserveRules = data.payee_lender_digital_reserve_rules || [];
    // Only default if a rule exists for this productTypeId and cancelReasonId
    const hasRule = isDigital && productTypeId && cancelReasonId && digitalReserveRules.some(
      rule => rule.product_type_id === productTypeId && rule.cancel_reason_id === Number(cancelReasonId)
    );
    if (hasRule) {
      if (!this.state.refundPayableToSelections[contractId]) {
        let payeeData = data.payee_lender || {};
        this.setState(prevState => ({
          refundPayableToSelections: {
            ...prevState.refundPayableToSelections,
            [contractId]: PAYEE_RESERVES
          },
          refundPayeeFields: {
            ...prevState.refundPayeeFields,
            [contractId]: {
              payee: PAYEE_RESERVES,
              payeeName: payeeData.name || '',
              payeeAttentionTo: payeeData.attention || '',
              payeeAddress: payeeData.address || '',
              payeeCity: payeeData.city || '',
              payeeState: payeeData.state_code || '',
              payeePostalCode: payeeData.postal_code || '',
              cancelStoreID: '',
            }
          },
          reservesSetBySystem: {
            ...prevState.reservesSetBySystem,
            [contractId]: true
          }
        }));
      }
    }
  }

  // Add a helper to build contract_payees for selected contracts
  buildContractPayeesPayload = (selectedContracts) => {
    const { refundPayeeFields } = this.state;
    const contractPayees = {};
    selectedContracts.forEach(contractId => {
      const payeeFields = refundPayeeFields[contractId];
      if (payeeFields) {
        contractPayees[contractId] = {
          payee: payeeFields.payee,
          payee_name: payeeFields.payeeName,
          payee_attention_to: payeeFields.payeeAttentionTo,
          payee_address: payeeFields.payeeAddress,
          payee_city: payeeFields.payeeCity,
          payee_state: payeeFields.payeeState,
          payee_postal_code: payeeFields.payeePostalCode,
          cancel_store_id: payeeFields.cancelStoreID !== undefined && payeeFields.cancelStoreID !== null && payeeFields.cancelStoreID !== ""
            ? String(payeeFields.cancelStoreID)
            : "",
        };
      }
    });
    return contractPayees;
  }

  // Modal handlers for reserves change
  handleReservesChangeNoteInput = (e) => {
    this.setState({ reservesChangeModalNote: e.target.value });
  }

  handleReservesChangeModalConfirm = () => {
    const { reservesChangeModalContractId, reservesChangeModalNewPayee, reservesChangeModalNote, reservesChangeNotes, reservesSetBySystem } = this.state;
    // Save note for this contract
    const newNotes = { ...reservesChangeNotes, [reservesChangeModalContractId]: reservesChangeModalNote };
    // Set reservesSetBySystem to false to prevent modal loop
    const newReservesSetBySystem = { ...reservesSetBySystem, [reservesChangeModalContractId]: false };
    // Actually change the payee now
    this.setState({
      showReservesChangeModal: false,
      reservesChangeModalContractId: null,
      reservesChangeModalNewPayee: '',
      reservesChangeModalNote: '',
      reservesChangeNotes: newNotes,
      reservesSetBySystem: newReservesSetBySystem,
    }, () => {
      // Now update the payee selection
      this.handleRefundPayableToChange(reservesChangeModalContractId, reservesChangeModalNewPayee);
    });
  }

  handleReservesChangeModalCancel = () => {
    const { reservesChangeModalContractId } = this.state;
    // Revert payee selection to Reserves
    this.setState(prevState => ({
      showReservesChangeModal: false,
      reservesChangeModalContractId: null,
      reservesChangeModalNewPayee: '',
      reservesChangeModalNote: '',
      refundPayableToSelections: {
        ...prevState.refundPayableToSelections,
        [reservesChangeModalContractId]: PAYEE_RESERVES,
      }
    }));
  }

  renderReservesChangeModal() {
    const { showReservesChangeModal, reservesChangeModalNote } = this.state;
    if (!showReservesChangeModal) return null;
    return (
      <Modal visible={showReservesChangeModal} manualOverflow={true}  close={this.handleReservesChangeModalCancel}>
        <form onSubmit={e => { e.preventDefault(); this.handleReservesChangeModalConfirm(); }}>
          <h4>Change Lender Debit Reserves Selection</h4>
          <div className="form-group">
            <label>Please enter a note explaining why you are changing from &quot;Reserves - No Refund&quot;:</label>
            <textarea
              className="form-control"
              value={reservesChangeModalNote}
              onChange={this.handleReservesChangeNoteInput}
              required
              rows={3}
            />
          </div>
          <div className="clearfix">
            <div className="float-right">
              <button type="submit" className="btn btn-primary" disabled={!reservesChangeModalNote.trim()}>
                <i className="fa fa-check" /> Confirm
              </button>
              {' '}
              <button type="button" className="btn btn-secondary" onClick={this.handleReservesChangeModalCancel}>
                <i className="fa fa-ban" /> Cancel
              </button>
            </div>
          </div>
        </form>
      </Modal>
    );
  }

  prefillEmailIfNeeded = () => {
    const { isElectronicCheck, email, handleEmailChange } = this.props;
    const selectedCustomerIds = this.getSelectedContractIds().filter(
      id => this.state.refundPayableToSelections[id] === PAYEE_CUSTOMER
    );
    const contextSignature = JSON.stringify({
      isElectronicCheck,
      selectedCustomerIds,
    });

    // Always prefill if switching to Electronic Check
    const switchedToElectronicCheck = !this._prevIsElectronicCheck && isElectronicCheck;
    this._prevIsElectronicCheck = isElectronicCheck;

    if (
      isElectronicCheck &&
      selectedCustomerIds.length > 0 &&
      (this._lastPrefillContext !== contextSignature || switchedToElectronicCheck)
    ) {
      let foundEmail = '';
      for (const id of selectedCustomerIds) {
        const contractData = this.state.contractSupportingData[id] || {};
        if (contractData.payee_customer && contractData.payee_customer.email) {
          foundEmail = contractData.payee_customer.email;
          break;
        }
      }
      if (foundEmail && email !== foundEmail) {
        handleEmailChange(foundEmail);
      }
      this._lastPrefillContext = contextSignature;
    }
  };
}
