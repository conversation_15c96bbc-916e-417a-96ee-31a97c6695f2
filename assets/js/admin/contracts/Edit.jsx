import React from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import accounting from 'accounting';
import immstruct from 'immstruct';
import Immutable from 'immutable';
import Alert from 'react-s-alert';
import isEqual from 'lodash/isEqual';

import ErrorSummary from 'shared/components/ErrorSummary';
import FieldError from 'shared/components/FieldError';
import { jsonPromise as ajax } from 'shared/ajax';
import ContractAttachmentsManage from 'shared/components/contracts/ContractAttachmentsManage';
import * as roles from 'shared/roles';

const saleTypeDeal = "Finance Deal";
const saleTypeRO = "Service RO";

export default class ContractEdit extends React.Component {
  STRUCTURE_KEY = 'edit-product-data';
  static contextTypes = {
    router: PropTypes.object.isRequired,
  };

  static propTypes = {
    params: PropTypes.shape({
      code: PropTypes.string.isRequired
    }).isRequired,
    contract: PropTypes.object,
    edit_options: PropTypes.shape({
      remove_options: PropTypes.bool.isRequired
    }),
    product_variants: PropTypes.arrayOf(PropTypes.object),
    location: PropTypes.shape({
      query: PropTypes.shape({
        company_id: PropTypes.string,
        return_route: PropTypes.string,
        claim_count: PropTypes.string,
      }).isRequired,
    }).isRequired,
  }

  constructor(props, context) {
    super(props, context);
    this.structure = immstruct(this.STRUCTURE_KEY, this.initialValues());
    this.structure.on('swap', (newStructure, oldStructure, keyPath) => {
      this.setState({ form: this.structure.cursor() });
    });

    this.state = {
      form: this.structure.cursor(),
      attachmentUploaded: false,
    };
  }

  componentDidMount() {
    document.title = `TCA Portal ADMIN | Edit Contract ${this.props.contract.code}`;      
    this.loadAttachments();
  }

  componentWillUnmount() {
    this.structure.removeAllListeners();
    immstruct.remove(this.STRUCTURE_KEY);
  }

  initialValues() {
    let form = Immutable.Map({
      submitting: false,
      productVariantId: this.props.contract.product_variant.id,
      planId: this.props.contract.plan.id,
      nonGroupOptionIds: this.initialNonGroupOptionIds(),
      groupOptionIds: this.initialGroupOptionIds(),
      effectiveMileageChange:'',
      modifiedProductVariants:'',
      surchargeIds: this.initialSurchargeIds(),
      effectiveDateChange: this.props.contract.effective_date_change,
      errors: Immutable.Map(),
    });

    return form;
  }

  initialNonGroupOptionIds() {
    return Immutable.Set((this.props.contract.options || [])
      .filter(o => o.option_group === null)
      .map(o => o.option_id));
  }

  initialGroupOptionIds() {
    return Immutable.Map((this.props.contract.options || [])
      .filter(o => o.option_group !== null)
      .reduce((memo, o) => {
        memo[o.option_group] = o.option_id;
        return memo;
      }, {}));
  }

  initialSurchargeIds() {
    return Immutable.Set((this.props.contract.surcharges || [])
      .map(o => o.surcharge_id));
  }

  loadAttachments(props) {
    if (!props) {
      props = this.props;
    }
    var url = `/api/admin/contracts/${this.props.contract.id}/attachments`;
    this.setState({loadingAttachments: true}, ()=>{
      ajax(url, {}, {}).then(results => {
        if (results.status === 200) {
          this.setState({
            loadingAttachments: false,
          });
        } else {
          this.setState({
            loadingAttachments: false,
          });
          Alert.error(`Error loading attachments: ${results.data.message}`);
        }
      }).catch(reason => {
        this.setState({
          loadingAttachments: false,
        });
        Alert.error('Error loading attachments');
      });
    });
  }

  reloadDataOnEffectiveMileageChange(mileage) {
    if (!mileage || isNaN(mileage) || mileage < 0) {
      return;
    }
    const url = `/api/admin/contracts/${this.props.contract.code}/edit?effective_mileage_change=${mileage}`;
    this.setState({
      loading: true
    }, () => {
      ajax(url, {}, {}).then((results) => {
        if(results.status === 200){
          this.state.form.cursor('modifiedProductVariants').update(() => results.data.data.product_variants);
        } else{
          Alert.error("Error loading supporting data");
        }
        this.setState({
          loading: false
        });
      }, () => {
        Alert.error("Error loading supporting data");
        this.setState({
          loading: false
        });
      });
    });
  }

  renderEditMileage(currentPV) {
    if (currentPV) {
      return (
        <div className="form-group row">
          <label htmlFor={`edit-miles-${currentPV.id}`} className="col-3 col-form-label">Edit Mileage:</label>
          <div className="col-9">
            <input type="number" id={`edit-miles-${currentPV.id}`}
              className="form-control"
              value={this.state.form.cursor('effectiveMileageChange').deref()}
              onChange={this.onEditMileageChange.bind(this)}
              onBlur={this.onEditMileageBlur.bind(this)}
            />
          </div>
        </div>
      );
    }
  }

  onEditMileageChange(e) {
    this.state.form.cursor('effectiveMileageChange').update(() => e.target.value);
  }

  onEditMileageBlur(e) {
    this.reloadDataOnEffectiveMileageChange(e.target.value);
  }


  render() {
    const contract = this.props.contract;
    const pvs = this.props.product_variants;
    const pvId = this.state.form.cursor(['productVariantId']).deref();
    const currentPV = this.getProductVariant(pvs, pvId);
    return (
      <div>
        <div className="row">
          <div className="col-4">
            <h3>Sale # {this.props.contract.sale.id}</h3>
            {this.renderSale()}
          </div>
          <div className="col-4">
            <h3>Customer</h3>
            {this.renderCustomer()}
          </div>
          <div className="col-4">
            <h3>Vehicle</h3>
            {this.renderVehicle()}
          </div>
        </div>
        <ErrorSummary errors={this.state.form.cursor("errors").toJS()} />
        <div className="card mb-4">
          <form onSubmit={this.onSubmit.bind(this)}>
            <h3 className="card-header clearfix">
              <div className="float-right">{contract.status}</div>
              {contract.product_type.name} - <samp>{contract.code}</samp>
            </h3>
            <div className="card-body row">
              <div className="col-6">
                <div className="form-group row">
                  <label htmlFor={`product-type-id-${contract.product_type.id}`} className="col-3 col-form-label">Product:</label>
                  <div className="col-9">
                    <select id={`product-type-id-${contract.product_type.id}`}
                      className="form-control"
                      value={pvId}
                      onChange={this.onProductTypeChange.bind(this)}
                      required={true}
                    >
                      <option value=''>&mdash; Select &mdash;</option>
                      {pvs.map(pv => 
                        pv.dealer_pv_display_name ?
                          <option key={pv.id} value={pv.id}>{pv.display_name}&bull;{pv.dealer_pv_display_name}</option>:
                          <option key={pv.id} value={pv.id}>{pv.display_name}</option>
                      )}
                    </select>
                    {this.renderClassificationCode(currentPV)}
                  </div>
                </div>
                {this.renderPlans(currentPV)}
                {this.renderEffectiveDateChange(currentPV)}
                {this.renderGroupOptions(currentPV)}
                {this.renderEditMileage(currentPV)}
                {this.renderNonGroupOptions(currentPV)}
                {this.renderSelectableSurcharges(currentPV)}
              </div>
              {this.renderSummary(currentPV)}
            </div>
            {this.renderButtons(currentPV)}
          </form>

        </div>
        {this.renderAttachments()}
      </div>
    );
  }

  onSubmit(e) {
    e.preventDefault();
    this.state.form.cursor('submitting').update(() => true);
    let formState = this.state.form;
    let groupOptions = formState.cursor('groupOptionIds').deref().toJS() || {};
    let optionIds = Object.keys(groupOptions).map(k => groupOptions[k]);
    optionIds.push(...formState.cursor('nonGroupOptionIds').deref().toJS());
    const pv = this.getProductVariant(this.props.product_variants, this.getProductVariantId());
    const plan = this.getPlan(pv, this.getPlanId());
    const data = {
      store_id: this.props.contract.store.id,
      sale_id: this.props.contract.sale.id,
      contract_id: this.props.contract.id,
      product_variant_id: formState.cursor('productVariantId').deref(),
      plan_id: plan.id,
      effective_date_change: parseInt(formState.cursor(["effectiveDateChange"]).deref()) || -1,
      effective_mileage_change: parseInt(formState.cursor('effectiveMileageChange').deref(), 10) || 0,
      option_ids: optionIds,
      surcharge_ids: formState.cursor('surchargeIds').deref(),
      expected_total_cost: this.getTotalCost(formState.deref(), pv, plan)
    };

    ajax(`/api/admin/contracts/${this.props.contract.code}/edit`, data, { method: 'PUT' }).then(results => {
      this.state.form.cursor('submitting').update(() => false);
      if (results.status === 200) {
        Alert.success(`${results.data.contract_code} updated successfully`);
        const defaultReturnRoute = `contracts-search/${this.props.contract.code}`
          + `?product_code=${this.props.contract.product_type.code}`
          + `&id=${this.props.contract.id}`;
        this.context.router.push(this.props.location.query.return_route || defaultReturnRoute);

        if (this.props.contract.product_type_code === 'VSC') {
          const endorsementData = {
            product_variant_display_name: this.props.contract.product_variant_display_name,
            plan_name: this.props.contract.plan_name,
            expiration_mileage: this.props.contract.expiration_mileage,
            expiration_date: this.props.contract.expiration_date,
            deductible: (this.props.contract.options.find(o => o.name.toLowerCase().includes('deductible'))).name,
          };
          const endorsementURL = `/api/admin/contracts/${this.props.contract.code}/edit/endorsement-form?q=${encodeURIComponent(JSON.stringify(endorsementData))}`;
          window.open(endorsementURL,'_blank');
          window.location.reload();
        }
      } else {
        Alert.error(`Error updating contract: ${results.data.message}`);
        this.state.form.cursor('errors').update(() =>
          Immutable.fromJS(results.data.form_errors || {})
        );
      }
    }, reason => {
      Alert.error("Error updating contract");
      this.state.form.cursor('submitting').update(() => false);

    });
    

  }

  onProductTypeChange(e) {
    const pv = this.getProductVariant(this.props.product_variants, parseInt(e.target.value, 10));
    this.state.form.update(current => {
      return current.merge(Immutable.fromJS({
        productVariantId: parseInt(e.target.value, 10),
        planId: '',
        nonGroupOptionIds: this.findNonGroupOptionIds(pv),
        groupOptionIds: this.findGroupOptionIds(pv),
        effectiveMileageChange: '',
        modifiedProductVariants: '',
        surchargeIds: this.findSurchargeIds(pv),
      }));
    });
  }

  renderSale() {
    return (
      <div className="mb-3">
        {this.renderDeal()}<br />
        <strong>Contract Date:</strong> {moment.utc(this.props.contract.sale.contract_date).format(require('shared/date-format.js'))}<br />
        <strong>{this.props.contract.sale.payment_type}:</strong> {this.renderPaymentDetails()}<br />
        {this.renderInspection()}
      </div>
    );
  }

  renderDeal() {
    if (this.props.contract.sale.is_dms_deal) {
      switch (this.props.contract.sale.sale_type) {
      case saleTypeDeal:
        return <span><strong>Deal:</strong> {this.props.contract.sale.dms_number}</span>;
      case saleTypeRO:
        return <span><strong>RO:</strong> {this.props.contract.sale.dms_number}</span>;
      }
    } else {
      switch (this.props.contract.sale.sale_type) {
      case saleTypeDeal:
        return <strong>No Deal</strong>;
      case saleTypeRO:
        return <strong>No RO</strong>;
      }
    }
  }

  renderPaymentDetails() {
    switch (this.props.contract.sale.payment_type) {
    case "Loan":
    case "Lease":
      return <span>{this.props.contract.sale.finance_term.Int64} months {accounting.formatMoney(this.props.contract.sale.finance_amount || 0)}</span>;
    }
  }

  renderLenderDetails() {
    return (
      <address className="mb-0">
        <strong>Lender: </strong> {this.props.contract.sale.lender.name} <br />
        {this.props.contract.sale.lender.address}<br />
        {this.props.contract.sale.lender.city}, {this.props.contract.sale.lender.state_code} {this.props.contract.sale.customer.postal_code}
      </address>
    );
  }

  renderInspection() {
    const s = this.props.contract.sale;
    if (s.eligibility_level || s.inspection_liability.Valid) {
      return (
        <React.Fragment>
          <strong>Inspection:</strong> {s.eligibility_level ? s.eligibility_level : (s.inspection_liability.Valid ? s.inspection_liability.String + ' Liability' : 'Not Required')} <em>{s.is_certified ? 'Certified' : ''}</em>
        </React.Fragment>
      );
    }
  }

  renderCustomer() {
    return (
      <address>
        {this.renderCustomerBusiness()}
        {this.props.contract.customer.first_name} {this.props.contract.customer.last_name}<br />
        {this.props.contract.customer.address}<br />
        {this.props.contract.customer.city}, {this.props.contract.customer.state_code} {this.props.contract.customer.postal_code}<br />
        {this.props.contract.customer.phone}<br />
        {this.props.contract.customer.email}
      </address>
    );
  }

  renderCustomerBusiness() {
    if (this.props.contract.customer.is_business) {
      return <span>{this.props.contract.customer.business_name}<br /></span>;
    }
  }

  renderVehicle() {
    return (
      <div>
        {this.props.contract.vin_record.year} {this.props.contract.vin_record.make} {this.props.contract.vin_record.model} {this.props.contract.vin_record.trim}<br />
        <samp>{this.props.contract.vin_record.vin}</samp><br />
        {accounting.formatNumber(this.props.contract.sale.odometer)} miles<br />
        {this.props.contract.vin_record.basic_warranty_months} / {accounting.formatNumber(this.props.contract.vin_record.basic_warranty_miles)} basic<br />
        {this.props.contract.vin_record.drivetrain_warranty_months} / {accounting.formatNumber(this.props.contract.vin_record.drivetrain_warranty_miles)} drivetrain
      </div>
    );
  }

  renderClassificationCode(pv) {
    if (pv && pv.classification_code) {
      return (
        <small className="form-text text-muted">
          Classification Code: {pv.classification_code}
        </small>
      );
    }
  }

  getPlanId() {
    return parseInt(this.state.form.cursor('planId').deref(), 10) || '';
  }

  getProductVariantId() {
    return parseInt(this.state.form.cursor('productVariantId').deref(), 10) || '';
  }

  getEffectiveDateChange() {
    return parseInt(this.state.form.cursor('effectiveDateChange').deref(), 10) || 0;
  }

  renderPlans(currentPV) {
    if (currentPV) {
      let planId = this.getPlanId();

      const plans = this.props.product_variants
        .find(pv => pv.id === currentPV.id)
        .plans;

      const errors = this.state.form.cursor(['errors']).deref();

      return (
        <div className={"form-group row" + (errors.plan_id ? " has-danger" : "")}>
          <label htmlFor={"plan-id-" + planId} className="col-3 col-form-label">Plan:</label>
          <div className="col-9">
            <select id={"plan-id-" + planId}
              className={"form-control" + (errors.plan_id ? " form-control-danger" : "")}
              value={planId}
              onChange={this.onPlanChange.bind(this, plans)}
              required={true}
            >
              <option value=''>&mdash; Select &mdash;</option>
              {plans.map(p => {
                return <option key={p.id} value={p.id}>{p.name}</option>;
              })}
            </select>
            <FieldError err={errors.plan_id} />
          </div>
        </div>
      );
    }
  }

  onPlanChange(plans, e) {
    const qp = plans.find(obj => obj.id === parseInt(e.target.value, 10));
    if (qp) {
      const qpv = this.getProductVariant(this.props.product_variants, this.state.form.cursor(['productVariantId']).deref());
      const currentGroupOptionID = this.state.form.cursor(['groupOptionIds']).deref().valueSeq().reduce();
      var currentGroupOption = this.getOption(qpv, parseInt(currentGroupOptionID, 10));

      if (qpv.options) {
        // Loop through each option 
        let addOptions = [];
        let removeOptions = [];
        let addGroupOptions =[];
        qpv.options.forEach(obj => {
          // If any option has option group set then loop through them
          if (obj.quote_option_group && obj.quote_option_group.length > 0) {
            obj.quote_option_group.forEach(qog => {
            // Find the valid and matching duration for selected plan from option
            // If found select that for addition and add all other for removal
              if (qog && !qog.plan_duration || (qog.plan_duration && qog.plan_duration === qp.duration)) {
                if (qog.option_group && qog.option_group.length > 0) {
                  if (currentGroupOption.option_group === qog.option_group && currentGroupOption.name === qog.name) {
                    addGroupOptions.push(qog);
                  }
                } else {
                  addOptions.push(qog.id);
                }
              } else {
                removeOptions.push(qog.id);
              }
            });
          } else {
            if (obj.option_group && obj.option_group.length > 0){
              if (currentGroupOption.option_group === obj.option_group &&
                  obj.name === currentGroupOption.name) {
                addGroupOptions.push(obj);
              }
            }
          }
        });

        let currentOptionIDs = this.state.form.cursor(['nonGroupOptionIds']).deref();
        if (addOptions.length > 0) {
          addOptions.forEach(obj => {
            currentOptionIDs = currentOptionIDs.add(obj);
          });
        }
        if (removeOptions.length > 0) {
          removeOptions.forEach(obj => {
            currentOptionIDs = currentOptionIDs.remove(obj);
          });
        }

        this.state.form.cursor(['nonGroupOptionIds']).update(() => currentOptionIDs);

        let newGroupOptionIDs = Immutable.Map();
        if (addGroupOptions.length > 0) {
          addGroupOptions.forEach(obj => {
            newGroupOptionIDs = newGroupOptionIDs.set(obj.option_group, obj.id);
          });
        }
        this.state.form.cursor(['groupOptionIds']).update(() => newGroupOptionIDs);
      }
      if (qpv.surcharges) {
        let surchargeIDs = Immutable.Set();
        qpv.surcharges.forEach(surcharge => {
          if (surcharge.plan_duration === null || surcharge.plan_duration === qp.duration) {
            surchargeIDs = surchargeIDs.add(surcharge.id);
          }
        });
        this.state.form.cursor(['surchargeIds']).update(() => surchargeIDs);
      }
    }
    this.state.form.cursor('planId').update(() => parseInt(e.target.value, 10));
  }

  getProductVariant(pvs, pvID) {
    if (pvID > 0) {
      return pvs.find(pv => pv.id === pvID) || null;
    }
    return null;
  }

  getPlan(pv, planId) {
    if (!pv || !planId) {
      return null;
    }
    let plan = pv.plans.find(plan => plan.id === planId);
    if (!plan) {
      return null;
    }

    let modifiedPVList = this.state.form.cursor('modifiedProductVariants').deref();
    if(!modifiedPVList) {
      return plan;
    }

    const modifiedPV = modifiedPVList.find(pv => pv.id === this.getProductVariantId());
    if (!modifiedPV || !modifiedPV.plans) {
      return null;
    }
    return modifiedPV.plans.find(p => p.name === plan.name);
  }

  getTotalCost(formState, pv, plan) {
    let cost = formState.get("nonGroupOptionIds").union(formState.get("groupOptionIds").values()).reduce((memo, optId) => {
      const id = parseInt(optId, 10);

      // if the option id has not been changed, then use the original price
      let opt = this.getOriginalOption(id);
      if (opt === null) {
        opt = this.getOption(pv, id);
      }
      if (opt) {
        return memo + parseFloat(this.getAmount(opt, id));
      }
      return memo;
    }, parseFloat(plan.total_cost));
    
    cost = formState.get("surchargeIds").reduce((memo, surchargeId) => {
      const id = parseInt(surchargeId, 10);
      
      // if the surcharge id has not been changed, then use the original surcharge price
      let surcharge = this.getOriginalSurcharge(id);
      if (surcharge === null) {
        surcharge = this.getSurcharge(pv, parseInt(surchargeId, 10));
      }
      if (surcharge) {
        return memo + parseFloat(surcharge.cost);
      }
      return memo;
    }, cost);

    if (this.props.contract.post_sale_adjustments !== null) {
      cost = this.props.contract.post_sale_adjustments.reduce((memo, adj) => {
        return memo + parseFloat(adj.amount);
      }, cost);
    }
    return cost;
  }

  getOption(pv, optionId) {
    if (!pv || !optionId || !pv.options) return null;

    return pv.options.find(o => {
      if (o.id === optionId) {
        return true;
      } else if (o.quote_option_group && o.quote_option_group.length > 0) {
        const existingGroupOptions = o.quote_option_group.map(obj => obj.id);
        return existingGroupOptions.includes(optionId);
      }
      return false;
    }) || null;
  }

  getAmount(option, optionId) {
    if (option.quote_option_group && option.quote_option_group.length > 0) {
      const groupedOption = option.quote_option_group.find(obj => obj.id === optionId);
      if (groupedOption) {
        return groupedOption.cost;
      }
      return option.cost;
    }
    return option.cost;
  }

  getOriginalOption(optionId) {
    if (!optionId || !this.props.contract.options) return null;
    return this.props.contract.options.find(o => o.option_id === optionId) || null;
  }

  getSurcharge(pv, surchargeId) {
    if (!pv || !surchargeId || !pv.surcharges) return null;
    return pv.surcharges.find(s => s.id === surchargeId) || null;
  }

  getOriginalSurcharge(surchargeId) {
    if (!surchargeId ||!this.props.contract.surcharges) return null;
    return this.props.contract.surcharges.find(s => s.surcharge_id === surchargeId) || null;
  }

  findNonGroupOptionIds(pv) {
    let plan = this.getPlan(pv, this.getPlanId());
    let optionIDs = Immutable.Set();
    if (pv && plan) {
      (pv.options || []).forEach(opt => {
        if (!opt.option_group && (opt.preselected || opt.plan_duration === plan.duration)) {
          optionIDs = optionIDs.add(opt.id);
        }
      });
    }
    return optionIDs;
  }

  findGroupOptionIds(pv) {
    let optionIDs = Immutable.Map();
    if (pv) {
      (pv.options || []).forEach(opt => {
        if (opt.option_group && (opt.preselected || !optionIDs.has(opt.option_group))) {
          optionIDs = optionIDs.set(opt.option_group, opt.id);
        }
      });
    }
    return optionIDs;
  }

  findSurchargeIds(pv) {
    let surchargeIDs = Immutable.Set();
    let plan = this.getPlan(pv, this.getPlanId());
    if (pv) {
      (pv.surcharges || []).forEach(surcharge => {
        if (!surcharge.selectable || surcharge.plan_duration === plan.duration) {
          surchargeIDs = surchargeIDs.add(surcharge.id);
        }
      });
    }
    return surchargeIDs;
  }

  renderEffectiveDateChange(currentPV) {
    const contract = this.props.contract;
    const errors = this.state.form.cursor(['errors']).deref();
    if (currentPV && contract.product_type.code === 'MNT' && contract.store.allow_maintenance_effective_date_change) {
      return (
        <div className={"form-group row" + (errors.effective_date_change ? " has-danger" : "")}>
          <label htmlFor={`effective_date_change-${currentPV.id}`} className="col-3 col-form-label">Effective Date Change:</label>
          <div className="col-9">
            <select id={`effective_date_change-${currentPV.id}`}
              value={this.state.form.cursor(['effectiveDateChange']).deref()}
              onChange={this.onEffectiveDateChange.bind(this)}
              className="form-control"
            >
              <option value="0">0 months</option>
              <option value="6">6 months</option>
              <option value="12">12 months</option>
              <option value="18">18 months</option>
              <option value="24">24 months</option>
              <option value="36">36 months</option>
            </select>
            <FieldError err={errors.effective_date_change} />
          </div>
        </div>
      );
    }
  }

  onEffectiveDateChange(e) {
    this.state.form.cursor(['effectiveDateChange']).set(e.target.value);
  }

  renderGroupOptions(pv) {
    const plan = this.getPlan(pv, this.getPlanId());
    if (pv && pv.options && plan) {
      return this.state.form.cursor(['groupOptionIds']).deref().keySeq().map(optionGroup => {
        var id = "group-options-" + optionGroup;
        return (
          <div key={optionGroup} className="form-group row">
            <label htmlFor={id} className="col-3 col-form-label">{optionGroup}</label>
            <div className="col-9">
              <select id={id}
                className="form-control"
                value={this.state.form.cursor(['groupOptionIds']).deref().get(optionGroup)}
                onChange={this.onGroupOptionChange.bind(this, optionGroup)}
              >
                {pv.options.reduce((memo, opt) => {
                  if (opt.option_group === optionGroup) {
                    if (opt.quote_option_group && opt.quote_option_group.length > 0) {
                      opt.quote_option_group.forEach(obj => {
                        if (!obj.plan_duration || (obj.plan_duration === plan.duration)) {
                          memo.push(<option key={obj.id} value={obj.id}>{obj.name}</option>);
                        }
                      });
                    } else {
                      memo.push(<option key={opt.id} value={opt.id}>{opt.name}</option>);
                    }
                  }
                  return memo;
                }, [])}
              </select>
            </div>
          </div>
        );
      });
    }
  }

  onGroupOptionChange(optionGroup, e) {
    this.state.form.update((current) => {
      const newGroupOptionIds = Immutable.Map().set(optionGroup, parseInt(e.target.value, 10));
      let formState = current.set("groupOptionIds", newGroupOptionIds);
      return formState;
    });
  }

  renderNonGroupOptions(pv) {
    if (pv && pv.options) {
      return pv.options.reduce((memo, opt) => {
        if (!opt.option_group) {
          memo.push(
            <div key={opt.id} className="row">
              <div className="col-7">
                <div className="form-check">
                  <input type="checkbox" id={`option-${opt.id}`}
                    checked={this.isNonGroupOptionSelected(opt.id, opt)}
                    onChange={this.onNonGroupOptionChange.bind(this, opt.id)}
                    disabled={this.isNonGroupOptionDisabled(opt)}
                    className="form-check-input"
                  />
                  <label htmlFor={`option-${opt.id}`} className="form-check-label">{opt.name}</label>
                </div>
              </div>
            </div>
          );
        }
        return memo;
      }, []);
    }
  }

  isNonGroupOptionDisabled(opt) {
    if (!opt.selectable) return true;

    // if removing options is not enabled and the option was originally checked, then 
    // do not allow it to be unchecked
    const optSet = this.props.contract.options.some(option => option.option_id === opt.id);
    return this.props.edit_options.remove_options ? false : optSet;
  }

  isNonGroupOptionSelected(optId, opt) {
    if (opt && opt.quote_option_group && opt.quote_option_group.length > 0) {
      let foundInGroup = false;
      opt.quote_option_group.forEach(obj => {
        if (this.state.form.cursor(["nonGroupOptionIds"]).deref().has(obj.id)) {
          foundInGroup = true;
          return;
        }
      });
      return foundInGroup;
    } 
    return this.state.form.cursor(["nonGroupOptionIds"]).deref().has(optId);
  }

  onNonGroupOptionChange(optId, e) {
    this.state.form.update(current => {
      let newNonGroupOptionIds = current.get("nonGroupOptionIds");
      if (e.target.checked) {
        newNonGroupOptionIds = newNonGroupOptionIds.add(optId);
      } else {
        newNonGroupOptionIds = newNonGroupOptionIds.remove(optId);
      }
      let formState = current.set("nonGroupOptionIds", newNonGroupOptionIds);
      return formState;
    });
  }

  renderSelectableSurcharges(pv) {
    if (pv && pv.surcharges) {
      return pv.surcharges.reduce((memo, surcharge) => {
        if (surcharge.selectable) {
          memo.push(
            <div key={surcharge.id} className="row">
              <div className="col-7">
                <div className="form-check">
                  <input type="checkbox" id={`surcharge-${surcharge.id}`}
                    checked={this.isSurchargeSelected(surcharge.id)}
                    onChange={this.onSurchargeChange.bind(this, surcharge.id)}
                    className="form-check-input"
                  />
                  <label htmlFor={`surcharge-${surcharge.id}`} className="form-check-label">{surcharge.name}</label>
                </div>
              </div>
            </div>
          );
        }
        return memo;
      }, []);
    }
  }

  isSurchargeSelected(surchargeId) {
    return this.state.form.cursor(["surchargeIds"]).deref().has(surchargeId);
  }

  onSurchargeChange(surchargeId, e) {
    this.state.form.update(current => {
      let newSurchargeIds = current.get("surchargeIds");
      if (e.target.checked) {
        newSurchargeIds = newSurchargeIds.add(surchargeId);
      } else {
        newSurchargeIds = newSurchargeIds.remove(surchargeId);
      }
      let formState = current.set("surchargeIds", newSurchargeIds);
      return formState;
    });
  }


  renderSummary(pv) {
    let plan = this.getPlan(pv, this.getPlanId());
    if (pv && plan) {
      const totalCost = this.getTotalCost(this.state.form.deref(), pv, plan);
      return (
        <div className="col-6">
          {this.renderCurrentPlan(plan, parseFloat(plan.total_cost))}
          {this.renderCurrentGroupOptions(pv)}
          {this.renderCurrentNonGroupOptions(pv)}
          {this.renderCurrentSurcharges(pv)}
          <div className="row">
            <div className="col-5 ml-auto">
              <hr />
            </div>
          </div>
          {this.renderCurrentTotalCost(totalCost)}
          <div className="row">
            <div className="col">&nbsp;</div>
          </div>
          {this.renderOriginalCost()}
          <div className="row">
            <div className="col-5 ml-auto">
              <hr />
            </div>
          </div>
          {this.renderCostDifference(totalCost)}
        </div>
      );
    }
  }

  renderCurrentPlan(plan, cost) {
    return (
      <div className="row">
        <div className="col-7">{plan.name}</div>
        <div className="col-5 text-right">{accounting.formatMoney(cost)}</div>
      </div>
    );
  }

  renderCurrentGroupOptions(pv) {
    return this.state.form.cursor(['groupOptionIds']).deref().valueSeq().reduce((memo, optId) => {
      var opt = this.getOption(pv, parseInt(optId, 10));
      if (opt) {
        memo.push(
          <div key={opt.id} className="row">
            <div className="col-7">{opt.name}</div>
            <div className="col-5 text-right">{accounting.formatMoney(this.getAmount(opt, parseInt(optId, 10)))}</div>
          </div>
        );
      }
      return memo;
    }, []);
  }

  renderCurrentNonGroupOptions(pv) {
    return this.state.form.cursor(['nonGroupOptionIds']).deref().reduce((memo, optId) => {
      var opt = this.getOption(pv, parseInt(optId, 10));
      if (opt) {
        memo.push(
          <div key={opt.id} className="row">
            <div className="col-7">{opt.name}</div>
            <div className="col-5 text-right">{accounting.formatMoney(this.getAmount(opt, parseInt(optId, 10)))}</div>
          </div>
        );
      }
      return memo;
    }, []);
  }

  renderCurrentSurcharges(pv) {
    return this.state.form.cursor(['surchargeIds']).deref().reduce((memo, surchargeId) => {
      var surcharge = this.getSurcharge(pv, parseInt(surchargeId, 10));
      if (surcharge) {
        memo.push(
          <div key={surcharge.id} className="row">
            <div className="col-7">{surcharge.name}</div>
            <div className="col-5 text-right">{accounting.formatMoney(surcharge.cost)}</div>
          </div>
        );
      }
      return memo;
    }, []);
  }

  renderCurrentTotalCost(cost) {
    return (
      <div className="row">
        <div className="col-7"><strong>Total Cost</strong></div>
        <div className="col-5 text-right"><strong>{accounting.formatMoney(cost)}</strong></div>
      </div>
    );
  }

  renderOriginalCost() {
    const originalCost = this.props.contract.current_cost;
    return (
      <div className="row">
        <div className="col-7">Original Cost</div>
        <div className="col-5 text-right">{accounting.formatMoney(originalCost)}</div>
      </div>
    );
  }

  renderCostDifference(cost) {
    const originalCost = this.props.contract.current_cost;
    const costDiff = cost - originalCost;
    return (
      <div className="row">
        <div className="col-7"><strong>Cost Difference</strong></div>
        <div className="col-5 text-right"><strong>{accounting.formatMoney(costDiff)}</strong></div>
      </div>
    );
  }

  renderButtons(currentPV) {
    return (
      <div className="card-footer">
        <div className="row">
          <div className="col"/>
          <div className="col-auto">
            <span className="mr-2"> {this.renderResetButton()} </span>
            <span> {this.renderSubmitButton(currentPV)} </span>
          </div>
        </div>
      </div>
    );
  }

  renderAttachments() {
    return (
      <ContractAttachmentsManage
        contractId={this.props.contract.id}
        addAttachmentsRoles={[roles.AccountRep]}
        isAdminView={true}
        uploadMessage={`* Document upload is required for all contract edits`}
        handleUpload={this.handleUpload}
      />
    );
  }

  handleUpload = () => {
    this.setState({attachmentUploaded:true});
  }

  renderResetButton() {
    return <button onClick={this.reset.bind(this)} disabled={!this.dirty()} className="btn btn-secondary"><i className="mr-1 fa fa-undo" /> Reset</button>;
  }

  reset(e) {
    e.preventDefault();
    this.state.form.update(() => this.initialValues());
  }

  renderSubmitButton(pv) {
    const plan = this.getPlan(pv, this.getPlanId());
    if (pv && plan && this.state.form.cursor("submitting").deref()) {
      return <button type="submit" disabled={true} className="btn btn-primary"><i className="fa fa-refresh fa-spin" /> Submitting...</button>;
    }
    return <button type="submit" disabled={!this.dirty()} className="btn btn-primary"><i className="fa fa-check" /> Submit</button>;
  }

  dirty() {
    const {
      attachmentUploaded,
    } = this.state;
    
    const original = {
      pvId: this.props.contract.product_variant.id,
      planId: this.props.contract.plan.id,
      effectiveDateChange: this.props.contract.effective_date_change,
      effectiveMileageChange: this.props.contract.effective_mileage_change,
      nonGroupOptionIds: this.initialNonGroupOptionIds().toJS(),
      groupOptionIds: this.initialGroupOptionIds().toJS(),
      surchargeIds: this.initialSurchargeIds().toJS(),
    };

    const formState = this.state.form;
    const pvId = this.getProductVariantId();
    const current = {
      pvId: pvId,
      planId: this.getPlanId(),
      effectiveDateChange: this.getEffectiveDateChange(),
      effectiveMileageChange: formState.cursor('effectiveMileageChange').deref(),
      nonGroupOptionIds: formState.cursor('nonGroupOptionIds').deref().toJS(),
      groupOptionIds: formState.cursor('groupOptionIds').deref().toJS(),
      surchargeIds: formState.cursor('surchargeIds').deref().toJS(),
    };
    return (!isEqual(original, current) && attachmentUploaded);
  }

}
