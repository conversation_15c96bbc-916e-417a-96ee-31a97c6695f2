import React from 'react';
import { Route, IndexRoute } from 'react-router';

var NotFound = require('shared/components/NotFound');
import Login from 'shared/components/Login';
import NewPasswordReset from '../shared/components/NewPasswordReset.jsx';
var Loader = require('shared/components/Loader');
import * as roles from 'shared/roles';

var Home = require('./Home.jsx');
var Forbidden = require('./Forbidden.jsx');

var RateTest = require('./rate_sheets/RateTest.jsx');

import CompaniesIndex from './companies/Index.jsx';
import CompaniesNew from './companies/New.jsx';
import CompaniesEdit from './companies/Edit.jsx';
import CompaniesView from './companies/View.jsx';
import ProductVariantsCompanies from './companies/ProductVariants.jsx';
import ReadableCompaniesForm from './companies/ReadableCompaniesForm.jsx';

import InvoicesSearchPage from 'shared/components/invoices/InvoicesSearch.jsx';

import StoresIndex from './stores/Index.jsx';
import StoresView from './stores/View.jsx';
import StoresNew from './stores/New.jsx';
import StoresEdit from './stores/Edit.jsx';
import StoresProductVariants from './stores/ProductVariants.jsx';
import StoresUsers from './stores/Users.jsx';
import CLPManagementIndex from './stores/CLP/Index.jsx';

import ProductTypesIndex from './product_types/Index.jsx';
import ProductTypesManage from './product_types/Manage.jsx';

import ProductsIndex from './products/Index.jsx';
import ProductsNew from './products/New.jsx';
import ProductsView from './products/View.jsx';
import ProductsEdit from './products/Edit.jsx';

import ProductRulesIndex from './product_rules/Index.jsx';
import ProductRulesNew from './product_rules/New.jsx';
import ProductRulesEdit from './product_rules/Edit.jsx';

import ProductVehicleComponentsIndex from './product_vehicle_components/Index.jsx';
import ProductVehicleComponentsTest from './product_vehicle_components/Test.jsx';
import ProductVehicleComponentsNew from './product_vehicle_components/New.jsx';
import ProductVehicleComponentsView from './product_vehicle_components/View.jsx';
import ProductVehicleComponentsEdit from './product_vehicle_components/Edit.jsx';

import ProductVariantsIndex from './product_variants/Index.jsx';
import ProductVariantsNew from './product_variants/New.jsx';
import ProductVariantsEdit from './product_variants/Edit.jsx';
import ProductVariantManger from './product_variants/ProductVariantManger.jsx';
import ProductVariantsStores from './product_variants/Stores.jsx';

var ClassificationListsIndex = require('./classification_lists/Index.jsx');
var ClassificationListsNew = require('./classification_lists/New.jsx');
var ClassificationListsEdit = require('./classification_lists/Edit.jsx');
import ClassificationListsLoad from './classification_lists/Load.jsx';
import ClassificationListsView from './classification_lists/View.jsx';
import ClassificationListsManage from './classification_lists/Manage.jsx';
var ClassificationListsTestVehicle = require('./classification_lists/TestVehicle.jsx');

import ClassificationsNew from './classification_lists/classifications/New.jsx';
import ClassificationsEdit from './classification_lists/classifications/Edit.jsx';
import ClassificationsLoad from './classification_lists/classifications/Load.jsx';

import RateSheets from './rate_sheets/RateSheets.jsx';

import UsersIndex from './users/Index.jsx';
import UsersNew from './users/New.jsx';
import UsersView from './users/View.jsx';
import UsersEdit from './users/Edit.jsx';

var VINDecode = require('./vins/Decode.jsx');

import VINOverridesIndex from './vin_overrides/Index.jsx';
import VINOverridesNew from './vin_overrides/New.jsx';
import VINOverridesEdit from './vin_overrides/Edit.jsx';

import DMSLookup from './dms/Lookup.jsx';

var RateBucketsIndex = require('./rate_buckets/Index.jsx');
import RateBucketsNew from './rate_buckets/New.jsx';
import RateBucketsEdit from './rate_buckets/Edit.jsx';

import AdjustmentsIndex from './adjustments/Index.jsx';
import AdjustmentsNew from './adjustments/New.jsx';
var AdjustmentView = require('./adjustments/View.jsx');
import AdjustmentEdit from './adjustments/Edit.jsx';
import AdjustmentClone from './adjustments/Clone.jsx';

import FeesIndex from './fees/Index.jsx';
import FeeView from './fees/View.jsx';
import FeeNew from './fees/New.jsx';
import FeeEdit from './fees/Edit.jsx';
import FeeClone from './fees/Clone.jsx';

import CapsIndex from './caps/Index.jsx';
var CapsView = require('./caps/View.jsx');
import CapsNew from './caps/New.jsx';
import CapsEdit from './caps/Edit.jsx';

import ContractFormsIndex from './contract_forms/Index.jsx';
import ContractFormsManage from './contract_forms/Manage.jsx';
import ContractFormsTest from './contract_forms/Test.jsx';
import ContractFormsView from './contract_forms/View.jsx';
import ContractFormsNew from './contract_forms/New.jsx';
import ContractFormsEdit from './contract_forms/Edit.jsx';
import ContractFormsStampTest from './contract_forms/StampTest.jsx';

import FormView from './forms/View.jsx';
import FormIndex from './forms/Index.jsx';
import FormNew from './forms/New.jsx';
import FormEdit from './forms/Edit.jsx';

var CouponsIndex = require('./coupons/Index.jsx');
var CouponsNew = require('./coupons/New.jsx');
var CouponsEdit = require('./coupons/Edit.jsx');
var CouponsView = require('./coupons/View.jsx');

import PricingFormulasIndex from "./pricing_formulas/Index.jsx";
import PricingFormulasNew from "./pricing_formulas/New.jsx";
import PricingFormulasEdit from "./pricing_formulas/Edit.jsx";
import PricingFormulasView from "./pricing_formulas/View.jsx";
import PricingFormulasClone from "./pricing_formulas/Clone.jsx";

var ContractCodeBanksIndex = require('./contract_code_banks/Index.jsx');
var ContractCodeBanksNew = require('./contract_code_banks/New.jsx');
var ContractCodeBanksAddCodes = require('./contract_code_banks/AddCodes.jsx');

import StoreUploadsIndex from './store_uploads/Index.jsx';
import StoreUploadsNew from './store_uploads/New.jsx';
import StoreUploadsEdit from './store_uploads/Edit.jsx';
var StoreUploadsView = require('./store_uploads/View.jsx');

import LendersIndex from './lenders/Index.jsx';
import LendersNew from './lenders/New.jsx';
import LendersEdit from './lenders/Edit.jsx';

import CancellationLendersIndex from './cancellation_lenders/Index.jsx';
import CancellationLendersNew from './cancellation_lenders/New.jsx';
import CancellationLendersEdit from './cancellation_lenders/Edit.jsx';

import GAPLicensesNew from './users/GAPLicensesNew.jsx';
import GAPLicensesLoader from './users/GAPLicensesLoader.jsx';
import GAPLicensesEdit from './users/GAPLicensesEdit.jsx';

import SalesIndex from './sales/Index.jsx';
import SalesView from './sales/View.jsx';
import SalesQuoteView from './sales/QuoteView.jsx';

import InspectionsIndex from './inspections/Index.jsx';
import InspectionsView from './inspections/View.jsx';
import InspectionsEdit from './inspections/Edit.jsx';

import VehicleComponentsIndex from './vehicle_components/Index.jsx';
import VehicleComponentsNew from './vehicle_components/New.jsx';
import VehicleComponentsEdit from './vehicle_components/Edit.jsx';
import VehicleComponentsView from './vehicle_components/View.jsx';

import StampedContractsIndex from './stamped_contracts/Index.jsx';
import BannerSettings from './banner_settings/Index.jsx';
import AlphaCancels from './alpha_cancels/Index.jsx';

import ContractsIndex from './contracts/Index.jsx';
import ContractsView from './contracts/View.jsx';

import CancellationStopRuleIndex from './cancellation_stop_rules/Index.jsx';
import CancellationStopRuleNew from './cancellation_stop_rules/New.jsx';
import CancellationStopRuleEdit from './cancellation_stop_rules/Edit.jsx';
import CancellationStopRulesView from './cancellation_stop_rules/View.jsx';

import CancelRulesIndex from './cancel_rules/Index.jsx';
import CancelRulesNew from './cancel_rules/New.jsx';
import CancelRuleEdit from './cancel_rules/Edit.jsx';
import CancelRuleView from './cancel_rules/View.jsx';
import CancelRulesTest from './cancel_rules/Test.jsx';

import ContractsSearchDashboard from './contracts_search/Index.jsx';
import Contracts from './contracts_search/Contract.jsx';
import CancellationPage from './contracts_search/Cancellation.jsx';
import ReinstatePage from './contracts_search/Reinstate.jsx';
import TransferPage from './contracts_search/Transfer';

import ContractEdit from './contracts/Edit.jsx';

import CancellationReviewContainer from './contracts_search/CancellationReviewContainer.jsx';

import ContractTransit from './contract_transit/ContractTransit.jsx';

import AccountingRulesIndex from './accounting/rules/Index.jsx';
import AccountingRulesNew from './accounting/rules/New.jsx';
import AccountingRulesView from './accounting/rules/View.jsx';
import AccountingRulesEdit from './accounting/rules/Edit.jsx';
import AccountingRulesTest from './accounting/rules/Test.jsx';
import AccountingRulesHistory from './accounting/rules/History';
import AccountingInvoicingView from './accounting/invoicing/View.jsx';
import AccountingRulesManage from './accounting/manage/Form.jsx';
import SubmitCancels from './accounting/cancel-contract/SubmitCancels.jsx';
import CancelBatchHistory from './accounting/cancel_batch_history/CancelBatchHistory';

import AccountingFeeRulesIndex from './accounting/fee_rules/Index.jsx';
import AccountingFeeRulesNew from './accounting/fee_rules/New.jsx';
import AccountingFeeRulesView from './accounting/fee_rules/View.jsx';
import AccountingFeeRulesEdit from './accounting/fee_rules/Edit.jsx';


import InternalResourcesIndex from './internal_resources/Index.jsx';
import InternalResourcesNew from './internal_resources/New.jsx';
import InternalResourcesEdit from './internal_resources/Edit.jsx';
import InternalResourcesView from './internal_resources/View.jsx';

import IssuedCLPPolicyIndex from './internal_resources/issued_clp/Index.jsx';

import { DealerSystemsIndex } from './integrations/dealer_systems/index.jsx';
import { ManageDealerSystem } from './integrations/dealer_systems/manage.jsx';
import ProductVariantsDealerSystems from './integrations/dealer_systems/ProductVariants.jsx';
import { APITest } from './api/test/test.jsx';
import { APITestQuotes } from './api/test/quotes.jsx';

import Downloads from './Downloads.jsx';
import UserReports from './users/UserReports.jsx';
import PendingLenders from './pending_lenders/PendingLenders.jsx';
import PendingLendersReport from './pending_lenders/PendingLendersReport.jsx';
import UndoTransferPage from "./contracts_search/UndoTransfer";
import JobTitleManagement from "./job_title/JobTitleManagement";
import JobTitleNew from "./job_title/JobTitleNew";
import JobTitleEdit from "./job_title/JobTitleEdit";
import JobTitleView from "./job_title/JobTitleView";

import ArticleAdd from './news-updates/New.jsx';
import ArticleEdit from './news-updates/Edit.jsx';
import ArticleView from './news-updates/View.jsx';
import NewsUpdates from './news-updates/Index.jsx';
import NewsCategories from './news-updates/categories/Index.jsx';
import { FORM_TYPES, FORM_TYPES_URL_MAP } from './forms/constant.js';
import RolesIndex from './roles/Index.jsx';
import CancelReasons from './cancel_reasons/Index.jsx';
import {LenderManagement, DigitalReservesManagement} from "shared/roles";

import CompanyGroupsIndex from './company_groups/Index.jsx';
import CompanyGroupsNew from './company_groups/New.jsx';
import CompanyGroupsEdit from './company_groups/Edit.jsx';
import CompanyGroupsView from './company_groups/View.jsx';
import CancellationsDashboard from "./cancellations_dashboard/CancellationsDashboard";
import FileReview from "./cancellations_dashboard/FileReview";

import ClpRatesIndex from './clp_rates/ClpRates.jsx';

export default [
  <IndexRoute key="index" component={Home} />,

  <Route key="inspections" path="/inspections">
    <IndexRoute component={InspectionsIndex} />
    <Route path=':id' component={Loader} baseURL={(p) => { return `/api/admin-inspections/${p.id}`; }} dataName='inspection'>
      <IndexRoute component={InspectionsView} />
      <Route path='edit' component={InspectionsEdit} roles={['account_rep_ii','account_rep_manager']} />
    </Route>
  </Route>,

  <Route key="/store-uploads" path="/store-uploads">
    <IndexRoute component={StoreUploadsIndex} />
    <Route path="new" component={StoreUploadsNew} roles={['store_uploads']} />
    <Route path=":id" component={Loader} baseURL={function (p) { return '/api/store-uploads/' + p.id; }} dataName='storeUpload'>
      <IndexRoute component={StoreUploadsView} />
      <Route path='edit' component={StoreUploadsEdit} roles={['store_uploads']} />
    </Route>
  </Route>,

  <Route key="coupons" path="/coupons">
    <IndexRoute component={CouponsIndex} />
    <Route path="new" component={CouponsNew} roles={['coupon']} />
    <Route path=":id" component={Loader} baseURL={function (p) { return '/api/coupons/' + p.id; }} dataName='coupon'>
      <IndexRoute component={CouponsView} />
      <Route path='edit' component={CouponsEdit} roles={['coupon']} />
    </Route>
  </Route>,

  <Route key="pricing-formulas" path="/pricing-formulas">
    <IndexRoute component={PricingFormulasIndex} />
    <Route path="new" component={PricingFormulasNew} roles={["product_manager"]} />
    <Route path=":id" component={Loader} baseURL={function (p) { return '/api/admin/pricing-formulas/' + p.id; }} dataName='pricing_formula'>
      <IndexRoute component={PricingFormulasView} />
      <Route path='edit' component={PricingFormulasEdit} roles={["product_manager"]} />
      <Route path='clone' component={PricingFormulasClone} roles={["product_manager"]} />
    </Route>
  </Route>,

  <Route key="clp-rates" path="/clp-rates">
    <IndexRoute component={ClpRatesIndex} />
  </Route>,

  <Route key="contract-forms" path="/contract-forms">
    <IndexRoute component={ContractFormsIndex} />
    <Route path="product-types/:id/manage" component={ContractFormsManage} roles={["product_manager"]} />
    <Route path="product-types/:id/test" component={ContractFormsTest} />
    <Route path="new" component={ContractFormsNew} roles={["product_manager"]} />
    <Route path=":id" component={Loader} baseURL={function (p) { return '/api/contract-forms/' + p.id; }} dataName='contractForm'>
      <IndexRoute component={ContractFormsView} />
      <Route path='edit' component={ContractFormsEdit} roles={["product_manager"]} />
      <Route path="stamp-test" component={ContractFormsStampTest} />
    </Route>
  </Route>,

  <Route key={`forms-${FORM_TYPES.ClpPolicyForm}`} path={`/forms/${FORM_TYPES_URL_MAP[FORM_TYPES.ClpPolicyForm]}`}>
    <IndexRoute component={FormIndex} formType={FORM_TYPES.ClpPolicyForm} />
    <Route path="new" component={FormNew} formType={FORM_TYPES.ClpPolicyForm} />
    <Route path=":id" component={Loader} baseURL={function (p) { return '/api/admin/forms/' + p.id; }} dataName='form'>
      <IndexRoute component={FormView} formType={FORM_TYPES.ClpPolicyForm} />
      <Route path='edit' component={FormEdit} formType={FORM_TYPES.ClpPolicyForm} roles={["product_manager"]} />
    </Route>
  </Route>,

  <Route key={`forms-${FORM_TYPES.EndorsementForm}`} path={`/forms/${FORM_TYPES_URL_MAP[FORM_TYPES.EndorsementForm]}`}>
    <IndexRoute component={FormIndex} formType={FORM_TYPES.EndorsementForm} />
    <Route path="new" component={FormNew} formType={FORM_TYPES.EndorsementForm} />
    <Route path=":id" component={Loader} baseURL={function (p) { return '/api/admin/forms/' + p.id; }} dataName='form'>
      <IndexRoute component={FormView} formType={FORM_TYPES.EndorsementForm} />
      <Route path='edit' component={FormEdit} formType={FORM_TYPES.EndorsementForm} roles={["product_manager"]} />
    </Route>
  </Route>,
  
  <Route key='classification-lists' path="/classification-lists">
    <IndexRoute component={ClassificationListsIndex} />
    <Route path="new" component={ClassificationListsNew} roles={["product_manager"]} />
    <Route path=":id" component={ClassificationListsLoad}>
      <IndexRoute component={ClassificationListsView} />
      <Route path="edit" component={ClassificationListsEdit} roles={["product_manager"]} />
      <Route path="manage" component={ClassificationListsManage} roles={["product_manager"]} />
      <Route path="test-vehicle" component={ClassificationListsTestVehicle} />
      <Route path="classifications">
        <Route path="new" component={ClassificationsNew} roles={["product_manager"]} />
        <Route path=":classificationID" component={ClassificationsLoad}>
          <IndexRoute component={ClassificationsEdit} roles={["product_manager"]} />
        </Route>
      </Route>
    </Route>
  </Route>,

  <Route key='login' path="/login" component={Login} noAuth={true} noHeader={true} />,
  <Route key='admin-new-password-reset' path='/new-password-reset' component={NewPasswordReset} noAuth={true} noHeader={true} />,

  <Route key='company_groups' path="/company_groups">
    <IndexRoute component={ CompanyGroupsIndex } />
    <Route path="new" component={ CompanyGroupsNew } roles={ ["product_manager"] } />
    <Route path=":id/edit" component={ CompanyGroupsEdit } roles={ ["product_manager"] } />
    <Route path=":id" component={ CompanyGroupsView } />
  </Route>,

  <Route key='companies' path="/companies">
    <IndexRoute component={ CompaniesIndex } />
    <Route path="new" component={ CompaniesNew } roles={ ["product_manager"] } />
    <Route path=":id/edit" component={ CompaniesEdit } roles={ ["product_manager"] } />
    <Route path=":id" component={ CompaniesView } />
    <Route path=":id/readable-companies" component={ Loader } baseURL={function(p){return '/api/companies/'+p.id;}} dataName='company'>
      <IndexRoute component={ ReadableCompaniesForm } roles={ ["product_manager"] } />
    </Route>
    <Route path=":id/product-variants" component={ProductVariantsCompanies} roles={["product_manager"]} />
  </Route>,

  <Route key='stores' path="/stores">
    <IndexRoute component={StoresIndex} />
    <Route path="new" component={StoresNew} roles={["dealership_management"]} />
    <Route path=":id" component={Loader} baseURL={function (p) { return '/api/stores/' + p.id; }} dataName='store'>
      <IndexRoute component={StoresView} />
      <Route path="edit" component={StoresEdit} roles={["dealership_management"]} />
      <Route path="manage" component={CLPManagementIndex} roles={["product_manager"]} />
      <Route path="product-variants" component={StoresProductVariants} roles={["product_manager"]} />
      <Route path="users" component={StoresUsers} roles={["product_manager", "dealership_management", "admin_view"]} />
    </Route>
  </Route>,

  <Route key="product-types" path="/product-types">
    <IndexRoute component={ProductTypesIndex} />
    <Route path=":id" component={Loader} baseURL={function (p) { return '/api/product-types/' + p.id; }} dataName="product_type" propName="productType">
      <Route path="manage" component={ProductTypesManage} roles={["product_manager"]} />
      <Route path="cancellation-stop-rules">
        <IndexRoute component={CancellationStopRuleIndex} />
        <Route path="new" component={CancellationStopRuleNew} roles={["product_manager"]} />
        <Route path=":cancellation_stop_rule_id" component={Loader} baseURL={function (p) { return '/api/cancel-stop-rules/' + p.cancellation_stop_rule_id; }} dataName='cancel_stop_rule' propName='cancelStopRule'>
          <IndexRoute component={CancellationStopRulesView} roles={["admin_view", "product_manager"]} />
          <Route path="edit" component={CancellationStopRuleEdit} roles={["product_manager"]} />
        </Route>
      </Route>
      <Route path="cancel-rules">
        <IndexRoute component={CancelRulesIndex} />
        <Route path="new" component={CancelRulesNew} roles={[roles.ProductManager]} />
        <Route path="test" component={CancelRulesTest} roles={[roles.ProductManager]} />
        <Route path=":cancel_rule_id" component={Loader} baseURL={function (p) { return '/api/cancel-rules/' + p.cancel_rule_id; }} dataName='cancel_rule' propName='cancelRule'>
          <Route path="edit" component={CancelRuleEdit} roles={[roles.ProductManager]} />
          <Route path="view" component={CancelRuleView} roles={[roles.ProductManager, roles.AdminView]} />
        </Route>
      </Route>
    </Route>
  </Route>,

  <Route key='products' path="/products">
    <IndexRoute component={ProductsIndex} />
    <Route path="new" component={ProductsNew} roles={["product_manager"]} />
    <Route path=":id" component={Loader} baseURL={function (p) { return '/api/products/' + p.id; }} dataName='product'>
      <IndexRoute component={ProductsView} />
      <Route path="edit" component={ProductsEdit} roles={["product_manager"]} />
      <Route path="product-rules">
        <IndexRoute component={ProductRulesIndex} />
        <Route path="new" component={ProductRulesNew} roles={["product_manager"]} />
        <Route path=":product_rule_id" component={Loader} baseURL={function (p) { return '/api/product-rules/' + p.product_rule_id; }} dataName='product_rule' propName='productRule'>
          <Route path="edit" component={ProductRulesEdit} roles={["product_manager"]} />
        </Route>
      </Route>
      <Route path="vehicle-components">
        <IndexRoute component={ProductVehicleComponentsIndex} />
        <Route path="test" component={ProductVehicleComponentsTest} />
        <Route path="new" component={ProductVehicleComponentsNew} roles={["product_manager"]} />
        <Route path=":pvcl_id" component={Loader} baseURL={(p) => { return `/api/product-vehicle-component-lists/${p.pvcl_id}`; }} dataName="product_vehicle_component_list" propName="pvcl">
          <IndexRoute component={ProductVehicleComponentsView} />
          <Route path="edit" component={ProductVehicleComponentsEdit} roles={["product_manager"]} />
        </Route>
      </Route>
    </Route>
  </Route>,

  <Route key='product-variants' path="/product-variants">
    <IndexRoute component={ProductVariantsIndex} />
    <Route path="new" component={ProductVariantsNew} roles={["product_manager"]} />
    <Route path="stores" component={ProductVariantManger} roles={["product_manager"]} />
    <Route path=":id" component={Loader} baseURL={function (p) { return '/api/product-variants/' + p.id; }} dataName='product_variant' propName='productVariant'>
      <Route path="edit" component={ProductVariantsEdit} roles={["product_manager"]} />
      <Route path="rate-sheets" component={RateSheets} roles={["product_manager"]} />
      <Route path="stores" component={ProductVariantsStores} roles={["product_manager"]} />
    </Route>
  </Route>,

  <Route key='rate-sheets' path='/rate-sheets'>
    <Route path=':id/test' component={RateTest} />,
  </Route>,

  <Route key='users' path="/users">
    <IndexRoute component={UsersIndex} />
    <Route path="new" component={UsersNew} roles={["user_provisioning"]} />
    <Route path=":id" component={Loader} baseURL={function (p) { return '/api/users/' + p.id; }} dataName='user'>
      <IndexRoute component={UsersView} />
      <Route path="edit" component={UsersEdit} roles={["user_provisioning"]} />
      <Route path="gap-licenses">
        <Route path="new" component={GAPLicensesNew} roles={["gap_license_manager"]} />
        <Route path=":gap_license_id" component={GAPLicensesLoader}>
          <Route path="edit" component={GAPLicensesEdit} roles={["gap_license_manager"]} />
        </Route>
      </Route>,
    </Route>
  </Route>,

  <Route key='vin-overrides' path='vin-overrides'>
    <IndexRoute component={VINOverridesIndex} />
    <Route path="new" component={VINOverridesNew} roles={["product_manager"]} />
    <Route path=":id/edit" component={VINOverridesEdit} roles={["product_manager"]} />
  </Route>,

  <Route key='decode-vin' path='/vin-decode'>
    <IndexRoute component={VINDecode} />
  </Route>,

  <Route key='dms-lookup' path='/dms-lookup'>
    <IndexRoute component={DMSLookup} />
  </Route>,

  <Route key='rate-buckets' path='/rate-buckets'>
    <IndexRoute component={RateBucketsIndex} />
    <Route path="new" component={RateBucketsNew} roles={["product_manager"]} />
    <Route path=":id" component={Loader} baseURL={function (p) { return '/api/rate-buckets/' + p.id; }} dataName='rate_bucket' propName='rateBucket'>
      <Route path="edit" component={RateBucketsEdit} roles={["product_manager"]} />
    </Route>
  </Route>,

  <Route key='adjustments' path='/adjustments'>
    <IndexRoute component={AdjustmentsIndex} />
    <Route path="new" component={AdjustmentsNew} roles={["product_manager"]} />
    <Route path=":id" component={Loader} baseURL={function (p) { return '/api/adjustments/' + p.id; }} dataName='adjustment'>
      <IndexRoute component={AdjustmentView} />
      <Route path="edit" component={AdjustmentEdit} roles={["product_manager"]} />
      <Route path="clone" component={AdjustmentClone} roles={["product_manager"]} />
    </Route>
  </Route>,

  <Route key='caps' path='/caps'>
    <IndexRoute component={CapsIndex} />
    <Route path="new" component={CapsNew} roles={["product_manager"]} />
    <Route path=":id" component={Loader} baseURL={function (p) { return '/api/caps/' + p.id; }} dataName='cap'>
      <IndexRoute component={CapsView} />
      <Route path="edit" component={CapsEdit} roles={["product_manager"]} />
    </Route>
  </Route>,

  <Route key='fees' path='/fees'>
    <IndexRoute component={FeesIndex} />
    <Route path="new" component={FeeNew} roles={["product_manager"]} />
    <Route path=":id" component={Loader} baseURL={function (p) { return '/api/fees/' + p.id; }} dataName='fee'>
      <IndexRoute component={FeeView} />
      <Route path="edit" component={FeeEdit} roles={["product_manager"]} />
      <Route path="clone" component={FeeClone} roles={["product_manager"]} />
    </Route>
  </Route>,

  <Route key='contract-code-banks' path='/contract-code-banks'>
    <IndexRoute component={ContractCodeBanksIndex} />
    <Route path="new" component={ContractCodeBanksNew} roles={["product_manager"]} />
    <Route path=":id" component={Loader} baseURL={function (p) { return '/api/contract-code-banks/' + p.id; }} dataName='contract_code_bank' propName='contractCodeBank'>
      <Route path="add-codes" component={ContractCodeBanksAddCodes} roles={["product_manager"]} />
    </Route>
  </Route>,

  <Route key='lenders' path='/lenders'>
    <IndexRoute component={LendersIndex} />
    <Route path="new" component={LendersNew} roles={[LenderManagement]} />
    <Route path=":id" component={Loader} baseURL={function (p) { return '/api/lenders/' + p.id; }} dataName='lender'>
      <Route path="edit" component={LendersEdit} roles={[LenderManagement, DigitalReservesManagement]} />
    </Route>
  </Route>,

  <Route key='cancellation_lenders' path='/cancellation-lenders'>
    <IndexRoute component={CancellationLendersIndex} />
    <Route path="new" component={CancellationLendersNew} roles={[LenderManagement]} />
    <Route path=":id" component={Loader} baseURL={function (p) { return '/api/cancellation-lenders/' + p.id; }} dataName='cancellation_lender'>
      <Route path="edit" component={CancellationLendersEdit} roles={[LenderManagement]} />
    </Route>
  </Route>,

  <Route key="sales" path="/sales">
    <IndexRoute component={SalesIndex} />
    <Route path=":id/quote" component={Loader} baseURL={function (p) { return `/api/admin/sales/${p.id}/quote`; }} dataName='sale'>
      <IndexRoute component={SalesQuoteView} />
    </Route>
    <Route path=":id" component={Loader} baseURL={function (p) { return `/api/admin/sales/${p.id}`; }} dataName='sale'>
      <IndexRoute component={SalesView} />
    </Route>
  </Route>,

  <Route key="vehicle-components" path="/vehicle-components">
    <IndexRoute component={VehicleComponentsIndex} />
    <Route path="new" component={VehicleComponentsNew} roles={["vehicle_component_manager"]} />
    <Route path=":id" component={Loader} baseURL={(p) => { return `/api/admin/vehicle-components/${p.id}`; }} dataName="vehicle_component" propName="vehicleComponent">
      <IndexRoute component={VehicleComponentsView} />
      <Route path="edit" component={VehicleComponentsEdit} roles={["vehicle_component_manager"]} />
    </Route>
  </Route>,

  <Route key="stamped-contracts" path="/stamped-contracts">
    <IndexRoute component={StampedContractsIndex} />
  </Route>,

  <Route key="banner-settings" path="/banner">
    <IndexRoute component={BannerSettings} />
  </Route>,

  <Route key="news-updates" path="/news-updates">
    <IndexRoute component={NewsUpdates} />
    <Route path="categories" component={NewsCategories} roles={[roles.NewsUpdatesManager]} />
    <Route path="article/new" component={ArticleAdd} roles={[roles.NewsUpdatesManager]} />
    <Route path="article/:id" component={Loader} baseURL={p => `/api/admin/news-updates/articles/${p.id}`} dataName='article' propName='article'>
      <IndexRoute component={ArticleView} />
      <Route path="edit" component={ArticleEdit} roles={[roles.Controller]} />
    </Route>
  </Route>,

  <Route key="alpha-cancels" path="/alpha-cancels">
    <IndexRoute component={AlphaCancels} />
  </Route>,  

  <Route key="contracts" path="/contracts">
    <IndexRoute component={ContractsIndex} />
    <Route path=":code" component={Loader} baseURL={(p) => { return `/api/admin/contracts/${p.code}`; }} dataName="contract">
      <IndexRoute component={ContractsView} />
      <Route path="edit" component={Loader} baseURL={p => `/api/admin/contracts/${p.code}/edit`} dataName="data" propName="">
        <IndexRoute component={ContractEdit} roles={[roles.AccountRep]} />
      </Route>
    </Route>
  </Route>,

  <Route key="contracts-search" path="/contracts-search">
    <IndexRoute component={ContractsSearchDashboard} />
    <Route path=":contract_code">
      <IndexRoute component={Contracts} />
      <Route path="cancellation" component={CancellationPage} roles={[roles.AccountRep]} />
      <Route path="reinstate" component={ReinstatePage} roles ={[roles.AccountRep,roles.AccountRepII]} />
      <Route path="transfer" component={TransferPage} roles={[roles.AccountRep]} />
      <Route path="undo-transfer" component={UndoTransferPage} roles={[roles.AccountRepManager]} />
      <Route path="cancellation-review" component={CancellationReviewContainer} roles={[roles.AccountRep]} />
    </Route>
  </Route>,

  <Route key="accounting" path="accounting">
    <Route key="rules" path="rules">
      <IndexRoute component={AccountingRulesIndex} />
      <Route path="new" component={AccountingRulesNew} roles={[roles.Controller]} />
      <Route path="test" component={AccountingRulesTest} roles={[roles.Controller, roles.ProductManager]} />
      <Route path="history" component={AccountingRulesHistory} roles={[roles.Controller]} />
      <Route path=":id" component={Loader} baseURL={p => `/api/admin/accounting/rules/${p.id}`} dataName='accounting_rule' propName=''>
        <IndexRoute component={AccountingRulesView} />
        <Route path="edit" component={AccountingRulesEdit} roles={[roles.Controller]} />
        <Route path="test" component={AccountingRulesTest} roles={[roles.Controller]} />
      </Route>
    </Route>
    <Route key="fee-rules" path="fee-rules">
      <IndexRoute component={AccountingFeeRulesIndex} />
      <Route path="new" component={AccountingFeeRulesNew} roles={[roles.Controller]} />
      <Route path=":id" component={Loader} baseURL={p => `/api/admin/accounting/fee-rules/${p.id}`} dataName='accounting_fee_rule' propName=''>
        <IndexRoute component={AccountingFeeRulesView} />
        <Route path="edit" component={AccountingFeeRulesEdit} roles={[roles.Controller]} />
      </Route>
    </Route>
    <Route path="invoices" component={(props) => <InvoicesSearchPage isAdmin={true} {...props} parentRoute={"accounting"} />}/>
    <Route path="invoicing" component={AccountingInvoicingView} roles={[roles.Controller, roles.ProductManager]} />
    <Route path="manage" component={AccountingRulesManage} roles={[roles.ControllerAdmin]} />
    <Route path="submit-cancels" component={SubmitCancels} roles={[roles.AccountingCancellationHandler]} />
    <Route path="cancel-batch-history" component={(props) => <CancelBatchHistory {...props} />} roles={[roles.AccountingCancellationHandler]} />
  </Route>,


  <Route key="contracts-in-transit" path="/contracts-in-transit">
    <IndexRoute component={ContractTransit} />
  </Route>,
  <Route key="cancellations-dashboard" path="/cancellations-dashboard">
    <IndexRoute component={CancellationsDashboard} />
    <Route key="cancellation" path="cancellation">
      <Route path=":id">
        <IndexRoute component={(props) => <FileReview {...props}/>} />
      </Route>
    </Route>
  </Route>,

  <Route key="/internal-resources" path="/internal-resources">
    <IndexRoute component={InternalResourcesIndex} />
    <Route path="new" component={InternalResourcesNew} roles={['internal_resources']} />
    <Route path=":id" component={Loader} baseURL={function (p) { return '/api/internal-resources/' + p.id; }} dataName='internalResource'>
      <IndexRoute component={InternalResourcesView} />
      <Route path='edit' component={InternalResourcesEdit} roles={['internal_resources']} />
    </Route>
  </Route>,
  
  <Route key='internal-resources-list' path='internal-resources-list' component={Downloads} />,

  <Route key="issued-clp-policy" path='issued-clp-policy' component={Loader} baseURL={function () { return '/api/admin/issued-clp-policy'; }} dataName='policies'>
    <IndexRoute component={IssuedCLPPolicyIndex} />
  </Route>,

  <Route key='dealer-systems' path='/dealer-systems'>
    <IndexRoute component={DealerSystemsIndex} />
    <Route path=":id">
      <Route path='manage' component={ManageDealerSystem} roles={[roles.AdminView]} />
      <Route path="product-variants" component={ProductVariantsDealerSystems} roles={["product_manager"]} />
    </Route>
  </Route>,

  <Route key='pending-lenders' path='/pending-lenders'>
    <IndexRoute component={PendingLenders} />
    <Route path=":id">
      <Route path='manage' component={PendingLenders} roles={[roles.AdminView]} />
    </Route>
  </Route>,

  <Route key='pending-lenders-report' path='/pending-lenders-report'>
    <IndexRoute component={PendingLendersReport} />
    <Route path=":id">
      <Route path='manage' component={PendingLendersReport} roles={[roles.AdminView]} />
    </Route>
  </Route>,

  <Route key="user-reports" path="/user-reports">
    <IndexRoute component={UserReports} />
  </Route>,

  <Route key='job-title-management' path='/job-title-management' roles={[roles.AdminView, roles.JobTitleManagementRole]}>
    <IndexRoute component={JobTitleManagement} />
    <Route path="new" component={JobTitleNew}  roles={[roles.JobTitleManagementRole]} />
    <Route path=":id" component={Loader} baseURL={p => `/api/job-title/${p.id}`} dataName='job_title'>
      <Route path="edit" component={JobTitleEdit} roles={[roles.JobTitleManagementRole]} />
      <Route path="view" component={JobTitleView} roles={[roles.AdminView,  roles.JobTitleManagementRole]} />
    </Route>
  </Route>,
  
  <Route key='roles' path='/roles' roles={[roles.AdminView, roles.JobTitleManagementRole]}>
    <IndexRoute component={RolesIndex} />
  </Route>,

  <Route key='cancel-reasons' path='/cancel-reasons' roles={[roles.AdminView, roles.AccountRepManager]}>
    <IndexRoute component={CancelReasons} />
  </Route>,

  <Route key='api' path='/api'>
    <Route path="test" >
      <IndexRoute component={APITest} roles={[roles.ProductManager]} />
      <Route  path="quotes" component={APITestQuotes} roles={[roles.ProductManager]} />
    </Route>
  </Route>,

  <Route key="forbidden" path="/forbidden" component={Forbidden} noAuth={true} noHeader={true} />,
  <Route key="notFound" path="*" component={NotFound} noAuth={true} noHeader={true} />,
  
];
