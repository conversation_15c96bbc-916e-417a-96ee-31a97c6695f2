import React from 'react';
import PropTypes from 'prop-types';
import {Link} from 'react-router';
import * as Roles from "../shared/roles";
import { FORM_TYPES, FORM_TYPES_URL_MAP } from './forms/constant.js';

export default class Header extends React.Component {
  static displayName = 'Header';

  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  static propTypes = {
    sessionLoaded: PropTypes.bool.isRequired,
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }),
    }),
    logoutUser: PropTypes.func,
  };

  componentDidMount() {
    document.addEventListener('dragstart', this.hideNav);
    document.addEventListener('dragend', this.showNav);
  }

  componentWillUnmount() {
    document.removeEventListener('dragstart', this.hideNav);
    document.removeEventListener('dragend', this.showNav);
  }

  hideNav = (e) => {
    this.navEle.style.display = 'none';
  }

  showNav = (e) => {
    this.navEle.style.display = 'flex';
  }

  isAccountingActive() {
    var a = this.context.router.isActive;
    return a("/accounting/rules") ||
      a("/accounting/invoices") ||
      a("/accounting/manage") ||
      a("/accounting/invoices/submit-cancels") ||
      a("/accounting/cancel-batch-history");
  }

  isContractActive() {
    var a = this.context.router.isActive;
    return a("/contracts-search") ||
      a("/contracts-in-transit") ||
      a("/cancellations-dashboard");
  }
  
  isProductActive() {
    var a = this.context.router.isActive;
    return (
      a("/classification-lists") ||
      a("/product-types") ||
      a("/products") ||
      a("/product-variants") ||
      a("/rate-buckets") ||
      a("/rate-sheets") ||
      a("/adjustments") ||
      a("/product-rules") ||
      a("/contract-forms") ||
      a(`/forms/${FORM_TYPES_URL_MAP[FORM_TYPES.ClpPolicyForm]}`) ||
      a(`/forms/${FORM_TYPES_URL_MAP[FORM_TYPES.EndorsementForm]}`) ||
      a("/contract-code-banks") ||
      a("/caps") ||
      a("/coupons") ||
      a("/pricing-formulas") ||
      a("/clp-rates")
    );
  }

  isUserActive() {
    var a = this.context.router.isActive;
    return a("/users") ||
      a("/user-reports") ||
      a("/stores") ||
      a("/companies") ||
      a("/company_groups") ||
      a("/lenders") ||
      a("/cancellation-lenders") ||
      a("/dealer-systems") ||
      a("/pending-lenders") ||
      a("/pending-lenders-report") || 
      a("/job-title-management") ||
      a("/roles");
    
  }

  isVehicleActive() {
    var a = this.context.router.isActive;
    return a("/vin-decode") ||
      a("vin-overrides") ||
      a("inspections") ||
      a('/vehicle-components');
  }

  isToolsActive() {
    var a = this.context.router.isActive;
    return a("/dms-lookup") ||
      a('/store-uploads') ||
      a('/sales') ||
      a('/stamped-contracts') ||
      a('/news-updates') ||
      a('/contracts');
  }

  isInternalResourcesActive() {
    var a = this.context.router.isActive;
    return a("/internal-resources") ||
      a('/issued-clp-policy');
  }

  isSessionActive() {
    if (!this.props.sessionLoaded) {
      return false;
    }
    return this.context.router.isActive("/users/" + this.props.user.id);
  }

  render() {
    return (
      <nav className="navbar navbar-expand-md fixed-top navbar-dark bg-dark sticky-top d-print-none" ref={ (ele)=> { this.navEle = ele; } }>
        <a href="/" className="navbar-brand">
          <img src='/static/img/TCA-logo.svg' alt='logo' id='nav-logo' />
        </a>
        <div className="navbar-nav mr-auto">
          <div className="nav-item dropdown">
            <button type="button" data-toggle="dropdown" className={ "btn btn-link nav-link dropdown-toggle" + (this.isAccountingActive() ? ' active' : '' ) }>Accounting Mgmt</button>
            <div className="dropdown-menu">
              <Link to="/accounting/rules" className="dropdown-item">Accounting Rules</Link>
              <Link to="/accounting/fee-rules" className="dropdown-item">Accounting Fee Rules</Link>
              <Link to="/accounting/invoices" className="dropdown-item">Invoices</Link>
              {this.renderAccountingMgmtManageLink()}
              {this.renderSubmitCancelsLink()}
              {this.renderCancelBatchHistoryLink()}
            </div>
          </div>
          <div className="nav-item dropdown">
            <button type="button" data-toggle="dropdown" className={ "btn btn-link nav-link dropdown-toggle" + (this.isContractActive() ? ' active' : '' ) }>Contract Mgmt</button>
            <div className="dropdown-menu">
              <Link to="/contracts-search" className="dropdown-item">Contracts</Link>
              <Link to="/contracts-in-transit" className="dropdown-item">CIT</Link>
              <Link to="/cancellations-dashboard" className="dropdown-item">Cancellations Dashboard</Link>
            </div>
          </div>
          <div className="nav-item dropdown">
            <button type="button" data-toggle="dropdown" className={ "btn btn-link nav-link dropdown-toggle" + (this.isProductActive() ? ' active' : '' ) }>Product Mgmt</button>
            <div className="dropdown-menu">
              <Link to="/product-types" className='dropdown-item'>Product Types</Link>
              <Link to="/products" className='dropdown-item'>Products</Link>
              <Link to="/product-variants" className='dropdown-item'>Product Variants</Link>
              <Link to="/classification-lists" className='dropdown-item'>Classifications</Link>
              <Link to="/rate-buckets" className='dropdown-item'>Rate Buckets</Link>
              <Link to="/adjustments" className='dropdown-item'>Adjustments</Link>
              <Link to="/caps" className='dropdown-item'>Caps</Link>
              <Link to="/fees" className='dropdown-item'>Fees</Link>
              <Link to="/contract-forms" className='dropdown-item'>Contract Forms</Link>
              <Link to={`/forms/${FORM_TYPES_URL_MAP[FORM_TYPES.ClpPolicyForm]}`} className='dropdown-item'>CLP Form Management</Link>
              <Link to={`/forms/${FORM_TYPES_URL_MAP[FORM_TYPES.EndorsementForm]}`} className='dropdown-item'>Endorsement Form Management</Link>
              <Link to="/contract-code-banks" className='dropdown-item'>Contract Code Banks</Link>
              <Link to="/coupons" className='dropdown-item'>Coupons</Link>
              <Link to="/pricing-formulas" className='dropdown-item'>Pricing Formulas</Link>
              <Link to="/clp-rates" className='dropdown-item'>CLP Rates</Link>
            </div>
          </div>
          <div className="nav-item dropdown">
            <button type="button" data-toggle="dropdown" className={ "btn btn-link nav-link dropdown-toggle" + (this.isUserActive() ? ' active' : '' ) }>User Mgmt</button>
            <div className="dropdown-menu">
              <Link to='/users' className='dropdown-item'>Users</Link>
              <Link to='/user-reports' className='dropdown-item'>User Reports</Link>
              <Link to='/stores' className='dropdown-item'>Stores</Link>
              <Link to='/companies' className='dropdown-item'>Companies</Link>
              <Link to='/company_groups' className='dropdown-item'>Company Groups</Link>
              <Link to='/lenders' className='dropdown-item'>Lenders</Link>
              <Link to='/cancellation-lenders' className='dropdown-item'>Cancellation Lenders</Link>
              <Link to='/dealer-systems' className='dropdown-item'>Dealer Systems</Link>
              <Link to='/pending-lenders' className='dropdown-item'>Pending Lenders</Link>
              <Link to='/pending-lenders-report' className='dropdown-item'>Pending Lenders Report</Link>
              <Link to='/job-title-management' className='dropdown-item'>Job Title Management</Link>
              <Link to='/roles' className='dropdown-item'>User Roles</Link>
              <Link to='/asbury' className='dropdown-item'>Asbury API</Link>
            </div>
          </div>
          <div className="nav-item dropdown">
            <button type="button" data-toggle="dropdown" className={ "btn btn-link nav-link dropdown-toggle" + (this.isVehicleActive() ? ' active' : '' ) }>Vehicle Mgmt</button>
            <div className="dropdown-menu">
              <Link to='/vin-decode' className='dropdown-item'>Decode VIN</Link>
              <Link to='/vin-overrides' className='dropdown-item'>VIN Overrides</Link>
              <Link to='/vehicle-components' className='dropdown-item'>Vehicle Components</Link>
              <Link to='/inspections' className='dropdown-item'>Inspections</Link>
            </div>
          </div>
          <div className="nav-item dropdown">
            <button type="button" data-toggle="dropdown" className={ "btn btn-link nav-link dropdown-toggle" + (this.isToolsActive() ? ' active' : '') }>Tools</button>
            <div className="dropdown-menu">
              <Link to='/dms-lookup' className='dropdown-item'>DMS Lookup</Link>
              <Link to='/sales' className='dropdown-item'>Sale Details</Link>
              <Link to='/store-uploads' className='dropdown-item'>Store Uploads</Link>
              <Link to="/stamped-contracts" className="dropdown-item">Stamped Contracts</Link>
              <Link to="/contracts" className="dropdown-item">Contracts</Link>
              <Link to="/banner" className="dropdown-item">Banner Settings</Link>
              { this.renderNewsAndUpdateLink() }
              <Link to="/alpha-cancels" className="dropdown-item">Alpha Cancels</Link>
              <Link to='/cancel-reasons' className='dropdown-item'>Cancel Reasons</Link>
            </div>
          </div>
          <div className="nav-item dropdown">
            <button type="button" data-toggle="dropdown" className={ "btn btn-link nav-link dropdown-toggle" + (this.isInternalResourcesActive() ? ' active' : '') }>Internal Resources</button>
            <div className="dropdown-menu">
              <Link to='/internal-resources' className='dropdown-item'>Internal Resources</Link>
              <Link to='/internal-resources-list' className='dropdown-item'>List</Link>
              <Link to='/issued-clp-policy' className='dropdown-item'>Issued CLP Policy</Link>
            </div>
          </div>
        </div>
        <div className="navbar-nav">
          <div className="nav-item dropdown">
            { this.renderUser() }
            <div className="dropdown-menu">
              { this.renderProfileLink() }
              <button type="button" className="btn btn-link dropdown-item" onClick={ this.props.logoutUser }>Logout</button>
            </div>
          </div>
        </div>
      </nav>
    );
  }

  renderUser() {
    if (!this.props.sessionLoaded) {
      return (
        <button type="button" data-toggle="dropdown" className={ "btn btn-link nav-link dropdown-toggle" + (this.isSessionActive() ? ' active' : '' ) }>
          <i className="fa fa-user" /> Session <i className='fa fa-caret-down'></i>
        </button>
      );
    }
    return (
      <button type="button" data-toggle="dropdown" className={ "btn btn-link nav-link dropdown-toggle" + (this.isSessionActive() ? ' active' : '' ) }>
        <i className="fa fa-user" /> { this.props.user.first_name } { this.props.user.last_name }
      </button>
    );
  }

  renderProfileLink() {
    if (this.props.sessionLoaded) {
      return <Link to={ { pathname: "/users/" + this.props.user.id } } className="dropdown-item">Profile</Link>;
    }
  }
  
  renderNewsAndUpdateLink() {
    return Roles.UserHasAnyRole(this.props, [Roles.NewsUpdatesManager]) &&
        <Link to="/news-updates" className="dropdown-item">News Updates</Link>;
  }

  renderAccountingMgmtManageLink() {
    return Roles.UserHasAnyRole(this.props, [Roles.ControllerAdmin]) &&
        <Link to="/accounting/manage" className="dropdown-item">Manage</Link>;
  }

  renderSubmitCancelsLink() {
    return Roles.UserHasAnyRole(this.props, [Roles.AccountingCancellationHandler]) &&
      <Link to="/accounting/submit-cancels" className="dropdown-item">Submit Cancels</Link>;
  }

  renderCancelBatchHistoryLink() {
    return Roles.UserHasAnyRole(this.props, [Roles.AccountingCancellationHandler]) &&
      <Link to="/accounting/cancel-batch-history" className="dropdown-item">Cancel Batch History</Link>;
  }
}
