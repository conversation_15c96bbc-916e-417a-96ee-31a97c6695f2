import React, {useEffect, useState} from 'react';
import moment from "moment";
import PropTypes from "prop-types";
import Select from "react-select";
import DatePicker from "react-datepicker";
import Alert from "react-s-alert";
import { Loading } from "../../shared/components";
import PageHeader from "../../app/pageHeader/PageHeader";
import Table from "../../shared/components/Table";
import {jsonPromise as ajax} from "../../shared/ajax";
import Attachments, {Status} from "../../shared/components/Attachments";
import ConfirmModal from "../../shared/components/ConfirmModal";
import hstore from 'shared/hstore';
import * as Roles from 'shared/roles';
import * as Context from 'shared/context';

const URL_DATE_FORMAT = "YYYYMMDD";
const API_DATE_FORMAT = "YYYY-MM-DDTHH:mm:ss[Z]";

CancellationsDashboard.contextTypes = {
  router: PropTypes.object.isRequired,
  user: Context.User,
};

CancellationsDashboard.propTypes = {
  location: PropTypes.shape({
    query: PropTypes.shape({
      search_text: PropTypes.string,
      status: PropTypes.string,
      begin_date: PropTypes.string,
      end_date: PropTypes.string,
    }).isRequired,
  }).isRequired
};

const REQUEST_STATUS_PROCESSING = 'Processing';
const REQUEST_STATUS_PROCESSED = 'Processed';
const REQUEST_STATUS_CANCELLED = 'Cancelled';
const REQUEST_STATUS_FAILED = 'Failed';

export default function CancellationsDashboard(props, context) {
  const [requests, setRequests] = useState([]);
  const [filters, setFilters] = useState({
    searchText: '',
    status: '',
    beginDate: null,
    endDate: null,
  });
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [attachments, setAttachments] = useState([]);
  const [attachmentStatus, setAttachmentStatus] = useState(Status.READY);
  const [requestToDelete, setRequestToDelete] = useState('');
  const [selectedRequests, setSelectedRequests] = useState([]);
  const [assignToUsers, setAssignToUsers] = useState([]);
  const [reassignedToUser, setReassignedToUser] = useState({});
  const [deleteNote, setDeleteNote] = useState('');

  useEffect(() => {
    document.title = 'TCA Admin | Cancellations Dashboard';

    const { search_text, status, begin_date, end_date } = props.location.query;
    setFilters({
      searchText: search_text ?? '',
      status: status ?? '',
      beginDate: begin_date ? moment(begin_date) : null,
      endDate: end_date ? moment(end_date) : null,
    });
    getAllFiles();
  }, [props.location.query]);

  useEffect(() => {
    // TODO: Replace the polling logic with a websocket
    const interval = setInterval(getAllFiles, 90000);

    return () => clearInterval(interval);
  }, [props.location.query]);

  const getAllFiles = () => {
    const { search_text, status, begin_date, end_date } = props.location.query;
    let url = '/api/admin/cancellations-dashboard/cancellations?';
    if (search_text !== '' && search_text !== undefined) {
      url += `search_text=${search_text}`;
    }
    if (status !== '' && status !== undefined) {
      url += `&status=${status}`;
    }
    if (begin_date !== undefined) {
      url += `&begin_date=${moment(begin_date).startOf('day').utc(true).format(API_DATE_FORMAT)}`;
    }
    if (end_date !== undefined) {
      url += `&end_date=${moment(end_date).endOf('day').utc(true).format(API_DATE_FORMAT)}`;
    }
    ajax(url, {}, {}).then(results => {
      if (results.status !== 200) {
        Alert.error(`${results.data.message || `Unexpected error in getting cancellation requests data`}`);
      } else {
        setRequests(results.data.requests);
        setAssignToUsers(results.data.users);
      }
    }).catch(() => {
      Alert.error(`Failed to fetch cancellation requests`);
    }).finally(() => {
      setLoading(false);
    });
  };
  
  const handleDeleteRequest = () => {
    ajax(`/api/admin/cancellations-dashboard/cancellation/${requestToDelete}/delete`, {note:deleteNote}, {method: 'PUT'})
      .then(results => {
        if (results.status !== 200) {
          Alert.error(`${results.data.message || `Unexpected error in deleting request`}`);
        } else {
          Alert.success(`Request deleted successfully`);
          setRequestToDelete('');
          getAllFiles();
        }
      }).catch((e) => {
        Alert.error(`Exception ${e}`);
      });
  };

  const handleInputChange = (inputType, value) => {
    const updatedFilters = {
      ...filters,
      [inputType]: value,
    };
    setFilters(updatedFilters);

  };

  const handleDateChange = (inputType, date) => {
    if (inputType === 'beginDate' && filters.endDate && date?.isAfter(filters.endDate)) {
      Alert.error('Begin date cannot be after end date');
      return;
    } else if (inputType === 'endDate' && filters.beginDate && date?.isBefore(filters.beginDate)) {
      Alert.error('End date cannot be before begin date');
      return;
    }
    const updatedFilters = {
      ...filters,
      [inputType]: date,
    };
    setFilters(updatedFilters);
  };
  
  const handleClear = () => {
    const url = { pathname: '/cancellations-dashboard' };
    context.router.push(url);
    setSelectedRequests([]);
    setReassignedToUser({id: '', name: ''});
  };
  
  const handleSearch = () => {
    const query = {};
    if (filters.searchText !== '') {
      query.search_text = filters.searchText;
    }
    if (filters.status !== '') {
      query.status = filters.status;
    }
    if (filters.beginDate !== null) {
      query.begin_date = filters.beginDate.format(URL_DATE_FORMAT);
    }
    if (filters.endDate !== null) {
      query.end_date = filters.endDate.format(URL_DATE_FORMAT);
    }
    const url = { pathname: '/cancellations-dashboard', query: query };
    context.router.push(url);
    setSelectedRequests([]);
    setReassignedToUser({id: '', name: ''});
  };

  const handleResubmit = (id) => {
    ajax(`/api/admin/cancellations-dashboard/cancellation/${id}/resubmit`, {}, {method: 'POST'})
      .then(results => {
        if (results.status !== 200) {
          Alert.error(`${results.data.message || `Unexpected error in resubmitting request`}`);
        } else {
          Alert.success(`Request resubmitted successfully`);
          setRequests(requests.map(request =>
            request.id === id
              ? {...request, status: REQUEST_STATUS_PROCESSING}
              : request
          ));
          getAllFiles();
        }
      }).catch((e) => {
        Alert.error(`Exception ${e}`);
      });
  };

  const renderAssignTo = () => {
    let hasRole = hstore.has(context.user.roles, Roles.CancelDashboardManager);
    return (
      <select id='claim-owner'
        className="form-control"
        value={selectedRequests.length > 0 ? reassignedToUser.id: ''}
        disabled={!hasRole || uploading || selectedRequests.length === 0}
        onChange={(e) => {
          let user = assignToUsers.find(u => u.id == e.target.value);
          setReassignedToUser({id: e.target.value, name: `${user.first_name} ${user.last_name}`});
        }}>
        <option value=''>Assign To</option>
        {assignToUsers.map((u)=> {
          return (<option value={u.id} key={u.id}>{`${u.first_name} ${u.last_name}`}</option>);})}
      </select>
    );   
  };

  const assignToUser = (userID) => {
    const requestIDs = selectedRequests.map(r => r.id);
    ajax('/api/admin/cancellations-dashboard/cancellations/assign', {
      request_ids: requestIDs,
      user_id: reassignedToUser.id,
    }, {method: 'POST'}).then(results => {
      if (results.status !== 200) {
        Alert.error(`${results.data.message || `Unexpected error in assigning requests`}`);
      } else {
        Alert.success(`Requests assigned successfully`);
        setRequests(requests.map(request => {
          if (requestIDs.includes(request.id)) {
            return {...request, assigned_to: results.data.user_name};
          }
          return request;
        }));
        setSelectedRequests([]);
        setReassignedToUser({id: '', name: ''});
        getAllFiles();
      }
    }).catch((e) => {
      Alert.error(`Exception ${e}`);
    });
  };

  const renderFilters = () => {
    const { searchText, status, beginDate, endDate } = filters;
    return (
      <>
        <div className="pl-2 mb-1 row">
          <div className="px-2 col-3">
            <label>Search</label>
            <input
              id="file-name-searchText"
              className="form-control"
              placeholder="Search file name"
              value={searchText}
              onChange={(e) => handleInputChange('searchText', e?.target?.value ?? '')}
            />
          </div>
          <div className="px-2 col-2">
            <label>Status</label>
            <Select
              options={[
                {label: REQUEST_STATUS_PROCESSING, value: REQUEST_STATUS_PROCESSING},
                {label: REQUEST_STATUS_PROCESSED, value: REQUEST_STATUS_PROCESSED},
                {label: REQUEST_STATUS_CANCELLED, value: REQUEST_STATUS_CANCELLED},
                {label: REQUEST_STATUS_FAILED, value: REQUEST_STATUS_FAILED},
              ]}
              clearable={true}
              value={status}
              onChange={(selected) => handleInputChange('status', selected?.value ?? '')}
            />
          </div>
          <div className="px-2 col-2">
            <label>Begin Date</label>
            <DatePicker
              id="begin-date"
              className='form-control'
              onChange={(date) => handleDateChange('beginDate', date)}
              selected={beginDate}
            />
          </div>
          <div className="px-2 col-2">
            <label>End Date</label>
            <DatePicker
              id="end-date"
              className='form-control'
              onChange={(date) => handleDateChange('endDate', date)}
              selected={endDate}
            />
          </div>
        </div>
        <div className="mb-4 row">
          <div className="text-right col">
            <button
              className="btn btn-primary mr-2"
              onClick={handleSearch}
              disabled={uploading}
            >
              <span className="fa fa-searchText"/>{` Search`}
            </button>
            <button
              className="btn btn-primary"
              onClick={handleClear}
              disabled={uploading}
            >
              <span className="fa fa-searchText"/>{` Clear`}
            </button>
          </div>
        </div>
        <div className="mb-4 row">
          <div className="col-3">
            {renderAssignTo()}
          </div>
        </div>
      </>
    );
  };

  const handleSelectFile = (id) => {
    const processedIDs = [];
    requests.forEach(request => {
      if (request.status === REQUEST_STATUS_PROCESSED) {
        processedIDs.push(request.id);
      }
    });
    localStorage.setItem('cancellationQueue', JSON.stringify(processedIDs));
    const url = {pathname: `/cancellations-dashboard/cancellation/${id}`, query: {}};
    context.router.push(url);
  };

  const renderFiles = () => {
    let hasRole = hstore.has(context.user.roles, Roles.CancelDashboardManager);
    return requests.length === 0 ? (
      <div className="mt-2">
        No requests found
      </div>
    ) : (
      <Table className="mt-2">
        <thead>
          <tr>
            <th className="col-1 font-weight-bold">Select</th>
            <th className="col-3 font-weight-bold">File Name</th>
            <th className="col-2 font-weight-bold">Assigned To</th>
            <th className="col-2 font-weight-bold">Uploaded At</th>
            <th className="col-1 font-weight-bold">Status</th>
            <th className="col-2 font-weight-bold">Customer Name</th>
            <th className="col-4 font-weight-bold"></th>
          </tr>
        </thead>
        <tbody>
          {requests?.map((request, index) => {
            return (
              <tr key={`file-${index}`}>
                <td className="col-1">{<input type="checkbox" 
                  value={request.id} 
                  checked={selectedRequests.find(r=> request.id == r.id)}
                  disabled={!hasRole}
                  onChange={(e) => {
                    const id = e.target.value;
                    if (e.target.checked) {
                      setSelectedRequests([...selectedRequests, {id}]);
                    } else {
                      setSelectedRequests(selectedRequests.filter(r => r.id !== id));
                    }
                  }}/>}</td>
                <td className="col-3">{request.file_names}</td>
                <td className="col-2">{request.assigned_to}</td>
                <td className="col-2">{moment(request.created_at).utc(true).format('MM/DD/YYYY')}</td>
                <td className="col-1">{request.status}</td>
                <td className="col-2">{request.customer_name}</td>
                <td className="col-4">
                  <div className="d-flex justify-content-end">
                    {request.status === REQUEST_STATUS_PROCESSING && 
                     moment().diff(moment(request.updated_at), 'minutes') >= 30 && (
                      <button
                        style={{width: "90px"}}
                        disabled={uploading}
                        className="btn btn-success btn-sm mr-2"
                        onClick={() => handleResubmit(request.id)}
                      >
                        <i className="fa fa-redo"/>&nbsp;Resubmit
                      </button>
                    )}
                    <button
                      style={{width: "80px"}}
                      disabled={uploading || request.status !== REQUEST_STATUS_PROCESSED}
                      className="btn btn-secondary btn-sm mr-2"
                      onClick={() => handleSelectFile(request.id)}
                    >
                      <i className="fa fa-pen"/>&nbsp;Cancel
                    </button>
                    <button
                      style={{width: "40px"}}
                      disabled={uploading || request.status === REQUEST_STATUS_CANCELLED}
                      className="btn btn-danger btn-sm"
                      onClick={() => setRequestToDelete(request.id)}
                    >
                      <i className="fa fa-trash fa-lg"/>
                    </button>
                  </div>
                </td>
              </tr>
            );
          })}
        </tbody>
      </Table>
    );
  };

  const handleAttachmentStatus = (status) => {
    setAttachmentStatus(status);
  };

  const handleUpdateAttachments = (attachments) => {
    setAttachments(attachments);
  };

  const handleFileUpload = () => {
    if (attachments.length === 0) {
      Alert.error('Please select a file to upload');
      return;
    }
    setUploading(true);
    const attachmentData = [];
    
    const upload = () => {
      ajax('/api/admin/cancellations-dashboard/cancellations', attachmentData, {method: 'POST'}).then(results => {
        if (results.status !== 200) {
          Alert.error(`${results.data.message || `Unexpected error in uploading files`}`);
        } else {
          Alert.success(`Successfully uploaded files`);
          const url = { pathname: '/cancellations-dashboard' };
          context.router.push(url);
        }
      }).catch((e) => {
        Alert.error(`Exception ${e}`);
      }).finally(() => {
        setUploading(false);
        setAttachments([]);
        setAttachmentStatus(Status.READY);
      });
    };

    let promises = [];
    // read all attachments then submit the request.
    for (let i = 0; i < attachments.length; i++) {
      const file = attachments[i];
      let att = {
        content_type: file.type,
        name: file.name,
      };
      attachmentData.push(att);

      // Read each file content asynchronously
      let readerPromise = new Promise(resolve => {
        const reader = new FileReader();
        reader.onload = (e) => {
          att.file_content = btoa(e.target.result);
          resolve();
        };
        reader.readAsBinaryString(file);
      });
      promises.push(readerPromise);
    }

    // Wait for all files to be read, then submit
    Promise.all(promises).then(() => {
      upload();
    });
  };
  
  const renderUploadSection = () => {
    return (
      <>
        <h4 className="mt-4">Start a New Cancellation:</h4>
        <div className="row">
          <div className="col-10">
            {
              uploading ? (
                <div className="d-flex justify-content-center align-items-center">
                  <Loading/>
                </div>
              ) : (
                <Attachments
                  id="attachment-files"
                  acceptedFileTypes={[
                    'image/*',
                    'application/pdf', // *.pdf
                  ]}
                  allowMultiple={true}
                  maxFiles={5}
                  onStatusChange={handleAttachmentStatus}
                  onUpdateAttachments={handleUpdateAttachments}
                />
              )
            }
          </div>
          <div className="col-2 d-flex justify-content-center align-items-center">
            <button
              className="btn btn-primary"
              disabled={uploading || attachmentStatus !== Status.READY || attachments.length === 0}
              onClick={handleFileUpload}
            >
              <i className="fa fa-upload"/>&nbsp;&nbsp;
              {uploading ? 'Uploading...' : 'Start Cancellation'}
            </button>
          </div>
        </div>
      </>
    );
  };

  const renderAssignConfirmation = () => {
    return (
      <ConfirmModal
        visible={selectedRequests.length > 0}
        close={() => setReassignedToUser({})}
        confirm={() => assignToUser()}
      >
        <div className="m-3">
          <h4><i className='fa fa-warning'></i> Confirm Assign</h4>
          <p>
                Are you sure you want to assign the selected {selectedRequests.length} requests to ?<br/>
            <strong>{reassignedToUser.name} </strong>
          </p>
        </div>
      </ConfirmModal>);
  };

  const renderDeleteConfirmation = () => {
    return (
      <ConfirmModal
        visible={!!requestToDelete}
        close={() => setRequestToDelete('')}
        confirm={handleDeleteRequest}
        disabled={deleteNote.trim() === '' && requests.filter(r => r.id === requestToDelete)[0]?.assigned_to_user_id !== context.user.id}
      >
        <div className="m-3">
          <h4><i className='fa fa-warning'></i> Confirm Delete</h4>
          <p>
                Are you sure you want to delete this request?<br/>
            <strong>{requests.filter(r => r.id === requestToDelete)[0]?.file_names}</strong>
          </p>
          <p><small className="tip">Note is required for deleting other&apos;s request</small></p>
          <input type="text" className="form-control"
            onChange={(e) => setDeleteNote(e.target.value)}
            value={deleteNote}
          />
        </div>
      </ConfirmModal>);
  };

  return (
    <div className="mt-2">
      <PageHeader pageTitle={'Cancellations Dashboard'}/>
      {renderUploadSection()}
      {
        requestToDelete && renderDeleteConfirmation()
      }
      <hr/>
      <h4 className="mt-4 mb-2">Process Uploaded Files:</h4>
      {renderFilters()}
      {
        loading ? (
          <div className="d-flex justify-content-center align-items-center">
            <Loading/>
          </div>
        ) : (
          <>
            {renderFiles()}
          </>
        )
      }
      {
        reassignedToUser.id > 0 && selectedRequests.length > 0 && renderAssignConfirmation()
      }
    </div>
  );
}
