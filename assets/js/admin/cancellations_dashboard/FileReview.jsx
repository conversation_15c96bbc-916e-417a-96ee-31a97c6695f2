import React, {useEffect, useState} from 'react';
import PropTypes from "prop-types";
import { Loading } from "../../shared/components";
import {jsonPromise as ajax} from "../../shared/ajax";
import Alert from "react-s-alert";
import PageHeader from "../../app/pageHeader/PageHeader";
import DocumentPanel from "./DocumentPanel";
import CancellationPanel from "./CancellationPanel";
import ConfirmModal from "../../shared/components/ConfirmModal";

const stylesLeftPanel = {
  width: '36vw',
  height: '75vh',
  overflow: 'auto',
  padding: '10px',
  marginLeft: -10
};

FileReview.contextTypes = {
  router: PropTypes.object.isRequired,
};

FileReview.propTypes = {
  params: PropTypes.shape({
    id: PropTypes.string.isRequired,
  }).isRequired,
};

const retriesLimit = 3;
let retries = 0;

export default function FileReview(props, context) {
  const [loading, setLoading] = useState(false);
  const [loadingAttachments, setLoadingAttachments] = useState(false);
  const [requestData, setRequestData] = useState({});
  const [requestAttachments, setRequestAttachments] = useState([]);
  const [isCancelled, setIsCancelled] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  
  const cancellationQueue = JSON.parse(localStorage.getItem('cancellationQueue') ?? '[]');
  const currentIndex = cancellationQueue.findIndex((item) => item === parseInt(props.params.id));

  useEffect(() => {
    getRequestInfo();
  }, []);

  useEffect(() => {
    getRequestAttachments();
  }, [props.params.id]);

  const getRequestInfo = () => {
    setLoading(true);
    const { id } = props.params;
    const url = `/api/admin/cancellations-dashboard/cancellation/${id}`;
    ajax(url, {}, {}).then(results => {
      if (results.status !== 200) {
        Alert.error(`${results.data.message || `Unexpected error in getting request data`}`);
        context.router.push('/cancellations-dashboard');
      } else {
        setRequestData(results.data);
        setIsCancelled(results.data.status === 'cancelled');
      }
      setLoading(false);
    }).catch((e) => {
      setLoading(false);
      Alert.error(`An error occurred while fetching the cancellation request`);
      context.router.push('/cancellations-dashboard');
    });
  };

  const getRequestAttachments = () => {
    setLoadingAttachments(true);
    const { id } = props.params;
    const url = `/api/admin/cancellations-dashboard/cancellation/${id}/attachments`;
    ajax(url, {}, {}).then(results => {
      if (results.status !== 200) {
        Alert.error(`${results.data.message || `Unexpected error in getting request attachments`}`);
      } else {
        setRequestAttachments(results.data.attachments);
      }
      setLoadingAttachments(false);
    }).catch((e) => {
      setLoadingAttachments(false);
      Alert.error(`Exception ${e}`);
    });
  };
  
  const handleDownloadAll = () => {
    requestAttachments.forEach((attachment) => {
      window.open(attachment.url, '_blank');
    });
  };
  
  const handleNext = () => {
    if (currentIndex < cancellationQueue.length - 1) {
      const nextRequestID = cancellationQueue[currentIndex + 1];
      context.router.push(`/cancellations-dashboard/cancellation/${nextRequestID}`);
    }
  };
  
  const handlePrevious = () => {
    if (currentIndex > 0) {
      const previousRequestID = cancellationQueue[currentIndex - 1];
      context.router.push(`/cancellations-dashboard/cancellation/${previousRequestID}`);
    }
  };
  
  const renderButtons = () => {
    return (
      <div style={{marginLeft: -25}} className="col">
        <button
          className='btn btn-secondary'
          onClick={() => context.router.push('/cancellations-dashboard')}
        >
          <i className='fa fa-arrow-left'/> Cancellations List
        </button>
        <button
          onClick={handlePrevious}
          className='ml-3 btn btn-outline-primary'
          disabled={currentIndex === -1 || currentIndex === 0}
        >
          <i className='fa fa-chevron-left'/> Previous
        </button>
        <button
          onClick={handleNext}
          className='ml-3 btn btn-outline-primary'
          disabled={currentIndex === -1 || currentIndex === cancellationQueue.length - 1}
        >
          <i className='fa fa-chevron-right'/> Next
        </button>
        <a
          onClick={handleDownloadAll}
          className="ml-3 btn btn-outline-dark"
          target="_blank"
          rel="noopener noreferrer"
        >
          <i className="fa fa-download"/> Download Files
        </a>
        <button
          className="ml-2 btn btn-danger"
          onClick={() => setShowDeleteModal(true)}
        >
          <i className="fa fa-trash"/> Delete
        </button>
        {
          requestData.status === 'cancelled' && (
            <>
              <span className="ml-4 p-2 badge badge-success" style={{height: 30}}>
                <i className="fa fa-check"/> Cancelled
              </span>
            </>
          )
        }
      </div>
    );
  };
  
  const handleSuccess = () => {
    if ((currentIndex === -1) || (currentIndex === cancellationQueue.length - 1)) {
      context.router.push('/cancellations-dashboard');
      return;
    }
    const nextRequestID = cancellationQueue[currentIndex + 1];
    const newQueue = [...cancellationQueue];
    newQueue.splice(currentIndex, 1);
    localStorage.setItem('cancellationQueue', JSON.stringify(newQueue));
    context.router.push(`/cancellations-dashboard/cancellation/${nextRequestID}`);
  };
  
  const reFetchAttachments = () => {
    if (retries < retriesLimit) {
      getRequestAttachments();
      retries++;
    }
  };

  const handleDelete = () => {
    setLoading(true);
    ajax(`/api/admin/cancellations-dashboard/cancellation/${props.params.id}`, {}, { method: 'DELETE' })
      .then(results => {
        if (results.status !== 200) {
          Alert.error(`${results.data.message || 'Unexpected error in deleting cancellation'}`);
        } else {
          Alert.success('Cancellation deleted successfully');
          context.router.push('/cancellations-dashboard');
        }
      })
      .catch(() => {
        Alert.error('Error deleting cancellation');
      })
      .finally(() => {
        setLoading(false);
        setShowDeleteModal(false);
      });
  };

  return (
    <div className="mt-2">
      <PageHeader pageTitle={'Cancellation'}/>
      <div className="mt-2">
        <div className="row">
          {renderButtons()}
        </div>
      </div>
      {
        loading ? (
          <div className="d-flex justify-content-center align-items-center">
            <Loading/>
          </div>
        ) : (
          <div className="my-3 row" style={{marginLeft: -25}}>
            <div className="col-7" style={stylesLeftPanel}>
              <CancellationPanel
                isCancelled={isCancelled}
                cancellationID={props.params.id}
                vin={requestData?.processing_result?.vehicle_information?.vin ?? ''}
                processingResult={requestData.processing_result}
                handleSuccess={handleSuccess}
              />
            </div>
            <div className="ml-2 col-5">
              <DocumentPanel
                loadingAttachments={loadingAttachments}
                requestAttachments={requestAttachments}
                reFetchAttachments={reFetchAttachments}
              />
            </div>
          </div>
        )
      }
      {showDeleteModal && (
        <ConfirmModal
          visible={showDeleteModal}
          close={() => setShowDeleteModal(false)}
          confirm={handleDelete}
          proceedLabel="Yes"
          cancelLabel="No"
        >
          <div className="m-3">
            <h4><i className='fa fa-warning'></i> Confirm Delete</h4>
            <p>
              Are you sure you want to delete this cancellation?
            </p>
          </div>
        </ConfirmModal>
      )}
    </div>
  );
}