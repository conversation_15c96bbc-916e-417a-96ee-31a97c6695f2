import accounting from "accounting";
import moment from "moment/moment";
import PropTypes from "prop-types";
import React, { useEffect, useState } from "react";
import DatePicker from "react-datepicker";
import Alert from "react-s-alert";
import Select from "react-select";
import { jsonPromise as ajax } from "../../shared/ajax";
import { GetDisplayClassForViolationType, ViolationTypes } from "../../shared/cancel-rules";
import { Loading } from "../../shared/components";
import Attachments from "../../shared/components/Attachments";
import { CONTRACT_STATUS_MAP, PRODUCT_CODE_MAP } from "../../shared/components/contracts/constant";
import Modal from "../../shared/components/Modal";
import * as Context from "../../shared/context";
import dateFormat from "../../shared/date-format";
import hstore from "../../shared/hstore";
import * as Roles from "../../shared/roles";
import CancellationHelpModal from "../contracts_search/CancellationHelpModal";
import { CONSTANTS } from "../contracts_search/constants";
import { productTypeMap } from "./constants";
import { checkBackdatingWarning } from "../../shared/backdating-warning";

const inputSectionStyles = {
  minHeight: '160px',
};

const CANCELLATION_TAB = "cancellation";
const BASIC_INPUT_TAB = "basic_input";
const OPTIONAL_INPUT_TAB = "optional_input";
const API_DATE_FORMAT = "YYYY-MM-DD";

const CONTRACT_STATUS_ACTIVE = 'Active';
const CONTRACT_STATUS_CANCELLED = 'Cancelled';
const CONTRACT_STATUS_EXPIRED = 'Expired';

const PAYEE_LENDER = 'Lender';
const PAYEE_RESERVES = 'Reserves';
const PAYEE_CUSTOMER = 'Customer';
const PAYEE_DOWN_PAYMENT = 'DownPayment';
const PAYEE_STORE_REFUND = 'Store Issued Refund';

const CANCEL_REASON_FLAT_UNWIND = 'Flat Cancel/Unwind';
const CANCEL_REASON_SPP_DEFAULT = 'SPP Default';
const CANCEL_REASON_SPP_CUSTOMER_REQUEST = 'SPP Customer Request';
const CANCEL_REASON_VEHICLE_TRADE_IN = 'Vehicle Trade In';
const CANCEL_REASON_SOLD_TRADED = 'Sold/Traded (NOT down payment)';

CancellationPanel.contextTypes = {
  router: Context.Router.isRequired,
  user: Context.User,
};

CancellationPanel.propTypes = {
  isCancelled: PropTypes.bool.isRequired,
  cancellationID: PropTypes.string,
  vin: PropTypes.string,
  processingResult: PropTypes.object,
  handleSuccess: PropTypes.func,
};

const initialUserInput = {
  vin: '',
  cancel_date: '',
  cancel_reason_id: '',
  current_mileage: '',
  tax_rate: '',
  nsd_key_claims: '',
  nsd_tire_and_wheel_claims: '',
  nsd_vta_claims: '',
  spp_customer_paid: '',
  spp_balance: '',
};

export default function CancellationPanel(props, context) {
  const [activeInputTab, setActiveInputTab] = useState(BASIC_INPUT_TAB);
  const [activeTab, setActiveTab] = useState(CANCELLATION_TAB);
  const [vinContracts, setVINContracts] = useState([]);
  const [showContractModal, setShowContractModal] = useState(false);
  const [selectedContract, setSelectedContract] = useState({});
  const [supportingData, setSupportingData] = useState({});
  const [loadingQuotes, setLoadingQuotes] = useState(false);
  const [quotes, setQuotes] = useState([]);
  const [userInput, setUserInput] = useState(initialUserInput);
  const [loadingContracts, setLoadingContracts] = useState(false);
  const [showHelp, setShowHelp] = useState(false);
  const [attachments, setAttachments] = useState([]);
  const [contractsToCancel, setContractsToCancel] = useState([]);
  const [includeBill, setIncludeBill] = useState(true);
  const [applyFee, setApplyFee] = useState(true);
  const [correctedData, setCorrectedData] = useState({});
  const [isElectronicCheck, setIsElectronicCheck] = useState(!!props.processingResult?.refund_information?.is_electronic_check);
  const [email, setEmail] = useState('');
  const [contractUrl, setContractUrl] = useState('');
  const [isSPPContractInEstimate, setIsSPPContractInEstimate] = useState(false);
  const [refundType, setRefundType] = useState('Customer Refund');
  const [selectedQuotes, setSelectedQuotes] = useState([]);
  const [showSPPConfirmation, setShowSPPConfirmation] = useState(false);
  const [sppContracts, setSppContracts] = useState([]);
  const [showExpiredConfirmation, setShowExpiredConfirmation] = useState(false);
  const [refundPayableToSelections, setRefundPayableToSelections] = useState({});
  const [refundPayeeFields, setRefundPayeeFields] = useState({});
  const [contractSupportingData, setContractSupportingData] = useState({});
  const [showReservesChangeModal, setShowReservesChangeModal] = useState(false);
  const [reservesChangeModalContractId, setReservesChangeModalContractId] = useState(null);
  const [reservesChangeModalNewPayee, setReservesChangeModalNewPayee] = useState('');
  const [reservesChangeModalNote, setReservesChangeModalNote] = useState('');
  const [reservesChangeNotes, setReservesChangeNotes] = useState({});
  const [reservesSetBySystem, setReservesSetBySystem] = useState({});
  // Add a state to synchronize contract and userInput updates
  const [pendingQuote, setPendingQuote] = useState(null);
  // Add a flag to deterministically trigger quote fetching
  const [shouldFetchQuote, setShouldFetchQuote] = useState(false);

  const inFlightSupportingDataRequests = React.useRef(new Set());
  
  const { isCancelled } = props;
  
  const resetQuoteData = () => {
    setAttachments([]);
    setIncludeBill(true);
    setApplyFee(true);
    setQuotes([]);
    setSelectedQuotes([]);
    setContractsToCancel([]);
    setRefundPayableToSelections({});
    setRefundPayeeFields({});
    setEmail(''); // Clear email on reset
  };

  useEffect(() => {
    if (props.vin) {
      setUserInput({
        ...initialUserInput,
        vin: props.vin,
      });
    }
    getVINContracts();
  }, [props.vin]);

  useEffect(() => {
    // When quotes or selections change, we need to get fresh supporting data
    // because cancellation eligibility (especially for digital reserves) can
    // depend on the cancel reason, which may have just changed.
    selectedQuotes.forEach(id => {
      // We always refetch for selected contracts, unless a request is already in flight.
      if (!inFlightSupportingDataRequests.current.has(id)) {
        fetchSupportingDataForContract(id);
      }
    });
  }, [quotes, selectedQuotes]);

  useEffect(() => {
    // Check if any quotes are SPP contracts, regardless of selection
    const hasSPPContracts = quotes.some(quote => quote.payment_type === "SPP");
    setIsSPPContractInEstimate(hasSPPContracts);

    // Set default refund type to "SPP Refund" if SPP contracts exist
    if (hasSPPContracts) {
      setRefundType('SPP Refund');
      setUserInput(prev => ({
        ...prev,
        refund_type: 'SPP Refund'
      }));
    }

    // Still track selected SPP contracts separately for other functionality
    const selectedSPPContracts = quotes
      .filter(quote => quote.selected && quote.payment_type === "SPP")
      .map(quote => quote.id);
    setSppContracts(selectedSPPContracts);
  }, [quotes]);

  useEffect(() => {
    if (
      isElectronicCheck &&
      selectedQuotes.some(id => refundPayableToSelections[id] === PAYEE_CUSTOMER) &&
      supportingData.payee_customer &&
      supportingData.payee_customer.email
    ) {
      // Only prefill if email is empty (so user edits are not overwritten)
      if (!email) {
        setEmail(supportingData.payee_customer.email);
      }
    }
    // If not electronic check or no customer payee, do not overwrite email
  }, [isElectronicCheck, refundPayableToSelections, selectedQuotes, supportingData, email]);

  const fetchSupportingDataForContract = (contractId) => {
    inFlightSupportingDataRequests.current.add(contractId);
    ajax(`/api/contracts/cancellation/quote-supporting-data?contract_id=${contractId}`, {}, {})
      .then(results => {
        if (results.status === 200) {
          setContractSupportingData(prevData => ({
            ...prevData,
            [contractId]: results.data
          }));
          applyDigitalReservesDefaultIfEligible(contractId, results.data);
        }
      })
      .finally(() => {
        inFlightSupportingDataRequests.current.delete(contractId);
      });
  };

  const applyDigitalReservesDefaultIfEligible = (contractId, data) => {
    if (!data) return;

    const estimate = quotes.find(e => e.id === contractId);
    const productTypeId = estimate?.product_type_id;
    const cancelReasonId = userInput.cancel_reason_id;

    const isDigital = data.payee_lender_is_digital_reserves;
    const digitalReserveRules = data.payee_lender_digital_reserve_rules || [];
    // Only default if a rule exists for this productTypeId and cancelReasonId
    const hasRule = isDigital && productTypeId && cancelReasonId && digitalReserveRules.some(
      rule => rule.product_type_id === productTypeId && rule.cancel_reason_id === Number(cancelReasonId)
    );
    if (hasRule) {
      if (!refundPayableToSelections[contractId]) {
        let payeeData = data.payee_lender || {};
        setRefundPayableToSelections(prev => ({ ...prev, [contractId]: PAYEE_RESERVES }));
        setRefundPayeeFields(prev => ({
          ...prev,
          [contractId]: {
            payee: PAYEE_RESERVES,
            payeeName: payeeData.name || '',
            payeeAttentionTo: payeeData.attention || '',
            payeeAddress: payeeData.address || '',
            payeeCity: payeeData.city || '',
            payeeState: payeeData.state_code || '',
            payeePostalCode: payeeData.postal_code || '',
            cancelStoreID: '',
          }
        }));
        setReservesSetBySystem(prev => ({ ...prev, [contractId]: true }));
      }
    }
  };

  // Helper: Sort contracts by product type using provided productTypes
  const sortByProductOrderWithTypes = (contractA, contractB, productTypes) => {
    if (productTypes && productTypes.length > 0) {
      const productOrder = {};
      productTypes.forEach((productType, idx) => {
        productOrder[productType.code] = typeof productType.position === 'number' ? productType.position : idx;
      });
      const posA = productOrder[contractA.product_type_code];
      const posB = productOrder[contractB.product_type_code];
      if (typeof posA === 'undefined' && typeof posB === 'undefined') return 0;
      if (typeof posA === 'undefined') return 1;
      if (typeof posB === 'undefined') return -1;
      return posA - posB;
    }
    return 0;
  };

  // Update getSupportingData to pass product_types to getCancellationQuote
  const getSupportingData = (contractID, userVIN) => {
    setLoadingContracts(true);
    const url = `/api/contracts/cancellation/quote-supporting-data?contract_id=${contractID}`;
    ajax(url, {}, {}).then(results => {
      if (results.status !== 200) {
        Alert.error(`${results.data.message || `Unexpected error in getting supporting data`}`);
      } else {
        setSupportingData(results.data);
        // Prefill email with customer email if electronic check and payee is customer
        if (
          isElectronicCheck &&
          results.data.payee_customer &&
          results.data.payee_customer.email
        ) {
          setEmail(results.data.payee_customer.email);
        } else {
          setEmail('');
        }
        // 'Vehicle Trade In' is same as 'Trade In'
        if (props.processingResult?.cancellation_information?.reason === CANCEL_REASON_VEHICLE_TRADE_IN) {
          props.processingResult.cancellation_information.reason = CANCEL_REASON_SOLD_TRADED;
        }
        // Prefill logic for processingResult should always run if present
        if (props.processingResult) {
          const inputData = {
            vin: userVIN ? userVIN : props.vin ?? '',
            cancel_date: props.processingResult?.cancellation_information?.signature_date ? moment(props.processingResult.cancellation_information.signature_date) : '',
            cancel_reason_id: results.data.cancel_reasons.find(cr =>
              cr.name === props.processingResult?.cancellation_information?.reason
            )?.id ?? '',
            current_mileage: props.processingResult?.vehicle_information?.mileage ?? '',
            tax_rate: '',
            nsd_key_claims: '',
            nsd_tire_and_wheel_claims: '',
            nsd_vta_claims: '',
            spp_customer_paid: '',
            spp_balance: '',
          };
          setUserInput(inputData);
        }
      }
    }).catch((e) => {
      Alert.error(`Exception ${e}`);
    }).finally(() => {
      setLoadingContracts(false);
    });
  };
  
  const autoSelectContract = (contracts) => {
    let activeContract = null;
    let activeCustomer = null;
    for (let i = 0; i < contracts.length; i++) {
      if (contracts[i].status === CONTRACT_STATUS_ACTIVE ||
          contracts[i].status === CONTRACT_STATUS_CANCELLED ||
          contracts[i].status === CONTRACT_STATUS_EXPIRED) {
        activeContract = contracts[i];
        if (activeCustomer === null) {
          activeCustomer = contracts[i].customer_name;
        } else {
          if (activeCustomer !== contracts[i].customer_name) {
            activeContract = null;
            break;
          }
        }
      }
    }
    return activeContract;
  };

  const getVINContracts = () => {
    if ((props.vin === undefined || props.vin === '') && (userInput.vin === undefined || userInput.vin === '')) {
      return;
    }
    setLoadingContracts(true);
    let vin = props.vin;
    if (userInput.vin) {
      vin = userInput.vin;
    }
    const url = `/api/admin/contracts?search=${vin}&page_size=100&page=1`;
    ajax(url, {}, {}).then(results => {
      if (results.status !== 200) {
        Alert.error(`${results.data.message || `Unexpected error in getting VIN contracts`}`);
      } else {
        if (!results.data.contracts) {
          Alert.error(`No contracts found for the VIN`);
          return;
        } else if (results.data.contracts.length === 1) {
          setPendingQuote({
            contract: results.data.contracts[0],
            userInput: {
              ...initialUserInput,
              vin: results.data.contracts[0].vin,
            }
          });
          setContractUrl(`/contracts-search/${results.data.contracts[0].code}?product_code=${results.data.contracts[0].product_code}&id=${results.data.contracts[0].id}`);
          getSupportingData(results.data.contracts[0].id, results.data.contracts[0].vin);
          return;
        }
        setVINContracts(results.data.contracts);
        const activeContract = autoSelectContract(results.data.contracts);
        if (activeContract) {
          setPendingQuote({
            contract: activeContract,
            userInput: {
              ...initialUserInput,
              vin: activeContract.vin,
            }
          });
          setContractUrl(`/contracts-search/${activeContract.code}?product_code=${activeContract.product_code}&id=${activeContract.id}`);
          getSupportingData(activeContract.id, activeContract.vin);
        } else {
          setShowContractModal(true);
        }
      }
    }).catch(() => {
      Alert.error(`Unable to get contracts for VIN ${vin}`);
    }).finally(() => {
      setLoadingContracts(false);
    });
  };
  
  const handlePreSelectedContracts = (quotes) => {
    const selectedContracts = [];

    // First mark quotes as selected based on processing result
    const updatedQuotes = quotes.map(quote => {
      let isSelected = false;
      Object.keys(props.processingResult?.contract_information || {}).forEach((contractType) => {
        const contractInfo = props.processingResult?.contract_information[contractType];
        if (
          quote.cancellable &&
          contractInfo?.is_selected &&
          (quote.original_code === contractInfo.number_code ||
          (contractInfo.number_code !== '' &&
           quote.product_type_name === productTypeMap["app_contract"] &&
           productTypeMap["app_contract"].includes(contractInfo.number_code)) ||
          (quote.product_type_name === productTypeMap[contractType]))
        ) {
          isSelected = true;
          selectedContracts.push(quote.id);
        }
      });
      return { ...quote, selected: isSelected };
    });

    // This will trigger the useEffect to fetch supporting data
    const initiallySelected = updatedQuotes.filter(q => q.selected).map(q => q.id);
    setSelectedQuotes(initiallySelected);
    setContractsToCancel(initiallySelected);

    // Update quotes state with selection status
    setQuotes(updatedQuotes);
  };

  // Update getCancellationQuote to accept productTypes and use it for sorting
  const getCancellationQuote = (processedData, contractID, productTypes) => {
    // Reset values when getting new quotes
    resetQuoteData();

    let {
      cancel_date,
      current_mileage,
      cancel_reason_id,
      spp_customer_paid,
      spp_balance,
      nsd_key_claims,
      nsd_tire_and_wheel_claims,
      nsd_vta_claims,
      tax_rate
    } = userInput;
    let contract = selectedContract.id;
    if (processedData?.cancel_date) {
      cancel_date = processedData.cancel_date;
      current_mileage = processedData.current_mileage;
      cancel_reason_id = processedData.cancel_reason_id;
      spp_customer_paid = processedData.spp_customer_paid;
      spp_balance = processedData.spp_balance;
      nsd_key_claims = processedData.nsd_key_claims;
      nsd_tire_and_wheel_claims = processedData.nsd_tire_and_wheel_claims;
      nsd_vta_claims = processedData.nsd_vta_claims;
      tax_rate = processedData.tax_rate;
      contract = contractID;
    }
    setLoadingQuotes(true);
    const url = `/api/admin/contracts/${contract}/cancellation/estimate/quote?cancel_date=${moment(cancel_date).format(API_DATE_FORMAT)}&mileage=${current_mileage}&cancel_reason_id=${cancel_reason_id}&spp_customer_paid=${spp_customer_paid || 0}&spp_balance=${spp_balance || 0}&nsd_key_claims=${nsd_key_claims || 0}&nsd_tire_and_wheel_claims=${nsd_tire_and_wheel_claims || 0}&nsd_vta_claims=${nsd_vta_claims || 0}&manual_tax=${tax_rate}`;
    ajax(url, {}, {}).then(results => {
      if (results.status !== 200) {
        Alert.error(`${results.data.message || `Unexpected error in getting quotes data`}`);
      } else if (results.data.estimates.length === 0) {
        Alert.error('No relevant contracts (Active, Expired, or Cancelled) found for the VIN.');
      } else {
        // Use productTypes from argument if provided, else fallback to supportingData.product_types
        const types = productTypes || supportingData.product_types;
        results.data.estimates.sort((a, b) => sortByProductOrderWithTypes(a, b, types));
        setQuotes(results.data.estimates);
        handlePreSelectedContracts(results.data.estimates);
      }
    }).catch((e) => {
      Alert.error(`Exception ${e}`);
    }).finally(() => {
      setLoadingQuotes(false);
    });
  };

  const canSelectFutureDate = (cancellationReason) => {
    if (!cancellationReason) return false;
    return [
      CANCEL_REASON_FLAT_UNWIND,
    ].includes(cancellationReason.name);
  };



  const handleInputChange = (field, value) => {
    if (field === 'tax_rate' && value > CONSTANTS.MAX_SALES_TAX) {
      Alert.error(`Tax cannot be greater than ${CONSTANTS.MAX_SALES_TAX}%`);
      return;
    }

    const isRequiredField = ['cancel_date', 'cancel_reason_id', 'current_mileage', 'tax_rate'].includes(field);

    const nextUserInput = {
      ...userInput,
      [field]: value,
    };

    // If reason changes and doesn't allow future dates, reset date to current date
    if (field === 'cancel_reason_id') {
      const newReason = supportingData?.cancel_reasons?.find(reason => reason.id === value);
      if (!canSelectFutureDate(newReason) && moment(userInput.cancel_date).isAfter(moment())) {
        nextUserInput.cancel_date = moment();
      }
      // Check backdating warning when reason changes (if date is already set)
      if (userInput.cancel_date) {
        checkBackdatingWarning(
          userInput.cancel_date,
          value,
          supportingData?.cancel_reasons,
          () => setUserInput(prev => ({ ...prev, cancel_date: moment() }))
        );
      }
    }

    // Check for backdating warning when cancel_date changes
    if (field === 'cancel_date' && value) {
      const shouldContinue = checkBackdatingWarning(
        value,
        userInput.cancel_reason_id,
        supportingData?.cancel_reasons,
        () => setUserInput(prev => ({ ...prev, cancel_date: moment() }))
      );
      if (!shouldContinue) {
        return; // User cancelled, don't update the input
      }
    }

    setUserInput(nextUserInput);

    // When any required input changes, clear quotes so the table disappears
    if (isRequiredField) {
      setQuotes([]);
      setSelectedQuotes([]);
      setContractsToCancel([]);
    }

    if (field === 'vin') {
      setCorrectedData({
        ...correctedData,
        "vehicle_information" : {
          "vin": value,
        }
      });
    } else if (field === 'cancel_date') {
      setCorrectedData({
        ...correctedData,
        "cancellation_information" : {
          ...correctedData.cancellation_information,
          "date": moment(nextUserInput.cancel_date).format(API_DATE_FORMAT),
        }
      });
    } else if (field === 'cancel_reason_id') {
      const reasonName = supportingData?.cancel_reasons?.find(cr => cr.id === value)?.name;
      setCorrectedData({
        ...correctedData,
        "cancellation_information" : {
          ...correctedData.cancellation_information,
          "reason": reasonName,
        }
      });
    } else if (field === 'current_mileage') {
      setCorrectedData({
        ...correctedData,
        "vehicle_information" : {
          ...correctedData.vehicle_information,
          "current_mileage": value,
        }
      });
    }
  };

  const handleCancelContract = () => {
    const sppContracts = [];
    let expiredContractsExists = false;

    for(const quote of quotes){
      if(quote.selected && quote.payment_type === "SPP"){
        sppContracts.push(quote.id);
      }
      if(quote.selected && quote.status === 'Expired'){
        expiredContractsExists = true;
      }
    }

    if (sppContracts.length > 0) {
      setShowSPPConfirmation(true);
      setSppContracts(sppContracts);
    } else if (expiredContractsExists) {
      setShowExpiredConfirmation(true);
    } else {
      submit();
    }
  };

  const onCancelSppContract = () => {
    setShowSPPConfirmation(false);
    setSppContracts([]);

    // Check for expired contracts next
    const expiredContractsExists = quotes.some(quote =>
      quote.selected && quote.status === 'Expired'
    );

    if (expiredContractsExists) {
      setShowExpiredConfirmation(true);
    } else {
      submit();
    }
  };

  const onCancelExpiredContract = () => {
    setShowExpiredConfirmation(false);
    submit();
  };

  const submit = () => {
    const { cancellationID } = props;
    const cancelContractUrl = `/api/admin/cancellations-dashboard/cancellation/${cancellationID}/cancel`;

    const contractPayees = buildContractPayeesPayload(contractsToCancel, refundPayeeFields);
    // Prepend required text to each contract note
    const contractNotes = {};
    Object.entries(reservesChangeNotes).forEach(([id, note]) => {
      contractNotes[id] = `Refund Payable change from Reserves: ${note}`;
    });

    const cancelRequest = {
      "cancel_date": userInput.cancel_date ? moment(userInput.cancel_date).format(API_DATE_FORMAT): moment().format(API_DATE_FORMAT),
      "mileage": parseInt(userInput.current_mileage, 10),
      "cancel_reason_id": parseInt(userInput.cancel_reason_id, 10),
      "spp_customer_paid": parseFloat(userInput.spp_customer_paid || 0),
      "spp_balance": parseFloat(userInput.spp_balance || 0),
      "nsd_key_claims": parseFloat(userInput.nsd_key_claims || 0),
      "nsd_tire_and_wheel_claims": parseFloat(userInput.nsd_tire_and_wheel_claims || 0),
      "nsd_vta_claims": parseFloat(userInput.nsd_vta_claims || 0),
      "contracts": contractsToCancel,
      "manual_tax_rate": parseFloat(userInput.tax_rate || 0),
      "contract_payees": contractPayees,
      "contract_notes": contractNotes,
    };
    // Conditionally add fields only when applicable
    if (applyFee) {
      cancelRequest.apply_fee = applyFee;
    }
    if (!includeBill) {
      cancelRequest.skip_bill = true;
    }
    if (isElectronicCheck) {
      cancelRequest.is_electronic_check = isElectronicCheck;
      if (email) {
        cancelRequest.email = email;
      }
    }
    if (Object.keys(correctedData).length > 0) {
      cancelRequest.corrected_data = correctedData;
    }

    const submit = () => {
      setLoadingContracts(true);
      ajax(cancelContractUrl, cancelRequest, {method: "PUT"}).then(
        (results) => {
          setLoadingContracts(true);
          if (results.status === 200) {
            if (results.data.warnings && results.data.warnings.length > 0) {
              Alert.warning(results.data.warnings.join(", "));
            }
            if (results.data.count === 0) {
              Alert.warning("Something went wrong, No contract was canceled");
            } else {
              Alert.success(results.data.count + " Contracts " + results.data.message);
            }
            props.handleSuccess();
          } else {
            // If there was a message in the response, then show that and if not, then show the generic error message.
            if (results.data.message) {
              Alert.error("Error: " + results.data.message);
            } else {
              Alert.error("Error updating contract cancellations");
            }
          }
        },
        (reason) => {
          // If there was a message in the response, then show that and if not, then show the generic error message.
          if (reason.data && reason.data.message) {
            Alert.error("Error: " + reason.data.message);
          } else {
            Alert.error("Error updating contract cancellations");
          }
          setLoadingContracts(false);
        }
      );
    };

    const attachmentType = supportingData.attachment_types.find(at => at.name === 'Cancel Quote');
    if (attachments && attachments.length > 0) {
      cancelRequest.attachments = [];
      let promises = [];
      // read all attachments then submit the request.
      for (let i = 0; i < attachments.length; i++) {
        const file = attachments[i];
        let att = {
          content_type: file.type,
          name: file.name,
          description: 'Cancel Contract',
          attachment_type_id: attachmentType.id,
        };
        cancelRequest.attachments.push(att);

        // Read each file content asynchronously
        let readerPromise = new Promise(resolve => {
          const reader = new FileReader();
          reader.onload = (e) => {
            att.file_content = btoa(e.target.result);
            resolve();
          };
          reader.readAsBinaryString(file);
        });
        promises.push(readerPromise);
      }

      // Wait for all files to be read, then submit
      Promise.all(promises).then(() => {
        submit();
      });
    } else {
      submit();
    }
  };

  const renderVINInput = () => {
    return (
      <div className="d-flex align-items-center mb-2" style={{gap: 12}}>
        <input
          className="form-control"
          style={{maxWidth: 350}}
          placeholder="Enter VIN or Contract Number"
          name="vin"
          disabled={isCancelled}
          value={userInput?.vin}
          onChange={(e) => handleInputChange('vin', e.target.value)}
        />
        <button
          className="btn btn-primary"
          disabled={isCancelled || userInput.vin.length < 1}
          onClick={getVINContracts}
        >
          <i className="fa fa-search"/> Search
        </button>
        {selectedContract.id && (
          <button
            className="btn btn-primary"
            onClick={() => {
              context.router.push(contractUrl);
            }}
          >
            Contract View
          </button>
        )}
      </div>
    );
  };

  const renderContractModal = () => {
    const customOverlayStyles = {
      background: 'rgba(50, 50, 50, 0.5)',
      pointerEvents: 'none'
    };

    return (
      <Modal
        visible={showContractModal}
        close={() => setShowContractModal(false)}
        size="large"
        minHeight={100}
        manualOverflow={true}
        allowBackgroundScroll={true}
        overlayStyles={customOverlayStyles}
      >
        <h4>Select contract:</h4>
        {renderContractTable()}
      </Modal>
    );
  };
  
  const getContractCodes = (contractCodes) => {
    let trimmedCodes = [...contractCodes];
    if (contractCodes.length > 5) {
      trimmedCodes = trimmedCodes.slice(0, 5);
    }
    return (
      <>
        {
          trimmedCodes.map(code => (
            <div key={code}>{code}</div>
          ))
        }
        {
          contractCodes.length > 5 && (
            <div>{`+ ${contractCodes.length - 5} contracts`}</div>
          )
        }
      </>
    );
  };

  const renderContractTable = () => {
    const customerMap = new Map();
    vinContracts.forEach((contract) => {
      if (contract.status !== CONTRACT_STATUS_ACTIVE &&
          contract.status !== CONTRACT_STATUS_CANCELLED &&
          contract.status !== CONTRACT_STATUS_EXPIRED) {
        return;
      }
      if (!customerMap.has(contract.customer_name)) {
        customerMap.set(contract.customer_name, {
          contract: contract,
          code: [contract.code],
        });
      } else {
        customerMap.get(contract.customer_name).code.push(contract.code);
      }
    });

    return (
      <table className='table table-hover table-striped table-sm mt-3 ml-1'>
        <thead>
          <tr>
            <th>Customer Name</th>
            <th>Vehicle</th>
            <th>Year</th>
            <th>Make</th>
            <th>Model</th>
            <th>Contract#</th>
            <th/>
          </tr>
        </thead>
        <tbody>
          {
            Array.from(customerMap.keys()).map((customer) => {
              return (
                <tr key={customer}>
                  <td>{customer}</td>
                  <td>{customerMap.get(customer).contract.vin}</td>
                  <td>{customerMap.get(customer).contract.vehicle_year}</td>
                  <td>{customerMap.get(customer).contract.vehicle_make}</td>
                  <td>{customerMap.get(customer).contract.vehicle_model}</td>
                  <td>{getContractCodes(customerMap.get(customer).code)}</td>
                  <td>
                    <button
                      className="btn btn-primary ml-2"
                      onClick={() => {
                        const contract = customerMap.get(customer).contract;
                        setPendingQuote({
                          contract,
                          userInput: {
                            ...initialUserInput,
                            vin: contract.vin,
                          }
                        });
                        setContractUrl(`/contracts-search/${contract.code}?product_code=${contract.product_code}&id=${contract.id}`);
                        getSupportingData(contract.id, contract.vin);
                        setQuotes([]);
                        setShowContractModal(false);
                      }}
                    >
                      Select
                    </button>
                  </td>
                </tr>
              );
            })
          }
        </tbody>
      </table>
    );
  };

  const renderVehicleCustomerInfo = () => {
    return (
      <div className="d-flex align-items-start flex-wrap">
        <h6 className="d-inline-block">
          <small>Vehicle:</small>
          <br/>
          {selectedContract.vin}
          <br/>
          <small>
            {selectedContract.vehicle_year} {selectedContract.vehicle_make} {selectedContract.vehicle_model}&nbsp;
          </small>
        </h6>
        <h6
          className="d-inline-block ml-3 text-truncate"
          style={{ maxWidth: '150px' }}
          title={selectedContract.customer_name}
        >
          <small>Customer:</small>
          <br/>
          {selectedContract.customer_name}
          <br/>
          <small>&nbsp;</small>
        </h6>
        <h6 className="d-inline-block ml-3">
          <small>Deal Number:</small>
          <br/>
          {selectedContract.deal_number}
          <br/>
          <small>&nbsp;</small>
        </h6>
        <h6 className="d-inline-block ml-3">
          <small>Store Code/#:</small>
          <br/>
          {selectedContract.store_code || selectedContract.store_number || '-'}
          <br/>
          <small>&nbsp;</small>
        </h6>
      </div>
    );
  };

  const isFlatCancel = (theUserInput = userInput) => {
    if (!supportingData || !theUserInput.cancel_reason_id) return false;
    let cancelReason = supportingData?.cancel_reasons?.find(reason => reason.id === theUserInput.cancel_reason_id);
    if (!cancelReason) return false;
    return cancelReason.name.toLowerCase().includes('flat cancel');
  };

  const basicInputSection = () => {
    let taxTipText = "* This rate will be use if only the CDK Deal Lookup fails";
    return (
      <div style={inputSectionStyles}>
        <div className="row mt-2">
          <div className="col-6">
            <label>* Cancel Date</label>
            <DatePicker
              id="cancel_date"
              className='form-control'
              required={true}
              disabled={isFlatCancel() || isCancelled}
              onChange={(date) => handleInputChange('cancel_date', date)}
              selected={userInput.cancel_date
                ? moment(userInput.cancel_date)
                : undefined}
              maxDate={canSelectFutureDate(supportingData?.cancel_reasons?.find(reason => reason.id === userInput.cancel_reason_id)) ? undefined : moment()}
            />
          </div>
          <div className="col-6">
            <label>* Cancel Reason</label>
            <Select
              id="cancel_reason_id"
              required={true}
              disabled={isCancelled}
              value={userInput.cancel_reason_id}
              onChange={(e) => handleInputChange('cancel_reason_id', e.value)}
              options={supportingData?.cancel_reasons?.map(opt => {
                return {value: opt.id, label: opt.name};
              }) ?? []}
            />
          </div>
        </div>
        <div className="row mt-1">
          <div className="col-6">
            <label>* Mileage</label>
            <br/>
            <input
              type="number"
              className="form-control ml-1"
              name="current_mileage"
              disabled={isFlatCancel() || isCancelled}
              value={userInput?.current_mileage}
              onChange={(e) => handleInputChange('current_mileage', e.target.value)}
            />
          </div>
          <div className="col-6">
            <div className="row">
              <div className="col">
                <label>Tax Rate</label>
                <br/>
                <input
                  type="number"
                  name="tax_rate"
                  className="form-control"
                  disabled={isCancelled}
                  value={userInput?.tax_rate}
                  onChange={(e) => handleInputChange('tax_rate', e.target.value)}
                />
              </div>
            </div>
            <div className="row mb-2">
              <div className="col">
                <small className="tip">{taxTipText}</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const handleTypeChange = (field, value) => {
    if (field === "refundType") {
      setRefundType(value);
      setUserInput(prev => ({
        ...prev,
        refund_type: value
      }));
    }
  };

  const optionalInputSection = () => {
    return (
      <div style={inputSectionStyles}>
        <div className="row mt-2">
          <div className="col-6">
            <label>NSD Key Claims</label>
            <br/>
            <input
              type="number"
              className="form-control"
              name="nsd_key_claims"
              disabled={isCancelled}
              value={userInput?.nsd_key_claims}
              onChange={(e) => handleInputChange('nsd_key_claims', e.target.value)}
            />
          </div>
          <div className="col-6">
            <label>NSD Tire & Wheel Claims</label>
            <br/>
            <input
              type="number"
              className="form-control"
              name="nsd_tire_and_wheel_claims"
              disabled={isCancelled}
              value={userInput?.nsd_tire_and_wheel_claims}
              onChange={(e) => handleInputChange('nsd_tire_and_wheel_claims', e.target.value)}
            />
          </div>
        </div>
        <div className="row mt-1">
          <div className="col-6">
            <label>NSD VTA Claims</label>
            <br/>
            <input
              type="number"
              className="form-control"
              name="nsd_vta_claims"
              disabled={isCancelled}
              value={userInput?.nsd_vta_claims}
              onChange={(e) => handleInputChange('nsd_vta_claims', e.target.value)}
            />
          </div>
          <div className="col-6">
            <label>SPP Amount Paid</label>
            <br/>
            <input
              type="number"
              className="form-control"
              name="spp_customer_paid"
              disabled={isCancelled}
              value={userInput?.spp_customer_paid}
              onChange={(e) => handleInputChange('spp_customer_paid', e.target.value)}
            />
          </div>
        </div>
        <div className="row mt-1">
          <div className="col-6">
            <label>SPP Balance</label>
            <br/>
            <input
              type="number"
              className="form-control"
              name="spp_balance"
              disabled={isCancelled}
              value={userInput?.spp_balance}
              onChange={(e) => handleInputChange('spp_balance', e.target.value)}
            />
          </div>
          {isSPPContractInEstimate && (
            <div className="col-6">
              <label>SPP Refund Type </label>
              <br/>
              <div className="form-check form-check-inline">
                <input
                  type="radio"
                  className="form-check-input"
                  name="refund_type"
                  id="spp_refund-radio"
                  onChange={() => handleTypeChange("refundType", "SPP Refund")}
                  checked={refundType === "SPP Refund"}
                  disabled={isCancelled}
                />
                <label className="form-check-label" htmlFor="spp_refund-radio">
                  SPP Refund
                </label>
              </div>
              <div className="form-check form-check-inline">
                <input
                  type="radio"
                  className="form-check-input"
                  name="refund_type"
                  id="customer_refund-radio"
                  onChange={() => handleTypeChange("refundType", "Customer Refund")}
                  checked={refundType === "Customer Refund"}
                  disabled={isCancelled}
                />
                <label className="form-check-label" htmlFor="customer_refund-radio">
                  Customer Refund
                </label>
              </div>
            </div>
          )}
        </div>
        {isSPPContractInEstimate && (
          <div className="row mt-1">
            <div className="col" style={{color:'red'}}>
              NOTE: There is one or more SPP contract in cancel list.
            </div>
          </div>
        )}
      </div>
    );
  };

  const inputSection = () => {
    return (
      <div className="row mt-2">
        <div className="col">
          <ul className="nav nav-tabs" role="tablist">
            {
              <li className="nav-item" key={BASIC_INPUT_TAB}>
                <a
                  className={`nav-link ${activeInputTab === BASIC_INPUT_TAB ? "active" : "text-primary"}`}
                  data-toggle="tab"
                  role="tab"
                  accessKey={BASIC_INPUT_TAB}
                  aria-controls={BASIC_INPUT_TAB}
                  onClick={() => setActiveInputTab(BASIC_INPUT_TAB)}
                >
                  Required Input
                </a>
              </li>
            }
            {
              <li className="nav-item" key={OPTIONAL_INPUT_TAB}>
                <a
                  className={`nav-link ${activeInputTab === OPTIONAL_INPUT_TAB ? "active" : "text-primary"}`}
                  data-toggle="tab"
                  role="tab"
                  accessKey={OPTIONAL_INPUT_TAB}
                  aria-controls={OPTIONAL_INPUT_TAB}
                  onClick={() => setActiveInputTab(OPTIONAL_INPUT_TAB)}
                >
                  Additional Input
                </a>
              </li>
            }
          </ul>
          <div className="tab-content">
            {activeInputTab === BASIC_INPUT_TAB ? basicInputSection() : optionalInputSection()}
          </div>
        </div>
      </div>
    );
  };

  const quoteSection = () => {
    return (
      <div className="row mt-2 text-center">
        <div className="col">
          <ul className="nav nav-tabs" role="tablist">
            {
              <li className="nav-item" key={CANCELLATION_TAB}>
                <a
                  className={`nav-link ${activeTab === CANCELLATION_TAB ? "active" : "text-primary"}`}
                  data-toggle="tab"
                  role="tab"
                  accessKey={CANCELLATION_TAB}
                  aria-controls={CANCELLATION_TAB}
                  onClick={() => setActiveTab(CANCELLATION_TAB)}
                >
                  Contract List
                </a>
              </li>
            }
          </ul>
          <div className="tab-content">
            <div style={{overflowX: 'scroll', width: '100%'}}>
              {showQuotes()}
            </div>
          </div>
        </div>
      </div>
    );
  };
  
  const getAllSelectedForCancellation = () => {
    if (contractsToCancel.length === 0){
      return false;
    }
    return contractsToCancel.length === quotes.filter(quote => quote.cancellable && quote.message_type !== "error").length;
  };

  const handleSelectAll = (checked) => {
    if (checked) {
      const updatedList = [];
      const updatedSelected = [];
      const newRefundPayeeFields = { ...refundPayeeFields };
      const newRefundPayableToSelections = { ...refundPayableToSelections };
      const updatedQuotes = quotes.map(quote => {
        if (quote.cancellable && quote.message_type !== "error") {
          updatedList.push(quote.id);
          updatedSelected.push(quote.id);
          // Initialize payee fields if not already set
          if (!newRefundPayeeFields[quote.id]) {
            newRefundPayeeFields[quote.id] = {
              payee: '',
              payeeName: '',
              payeeAttentionTo: '',
              payeeAddress: '',
              payeeCity: '',
              payeeState: '',
              payeePostalCode: '',
              cancelStoreID: '',
            };
          }
          if (!newRefundPayableToSelections[quote.id]) {
            newRefundPayableToSelections[quote.id] = '';
          }
          return { ...quote, selected: true };
        }
        return { ...quote, selected: false };
      });
      setContractsToCancel(updatedList);
      setSelectedQuotes(updatedSelected);
      setRefundPayeeFields(newRefundPayeeFields);
      setRefundPayableToSelections(newRefundPayableToSelections);
      setQuotes(updatedQuotes);
    } else {
      setContractsToCancel([]);
      setSelectedQuotes([]);
      // Remove all payee fields and selections for all contracts
      setRefundPayeeFields({});
      setRefundPayableToSelections({});
      // Also update quotes to set selected: false for all
      const updatedQuotes = quotes.map(q => ({ ...q, selected: false }));
      setQuotes(updatedQuotes);
    }
  };
  
  const getContractStatusBlock = (estimate) => {
    let contractStatus = estimate.status;
    let contractStatusMap = CONTRACT_STATUS_MAP;
    let contractStatusBlock = "";
    if (contractStatus === contractStatusMap.Active || contractStatus === contractStatusMap.Remitted) {
      contractStatusBlock = <span className="badge badge-success">{`Active`}</span>;
    } else if (contractStatus === contractStatusMap.Pending) {
      if(estimate.product_type_code === PRODUCT_CODE_MAP.gap){
        contractStatusBlock = <span className="badge badge-warning">{`Paid`}</span>;
      } else {
        contractStatusBlock = <span className="badge badge-warning">{`Pending`}</span>;
      }
    } else if (contractStatus === contractStatusMap.Canceled) {
      contractStatusBlock = <span className="badge badge-danger">{`Cancelled`}</span>;
    } else if (contractStatus === contractStatusMap.Expired) {
      contractStatusBlock = <span className="badge badge-danger">{`Expired`}</span>;
    } else if (contractStatus === contractStatusMap.Generated) {
      contractStatusBlock = <span className="badge badge-success">{`New`}</span>;
    }
    return contractStatusBlock;
  };

  const renderQuotesTableHeaders = () => {
    return (
      <thead>
        <tr className="table-header-row">
          <th>
            <input
              type="checkbox"
              className="form-check-inline"
              checked={getAllSelectedForCancellation()}
              onChange={(e) => handleSelectAll(e.target.checked)}
            />
          </th>
          <th>Contract</th>
          <th>Factor</th>
          <th>Fee</th>
          <th className="text-right">Customer Refund</th>
          <th className="text-right">Store Refund</th>
          <th className="text-right">Sales Tax</th>
          <th>Total Claims</th>
          <th className="text-right">Total Claim Amount Paid</th>
          <th>Type</th>
          <th>Issuing Dealer</th>
          <th>Effective Date</th>
        </tr>
      </thead>
    );
  };

  const handleCancellationSelection = (checked, id) => {
    if (checked) {
      setContractsToCancel([...contractsToCancel, id]);
      setSelectedQuotes([...selectedQuotes, id]);
      // Reset payee fields and selection to defaults
      setRefundPayeeFields(prev => ({
        ...prev,
        [id]: {
          payee: '',
          payeeName: '',
          payeeAttentionTo: '',
          payeeAddress: '',
          payeeCity: '',
          payeeState: '',
          payeePostalCode: '',
          cancelStoreID: '',
        }
      }));
      setRefundPayableToSelections(prev => ({
        ...prev,
        [id]: ''
      }));
    } else {
      const updatedList = contractsToCancel.filter(contractIDs => contractIDs !== id);
      const updatedSelected = selectedQuotes.filter(quoteId => quoteId !== id);
      setContractsToCancel(updatedList);
      setSelectedQuotes(updatedSelected);
      // Remove payee fields and selection for this contract
      setRefundPayeeFields(prev => {
        const copy = { ...prev };
        delete copy[id];
        return copy;
      });
      setRefundPayableToSelections(prev => {
        const copy = { ...prev };
        delete copy[id];
        return copy;
      });
    }

    const updatedQuotes = quotes.map(quote => {
      if (quote.id === id) {
        return { ...quote, selected: checked };
      }
      return quote;
    });
    setQuotes(updatedQuotes);
  };

  const renderQuotesTableBody = () => {
    return (
      <>
        {
          quotes?.map((quote, index) => {
            let violationDisplayMessage = ViolationTypes.AdditionalStepsDisplay;
            let violationType = ViolationTypes.AdditionalSteps;
            (quote.rule_violations || []).forEach(rv => {
              if (rv.violation_type === ViolationTypes.NotCancellable) {
                violationType = ViolationTypes.NotCancellable;
                violationDisplayMessage = ViolationTypes.NotCancellableDisplay;
              }
            });
            return (
              <tbody key={quote.id} className={index % 2 === 0 ? 'table-row-odd' : 'table-row-even'}>
                <tr key={index}>
                  <td>
                    {
                      quote.cancellable && (
                        <input
                          type="checkbox"
                          className="form-check-inline"
                          checked={selectedQuotes.includes(quote.id)}
                          onChange={(e) => handleCancellationSelection(e.target.checked, quote.id)}
                        />
                      )
                    }
                  </td>
                  <td>
                    {quote.original_code || quote.code}<br/>
                    {getContractStatusBlock(quote)}
                  </td>
                  {
                    quote.cancellable ? (
                      <>
                        <td>{quote.factor}</td>
                        <td>{accounting.formatMoney(quote.fee)}</td>
                        <td className="text-right">{accounting.formatMoney(quote.customer_refund)}</td>
                        <td className="text-right">
                          {selectedQuotes.includes(quote.id) && accounting.formatMoney(quote.store_refund)}
                        </td>
                        <td className="text-right">{accounting.formatMoney(quote.sales_tax)}</td>
                        <td>
                          {quote.claim_count}
                        </td>
                        <td className="text-right">
                          {accounting.formatMoney(quote.claim_total_amount)}
                        </td>
                      </>
                    ) : (
                      <td colSpan={7}>
                        <span
                          data-toggle="popover"
                          data-placement="top"
                          data-trigger="hover focus"
                          data-delay={100}
                          className="popover-container"
                          data-html="true"
                          title={`${quote.rule_violations.map((rule, i) => {
                            return rule.violation_message.indexOf('Cancellation contact information') !== -1 ? `<b>${rule.violation_message}</b>` : rule.violation_message;
                          }).join("\n")}`}
                        >
                          <span className={`${GetDisplayClassForViolationType(violationType)}`}>
                            <i className="fa fa-exclamation-triangle"/> {violationDisplayMessage}
                          </span>
                        </span>
                      </td>
                    )
                  }
                  <td>
                    {`${quote.product_type_name}*`}
                  </td>
                  <td>
                    {quote.store_code}
                  </td>
                  <td>
                    {moment.utc(quote.effective_date).format(dateFormat)}
                  </td>
                </tr>
                {quote.payment_type === "SPP" && quote.cancellable && (
                  <>
                    <tr className="d-none"></tr>
                    <tr>
                      <td colSpan="100%">
                        <div className="container">
                          <div className="row mb-1">
                            <div className="col font-weight-bold">Adjusted Customer Refund</div>
                            <div className="col font-weight-bold">Store Chargeback</div>
                            <div className="col font-weight-bold">SPP Refund</div>
                            <div className="col font-weight-bold">NSD Refund</div>
                          </div>
                          <div className="row">
                            <div className="col">{accounting.formatMoney(quote.adjusted_customer_refund)}</div>
                            <div className="col">{accounting.formatMoney(quote.store_chargeback)}</div>
                            <div className="col">{accounting.formatMoney(quote.spp_refund)}</div>
                            <div className="col">{accounting.formatMoney(quote.nsd_refund)}</div>
                          </div>
                        </div>
                      </td>
                    </tr>
                  </>
                )}
                {quote.payment_type === "SPP" && (
                  <>
                    <tr className="d-none"></tr>
                    <tr>
                      <td colSpan="100%">
                        <div style={{color:'red', textAlign: 'left', paddingLeft: '10px'}}>
                          {`NOTE: This contract is an SPP payment type.`}
                        </div>
                      </td>
                    </tr>
                  </>
                )}
                {quote.selected && !disablePayeeInput() && (
                  <tr>
                    <td colSpan="100%" style={{ borderBottom: '1px solid #e3e3e3', padding: 0 }}>
                      <div style={{ padding: '10px 16px 10px 80px' }}>
                        {renderRefundPayableToDropdown(quote)}
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            );
          })
        }
      </>
    );
  };

  const renderFooter = () => {
    // First check if there are any selected quotes
    const hasSelectedQuotes = selectedQuotes.length > 0;

    let customerRefundTotal = 0;
    let storeRefundTotal = 0;
    let salesTaxTotal = 0;
    let customerRefundAmount = false;
    let storeRefundAmount = false;
    let salesTaxAmount = false;

    if (hasSelectedQuotes) {
      for (const quote of quotes) {
        if (selectedQuotes.includes(quote.id) && quote.cancellable) {
          if (quote.customer_refund) {
            customerRefundAmount = true;
            customerRefundTotal += accounting.unformat(quote.customer_refund);
          }
          if (quote.store_refund) {
            storeRefundAmount = true;
            storeRefundTotal += accounting.unformat(quote.store_refund);
          }
          if (quote.sales_tax) {
            salesTaxAmount = true;
            salesTaxTotal += accounting.unformat(quote.sales_tax);
          }
        }
      }
    }

    return (
      <tr className='footer'>
        <td colSpan={4} style={{ textAlign: "right" }} className='total'>
          <strong>Grand Total</strong>
        </td>
        <td style={{ textAlign: "right" }}>
          <strong>{hasSelectedQuotes && customerRefundAmount ? accounting.formatMoney(customerRefundTotal) : null}</strong>
        </td>
        <td style={{ textAlign: "right" }}>
          <strong>{hasSelectedQuotes && storeRefundAmount ? accounting.formatMoney(storeRefundTotal) : null}</strong>
        </td>
        <td style={{ textAlign: "right" }}>
          <strong>{hasSelectedQuotes && salesTaxAmount ? accounting.formatMoney(salesTaxTotal) : null}</strong>
        </td>
        <td colSpan="5" />
      </tr>
    );
  };

  const showQuotes = () => {
    if (quotes.length === 0) {
      return null;
    }

    return (
      <table className="table table-hover quotes-table">
        {renderQuotesTableHeaders()}
        {renderQuotesTableBody()}
        <tfoot>
          {renderFooter()}
        </tfoot>
      </table>
    );
  };

  const buildContractPayeesPayload = (selectedContracts, refundPayeeFields) => {
    const contractPayees = {};
    selectedContracts.forEach(contractId => {
      const payeeFields = refundPayeeFields[contractId];
      if (payeeFields) {
        contractPayees[contractId] = {
          payee: payeeFields.payee,
          payee_name: payeeFields.payeeName,
          payee_attention_to: payeeFields.payeeAttentionTo,
          payee_address: payeeFields.payeeAddress,
          payee_city: payeeFields.payeeCity,
          payee_state: payeeFields.payeeState,
          payee_postal_code: payeeFields.payeePostalCode,
          cancel_store_id: payeeFields.cancelStoreID !== undefined && payeeFields.cancelStoreID !== null && payeeFields.cancelStoreID !== ""
            ? String(payeeFields.cancelStoreID)
            : "",
        };
      }
    });
    return contractPayees;
  };

  const renderRefundPayableToDropdown = (estimate) => {
    const payee = refundPayableToSelections[estimate.id] || '';
    let payeeFields = refundPayeeFields[estimate.id];

    if (estimate.selected && !payeeFields) {
      payeeFields = {
        payee: '',
        payeeName: '',
        payeeAttentionTo: '',
        payeeAddress: '',
        payeeCity: '',
        payeeState: '',
        payeePostalCode: '',
        cancelStoreID: '',
      };
    }

    const supportingData = contractSupportingData[estimate.id] || {};
    const payeeOptions = supportingData.cancel_payees || [];
    const storeOptions = supportingData.cancel_stores || [];
    const stateOptions = supportingData.states || {};
    let disablePayeeName = true;
    if (hstore.has(context.user.roles, Roles.AccountRepManager) && (payee === PAYEE_LENDER || payee === PAYEE_RESERVES)) {
      disablePayeeName = false;
    }

    return (
      <div style={{ width: '100%' }}>
        {/* Row 1: Payee Selection and Name Fields */}
        <div className="row mb-2" style={{ alignItems: 'center' }}>
          <div className="col" style={{maxWidth: '240px', minWidth: '180px'}}>
            <div className="form-group mb-0">
              <label className="text-left w-100" style={{textAlign: 'left'}}><strong>Refund Payable To*</strong></label>
              <select
                className="form-control"
                style={{ minWidth: 140 }}
                value={payee}
                onChange={e => handleRefundPayableToChange(estimate.id, e.target.value)}
                disabled={disablePayeeInput()}
              >
                <option key="" value="">Select payee...</option>
                {payeeOptions.map(opt => (
                  <option key={opt} value={opt}>{opt}{opt === "Reserves" && " - no refund"}</option>
                ))}
              </select>
            </div>
          </div>
          {payee && (
            <>
              {/* For DownPayment, show Store dropdown before Store Name */}
              {payee === PAYEE_DOWN_PAYMENT && (
                <div className="col" style={{maxWidth: '350px'}}>
                  <div className="form-group mb-0">
                    <label className="text-left w-100" style={{textAlign: 'left'}}>Store</label>
                    <select className="form-control"
                      value={payeeFields.cancelStoreID || ''}
                      onChange={e => handleRefundPayeeFieldChange(estimate.id, 'cancelStoreID', e.target.value)}>
                      <option key="" value="">Select store...</option>
                      {storeOptions.map(store => (
                        <option key={store.id} value={store.id}>{store.name} - {store.code}</option>
                      ))}
                    </select>
                  </div>
                </div>
              )}
              <div className="col" style={{maxWidth: '300px'}}>
                <div className="form-group mb-0">
                  <label className="text-left w-100" style={{textAlign: 'left'}}>{ payee === PAYEE_CUSTOMER && PAYEE_CUSTOMER}{ (payee === PAYEE_LENDER || payee === PAYEE_RESERVES) && PAYEE_LENDER}{ (payee === PAYEE_DOWN_PAYMENT || payee === PAYEE_STORE_REFUND) && 'Store'} Name*</label>
                  <input type="string"
                    className="form-control"
                    value={payeeFields.payeeName || ''}
                    onChange={e => handleRefundPayeeFieldChange(estimate.id, 'payeeName', e.target.value)}
                    disabled={disablePayeeName}
                  />
                </div>
              </div>
              {/* For Store Issued Refund, do not show Store dropdown */}
              { payee === PAYEE_LENDER && (
                <div className="col" style={{maxWidth: '250px'}}>
                  <div className="form-group mb-0">
                    <label className="text-left w-100" style={{textAlign: 'left'}}>Attention To</label>
                    <input type="string"
                      className="form-control"
                      value={payeeFields.payeeAttentionTo || ''}
                      onChange={e => handleRefundPayeeFieldChange(estimate.id, 'payeeAttentionTo', e.target.value)}
                    />
                  </div>
                </div>
              )}
            </>
          )}
        </div>

        {/* Row 2: Address Fields */}
        {payee && (
          <div className="row" style={{ alignItems: 'center' }}>
            <div className="col" style={{maxWidth: '350px'}}>
              <div className="form-group mb-0">
                <label className="text-left w-100" style={{textAlign: 'left'}}>Address*</label>
                <input type="string"
                  className="form-control"
                  value={payeeFields.payeeAddress || ''}
                  onChange={e => handleRefundPayeeFieldChange(estimate.id, 'payeeAddress', e.target.value)}
                />
              </div>
            </div>
            <div className="col" style={{maxWidth: '200px'}}>
              <div className="form-group mb-0">
                <label className="text-left w-100" style={{textAlign: 'left'}}>City*</label>
                <input type="string"
                  className="form-control"
                  value={payeeFields.payeeCity || ''}
                  onChange={e => handleRefundPayeeFieldChange(estimate.id, 'payeeCity', e.target.value)}
                />
              </div>
            </div>
            <div className="col" style={{maxWidth: '200px'}}>
              <div className="form-group mb-0">
                <label className="text-left w-100" style={{textAlign: 'left'}}>State*</label>
                <select className="form-control"
                  value={payeeFields.payeeState || ''}
                  onChange={e => handleRefundPayeeFieldChange(estimate.id, 'payeeState', e.target.value)}>
                  <option key="" value="">Select state...</option>
                  {Object.keys(stateOptions).map(code => (
                    <option key={code} value={code}>{code} - {stateOptions[code]}</option>
                  ))}
                </select>
              </div>
            </div>
            <div className="col" style={{maxWidth: '150px'}}>
              <div className="form-group mb-0">
                <label className="text-left w-100" style={{whiteSpace: 'nowrap', textAlign: 'left'}}>Postal Code*</label>
                <input type="string"
                  className="form-control"
                  value={payeeFields.payeePostalCode || ''}
                  onChange={e => handleRefundPayeeFieldChange(estimate.id, 'payeePostalCode', e.target.value)}
                />
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  const handleUpdateAttachments = (attachments) => {
    setAttachments(attachments);
  };

  const handleCheckPreference = (isElectronicCheck) => {
    setIsElectronicCheck(isElectronicCheck);
    if (isElectronicCheck) {
      // Find the first selected contract with payee Customer
      let foundEmail = '';
      for (const id of selectedQuotes) {
        if (refundPayableToSelections[id] === PAYEE_CUSTOMER) {
          const contractData = contractSupportingData[id] || {};
          if (contractData.payee_customer && contractData.payee_customer.email) {
            foundEmail = contractData.payee_customer.email;
            break;
          }
        }
      }
      setEmail(foundEmail);
    } else {
      setEmail('');
    }
  };

  const handleEmailChange = (value) => {
    setEmail(value.trim());
  };

  const uploadDataSection = () => {
    return (
      <>
        <div className="row">
          <div className="col-12">
            <button
              type="button"
              className="btn btn-secondary btn-sm"
              onClick={() => setShowHelp(true)}
            >
              <i className='fa fa-question-circle'></i> Help
            </button>
          </div>
        </div>
        {
          showHelp && (
            <Modal
              visible={showHelp}
              close={() => setShowHelp(false)}
              size="large"
            >
              <CancellationHelpModal
                cancellationReasons={supportingData?.cancel_reasons || []}
                close={() => setShowHelp(false)}
              />
            </Modal>
          )
        }
        <div className="row mt-2">
          <div className="col-6">
            {(() => {
              if (disablePayeeInput()) return null;
              let showRefundPayeeMsg = false;
              for (const quote of quotes) {
                if (selectedQuotes.includes(quote.id)) {
                  const payeeFields = refundPayeeFields[quote.id];
                  if (!payeeFields || !payeeFields.payee || !payeeFields.payeeName || !payeeFields.payeeAddress || !payeeFields.payeeCity || !payeeFields.payeeState || !payeeFields.payeePostalCode) {
                    showRefundPayeeMsg = true;
                    break;
                  }
                }
              }
              if (showRefundPayeeMsg) {
                return (
                  <div className="font-weight-bold text-danger mb-2">
                    * Please select refund payable to for all contracts to cancel.
                  </div>
                );
              }
              return null;
            })()}
          </div>
        </div>
        <div className="row mt-2">
          <div className="col-6">
            <Attachments
              id="attachment-files"
              allowMultiple={true}
              maxFiles={2}
              acceptedFileTypes={[
                'image/*',
                'application/pdf', // *.pdf
                'text/csv',  // *.csv
                'text/plain',   // *.txt
                'application/msword', // *.doc
                'application/vnd.ms-excel', // *.xls
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // *.docx
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' // *.xlsx
              ]}
              onUpdateAttachments={handleUpdateAttachments}
            />
          </div>
        </div>
        <div className="row mt-1">
          <div className="col-6">
            <input
              type="checkbox"
              className="form-check-inline"
              checked={!includeBill}
              onChange={() => setIncludeBill(!includeBill)}
            />
            <label className="form-check-label"><strong>Don&apos;t Include Bill</strong></label>
          </div>
        </div>
        <div className="row mt-1">
          <div className="col-6">
            <input
              type="checkbox"
              className="form-check-inline"
              checked={applyFee}
              onChange={() => setApplyFee(!applyFee)}
            />
            <label className="form-check-label"><strong>Apply Processing Fee</strong></label>
          </div>
        </div>
        {
          (() => {
            const showElectronicCheckOption = selectedQuotes.some(id => {
              const payee = refundPayableToSelections[id];
              return payee === PAYEE_CUSTOMER;
            });

            if (!showElectronicCheckOption) return null;

            return (
              <>
                <div className="check-options-box">
                  <div style={{ fontSize: '0.95em', color: '#6c757d', marginBottom: '8px' }}>
                    Applicable for Customer only
                  </div>
                  <div className="form-group mb-2 d-flex align-items-center" style={{ gap: '24px' }}>
                    <div className="form-check form-check-inline mb-0">
                      <input
                        type='radio'
                        name="check_preferance"
                        className="form-check-input"
                        checked={isElectronicCheck}
                        onChange={() => handleCheckPreference(true)}
                        radioGroup="check_preferance"
                        id="electronic-check-radio"
                      />
                      <label className="form-check-label" htmlFor="electronic-check-radio"><strong>Electronic Check</strong></label>
                    </div>
                    <div className="form-check form-check-inline mb-0">
                      <input
                        type='radio'
                        name="check_preferance"
                        className="form-check-input"
                        checked={!isElectronicCheck}
                        onChange={() => handleCheckPreference(false)}
                        radioGroup="check_preferance"
                        id="paper-check-radio"
                      />
                      <label className="form-check-label" htmlFor="paper-check-radio"><strong>Paper Check</strong></label>
                    </div>
                  </div>
                  {
                    isElectronicCheck && (
                      <div className="form-group mt-1">
                        <label>Email Address*</label>
                        <input
                          type="email"
                          id="email"
                          className="form-control"
                          style={{ maxWidth: '450px' }}
                          value={email}
                          onChange={(e) => handleEmailChange(e.target.value)}
                        />
                      </div>
                    )
                  }
                </div>
              </>
            );
          })()
        }
      </>
    );
  };

  const closeSPPCancel = () => {
    setShowSPPConfirmation(false);
    setSppContracts([]);
  };

  const renderSPPCancel = () => {
    return (
      <Modal visible={showSPPConfirmation} manualOverflow={true}>
        <form onSubmit={onCancelSppContract}>
          <h3>{`SPP Cancel Confirmation`}</h3>
          <hr/>
          <div className='form-group'>
            <label><h5>Confirm: </h5></label>
            <br />
            {`Do you want to cancel ${sppContracts.length} spp contract ?`}
          </div>
          <div className='clearfix'>
            <div className='float-right'>
              <button type='submit' className='btn btn-primary'>
                <i className='fa fa-check' /> Yes
              </button>
              {' '}
              <button type='button' className='btn btn-secondary' onClick={closeSPPCancel}>
                <i className='fa fa-ban' /> Cancel
              </button>
            </div>
          </div>
        </form>
      </Modal>
    );
  };

  const renderExpiredCancel = () => {
    return (
      <Modal visible={showExpiredConfirmation} manualOverflow={true}>
        <form onSubmit={onCancelExpiredContract}>
          <h3>{`Expired Cancel Confirmation`}</h3>
          <hr/>
          <div className='form-group'>
            <label><h5>Confirm: </h5></label>
            <br />
            {`You are canceling one or more expired contracts. Are you sure you want to proceed?`}
          </div>
          <div className='clearfix'>
            <div className='float-right'>
              <button type='submit' className='btn btn-primary'>
                <i className='fa fa-check' /> Yes
              </button>
              {' '}
              <button type='button' className='btn btn-secondary' onClick={() => setShowExpiredConfirmation(false)}>
                <i className='fa fa-ban' /> No
              </button>
            </div>
          </div>
        </form>
      </Modal>
    );
  };

  const cancellationReason = supportingData?.cancel_reasons?.find(reason => userInput.cancel_reason_id === reason.id);
  const disablePayeeInput = () => {
    return (cancellationReason?.name === CANCEL_REASON_FLAT_UNWIND || 
      cancellationReason?.name === CANCEL_REASON_SPP_DEFAULT ||
      cancellationReason?.name === CANCEL_REASON_SPP_CUSTOMER_REQUEST);
  };

  const showElectronicCheckOption = selectedQuotes.some(id => {
    const payee = refundPayableToSelections[id];
    return payee === PAYEE_CUSTOMER;
  });

  let disableCancel = contractsToCancel.length === 0 || (showElectronicCheckOption && isElectronicCheck && email === '');
  if (!isCancelled && !disableCancel && !disablePayeeInput()) {
    for (const quote of quotes) {
      if (quote.selected) {
        const payeeFields = refundPayeeFields[quote.id];
        if (!payeeFields || !payeeFields.payee || !payeeFields.payeeName || !payeeFields.payeeAddress || !payeeFields.payeeCity || !payeeFields.payeeState || !payeeFields.payeePostalCode) {
          disableCancel = true;
          break;
        }
      }
    }
  }
  
  const disableGetQuote = (theUserInput = userInput) => {
    const {cancel_date, cancel_reason_id, current_mileage} = theUserInput;
    if (loadingQuotes || !cancel_reason_id) {
      return true;
    }
    if (isFlatCancel(theUserInput)) {
      return false;
    }
    if (!current_mileage || !cancel_date) {
      return true;
    }
    return false;
  };

  const handleReservesChangeModalConfirm = () => {
    // Save note for this contract
    const newNotes = { ...reservesChangeNotes, [reservesChangeModalContractId]: reservesChangeModalNote };
    // Set reservesSetBySystem to false to prevent modal loop
    const newReservesSetBySystem = { ...reservesSetBySystem, [reservesChangeModalContractId]: false };
    // Actually change the payee now
    setShowReservesChangeModal(false);
    setReservesChangeModalContractId(null);
    setReservesChangeModalNewPayee('');
    setReservesChangeModalNote('');
    setReservesChangeNotes(newNotes);
    setReservesSetBySystem(newReservesSetBySystem);
    // Now update the payee selection
    handleRefundPayableToChange(reservesChangeModalContractId, reservesChangeModalNewPayee, true);
  };

  const handleReservesChangeModalCancel = () => {
    // Revert payee selection to Reserves
    setShowReservesChangeModal(false);
    setReservesChangeModalContractId(null);
    setReservesChangeModalNewPayee('');
    setReservesChangeModalNote('');
    setRefundPayableToSelections(prev => ({
      ...prev,
      [reservesChangeModalContractId]: PAYEE_RESERVES,
    }));
  };

  const renderReservesChangeModal = () => {
    if (!showReservesChangeModal) return null;
    return (
      <Modal visible={showReservesChangeModal} manualOverflow={true} close={handleReservesChangeModalCancel}>
        <form onSubmit={e => { e.preventDefault(); handleReservesChangeModalConfirm(); }}>
          <h4>Change Lender Debit Reserves Selection</h4>
          <div className="form-group">
            <label>Please enter a note explaining why you are changing from &quot;Reserves - No Refund&quot;:</label>
            <textarea
              className="form-control"
              value={reservesChangeModalNote}
              onChange={(e) => setReservesChangeModalNote(e.target.value)}
              required
              rows={3}
            />
          </div>
          <div className="clearfix">
            <div className="float-right">
              <button type="submit" className="btn btn-primary" disabled={!reservesChangeModalNote.trim()}>
                <i className="fa fa-check" /> Confirm
              </button>
              {' '}
              <button type="button" className="btn btn-secondary" onClick={handleReservesChangeModalCancel}>
                <i className="fa fa-ban" /> Cancel
              </button>
            </div>
          </div>
        </form>
      </Modal>
    );
  };

  const handleRefundPayableToChange = (id, value, skipModalCheck = false) => {
    const prevPayee = refundPayableToSelections[id] || '';
    const newReservesSetBySystem = { ...reservesSetBySystem };
    if (
      !skipModalCheck &&
      prevPayee === PAYEE_RESERVES &&
        value !== PAYEE_RESERVES &&
        prevPayee !== value &&
        reservesSetBySystem[id]
    ) {
      setShowReservesChangeModal(true);
      setReservesChangeModalContractId(id);
      setReservesChangeModalNewPayee(value);
      setReservesChangeModalNote('');
      return;
    }

    if (prevPayee === PAYEE_RESERVES && value !== PAYEE_RESERVES) {
      newReservesSetBySystem[id] = false;
    }
    // If user manually selects reserves, do NOT set reservesSetBySystem to true
    if (value === PAYEE_RESERVES && !newReservesSetBySystem[id]) {
      newReservesSetBySystem[id] = false;
    }

    const newRefundPayableToSelections = { ...refundPayableToSelections, [id]: value };
    const newRefundPayeeFields = { ...refundPayeeFields };
    const contractData = contractSupportingData[id] || {};
    let payeeData = {};
    let cancelStoreID = '';

    if (value === PAYEE_LENDER && contractData.payee_lender) {
      payeeData = contractData.payee_lender;
    } else if (value === PAYEE_CUSTOMER && contractData.payee_customer) {
      payeeData = contractData.payee_customer;
    } else if (
      (value === PAYEE_DOWN_PAYMENT || value === PAYEE_STORE_REFUND) &&
        contractData.cancel_stores
    ) {
      cancelStoreID = refundPayeeFields[id]?.cancelStoreID || contractData.payee_store_id || '';
      const storeObj = contractData.cancel_stores.find(s => s.id == cancelStoreID);
      if (storeObj) {
        payeeData = storeObj;
      }
    } else if (value === PAYEE_RESERVES && contractData.payee_lender) {
      payeeData = contractData.payee_lender;
    }

    newRefundPayeeFields[id] = {
      payee: value,
      payeeName: payeeData.name || '',
      payeeAttentionTo: payeeData.attention || '',
      payeeAddress: payeeData.address || '',
      payeeCity: payeeData.city || '',
      payeeState: payeeData.state_code || '',
      payeePostalCode: payeeData.postal_code || '',
      cancelStoreID: cancelStoreID,
    };

    setRefundPayableToSelections(newRefundPayableToSelections);
    setRefundPayeeFields(newRefundPayeeFields);
    setReservesSetBySystem(newReservesSetBySystem);
  };

  const handleRefundPayeeFieldChange = (id, field, value) => {
    const newRefundPayeeFields = { ...refundPayeeFields };
    let updatedFields = { ...newRefundPayeeFields[id], [field]: value };

    const payee = updatedFields.payee;
    const contractData = contractSupportingData[id] || {};
    if (
      (payee === PAYEE_DOWN_PAYMENT || payee === PAYEE_STORE_REFUND) &&
        field === 'cancelStoreID' &&
        contractData.cancel_stores
    ) {
      const storeObj = contractData.cancel_stores.find(s => s.id == value);
      if (storeObj) {
        updatedFields = {
          ...updatedFields,
          payeeName: storeObj.name || '',
          payeeAttentionTo: '',
          payeeAddress: storeObj.address || '',
          payeeCity: storeObj.city || '',
          payeeState: storeObj.state_code || '',
          payeePostalCode: storeObj.postal_code || '',
        };
      }
    }
    newRefundPayeeFields[id] = updatedFields;
    setRefundPayeeFields(newRefundPayeeFields);
  };

  // Deterministic effect to fetch quotes only after both contract and userInput are updated
  useEffect(() => {
    if (
      shouldFetchQuote &&
      userInput.cancel_reason_id > 0 &&
      userInput.cancel_date !== '' &&
      userInput.current_mileage !== 0 &&
      selectedContract.id &&
      supportingData.product_types
    ) {
      getCancellationQuote(userInput, selectedContract.id, supportingData.product_types);
      setShouldFetchQuote(false);
    }
  }, [shouldFetchQuote, userInput, selectedContract, supportingData]);

  // Synchronize selectedContract and userInput updates when pendingQuote is set
  useEffect(() => {
    if (pendingQuote) {
      setSelectedContract(pendingQuote.contract);
      setUserInput(pendingQuote.userInput);
      setShouldFetchQuote(true);
      setPendingQuote(null);
    }
  }, [pendingQuote]);

  // Add this effect to clear reservesSetBySystem when cancel reason changes and digital reserves rule is not applicable
  useEffect(() => {
    // Only run if cancel reason changes and we have contractSupportingData
    if (!userInput.cancel_reason_id) return;
    let changed = false;
    const newReservesSetBySystem = { ...reservesSetBySystem };
    quotes.forEach(estimate => {
      const contractId = estimate.id;
      const data = contractSupportingData[contractId];
      if (newReservesSetBySystem[contractId]) {
        const isDigital = data?.payee_lender_is_digital_reserves;
        const digitalReserveRules = data?.payee_lender_digital_reserve_rules || [];
        const hasRule = isDigital && estimate.product_type_id && userInput.cancel_reason_id && digitalReserveRules.some(
          rule => rule.product_type_id === estimate.product_type_id && rule.cancel_reason_id === Number(userInput.cancel_reason_id)
        );
        if (!hasRule) {
          newReservesSetBySystem[contractId] = false;
          changed = true;
        }
      }
    });
    if (changed) {
      setReservesSetBySystem(newReservesSetBySystem);
    }
  }, [userInput.cancel_reason_id, contractSupportingData, quotes]);

  return (
    <div style={{width: '100%'}}>
      {renderVINInput()}
      {renderContractModal()}
      {
        loadingContracts ? (
          <div className="d-flex justify-content-center align-items-center">
            <Loading/>
          </div>
        ) : (
          <>
            {
              selectedContract.code && (
                <>
                  {renderSPPCancel()}
                  {renderExpiredCancel()}
                  {renderReservesChangeModal()}
                  {renderVehicleCustomerInfo()}
                  {inputSection()}
                  <div className="row mt-1">
                    <div className="text-right col-12">
                      <button
                        className="ml-2 btn btn-primary"
                        onClick={getCancellationQuote}
                        disabled={disableGetQuote()}
                      >
                        Get Quotes
                      </button>
                    </div>
                  </div>
                  {
                    loadingQuotes ? (
                      <div className="d-flex justify-content-center align-items-center">
                        <Loading/>
                      </div>
                    ) : (
                      <>
                        {
                          quotes?.length > 0 && (
                            <>
                              <div className="row mt-2">
                                <div className="col-12" style={{color: '#ff9900'}}>
                                  NOTE: Making any changes to the above inputs will clear the current quotes and require
                                  getting new
                                  quotes.
                                </div>
                              </div>
                              <div className="row mt-2">
                                <div className="col">
                                  {quoteSection()}
                                </div>
                              </div>
                              <div className="row mt-2">
                                <div className="col-12">
                                  {uploadDataSection()}
                                </div>
                              </div>
                              <div className="row mt-2">
                                <div className="col">
                                  <small className="tip">
                                    * Some products may not be cancellable or may not provide a quote for cancellation.
                                  </small>
                                </div>
                              </div>
                              <div className="row mt-3">
                                {
                                  !isCancelled && (
                                    <>
                                      <div className="text-right col">
                                        {(() => {
                                          const data = {
                                            "cancel_date": userInput.cancel_date ? moment(userInput.cancel_date).format(API_DATE_FORMAT) : moment().format(API_DATE_FORMAT),
                                            "mileage": parseInt(userInput.current_mileage, 10),
                                            "cancel_reason_id": parseInt(userInput.cancel_reason_id, 10),
                                            "spp_customer_paid": parseFloat(userInput.spp_customer_paid || 0),
                                            "spp_balance": parseFloat(userInput.spp_balance || 0),
                                            "nsd_key_claims": parseFloat(userInput.nsd_key_claims || 0),
                                            "nsd_tire_and_wheel_claims": parseFloat(userInput.nsd_tire_and_wheel_claims || 0),
                                            "nsd_vta_claims": parseFloat(userInput.nsd_vta_claims || 0),
                                            "manual_tax_rate": parseFloat(userInput.tax_rate || 0),
                                            "contracts": contractsToCancel,
                                            "contract_payees": buildContractPayeesPayload(contractsToCancel, refundPayeeFields),
                                          };
                                          const cancelContractPdfUrl = `/api/admin/contracts/cancellation/estimate/pdf?q=${encodeURIComponent(JSON.stringify(data))}`;
                                          return (
                                            <>
                                              <a
                                                className={`btn btn-primary ${contractsToCancel.length > 0 ? "" : "disabled"}`}
                                                target='_blank'
                                                rel="noopener noreferrer"
                                                href={contractsToCancel.length > 0 ? cancelContractPdfUrl : ""}
                                              >
                                                <i className="fa fa-print"/> Print Quotes
                                              </a>
                                              &nbsp;
                                              <button
                                                className="btn btn-success"
                                                disabled={disableCancel}
                                                onClick={handleCancelContract}
                                              >
                                                <i className="fa fa-check"/>&nbsp; Cancel Contracts
                                              </button>
                                            </>
                                          );
                                        })()}
                                      </div>
                                    </>
                                  )
                                }
                              </div>
                            </>
                          )
                        }
                      </>
                    )
                  }
                </>
              )
            }
          </>
        )
      }
    </div>
  );
}
