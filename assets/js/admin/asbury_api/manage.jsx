import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import Modal from 'shared/components/Modal';

import * as Context from 'shared/context';
import { Loading } from 'shared/components/';
import SplitButtonDropdown from 'shared/components/SplitButtonDropdown';
import { jsonPromise as ajax } from 'shared/ajax';
import Alert from 'react-s-alert';
import dateFormat from 'shared/date-format.js';
import moment from 'moment';

export const ManageAsbury = ({params}, context) => {

  const [key, setKey] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [loadingData, setLoadingData] = useState(true);

  useEffect(() => {
    loadKey();
  }, []);

  const loadKey = () => {
    setLoadingData(true);
    let url = `/api/admin/asbury/key`;
    ajax(url, {}, {}).then(results => {
      if (results.status !== 200) {
        Alert.error(`${results.data.message || `Unexpected error in getting key`}`);
      } else {
        setKey(results.data.api_key);
      }
      setLoadingData(false);
    }).catch((e) => {
      setLoadingData(false);
      Alert.error(`Exception ${e}`);
    });
  };

  const handleEdit = () => {
    setShowModal(true);
  };

  const submit = () => {
    setLoadingData(true);
    let url = `/api/admin/asbury/key`;
    const data = {
      key_hash: key.key_hash,
    };
    ajax(url, {...data}, { method: 'PUT'})
      .then(results => {
        if (results.status !== 200) {
          Alert.error(`${results.data.message || `Unexpected error in saving key`}`);
        } else {
          Alert.success(`Key saved successfully`);
          setShowModal(false);
          loadKey();
        }
      }).catch((e) => {
        setLoadingData(false);
        Alert.error(`Exception ${e}`);
      });
  };

  return (
    <div>
      <div className='card mt-5'>
        <div className='card-header'>
          <div className='row'>
            <h3 className='col'>Asbury API Key</h3>
          </div>
        </div>
        <div className='card-body'>
          <table className='table table-striped table-hover m-0'>
            <thead>
              <tr>
                <th>Active</th>
                <th>Expires At</th>
                <th />
              </tr>
            </thead>
            <tbody>
              { loadingData && 
                <tr><td colSpan='3' className='pl-4'><Loading /></td></tr>
              }
              {           key && !loadingData &&  
                <>
                  <tr key={1}>
                    <td>{key.is_active ? `True` : `False` }</td>
                    <td>{key.expires_at ? moment(key.expires_at).format(dateFormat) : `N/A`}</td>
                    <td className='text-right compact'>
                      <SplitButtonDropdown btnSize='btn-sm' items={[{name: 'Edit', onClick: handleEdit.bind(null), icon: 'fa-edit' }]} />
                    </td>
                  </tr>
                </>
              }   
            </tbody>
          </table>
        </div>
        <div className='card-footer' />
      </div>
      <Modal visible={showModal} size='large' 
        close={() => {setShowModal(false); } }>
        <div className="row my-2">
          <label className="col-4">
                    Key Hash
          </label>
          <div className="col-8">
            <input type='Text' 
              className='form-control'
              value={key && key.key_hash}
              onChange={(e) => {setKey({...key, key_hash: e.target.value});}}
            />
          </div>
        </div>
        <div className="row my-4 justify-content-center">
          <button className="btn btn-secondary mr-3"
            onClick={() => {setShowModal(false);}}>
                    Cancel
          </button>
          <button className="btn btn-primary"
            onClick={submit}
            disabled={key && (!key.key_hash || key.key_hash.length != 64)} >
                    Submit
          </button>
        </div>

      </Modal>
    </div>
  );
};

const { shape, string } = PropTypes;
ManageAsbury.propTypes = {
  params: shape({
    id: string,
  }),
};
ManageAsbury.contextTypes = {
  router: Context.Router.isRequired,
  user: Context.User,
};