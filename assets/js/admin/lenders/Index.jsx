import React from 'react';
import PropTypes from 'prop-types';
import {Link} from 'react-router';
import Alert from 'react-s-alert';

import {jsonPromise as ajax} from 'shared/ajax';
import hstore from 'shared/hstore';
import Pagination from 'shared/components/Pagination';
import SplitButtonDropdown from 'shared/components/SplitButtonDropdown';
import {LenderManagement} from "shared/roles";
import { DigitalReservesManagement } from '../../shared/roles';

export default class LendersIndex extends React.Component {
  static contextTypes = {
    router: PropTypes.object.isRequired,
    sessionLoaded: PropTypes.bool.isRequired,
    user: PropTypes.shape({
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }),
    }),
  };

  static propTypes = {
    location: PropTypes.shape({
      query: PropTypes.shape({
        q: PropTypes.string,
        includeInactive: PropTypes.string,
        page: PropTypes.string,
      }).isRequired,
    }).isRequired
  };

  constructor(props) {
    super(props);
    this.state = {
      loading: true,
      loaded: false,
      lenders: [],
      lendersCount: null,
      includeInactive: props.location.query.includeInactive ? 'true' : '',
      q: props.location.query.q || '',
    };
  }

  componentDidMount() {
    document.title = 'Admin | Lenders';
    this.load();
  }

  UNSAFE_componentWillReceiveProps(nextProps, nextState) {
    if (
      this.currentQ(nextProps) !== this.currentQ(this.props) ||
      this.currentPage(nextProps) !== this.currentPage(this.props) ||
      this.props.location.query.includeInactive !== nextProps.location.query.includeInactive)
    {
      this.load(nextProps);
    }
  }

  load(props) {
    if (!props) {
      props = this.props;
    }
    this.setState({ loading: true, loaded: false, lenders: [], lendersCount: null }, function() {
      var url = '/api/lenders?page='+this.currentPage(props);
      if (props.location.query.q) {
        url += ('&q='+window.encodeURIComponent(props.location.query.q.trim()));
      }
      if (props.location.query.includeInactive) {
        url += ('&includeInactive='+window.encodeURIComponent(this.includeInactive()));
      }
      ajax(url, {}, {}).then(function(results) {
        if (results.status === 200) {
          this.setState({
            loaded: true,
            lenders: results.data.lenders,
            lendersCount: results.data.count,
          });
        } else {
          Alert.error('Lenders load error: ' + results.data.message);
        }
        this.setState({ loading: false });
      }.bind(this), function(reason){
        Alert.error('Lenders load error.');
        this.setState({ loading: false });
      }.bind(this));
    });
  }

  setPage(page) {
    var query = {
      page: page,
      q: this.props.location.query.q,
    };
    if (this.state.includeInactive) {
      query.includeInactive = this.state.includeInactive;
    }
    var next = {
      pathname: '/lenders',
      query: query
    };
    if (!this.context.router.isActive(next)) {
      this.context.router.push(next);
    }
  }

  onSubmit(e) {
    e.preventDefault();
    var query = {
      page: 1,
      q: (this.state.q ? this.state.q : undefined),
    };
    if (this.state.includeInactive) {
      query.includeInactive = 'true';
    } else {
      query.includeInactive = undefined;
    }
    var next = { pathname: '/lenders', query: query };
    if (!this.context.router.isActive(next)) {
      this.context.router.push(next);
    }
  }

  includeInactive = (props) => {
    if (!props) {
      props = this.props;
    }
    return props.location.query.includeInactive;
  };

  currentPage(props) {
    if (!props) {
      props = this.props;
    }
    return parseInt(props.location.query.page, 10) || 1;
  }

  currentQ(props) {
    if (!props) {
      props = this.props;
    }
    if (!props.location.query.q) {
      return undefined;
    }
    return props.location.query.q.trim();
  }

  onQChange(e) {
    this.setState({ q: e.target.value });
  }

  updateIncludeInactive = (e) => {
    this.setState({includeInactive: e.target.checked});
  };

  render() {
    const baseUrl = '/api/lenders/csv';
    const digitalReserveRulesUrl = '/api/lenders/digital-reserve-rules/csv';
    let params = {};
    if (this.state.q) {
      params.q = this.state.q;
    }
    if (this.state.includeInactive) {
      params.includeInactive = true;
    }
    const query = (new URLSearchParams(params)).toString();
    const exportUrl = query === '' ? baseUrl : `${baseUrl}?${query}`;
    const digitalReserveRulesExportUrl = query === '' ? digitalReserveRulesUrl : `${digitalReserveRulesUrl}?${query}`;

    return (
      <div>
        <div className="float-right">
          <form className="form-inline" onSubmit={ this.onSubmit.bind(this) }>
            <div className='form-check form-check-inline'>
              <input type="checkbox" id="include_inactive"
                className='form-check-input'
                onChange={this.updateIncludeInactive}
                checked={this.state.includeInactive}
              />
              <label htmlFor="include_inactive" className='form-check-label'>Include Inactive</label>
            </div>
            <div className="form-group">
              <input type="text" value={ this.state.q } onChange={ this.onQChange.bind(this) } className="form-control" placeholder="search" />
            </div>
            &nbsp;
            <button type="submit" className="btn btn-primary">
              <i className="fa fa-search" /> Search
            </button>
            &nbsp;
            <a href={exportUrl} className="btn btn-primary" target="_blank" rel="noopener noreferrer">
              <i className="fa fa-download"/> Export Lenders
            </a>
            &nbsp;
            <a href={digitalReserveRulesExportUrl} className="btn btn-primary" target="_blank" rel="noopener noreferrer">
              <i className="fa fa-download"/> Export Reserve Rules
            </a>
          </form>
        </div>
        <h1>Lenders List</h1>
        <div className="clearfix"></div>

        { this.renderNewButton() }

        { this.renderLenders() }
      </div>
    );
  }

  renderNewButton() {
    if (this.context.sessionLoaded && hstore.has(this.context.user.roles, LenderManagement)) {
      return (
        <p>
          <Link className="btn btn-secondary" to="/lenders/new">
            <i className="fa fa-plus"></i> New Lender
          </Link>
        </p>
      );
    }
  }

  renderLenders() {
    if (this.state.loaded) {
      return (
        <div>
          <Pagination page={ this.currentPage() } setPage={ this.setPage.bind(this) } limit={ 20 } count={ this.state.lendersCount } />
          <table className="table table-striped">
            <thead>
              <tr>
                <th>Active</th>
                <th>Intacct Vendor ID</th>
                <th>LHM CDK Finance Source</th>
                <th>Asbury CDK Finance Source</th>
                <th>Name</th>
                <th>API Display Name</th>
                <th>Location</th>
                <th>Cancellation Lender</th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              { this.state.lenders.map(function(lender) {
                var actions = [];
                if (this.context.sessionLoaded && hstore.hasAny(this.context.user.roles, [LenderManagement, DigitalReservesManagement])) {
                  actions.push({name: 'Edit', to: `/lenders/${lender.id}/edit`, icon: 'fa-edit'});
                }
                return <tr key={ lender.id }>
                  <td>{ lender.is_active ? <i className='fa fa-check' /> : ''}</td>
                  <td>{ lender.intacct_vendor_id }</td>
                  <td>{ lender.lhm_cdk_finance_code }</td>
                  <td>{ lender.asbury_cdk_finance_code }</td>
                  <td>{ lender.name }</td>
                  <td>{ lender.e_sale_api_display_name }</td>
                  <td>
                    { lender.address }<br />
                    { lender.city }, { lender.state_code } { lender.postal_code }
                  </td>
                  {
                    lender.cl_name ? (<td>
                      { lender.cl_name }<br />
                      { lender.cl_address }<br />
                      { lender.cl_city }, { lender.cl_state_code } { lender.cl_postal_code }
                    </td>):(<td></td>)
                  }
                  <td className="text-right">
                    <SplitButtonDropdown btnSize="btn-sm" items={ actions } />
                  </td>
                </tr>;
              }.bind(this)) }
            </tbody>
          </table>
          <Pagination page={ this.currentPage() } setPage={ this.setPage.bind(this) } limit={ 20 } count={ this.state.lendersCount } />
        </div>
      );
    } else if (this.state.loading) {
      return <h3><i className="fa fa-refresh fa-spin"></i> Loading lenders...</h3>;
    }
    return <h3><i className='fa fa-warning'></i> Error loading lenders</h3>;
  }
}
