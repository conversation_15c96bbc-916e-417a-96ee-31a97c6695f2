import React from 'react';
import PropTypes from 'prop-types';
import Alert from 'react-s-alert';
import Select from 'react-select';
import {Link} from 'react-router';

import FieldError from 'shared/components/FieldError';
import ErrorSummary from 'shared/components/ErrorSummary';
import {jsonPromise as ajax} from 'shared/ajax';
import hstore from 'shared/hstore';
import { DigitalReservesManagement, LenderManagement } from 'shared/roles';
import ConfirmModal from 'shared/components/ConfirmModal';

// Memoized helper functions to prevent recreation on every render
const createStoreOptions = (stores) => {
  if (!stores) return [];
  return stores.map(store => ({
    value: String(store.id),
    label: `${store.code} - ${store.name}`
  }));
};

const createProductTypeOptions = (productTypes) => {
  if (!productTypes) return [];
  return productTypes.map(pt => ({
    value: String(pt.id),
    label: pt.name
  }));
};

const createCancelReasonOptions = (cancelReasons) => {
  if (!cancelReasons) return [];
  return cancelReasons.map(reason => ({
    value: String(reason.id),
    label: reason.name
  }));
};

const createMakeOptions = (makes) => {
  if (!makes) return [];
  return makes.map(m => ({ label: m, value: m }));
};

function getInitialLenderState(lender = {}) {
  // Convert old format rules to new multiselect format if needed
  const convertedRules = (lender.digital_reserve_rules || []).map(rule => {
    // If rule has old format (single IDs), convert to new format (arrays)
    if (rule.store_id !== undefined && rule.store_ids === undefined) {
      return {
        store_ids: rule.store_id ? [String(rule.store_id)] : [],
        product_type_ids: rule.product_type_id ? [String(rule.product_type_id)] : [],
        cancel_reason_ids: rule.cancel_reason_id ? [String(rule.cancel_reason_id)] : [],
        id: rule.id || Date.now() + Math.random()
      };
    }
    // If rule already has new format, ensure IDs are strings
    return {
      store_ids: (rule.store_ids || []).map(id => String(id)),
      product_type_ids: (rule.product_type_ids || []).map(id => String(id)),
      cancel_reason_ids: (rule.cancel_reason_ids || []).map(id => String(id)),
      id: rule.id || Date.now() + Math.random()
    };
  });

  return {
    intacct_vendor_id: lender.intacct_vendor_id || '',
    name: lender.name || '',
    address: lender.address || '',
    city: lender.city || '',
    state_code: lender.state_code || '',
    postal_code: lender.postal_code || '',
    lhm_cdk_finance_code: lender.lhm_cdk_finance_code || '',
    asbury_cdk_finance_code: lender.asbury_cdk_finance_code || '',
    ucs_lender_id: lender.ucs_lender_id || '',
    tekion_lender_id: lender.tekion_lender_id || '',
    add_vta_overallownce: lender.add_vta_overallownce || false,
    is_active: lender.is_active === undefined || lender.is_active,
    is_digital_reserves: lender.is_digital_reserves || false,
    e_sale_api_display_name: lender.e_sale_api_display_name || '',
    certifiable_makes: lender.certifiable_makes || [],
    cancellation_lender_id: lender.cancellation_lender_id || '',
    digital_reserve_rules: convertedRules,
  };
}

export default class LendersForm extends React.Component {
  static contextTypes = {
    user: PropTypes.shape({
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }),
    }),
  };

  constructor(props) {
    super(props);
    this.state = {
      lender: getInitialLenderState(props.lender),
      selectedRuleIndexes: [],
      selectAllChecked: false,
      validationErrors: {},
      showDeleteRuleModal: false, // modal state
      ruleIndexToDelete: null,    // which rule to delete
      bulkDeleteMode: false,      // bulk delete mode
      // Search filters
      searchFilters: {
        store: null,
        productType: null,
        cancelReason: null
      },
      showSearchFilters: false, // toggle search panel visibility
    };
    this.ruleRefs = {}; // Add this for dropdown refs

    // Memoize expensive operations
    this.memoizedStoreOptions = null;
    this.memoizedProductTypeOptions = null;
    this.memoizedCancelReasonOptions = null;
    this.memoizedMakeOptions = null;
  }

  componentDidMount() {
    this.loadSupportingData();
  }

  componentDidUpdate(prevProps) {
    if (this.props.lender && prevProps.lender && this.props.lender.id !== prevProps.lender.id) {
      this.setState({ lender: getInitialLenderState(this.props.lender) });
    }
  }

  onAttrChange = (e, name) => {
    const value = e.target.type === 'checkbox' ? e.target.checked : e.target.value;
    this.setState(prevState => ({
      lender: { ...prevState.lender, [name]: value }
    }));
  }

  postData() {
    const data = { ...this.state.lender };
    if (!data.is_digital_reserves) {
      data.digital_reserve_rules = [];
    }
    if (Array.isArray(data.digital_reserve_rules)) {
      data.digital_reserve_rules = data.digital_reserve_rules
        .filter(rule => rule.store_ids && rule.store_ids.length > 0 &&
                       rule.product_type_ids && rule.product_type_ids.length > 0 &&
                       rule.cancel_reason_ids && rule.cancel_reason_ids.length > 0)
        .map(rule => ({
          store_ids: rule.store_ids.map(id => Number(id)),
          product_type_ids: rule.product_type_ids.map(id => Number(id)),
          cancel_reason_ids: rule.cancel_reason_ids.map(id => Number(id))
        }));
    }
    return data;
  }

  onSubmit(e) {
    e.preventDefault();
    const { lender } = this.state;
    if (lender.is_digital_reserves) {
      if (!lender.digital_reserve_rules || lender.digital_reserve_rules.length === 0) {
        this.setState({
          validationErrors: { ...this.state.validationErrors, digital_reserve_rules: 'At least one rule is required for Digital Reserves.' }
        });
        return;
      }
      // Check for any empty multiselect fields in rules
      let firstInvalid = null;
      lender.digital_reserve_rules.forEach((rule, idx) => {
        if (!firstInvalid && (!rule.store_ids || rule.store_ids.length === 0 ||
                             !rule.product_type_ids || rule.product_type_ids.length === 0 ||
                             !rule.cancel_reason_ids || rule.cancel_reason_ids.length === 0)) {
          if (!rule.store_ids || rule.store_ids.length === 0) firstInvalid = this.ruleRefs[`${rule.id}-store`];
          else if (!rule.product_type_ids || rule.product_type_ids.length === 0) firstInvalid = this.ruleRefs[`${rule.id}-product`];
          else if (!rule.cancel_reason_ids || rule.cancel_reason_ids.length === 0) firstInvalid = this.ruleRefs[`${rule.id}-reason`];
        }
      });
      if (firstInvalid && firstInvalid.focus) {
        firstInvalid.focus();
      }
      const incompleteRows = lender.digital_reserve_rules.some(rule =>
        !rule.store_ids || rule.store_ids.length === 0 ||
        !rule.product_type_ids || rule.product_type_ids.length === 0 ||
        !rule.cancel_reason_ids || rule.cancel_reason_ids.length === 0
      );
      if (incompleteRows) {
        this.setState({
          validationErrors: { ...this.state.validationErrors, digital_reserve_rules: 'All rule fields must have at least one selection for Digital Reserves.' }
        });
        return;
      }
      // Duplicate validation
      const duplicateErrors = this.validateDuplicateRules(lender.digital_reserve_rules);
      if (Object.keys(duplicateErrors).length > 0) {
        this.setState({ validationErrors: duplicateErrors });
        return;
      }
    }
    this.props.save(this.postData());
  }

  loadSupportingData() {
    const url = '/api/lenders/supporting-data';
    const defaultMsg = 'Error loading supporting data';
    ajax(url, {}, {})
      .then(results => {
        if (results.status === 200) {
          this.setState({ loadingSupportingData: false, supportingData: results.data });
        } else {
          this.setState({loadingSupportingData: false});
          Alert.error(results.data?.message || defaultMsg);
        }
      })
      .catch(() => { Alert.error(defaultMsg); })
      .then(() => {
        this.setState({loadingSupportingData: false});
      });
  }

  makeOptions() {
    if (this.state.supportingData && this.state.supportingData.makes) {
      return createMakeOptions(this.state.supportingData.makes);
    }
    return [];
  }

  onChangeCertifiableMakes(makes) {
    this.setState(prevState => ({
      lender: {
        ...prevState.lender,
        certifiable_makes: makes ? makes.map(m => m.value) : []
      }
    }));
  }

  cancellationLenderOptions() {
    if (this.state.supportingData && this.state.supportingData.cancellation_lenders) {
      return this.state.supportingData.cancellation_lenders.map(function(lender) { 
        return { value: lender.id, label: lender.name, lender: lender }; 
      });
    }
    return [];
  }

  // Memoized options getters to prevent recreation on every render
  getStoreOptions() {
    if (!this.memoizedStoreOptions && this.state.supportingData && this.state.supportingData.stores) {
      this.memoizedStoreOptions = createStoreOptions(this.state.supportingData.stores);
    }
    return this.memoizedStoreOptions || [];
  }

  getProductTypeOptions() {
    if (!this.memoizedProductTypeOptions && this.state.supportingData && this.state.supportingData.product_types) {
      this.memoizedProductTypeOptions = createProductTypeOptions(this.state.supportingData.product_types);
    }
    return this.memoizedProductTypeOptions || [];
  }

  getCancelReasonOptions() {
    if (!this.memoizedCancelReasonOptions && this.state.supportingData && this.state.supportingData.cancel_reasons) {
      this.memoizedCancelReasonOptions = createCancelReasonOptions(this.state.supportingData.cancel_reasons);
    }
    return this.memoizedCancelReasonOptions || [];
  }

  renderLenderOption(option, i) {
    return (
      <span>
        { option.lender.name }<br />
        <small>{ option.lender.address }</small><br />
        <small>{ option.lender.city }, { option.lender.state_code } { option.lender.postal_code }</small>
      </span>
    );
  }

  onCancellationLenderSelect(option) {
    this.setState(prevState => ({
      lender: {
        ...prevState.lender,
        cancellation_lender_id: Array.isArray(option) ? null : (option ? option.value : null)
      }
    }));
  }

  // --- Digital Reserve Rules Handlers ---
  handleAddRule = () => {
    this.setState(prevState => {
      const rules = [
        { store_ids: [], product_type_ids: [], cancel_reason_ids: [], id: Date.now() + Math.random() },
        ...(prevState.lender.digital_reserve_rules || []),
      ];
      const newErrors = this.validateDuplicateRules(rules);
      const incompleteError = this.getIncompleteRuleError(rules);
      if (incompleteError) {
        newErrors.digital_reserve_rules = incompleteError;
      }
      return {
        lender: { ...prevState.lender, digital_reserve_rules: rules },
        validationErrors: newErrors,
        showSearchFilters: false, // Hide search filters when adding new rule
        searchFilters: { // Clear all search filters
          store: null,
          productType: null,
          cancelReason: null
        }
      };
    });
  };

  handleRuleChange = (ruleId, field, value) => {
    this.setState(prevState => {
      const rules = [...(prevState.lender.digital_reserve_rules || [])];
      const ruleIndex = rules.findIndex(rule => rule.id === ruleId);
      if (ruleIndex === -1) return prevState;

      // Handle multiselect arrays
      const newValue = Array.isArray(value) ? value.map(v => String(v.value)) : [];
      rules[ruleIndex] = { ...rules[ruleIndex], [field]: newValue };
      let newErrors = this.validateDuplicateRules(rules);
      const incompleteError = this.getIncompleteRuleError(rules);
      if (incompleteError) {
        newErrors.digital_reserve_rules = incompleteError;
      }
      return {
        lender: { ...prevState.lender, digital_reserve_rules: rules },
        validationErrors: newErrors
      };
    });
  };

  handleRemoveRule = (ruleId) => {
    this.setState(prevState => {
      const rules = [...(prevState.lender.digital_reserve_rules || [])];
      const ruleIndex = rules.findIndex(rule => rule.id === ruleId);
      if (ruleIndex === -1) return prevState;

      rules.splice(ruleIndex, 1);
      const newErrors = this.validateDuplicateRules(rules);
      const incompleteError = this.getIncompleteRuleError(rules);
      if (incompleteError) {
        newErrors.digital_reserve_rules = incompleteError;
      }
      return {
        lender: { ...prevState.lender, digital_reserve_rules: rules },
        validationErrors: newErrors
      };
    });
  };

  handleRuleCheckbox = (ruleId, checked) => {
    let selected = [...this.state.selectedRuleIndexes];
    if (checked) {
      selected.push(ruleId);
    } else {
      selected = selected.filter(id => id !== ruleId);
    }
    const filteredRules = this.getFilteredRules();
    const selectAllChecked = selected.length === filteredRules.length && filteredRules.length > 0;
    this.setState({ selectedRuleIndexes: selected, selectAllChecked });
  };

  handleSelectAllCheckbox = (checked) => {
    const filteredRules = this.getFilteredRules();
    const ruleIds = filteredRules.map(rule => rule.id);
    this.setState({
      selectedRuleIndexes: checked ? ruleIds : [],
      selectAllChecked: checked
    });
  };

  handleDeleteSelectedRules = () => {
    const rules = [...(this.state.lender.digital_reserve_rules || [])];
    const selected = new Set(this.state.selectedRuleIndexes);
    const selectedRules = rules.filter(rule => selected.has(rule.id));
    const anyWithData = selectedRules.some(rule => this.hasRuleData(rule));
    if (anyWithData) {
      this.setState({ showDeleteRuleModal: true, bulkDeleteMode: true });
    } else {
      this.performBulkDelete();
    }
  };

  performBulkDelete = () => {
    const rules = [...(this.state.lender.digital_reserve_rules || [])];
    const selected = new Set(this.state.selectedRuleIndexes);
    const newRules = rules.filter(rule => !selected.has(rule.id));
    this.setState({
      lender: { ...this.state.lender, digital_reserve_rules: newRules },
      selectedRuleIndexes: [],
      selectAllChecked: false,
      showDeleteRuleModal: false,
      bulkDeleteMode: false,
    });
  };

  // Helper to check if a rule has any data
  hasRuleData(rule) {
    return (
      (rule.store_ids && rule.store_ids.length > 0) ||
      (rule.product_type_ids && rule.product_type_ids.length > 0) ||
      (rule.cancel_reason_ids && rule.cancel_reason_ids.length > 0)
    );
  }

  // Handler for delete icon click
  handleDeleteRuleClick = (ruleId) => {
    const rules = this.state.lender.digital_reserve_rules || [];
    const ruleIndex = rules.findIndex(rule => rule.id === ruleId);
    if (ruleIndex === -1) return;

    const rule = rules[ruleIndex];
    if (this.hasRuleData(rule)) {
      this.setState({ showDeleteRuleModal: true, ruleIndexToDelete: ruleIndex });
    } else {
      this.handleRemoveRule(ruleId);
    }
  };

  handleConfirmDeleteRule = () => {
    if (this.state.bulkDeleteMode) {
      this.performBulkDelete();
    } else if (this.state.ruleIndexToDelete !== null) {
      this.handleRemoveRule(this.state.lender.digital_reserve_rules[this.state.ruleIndexToDelete].id);
      this.setState({ showDeleteRuleModal: false, ruleIndexToDelete: null });
    }
  };

  handleCancelDeleteRule = () => {
    this.setState({ showDeleteRuleModal: false, ruleIndexToDelete: null, bulkDeleteMode: false });
  };

  // --- Search Filter Handlers ---
  handleSearchFilterChange = (field, value) => {
    this.setState(prevState => ({
      searchFilters: {
        ...prevState.searchFilters,
        [field]: value
      }
    }));
  };

  handleSearch = () => {
    // Search is handled by filtering in render, no additional action needed
  };

  handleClearSearch = () => {
    this.setState({
      searchFilters: {
        store: null,
        productType: null,
        cancelReason: null
      }
    });
  };

  toggleSearchFilters = () => {
    this.setState(prevState => {
      const newShowSearchFilters = !prevState.showSearchFilters;
      // If hiding search filters, clear all search filters
      const newSearchFilters = newShowSearchFilters ? prevState.searchFilters : {
        store: null,
        productType: null,
        cancelReason: null
      };
      return {
        showSearchFilters: newShowSearchFilters,
        searchFilters: newSearchFilters
      };
    });
  };

  // Filter rules based on search criteria
  getFilteredRules = () => {
    const { lender, searchFilters } = this.state;
    const rules = lender.digital_reserve_rules || [];

    if (!searchFilters.store && !searchFilters.productType && !searchFilters.cancelReason) {
      return rules;
    }

    return rules.filter(rule => {
      // Check store filter
      if (searchFilters.store && (!rule.store_ids || !rule.store_ids.includes(searchFilters.store.value))) {
        return false;
      }

      // Check product type filter
      if (searchFilters.productType && (!rule.product_type_ids || !rule.product_type_ids.includes(searchFilters.productType.value))) {
        return false;
      }

      // Check cancellation reason filter
      if (searchFilters.cancelReason && (!rule.cancel_reason_ids || !rule.cancel_reason_ids.includes(searchFilters.cancelReason.value))) {
        return false;
      }

      return true;
    });
  };

  isDigitalReservesOnlyMode() {
    const user = this.context.user;
    if (!user || !user.roles) return false;
    return (
      hstore.has(user.roles, DigitalReservesManagement) &&
      !hstore.has(user.roles, LenderManagement)
    );
  }

  // Helper to check for duplicate rules (all fields match, order-insensitive)
  validateDuplicateRules(rules) {
    const errors = {};
    for (let i = 0; i < rules.length; i++) {
      for (let j = i + 1; j < rules.length; j++) {
        const a = rules[i];
        const b = rules[j];
        const sameStores = JSON.stringify([...a.store_ids].sort()) === JSON.stringify([...b.store_ids].sort());
        const sameProducts = JSON.stringify([...a.product_type_ids].sort()) === JSON.stringify([...b.product_type_ids].sort());
        const sameReasons = JSON.stringify([...a.cancel_reason_ids].sort()) === JSON.stringify([...b.cancel_reason_ids].sort());
        if (sameStores && sameProducts && sameReasons) {
          errors[a.id] = { ...(errors[a.id] || {}), duplicate: true };
          errors[b.id] = { ...(errors[b.id] || {}), duplicate: true };
        }
      }
    }
    return errors;
  }

  // Helper to check for incomplete rules
  getIncompleteRuleError(rules) {
    const incompleteRows = rules.some(rule =>
      !rule.store_ids || rule.store_ids.length === 0 ||
      !rule.product_type_ids || rule.product_type_ids.length === 0 ||
      !rule.cancel_reason_ids || rule.cancel_reason_ids.length === 0
    );
    return incompleteRows
      ? 'All rule fields must have at least one selection for Digital Reserves.'
      : null;
  }

  renderStateField() {
    if (!this.state.supportingData) {
      return;
    }
    var lender = this.state.lender;
    const states = this.state.supportingData && this.state.supportingData.states;
    let options = Object.keys(states).map((s, i)=>{
      return <option key={i} value={s}>{s} - {states[s]}</option>;
    });
    options.unshift(<option value='' key=''>&mdash; Select &mdash;</option>);
    return (
      <select id='field-state_code' className='form-control' value={lender.state_code} onChange={e => this.onAttrChange(e, 'state_code')}>
        {options}
      </select>
    );
  }

  render() {
    if (!this.state.lender) return null;
    var lender = this.state.lender;
    const digitalReservesOnly = this.isDigitalReservesOnlyMode();
    return (
      <div>
        <ErrorSummary errors={ this.props.errors } />

        <form onSubmit={ this.onSubmit.bind(this) }>
          <div className="row">
            <div className="form-group col">
              <label htmlFor="field-lhm_cdk_finance_code">LHM CDK Finance Source</label>
              <input type="text" id="field-lhm_cdk_finance_code" value={ lender.lhm_cdk_finance_code } onChange={e => this.onAttrChange(e, 'lhm_cdk_finance_code') } required={ false } className="form-control" disabled={digitalReservesOnly} />
              <FieldError err={ this.props.errors.lhm_cdk_finance_code } />
            </div>
            <div className="form-group col">
              <label htmlFor="field-asbury_cdk_finance_code">Asbury CDK Finance Source</label>
              <input type="text" id="field-asbury_cdk_finance_code" value={ lender.asbury_cdk_finance_code } onChange={e => this.onAttrChange(e, 'asbury_cdk_finance_code') } required={ false } className="form-control" disabled={digitalReservesOnly} />
              <FieldError err={ this.props.errors.asbury_cdk_finance_code } />
            </div>
            <div className="form-group col">
              <label htmlFor="field-asbury_ucs_lender_id">UCS Lender ID</label>
              <input type="text" id="field-asbury_ucs_lender_id" value={ lender.ucs_lender_id } onChange={e => this.onAttrChange(e, 'ucs_lender_id') } required={ false } className="form-control" disabled={digitalReservesOnly} />
              <FieldError err={ this.props.errors.ucs_lender_id } />
            </div>
            <div className="form-group col">
              <label htmlFor="field-tekion_lender_id">Tekion Lender ID</label>
              <input type="text" id="field-tekion_lender_id" value={ lender.tekion_lender_id } onChange={e => this.onAttrChange(e, 'tekion_lender_id') } required={ false } className="form-control" disabled={digitalReservesOnly} />
              <FieldError err={ this.props.errors.tekion_lender_id } />
            </div>
          </div>
          <div className="row">
            <div className="form-group col">
              <label htmlFor="field-name">*Name</label>
              <input type="text" id="field-name" value={ lender.name } onChange={e => this.onAttrChange(e, 'name')} required={ true } className="form-control" disabled={digitalReservesOnly} />
              <FieldError err={ this.props.errors.name } />
            </div>
            <div className="form-group col">
              <label htmlFor="field-intacct_vendor_id">Intacct Vendor ID</label>
              <input type="text" id="field-intacct_vendor_id" value={ lender.intacct_vendor_id } onChange={e => this.onAttrChange(e, 'intacct_vendor_id')} className="form-control" disabled={digitalReservesOnly} />
              <FieldError err={ this.props.errors.intacct_vendor_id } />
            </div>
          </div>
          <div className="row">
            <div className="form-group col">
              <label htmlFor="field-address">*Address</label>
              <input type="text" id="field-address" value={ lender.address } onChange={e => this.onAttrChange(e, 'address') } required={ true } className="form-control" disabled={digitalReservesOnly} />
              <FieldError err={ this.props.errors.address } />
            </div>
          </div>
          <div className="row">
            <div className="form-group col">
              <label htmlFor="field-city">*City</label>
              <input type="text" id="field-city" value={ lender.city } onChange={e => this.onAttrChange(e, 'city') } required={ true } className="form-control" disabled={digitalReservesOnly} />
              <FieldError err={ this.props.errors.city } />
            </div>
            <div className="form-group col">
              <label htmlFor="field-state_code">*State Code</label>
              {this.renderStateField()}
              <FieldError err={ this.props.errors.state_code } />
            </div>
            <div className="form-group col">
              <label htmlFor="field-postal_code">*Postal Code</label>
              <input type="text" id="field-postal_code" value={ lender.postal_code } onChange={e => this.onAttrChange(e, 'postal_code') } required={ true } className="form-control" disabled={digitalReservesOnly} />
              <FieldError err={ this.props.errors.postal_code } />
            </div>
          </div>

          <div className="row">
            <div className="form-group col">
              <div className='checkbox'>
                <label>
                  <input type='checkbox' checked={lender.add_vta_overallownce} onChange={(e) => this.onAttrChange(e, 'add_vta_overallownce')} disabled={digitalReservesOnly} />
                  {' '}
                  Apply VTA Overallowance
                </label>
              </div>
              <div className='checkbox'>
                <label>
                  <input type='checkbox' checked={lender.is_active} onChange={(e) => this.onAttrChange(e, 'is_active')} disabled={digitalReservesOnly} />
                  {' '}
                  Active
                </label>
              </div>
            </div>
            <div className="form-group col">
              <label htmlFor="field-e_sale_api_display_name">API Display Name</label>
              <input type="text" id="field-e_sale_api_display_name" value={ lender.e_sale_api_display_name } onChange={e => this.onAttrChange(e, 'e_sale_api_display_name') } required={ false } className="form-control" disabled={digitalReservesOnly} />
              <FieldError err={ this.props.errors.e_sale_api_display_name } />
            </div>
          </div>
          <div className="row">
            <div className='col-md-6'>
              <fieldset className='form-group'>
                <label>Certifiable Makes</label>
                <Select options={this.makeOptions()} multi={true} tabSelectsValue={false} value={lender.certifiable_makes} onChange={this.onChangeCertifiableMakes.bind(this)} disabled={digitalReservesOnly} />
                <small className='text-muted'>These are the makes for which this lender is available.</small>
              </fieldset>
            </div>
            <div className='col-md-6'>
              <fieldset className='form-group'>
                <label>Cancellation Lenders</label>
                <Select
                  value={ lender.cancellation_lender_id }
                  options={ this.cancellationLenderOptions() }
                  optionRenderer={ this.renderLenderOption.bind(this) }
                  onChange={ this.onCancellationLenderSelect.bind(this) }
                  ignoreCase={ true }
                  disabled={digitalReservesOnly}
                />
              </fieldset>
            </div>
          </div>

          <div className='form-group'>
            <div className='checkbox'>
              <label>
                <input type='checkbox' checked={lender.is_digital_reserves} onChange={(e) => this.onAttrChange(e, 'is_digital_reserves')} />
                {' '}
                Digital Reserves
              </label>
            </div>
          </div>
          {/* --- Digital Reserve Rules Table --- */}
          {lender.is_digital_reserves && this.state.supportingData && (
            <div className="card mt-4 mb-4">
              <div className="card-header d-flex justify-content-between align-items-center">
                <span>Digital Reserve Rules</span>
                <button
                  type="button"
                  className="btn btn-outline-secondary btn-sm"
                  onClick={this.toggleSearchFilters}
                >
                  <i className="fa fa-search" /> Search
                </button>
              </div>
              <div className="card-body">
                {/* Search Filters Panel */}
                {this.state.showSearchFilters && (
                  <div className="mb-3 p-3 border rounded bg-light">
                    <div className="row">
                      <div className="col-md-4">
                        <label className="form-label">Store</label>
                        <Select
                          value={this.state.searchFilters.store}
                          options={this.getStoreOptions()}
                          onChange={value => this.handleSearchFilterChange('store', value)}
                          placeholder="Select Store"
                          isClearable={true}
                          isSearchable={true}
                        />
                      </div>
                      <div className="col-md-3">
                        <label className="form-label">Product Type</label>
                        <Select
                          value={this.state.searchFilters.productType}
                          options={this.getProductTypeOptions()}
                          onChange={value => this.handleSearchFilterChange('productType', value)}
                          placeholder="Select Product Type"
                          isClearable={true}
                          isSearchable={true}
                        />
                      </div>
                      <div className="col-md-3">
                        <label className="form-label">Cancellation Reason</label>
                        <Select
                          value={this.state.searchFilters.cancelReason}
                          options={this.getCancelReasonOptions()}
                          onChange={value => this.handleSearchFilterChange('cancelReason', value)}
                          placeholder="Select Cancellation Reason"
                          isClearable={true}
                          isSearchable={true}
                        />
                      </div>
                      <div className="col-md-2 d-flex align-items-end">
                        <button
                          type="button"
                          className="btn btn-outline-secondary btn-sm"
                          onClick={this.handleClearSearch}
                        >
                          <i className="fa fa-times" /> Clear
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                <div className="mb-2">
                  <button type="button" className="btn btn-secondary btn-sm" onClick={this.handleAddRule}>+ New</button>
                  <button
                    type="button"
                    className="btn btn-danger btn-sm ml-2"
                    onClick={this.handleDeleteSelectedRules}
                    disabled={this.state.selectedRuleIndexes.length === 0 || this.getFilteredRules().length === 0}
                  >
                    Delete Selected
                  </button>
                  {this.state.showSearchFilters && (
                    <span className="ml-3 text-muted small">
                      Showing {this.getFilteredRules().length} of {lender.digital_reserve_rules ? lender.digital_reserve_rules.length : 0} rules
                    </span>
                  )}
                </div>
                <table className="table table-bordered table-striped table-sm mb-0">
                  <thead className="thead-light">
                    <tr>
                      <th style={{width: 30}}>
                        <input
                          type="checkbox"
                          checked={this.state.selectAllChecked}
                          disabled={this.getFilteredRules().length === 0}
                          ref={el => { if (el) el.indeterminate = this.state.selectedRuleIndexes.length > 0 && !this.state.selectAllChecked; }}
                          onChange={e => this.handleSelectAllCheckbox(e.target.checked)}
                        />
                      </th>
                      <th>Store</th>
                      <th>Product Type</th>
                      <th>Cancellation Reason</th>
                      <th style={{width: 40}}></th>
                    </tr>
                  </thead>
                  <tbody>
                    {this.getFilteredRules().map((rule, idx) => {
                      const err = this.state.validationErrors[rule.id] || {};
                      const isDuplicate = err.duplicate;
                      return (
                        <tr key={rule.id || idx} style={isDuplicate ? { background: '#f8d7da' } : {}}>
                          <td>
                            <input type="checkbox" checked={this.state.selectedRuleIndexes.includes(rule.id)} onChange={e => this.handleRuleCheckbox(rule.id, e.target.checked)} />
                          </td>
                          <td style={{ minWidth: 180, background: err.store ? '#f8d7da' : undefined, borderColor: err.store ? '#dc3545' : undefined }}>
                            <Select
                              ref={el => { this.ruleRefs[`${rule.id}-store`] = el; }}
                              value={
                                this.state.supportingData && this.state.supportingData.stores ?
                                  (rule.store_ids || []).map(storeId => {
                                    const store = this.state.supportingData.stores.find(s => String(s.id) === String(storeId));
                                    return store ? { value: String(store.id), label: `${store.code} - ${store.name}` } : null;
                                  }).filter(Boolean) : []
                              }
                              options={this.getStoreOptions()}
                              onChange={opts => this.handleRuleChange(rule.id, 'store_ids', opts || [])}
                              multi={true}
                              clearable={true}
                              styles={err.store ? { control: base => ({ ...base, borderColor: '#dc3545', background: '#f8d7da' }) } : {}}
                            />
                          </td>
                          <td style={{ minWidth: 180, background: err.product ? '#f8d7da' : undefined, borderColor: err.product ? '#dc3545' : undefined }}>
                            <Select
                              ref={el => { this.ruleRefs[`${rule.id}-product`] = el; }}
                              value={
                                this.state.supportingData && this.state.supportingData.product_types ?
                                  (rule.product_type_ids || []).map(productTypeId => {
                                    const productType = this.state.supportingData.product_types.find(pt => String(pt.id) === String(productTypeId));
                                    return productType ? { value: String(productType.id), label: productType.name } : null;
                                  }).filter(Boolean) : []
                              }
                              options={this.getProductTypeOptions()}
                              onChange={opts => this.handleRuleChange(rule.id, 'product_type_ids', opts || [])}
                              multi={true}
                              clearable={true}
                              styles={err.product ? { control: base => ({ ...base, borderColor: '#dc3545', background: '#f8d7da' }) } : {}}
                            />
                          </td>
                          <td style={{ minWidth: 180, background: err.reason ? '#f8d7da' : undefined, borderColor: err.reason ? '#dc3545' : undefined }}>
                            <Select
                              ref={el => { this.ruleRefs[`${rule.id}-reason`] = el; }}
                              value={
                                this.state.supportingData && this.state.supportingData.cancel_reasons ?
                                  (rule.cancel_reason_ids || []).map(cancelReasonId => {
                                    const cancelReason = this.state.supportingData.cancel_reasons.find(cr => String(cr.id) === String(cancelReasonId));
                                    return cancelReason ? { value: String(cancelReason.id), label: cancelReason.name } : null;
                                  }).filter(Boolean) : []
                              }
                              options={this.getCancelReasonOptions()}
                              onChange={opts => this.handleRuleChange(rule.id, 'cancel_reason_ids', opts || [])}
                              multi={true}
                              clearable={true}
                              styles={err.reason ? { control: base => ({ ...base, borderColor: '#dc3545', background: '#f8d7da' }) } : {}}
                            />
                          </td>
                          <td>
                            <button
                              type="button"
                              className="btn btn-sm btn-link text-danger"
                              onClick={() => this.handleDeleteRuleClick(rule.id)}
                              title="Remove"
                            >
                              <i className="fa fa-trash" />
                            </button>
                          </td>
                        </tr>
                      );
                    })}
                    {this.getFilteredRules().length === 0 && (
                      <tr>
                        <td colSpan={5} className="text-center text-muted">
                          {lender.digital_reserve_rules && lender.digital_reserve_rules.length > 0
                            ? 'No rules match the current search criteria.'
                            : 'No rules added.'}
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
                <small className='text-muted'>Add rules for digital reserves. Each rule can have multiple stores, product types, and cancellation reasons selected.</small>
              </div>
            </div>
          )}
          {lender.is_digital_reserves && (this.state.validationErrors.digital_reserve_rules || Object.values(this.state.validationErrors).some(err => err.duplicate)) && (
            <div className="alert alert-danger mt-4">
              {this.state.validationErrors.digital_reserve_rules && (
                <div>{this.state.validationErrors.digital_reserve_rules}</div>
              )}
              {Object.values(this.state.validationErrors).some(err => err.duplicate) && (
                <div>Duplicate rule exists. Please remove or modify duplicate rules.</div>
              )}
            </div>
          )}
          <div className="text-right">
            <Link to="/lenders" className="btn btn-secondary">
              <i className="fa fa-ban" /> Cancel
            </Link>
            { ' ' }
            { this.renderSaveButton() }
          </div>
        </form>
        <ConfirmModal
          visible={!!this.state.showDeleteRuleModal}
          close={this.handleCancelDeleteRule}
          confirm={this.handleConfirmDeleteRule}
          proceedLabel="Yes"
          cancelLabel="No"
        >
          <h4><i className='fa fa-warning'></i> Really delete?</h4>
          <p>Really delete {typeof this.state.ruleIndexToDelete === 'number' && this.state.lender.digital_reserve_rules[this.state.ruleIndexToDelete] ? this.state.lender.digital_reserve_rules[this.state.ruleIndexToDelete].name || 'this rule' : 'the selected rule(s)'} ?</p>
        </ConfirmModal>
      </div>
    );
  }

  renderSaveButton() {
    if (this.props.submitting) {
      return (
        <button type="submit" disabled={ true } className="btn btn-primary">
          <i className="fa fa-refresh fa-spin" /> Saving...
        </button>
      );
    } else {
      return (
        <button type="submit" className="btn btn-primary">
          <i className="fa fa-check" /> Save
        </button>
      );
    }
  }
}

const { string, shape, func, bool } = PropTypes;

LendersForm.propTypes = {
  lender: shape({
    intacct_vendor_id: string.isRequired,
    name: string.isRequired,
    address: string.isRequired,
    city: string.isRequired,
    state_code: string.isRequired,
    postal_code: string.isRequired,
    lhm_cdk_finance_code: string,
    asbury_cdk_finance_code: string,
    ucs_lender_id: string,
    tekion_lender_id: string,
    certifiable_makes: PropTypes.shape({
      Map: PropTypes.object
    }).isRequired,
    is_digital_reserves: PropTypes.bool,
  }),
  save: func.isRequired,
  submitting: bool.isRequired,
  errors: shape({
    intacct_vendor_id: string,
    name: string,
    address: string,
    city: string,
    state_code: string,
    postal_code: string,
    lhm_cdk_finance_code: string,
    asbury_cdk_finance_code: string,
    ucs_lender_id: string,
    tekion_lender_id: string,
  }).isRequired,
};
