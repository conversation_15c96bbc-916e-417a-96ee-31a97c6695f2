import React from 'react';
import PropTypes from 'prop-types';
import immstruct from 'immstruct';
import { Link } from 'react-router';
import Alert from 'react-s-alert';
import ReactSelect from 'react-select';

import { jsonPromise as ajax } from 'shared/ajax';
import ErrorSummary from 'shared/components/ErrorSummary';
import FieldError from 'shared/components/FieldError';

export default class VehicleComponentsForm extends React.Component {
  IMMSTRUCT_KEY = 'VehicleComponentsForm';

  static propTypes = {
    vehicleComponent: PropTypes.shape({
      code: PropTypes.number,
      product_type_id: PropTypes.number,
      group_name: PropTypes.string,
      description: PropTypes.string,
    }).isRequired,
    submitting: PropTypes.bool.isRequired,
    errors: PropTypes.shape({
      code: PropTypes.string,
      product_type_id: PropTypes.string,
      group_name: PropTypes.string,
      description: PropTypes.string,
    }).isRequired,
    submit: PropTypes.func.isRequired,
  };

  constructor(props) {
    super(props);
    let vc = this.props.vehicleComponent;
    this.structure = immstruct(this.IMMSTRUCT_KEY, {
      code: vc.code || '',
      product_type_id: vc.product_type_id || '',
      group_name: vc.group_name || '',
      description: vc.description || '',
    });
    this.structure.on('swap', (newStructure, oldStructure, keyPath) => {
      this.setState({ vehicleComponent: this.structure.cursor() });
    });

    this.state = {
      isCodeDisabled: !!vc.code,
      vehicleComponent: this.structure.cursor(),
      loadingSupportingData: true,
      loadedSupportingData: false,
      supportingData: null,
      errors: {},
    };
  }

  componentDidMount() {
    this.loadSupportingData();
  }

  componentWillUnmount() {
    this.structure.removeAllListeners();
    immstruct.remove(this.IMMSTRUCT_KEY);
  }

  loadSupportingData() {
    this.setState({ loadingSupportingData: true, loadedSupportingData: false, supportingData: null }, () => {
      ajax('/api/admin/vehicle-components/supporting-data', {}, {}).then((results)=> {
        if (results.status === 200) {
          let dupData = Object.assign({}, results.data);
          dupData.groups = dupData.groups.map(function(group) { return { value: group, label: group }; });
          this.setState({ loadedSupportingData: true, supportingData: dupData });
        } else {
          Alert.error("Error loading supporting data: " + results.data.message);
        }
        this.setState({ loadingSupportingData: false });
      }).catch((reason)=>{
        Alert.error("Error loading supporting data");
        this.setState({ loadingSupportingData: false });
      });
    });
  }

  onAttrChange = (attr, e) => {
    this.state.vehicleComponent.cursor(attr).set(e.target.value);
  };

  onGroupSelect = (option) => {
    this.state.vehicleComponent.cursor('group_name').set(option);
  };

  postData = () => {
    let vc = this.state.vehicleComponent.toJS();
    vc.code = parseInt(vc.code, 10) || 0;
    vc.product_type_id = parseInt(vc.product_type_id, 10) || 0;
    if (vc.group_name) {
      vc.group_name = vc.group_name.value ? vc.group_name.value : vc.group_name;
    }
    return vc;
  };

  onSubmit = (e) => {
    e.preventDefault();
    this.props.submit(this.postData());
  };

  render() {
    let vc = this.state.vehicleComponent.toJS();
    let e = this.props.errors;
    return (
      <form onSubmit={ this.onSubmit }>
        <ErrorSummary errors={ e } />

        <div className="row">
          <div className="col form-group">
            <label htmlFor="code">*Code</label>
            <input type="number" id="code" value={ vc.code } onChange={ this.onAttrChange.bind(this, "code") } className="form-control" required={ true } disabled={ this.state.isCodeDisabled } />
            <FieldError err={ e.code } />
          </div>
          <div className="col form-group">
            <label htmlFor="product_type_id">*Product Type</label>
            { this.renderProductTypeField() }
            <FieldError err={ e.product_type_id } />
          </div>
        </div>
        <div className="row">
          <div className="col form-group">
            <label htmlFor="group_name">*Group</label>
            { this.renderGroupField() }
            <FieldError err={ e.group_name } />
          </div>
          <div className="col form-group">
            <label htmlFor="description">*Description</label>
            <input type="text" id="description" value={ vc.description } onChange={ this.onAttrChange.bind(this, "description") } className="form-control" required={ true } />
            <FieldError err={ e.description } />
          </div>
        </div>
        <div className="text-right">
          <Link to="/vehicle-components" className="btn btn-secondary"><i className="fa fa-ban" /> Cancel</Link>
          &nbsp;
          { this.renderSubmitButton() }
        </div>
      </form>
    );
  }

  renderProductTypeField() {
    if (this.state.loadedSupportingData) {
      return (
        <select id="product_type_id" value={ this.state.vehicleComponent.cursor("product_type_id").deref() } onChange={ this.onAttrChange.bind(this, "product_type_id") } className="form-control" required={ true }>
          <option value="">&mdash; Select product type &mdash;</option>
          { (this.state.supportingData.product_types || []).map((pt) => {
            return <option key={ pt.id } value={ pt.id }>{ pt.name }</option>;
          }) }
        </select>
      );
    } else if (this.state.loadingSupportingData) {
      return (
        <select id="product_type_id" value={ this.state.vehicleComponent.cursor("product_type_id").deref() } onChange={ this.onAttrChange.bind(this, "product_type_id") } className="form-control" required={ true } disabled={ true }>
          <option value="">&mdash; Loading product types&hellip; &mdash;</option>
        </select>
      );
    } else {
      return (
        <select id="product_type_id" value={ this.state.vehicleComponent.cursor("product_type_id").deref() } onChange={ this.onAttrChange.bind(this, "product_type_id") } className="form-control" required={ true } disabled={ true }>
          <option value="">&mdash; Error loading product types &mdash;</option>
        </select>
      );
    }
  }

  renderGroupField() {
    if (this.state.loadedSupportingData) {
      return (
        <ReactSelect.Creatable
          value={ this.state.vehicleComponent.cursor('group_name').deref() }
          options={ this.state.supportingData.groups }
          onChange={ this.onGroupSelect }
          clearable={ false }
          required={ true } />
      );
    } else if (this.state.loadingSupportingData) {
      return <span className="form-control-plaintext"><i className="fa fa-refresh fa-spin" /> Loading...</span>;
    }
  }

  renderSubmitButton() {
    if (this.props.submitting) {
      return (
        <button type="submit" className="btn btn-primary" disabled={ true }>
          <i className="fa fa-refresh fa-spin" /> Submitting&hellip;
        </button>
      );
    }
    return (
      <button type="submit" className="btn btn-primary">
        <i className="fa fa-check" /> Submit
      </button>
    );
  }
}
