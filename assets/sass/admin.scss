body {
  margin: 0 0 2rem 0;
}

img#nav-logo {
  height: 37px;
}

.expando-texto {
  overflow:hidden;
  text-overflow:ellipsis;
  white-space:nowrap;
}

.expando-texto:hover {
  overflow:visible;
  white-space: normal;
}

.react-datepicker__input-container {
  display:block;
}

.react-datepicker-wrapper {
	display:block;
}

.navbar {
  .dropdown-menu {
    border-width: 0px 1px 1px 1px;
    box-shadow: 2px 2px 2px 0px rgba(190, 190, 190, 0.75);
  }
}

.s-alert-box {
  z-index: 123456;
}

.Select-menu-outer {
  z-index: 12345;
}

// NOTE: Temporary fix(es) for bootstrap
select {
  overflow: auto;
}

// NOTE: Do not know why <PERSON><PERSON><PERSON> is explicitly setting focus outline to zero. (v4-alpha6)
// There seems to be some related discussion here: https://github.com/twbs/bootstrap/issues/17573
// Using styles that would be picked up in button:focus in bootstrap/scss/_reboot.scss
.dropdown-toggle:focus {
  outline: 1px dotted;
  outline: 5px auto -webkit-focus-ring-color;
}

.rule-group {
  margin: 0 0 10px 10px;
  padding: 5px;
  background: rgba(80, 80, 80, 0.05);
}

.rule-condition {
  padding: 5px;
  margin: 0 0 5px 0;
}

.datepicker-inside-modal, .react-datepicker-popper{
  z-index: 200000;
}

// Remove extra space around the contents of a table cell
td.compact {
  width: 1px;
  white-space: nowrap;
}

.reveal .hide {
  display: none;
}
.reveal:hover .hide {
  display: inline-block;
}

.cursor-pointer {
  cursor: pointer;
}

.wrapper-class {
  padding: 4px;
  border: 1px solid lightslategrey;
}

.editor-class {
  border: 1px solid #F1F1F1;
}

.draft-toolbar-ul{
  visibility: hidden;
}
.draft-toolbar-li:hover {
  background: #F1F1F1;
}
.draft-toolbar-block-dropdown{
  width:200px;
}

div
.sso-button {
  a:hover, a:visited, a:link, :active {
    text-decoration: none;
  }
  margin: 10px 0px;
}

div
.sso-box-outer {
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  border-radius: 8px;
  overflow: hidden;
  border-style: solid;
  border-width: 1px;
  border-color:#c0c0c0;
  color: #626262;
  padding: 10px 20px;
  height: 100;
  width: 300px;
  margin: 0px auto;

  display: flex;
  align-items: center;
}
div
.sso-box-outer:hover {
  background-color: #d9d9d9;
}



div
.sso-box-outer
.sso-box-inner {
  display: inline;
}


div
.sso-box-outer
.sso-logo {
  margin-right: 10px;
  max-width: 60px;
  max-height: 60px;
}

.cancellation-page {
  margin-left: 100px;
  margin-right: 100px;
  max-width: 100%;
  width: auto;
  padding-left: 0;
  padding-right: 0;
}

// Table row striping
.quotes-table {
  tbody.table-row-odd {
    background-color: rgba(0, 0, 0, 0.05);
    tr { background-color: inherit; }
  }
  tbody.table-row-even {
    background-color: #fff;
    tr { background-color: inherit; }
  }
  tbody:hover {
    background-color: rgba(0, 0, 0, 0.075);
    tr { background-color: inherit; }
  }
}

.check-options-box {
  border: 2px solid #d1d5db;
  border-radius: 6px;
  padding: 16px;
  margin-top: 12px;
  margin-bottom: 12px;
  background: #f5f5f5;
  box-shadow: 0 2px 6px rgba(0,0,0,0.03);
  max-width: 500px;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 600px) {
  .check-options-box {
    margin-left: 0;
  }
}

.check-options-box input[type="email"] {
  max-width: 400px;
}