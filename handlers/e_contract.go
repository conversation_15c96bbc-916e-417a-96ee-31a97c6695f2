package handlers

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"

	"whiz/conf"
	"whiz/db"
	"whiz/dms"
	"whiz/dmsfactory"
	"whiz/s3util"
	"whiz/slice"
	"whiz/types"
	"whiz/util"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	newrelic "github.com/newrelic/go-agent"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"gopkg.in/guregu/null.v3"
)

const apiDateFormat = "2006-01-02"

// getEContractingDetails gets econtracting details
func getEContractingDetails(ctx context.Context, contractCode, dealerID string) (db.CurrentUser, int, int, error) {
	// Get system user to process e contract void
	var user db.CurrentUser
	err := db.Get().Unsafe().GetContext(ctx, &user, `select * from current_users where first_name='SYSTEM'`)
	if err != nil {
		return user, 0, 0, createCustomError("Error Remitting contract", http.StatusInternalServerError,
			errors.Wrapf(err, "could not query _SYSTEM user for contract %s", contractCode), "")
	}

	var storeID int
	err = db.Get().GetContext(ctx, &storeID, `select id from stores where e_rating_dealer_id = $1`, dealerID)
	if err != nil {
		if err == sql.ErrNoRows {
			return user, 0, 0, createCustomError("Store not found, please provide valid dealer ID", http.StatusNotFound,
				errors.Wrapf(err, "error getting store id for contract %s", contractCode), db.ErrCodeDealerNotFound)
		}
		return user, 0, 0, createCustomError("Error getting store", http.StatusInternalServerError,
			errors.Wrapf(err, "error getting store id for contract %s", contractCode), "")
	}

	// Get contract id from contracts table for provided code
	var contractID int
	query := `select id from contracts where (code = $1 or original_code = $1) and is_e_contract = true`
	err = db.Get().GetContext(ctx, &contractID, query, contractCode)
	if err != nil {
		if err == sql.ErrNoRows {
			return user, 0, 0, createCustomError("Contract not found, please provide valid contract code", http.StatusNotFound,
				errors.Wrapf(err, "database error getting contract id for contract %s", contractCode), db.ErrCodeContractNotFound)
		}
		return user, 0, 0, createCustomError("Error getting contract", http.StatusInternalServerError,
			errors.Wrapf(err, "error getting store id for contract %s", contractCode), "")
	}

	return user, storeID, contractID, nil
}

type eContractLender struct {
	PendingLenderID int      `json:"-" db:"-"`
	LenderID        null.Int `json:"-" db:"lender_id"`
	Name            string   `json:"name" db:"name"`
	Address         string   `json:"address" db:"address"`
	City            string   `json:"city" db:"city"`
	StateCode       string   `json:"state_code" db:"state_code"`
	PostalCode      string   `json:"postal_code" db:"postal_code"`
	FinanceSource   string   `json:"finance_source" db:"finance_source"`
}

// EContractPayload is the payload for generating an e-contract
type EContractPayload struct {
	ESaleID        int         `json:"e_sale_id"`
	EmployeeNumber null.String `json:"employee_number"`
	Customer       struct {
		FirstName      string `json:"first_name"`
		LastName       string `json:"last_name"`
		Address        string `json:"address"`
		City           string `json:"city"`
		StateCode      string `json:"state_code"`
		PostalCode     string `json:"postal_code"`
		Email          string `json:"email"`
		Phone          string `json:"phone"`
		Cellular       string `json:"cellular"`
		CustomerNumber string `json:"customer_number"`
	} `json:"customer"`
	Cobuyer struct {
		FirstName  string      `json:"first_name"`
		LastName   string      `json:"last_name"`
		Address    null.String `json:"address"`
		City       null.String `json:"city"`
		StateCode  null.String `json:"state_code"`
		PostalCode null.String `json:"postal_code"`
		Email      null.String `json:"email"`
		HomePhone  null.String `json:"home_phone"`
		AltPhone   null.String `json:"alt_phone"`
	} `json:"cobuyer"`
	DMSNumber             string             `json:"dms_number"`
	FinanceAPR            *decimal.Decimal   `json:"finance_apr"`
	FinanceMonthlyPayment decimal.Decimal    `json:"finance_monthly_payment"`
	FirstPaymentDate      types.JSPQNullDate `json:"first_payment_date"`
	FinanceAmount         decimal.Decimal    `json:"finance_amount"`
	VehiclePrice          decimal.Decimal    `json:"vehicle_price"`
	Lender                eContractLender    `json:"lender"`
	Product               struct {
		ProductTypeID    int             `json:"product_type_id"`
		ProductVariantID int             `json:"product_variant_id"`
		PlanID           int             `json:"plan_id"`
		OptionIDs        []int           `json:"option_ids"`
		Price            decimal.Decimal `json:"price"`
		VTANumber        string          `json:"vta_number"`
	} `json:"product"`
}

func createRateQueryRecord(ctx context.Context, eSale *db.ESale, dp *db.DealerPlatform) (db.RateQuery, error) {
	var q db.RateQuery

	q.DealType = eSale.SaleType

	err := db.Get().Unsafe().GetContext(ctx, &q.Store, `select * from stores where id = $1`, eSale.StoreID)
	if err != nil {
		if err == sql.ErrNoRows {
			return q, errors.New("dealer ID not found")
		}
		err = errors.Wrap(err, "error getting store for rate query")
		return q, err
	}

	err = db.Get().Unsafe().GetContext(ctx, &q.VINRecord, `select * from vin_records where id = $1`, eSale.VINRecordID)
	if err != nil {
		err = errors.Wrap(err, "error getting vin record for rate query")
		return q, err
	}
	q.Odometer = eSale.Odometer
	q.IsNew = eSale.IsNew
	q.PaymentType = eSale.PaymentType
	q.IsCPO = eSale.IsCPO
	q.MSRP = eSale.MSRP
	if eSale.FinanceTerm.Valid {
		q.FinanceDetails.Term = int(eSale.FinanceTerm.Int64)
	}

	if eSale.FinanceAmount.Valid {
		q.FinanceDetails.Amount = eSale.FinanceAmount.Decimal
	}

	q.FinanceDetails.Lender = eSale.LenderName.ValueOrZero()

	q.KeysRemotes = eSale.KeysRemotes
	q.ContractDate = eSale.ContractDate

	var storeCompany string
	err = db.Get().GetContext(ctx, &storeCompany, `select c.code as code from companies c join stores s on s.company_id = c.id where s.id = $1`, q.Store.ID)
	if err != nil {
		return q, errors.Wrap(err, "error getting company")
	}

	companyGroup, err := db.GetCompanyGroupByStoreID(ctx, q.Store.ID)
	if err != nil {
		return q, errors.WithMessagef(err, "error getting company group for rate query of store id %d", q.Store.ID)
	}
	if companyGroup != nil {
		q.CompanyGroup = strconv.Itoa(companyGroup.ID)
	}

	q.Company = storeCompany
	if dp.SalesChannel == db.SalesChannelCustomer {
		hasGapLicense, err := db.FindActiveUserForGAPLicense(q.Store.ID)
		if err != nil {
			return q, errors.Wrap(err, "error finding active salesperson on store for GAP license")
		}
		q.HasGAPLicense = hasGapLicense
	}
	q.HasDeal = true
	return q, nil
}

func createSaleRecord(
	eSale *db.ESale,
	eContract *EContractPayload,
	lenderID int,
	employeeID null.Int,
	deal *dms.Deal,
	roundup bool,
) (*db.Sale, error) {
	var sale db.Sale

	sale.SaleType = eSale.SaleType
	sale.StoreID = eSale.StoreID
	sale.VINRecordID = eSale.VINRecordID
	sale.Odometer = eSale.Odometer
	sale.IsNew = eSale.IsNew
	sale.PaymentType = eSale.PaymentType
	sale.IsCPO = eSale.IsCPO
	sale.MSRP = eSale.MSRP
	sale.FinanceTerm = eSale.FinanceTerm
	sale.PriceRoundUp = roundup
	if deal != nil {
		sale.DMSStockNumber = sql.NullString{String: deal.Vehicle.DMSStockNumber, Valid: true}
	}

	sale.FinanceAmount = eSale.FinanceAmount
	// TODO: This is temprory fix for the TS-7842 need to change this after right fix
	if eContract.FinanceAmount.GreaterThan(decimal.Zero) {
		sale.FinanceAmount = decimal.NullDecimal{
			Decimal: eContract.FinanceAmount,
			Valid:   true,
		}
	}

	sale.KeysRemotes = eSale.KeysRemotes
	sale.VehiclePrice = eSale.VehiclePrice
	sale.ContractDate = eSale.ContractDate
	sale.QuoteExpiresAt = time.Now().Add(db.QuoteExpireDuration)
	sale.FinanceAPR = decimal.NullDecimal{Decimal: *eContract.FinanceAPR, Valid: true}
	sale.FinanceMonthlyPayment = decimal.NullDecimal{Decimal: eContract.FinanceMonthlyPayment, Valid: true}
	sale.FirstPaymentDate = eContract.FirstPaymentDate.NullTime
	if eContract.VehiclePrice.IsPositive() {
		sale.VehiclePrice = decimal.NullDecimal{Decimal: eContract.VehiclePrice, Valid: true}
	}
	sale.DMSNumber = eContract.DMSNumber
	sale.IsDMSDeal = true
	if eSale.PaymentType == dms.PaymentTypeLease || eSale.PaymentType == dms.PaymentTypeLoan {
		if lenderID != 0 {
			sale.LenderID.Int64 = int64(lenderID)
			sale.LenderID.Valid = true
		}
		if eContract.Lender.PendingLenderID != 0 {
			sale.PendingLenderID.SetValid(int64(eContract.Lender.PendingLenderID))
		}
	}
	sale.SalespersonID = employeeID
	if eContract.Customer.CustomerNumber != "" {
		sale.DMSCustomerNumber = sql.NullString{String: eContract.Customer.CustomerNumber, Valid: true}
	}
	sale.ESaleEmployeeNumber = eContract.EmployeeNumber
	return &sale, nil
}

func createCustomerRecord(payload *EContractPayload, eSale *db.ESale) *db.Customer {
	var lastName, businessName string
	var isBusiness bool
	// First check if the eSale was flagged for Commercial Use and treat the
	// Customer as a business.
	if eSale.IsCommercialUse || payload.Customer.FirstName == "" {
		isBusiness = true
		if payload.Customer.FirstName != "" {
			businessName = payload.Customer.FirstName + " " + payload.Customer.LastName
		} else {
			businessName = payload.Customer.LastName
		}
	} else {
		lastName = payload.Customer.LastName
	}

	return &db.Customer{
		BusinessName: businessName,
		IsBusiness:   isBusiness,
		FirstName:    payload.Customer.FirstName,
		LastName:     lastName,
		Address:      payload.Customer.Address,
		City:         payload.Customer.City,
		StateCode:    payload.Customer.StateCode,
		PostalCode:   payload.Customer.PostalCode,
		Email:        payload.Customer.Email,
		Phone:        payload.Customer.Phone,
		Cellular:     payload.Customer.Cellular,
	}
}

func createCobuyerRecord(payload *EContractPayload) *db.ContractCoBuyer {
	if payload.Cobuyer.FirstName == "" {
		return nil
	}
	return &db.ContractCoBuyer{
		FirstName:  payload.Cobuyer.FirstName,
		LastName:   payload.Cobuyer.LastName,
		Address:    payload.Cobuyer.Address,
		City:       payload.Cobuyer.City,
		StateCode:  payload.Cobuyer.StateCode,
		PostalCode: payload.Cobuyer.PostalCode,
		Email:      payload.Cobuyer.Email,
		HomePhone:  payload.Cobuyer.HomePhone,
		AltPhone:   payload.Cobuyer.AltPhone,
	}
}

func insertNewSaleForESale(
	ctx context.Context,
	tx *sqlx.Tx,
	eSale *db.ESale,
	sale *db.Sale,
	customer *db.Customer,
	cobuyer *db.ContractCoBuyer,
	user *db.CurrentUser,
) (*db.Sale, *db.Customer, error) {
	txn := newrelic.FromContext(ctx)

	var userID int
	if sale.SalespersonID.Valid {
		userID = int(sale.SalespersonID.Int64)
	} else {
		userID = user.ID
	}

	customer.CreatedByUserID, customer.UpdatedByUserID = userID, userID

	stmt, err := tx.PrepareNamedContext(ctx, `insert into customers
									("created_at", "created_by_user_id", "updated_at", "updated_by_user_id", 
									"is_business", "business_name", "first_name", "last_name", "address", "city",
									"state_code", "postal_code", "email", "phone", "cellular") 
									values (now() at time zone 'utc', :created_by_user_id,now() at time zone 'utc', 
									:updated_by_user_id, :is_business, :business_name, :first_name, :last_name,
									:address, :city, :state_code, :postal_code, :email, :phone, :cellular) returning id`)
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "could not prepare insert into customers")
		return sale, customer, err
	}
	defer func() { _ = stmt.Close() }()
	sgmt := newrelic.StartSegment(txn, "exec insert customers")
	var customerID int
	err = stmt.GetContext(ctx, &customerID, customer)
	sgmt.End()
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "could not insert customer version")
		return sale, customer, err
	}

	sale.CustomerID = customerID

	if cobuyer != nil {

		cobuyer.CreatedByUserID = userID
		cobuyer.UpdatedByUserID = null.NewInt(int64(userID), true)

		coBuyerStmt, err := tx.PrepareNamedContext(ctx, `insert into contract_cobuyers
			("created_at", "created_by_user_id", "updated_at", "updated_by_user_id", 
	 		"first_name", "last_name", "address", "city", 
			"state_code", "postal_code", "email", "home_phone", "alt_phone") 
			values (now() at time zone 'utc', :created_by_user_id,now() at time zone 'utc', 
			:updated_by_user_id, :first_name, :last_name, 
			:address, :city, :state_code, :postal_code, :email, :home_phone, :alt_phone) returning id`)

		if err != nil {
			_ = tx.Rollback()
			err = errors.Wrap(err, "could not prepare insert into contract_cobuyers")
			return sale, customer, err
		}
		defer func() { _ = coBuyerStmt.Close() }()
		sgmt = newrelic.StartSegment(txn, "exec insert contract_cobuyers")
		var contractCoBuyerID int
		err = coBuyerStmt.GetContext(ctx, &contractCoBuyerID, cobuyer)
		sgmt.End()
		if err != nil {
			_ = tx.Rollback()
			err = errors.Wrap(err, "could not insert contract cobuyer")
			return sale, customer, err
		}
		sale.ContractCoBuyerID.Valid = true
		sale.ContractCoBuyerID.Int64 = int64(contractCoBuyerID)
	}

	sale.CreatedByUserID = userID

	// for e_sale the quote version is 3
	sale.QuoteVersion = 3
	salesStmt, err := tx.PrepareNamedContext(ctx, `insert into sales 
											(created_at, created_by_user_id, customer_id, vin_record_id, store_id,
											sale_type, is_dms_deal, dms_number, dms_customer_number, salesperson_id,
											dms_stock_number, payment_type, finance_term, finance_amount, finance_apr, 
											finance_monthly_payment, is_cpo, keys_remotes, is_new, quote_expires_at, 
											odometer, contract_date, lender_id, inspection_liability, is_cudl, 
											vehicle_price, first_payment_date, msrp, quote_version, is_crc,
											is_referral_selected, is_quote_referred, contract_cobuyer_id, is_e_sale,
											pending_lender_id, e_sale_employee_number)
										values (now() at time zone 'utc', :created_by_user_id, :customer_id, 
											:vin_record_id, :store_id, :sale_type, :is_dms_deal,
											:dms_number, :dms_customer_number, :salesperson_id, :dms_stock_number, :payment_type, 
											:finance_term, :finance_amount, :finance_apr, :finance_monthly_payment, 
											:is_cpo, :keys_remotes, :is_new, :quote_expires_at, :odometer, 
											:contract_date, :lender_id, :inspection_liability, :is_cudl, :vehicle_price, 
											:first_payment_date, :msrp, :quote_version, :is_crc, :is_referral_selected,
											:is_quote_referred, :contract_cobuyer_id, true, :pending_lender_id,
											:e_sale_employee_number) returning id`)
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "could not prepare insert into sales")
		return sale, customer, err
	}
	defer func() { _ = salesStmt.Close() }()
	sgmt = newrelic.StartSegment(txn, "get insert sales")

	err = salesStmt.GetContext(ctx, &sale.ID, sale)
	sgmt.End()
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "could not insert sale")
		return sale, customer, err
	}
	_, err = tx.ExecContext(ctx, `update e_sales set sale_id=$1 where id = $2`, sale.ID, eSale.ID)
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "could not update e-sale")
		return sale, customer, err
	}
	return sale, customer, nil
}

// Clean cleans the payload
func (p *EContractPayload) Clean() {
	p.Customer.FirstName = strings.TrimSpace(p.Customer.FirstName)
	p.Customer.LastName = strings.TrimSpace(p.Customer.LastName)
	p.Customer.Address = strings.TrimSpace(p.Customer.Address)
	p.Customer.City = strings.TrimSpace(p.Customer.City)
	p.Customer.StateCode = strings.TrimSpace(p.Customer.StateCode)
	p.Customer.PostalCode = strings.TrimSpace(p.Customer.PostalCode)
	p.Customer.Email = strings.TrimSpace(p.Customer.Email)
	p.Customer.Phone = strings.TrimSpace(p.Customer.Phone)
	p.Customer.Cellular = strings.TrimSpace(p.Customer.Cellular)
	p.Customer.CustomerNumber = strings.TrimSpace(p.Customer.CustomerNumber)
	p.Cobuyer.FirstName = strings.TrimSpace(p.Cobuyer.FirstName)
	p.Cobuyer.LastName = strings.TrimSpace(p.Cobuyer.LastName)
	p.Cobuyer.Address.String = strings.TrimSpace(p.Cobuyer.Address.String)
	p.Cobuyer.City.String = strings.TrimSpace(p.Cobuyer.City.String)
	p.Cobuyer.StateCode.String = strings.TrimSpace(p.Cobuyer.StateCode.String)
	p.Cobuyer.PostalCode.String = strings.TrimSpace(p.Cobuyer.PostalCode.String)
	p.Cobuyer.Email.String = strings.TrimSpace(p.Cobuyer.Email.String)
	p.Cobuyer.HomePhone.String = strings.TrimSpace(p.Cobuyer.HomePhone.String)
	p.Cobuyer.AltPhone.String = strings.TrimSpace(p.Cobuyer.AltPhone.String)
	if p.EmployeeNumber.Valid {
		p.EmployeeNumber.String = strings.TrimSpace(p.EmployeeNumber.String)
	}
	p.DMSNumber = strings.TrimSpace(p.DMSNumber)
	p.Lender.Name = strings.TrimSpace(p.Lender.Name)
	p.Lender.Address = strings.TrimSpace(p.Lender.Address)
	p.Lender.City = strings.TrimSpace(p.Lender.City)
	p.Lender.StateCode = strings.TrimSpace(p.Lender.StateCode)
	p.Lender.PostalCode = strings.TrimSpace(p.Lender.PostalCode)
	p.Lender.FinanceSource = strings.TrimSpace(p.Lender.FinanceSource)
}

func (p EContractPayload) apiValidate(ctx context.Context, ds db.DealerPlatform, productTypeCode string) *APIError {
	if p.ESaleID == 0 {
		return &APIError{
			Err:            errors.New("value for e_sale_id is missing"),
			ErrCode:        db.ErrCodeInvalidRequestParam,
			StatusCode:     http.StatusBadRequest,
			DisplayMessage: "ESale ID is required",
		}
	}
	if p.Customer.LastName == "" {
		return &APIError{
			Err:            errors.New("value for customer_last_name is missing"),
			ErrCode:        db.ErrCodeInvalidRequestParam,
			StatusCode:     http.StatusBadRequest,
			DisplayMessage: "Customer last name is required",
		}
	}
	if p.Customer.Address == "" {
		return &APIError{
			Err:            errors.New("value for customer_address is missing"),
			ErrCode:        db.ErrCodeInvalidRequestParam,
			StatusCode:     http.StatusBadRequest,
			DisplayMessage: "Customer address is required",
		}
	}
	if p.Customer.City == "" {
		return &APIError{
			Err:            errors.New("value for customer_city is missing"),
			ErrCode:        db.ErrCodeInvalidRequestParam,
			StatusCode:     http.StatusBadRequest,
			DisplayMessage: "Customer city is required",
		}
	}
	if p.Customer.StateCode == "" {
		return &APIError{
			Err:            errors.New("value for customer_state_code is missing"),
			ErrCode:        db.ErrCodeInvalidRequestParam,
			StatusCode:     http.StatusBadRequest,
			DisplayMessage: "Customer state code is required",
		}
	}
	if p.Customer.PostalCode == "" {
		return &APIError{
			Err:            errors.New("value for customer_postal_code is missing"),
			ErrCode:        db.ErrCodeInvalidRequestParam,
			StatusCode:     http.StatusBadRequest,
			DisplayMessage: "Customer postal code is required",
		}
	}
	if p.Customer.Phone == "" {
		return &APIError{
			Err:            errors.New("value for customer_phone is missing"),
			ErrCode:        db.ErrCodeInvalidRequestParam,
			StatusCode:     http.StatusBadRequest,
			DisplayMessage: "Customer phone is required",
		}
	}

	if p.Cobuyer.FirstName != "" && p.Cobuyer.LastName == "" {
		return &APIError{
			Err:            errors.New("value for cobuyer_last_name is missing"),
			ErrCode:        db.ErrCodeInvalidRequestParam,
			StatusCode:     http.StatusBadRequest,
			DisplayMessage: "Cobuyer last name is required",
		}
	}
	if p.Cobuyer.LastName != "" && p.Cobuyer.FirstName == "" {
		return &APIError{
			Err:            errors.New("value for cobuyer_first_name is missing"),
			ErrCode:        db.ErrCodeInvalidRequestParam,
			StatusCode:     http.StatusBadRequest,
			DisplayMessage: "Cobuyer first name is required",
		}
	}
	if p.DMSNumber == "" {
		return &APIError{
			Err:            errors.New("value for dms_number is missing"),
			ErrCode:        db.ErrCodeInvalidRequestParam,
			StatusCode:     http.StatusBadRequest,
			DisplayMessage: "DMS Number is required",
		}
	}

	if p.Product.ProductTypeID == 0 {
		return &APIError{
			Err:            errors.New("value for product_type_id is missing"),
			ErrCode:        db.ErrCodeInvalidRequestParam,
			StatusCode:     http.StatusBadRequest,
			DisplayMessage: "Product type id is required.",
		}
	}
	if p.Product.ProductVariantID == 0 {
		return &APIError{
			Err:            errors.New("value for product_variant_id is missing"),
			ErrCode:        db.ErrCodeInvalidRequestParam,
			StatusCode:     http.StatusBadRequest,
			DisplayMessage: "Product variant id is required.",
		}
	}
	if p.Product.PlanID == 0 {
		return &APIError{
			Err:            errors.New("value for plan_id is missing"),
			ErrCode:        db.ErrCodeInvalidRequestParam,
			StatusCode:     http.StatusBadRequest,
			DisplayMessage: "Plan id is required.",
		}
	}
	if p.Product.Price.IsZero() {
		return &APIError{
			Err:            errors.New("value for product_price is missing"),
			ErrCode:        db.ErrCodeInvalidRequestParam,
			StatusCode:     http.StatusBadRequest,
			DisplayMessage: "Product price is required",
		}
	}

	if apiErr := validateESale(ctx, p, productTypeCode); apiErr != nil {
		return apiErr
	}

	if apiErr := validateVTANumber(ctx, p); apiErr != nil {
		return apiErr
	}

	return nil
}

func validateESale(ctx context.Context, payload EContractPayload, productTypeCode string) *APIError {
	var eSale db.ESale
	query := `select * from e_sales where id = $1`
	err := db.Get().Unsafe().GetContext(ctx, &eSale, query, payload.ESaleID)
	if err != nil {
		if err == sql.ErrNoRows {
			util.LogWarning(ctx, errors.Wrapf(err, "eSale not found: %d", payload.ESaleID))
			return &APIError{
				Err:            errors.New(fmt.Sprintf("e_sales with id %d not found", payload.ESaleID)),
				ErrCode:        db.ErrCodeSaleNotFound,
				StatusCode:     http.StatusNotFound,
				DisplayMessage: "ESale not found. Please provide a valid e-sale ID",
			}
		}
		return &APIError{
			Err:            errors.New("error getting e_sales data"),
			ErrCode:        db.ErrCodeUnexpectedErr,
			StatusCode:     http.StatusInternalServerError,
			DisplayMessage: "Error generating e-contract",
		}
	}

	// We only require this when deal is lease or loan
	if eSale.PaymentType == dms.PaymentTypeLease || eSale.PaymentType == dms.PaymentTypeLoan {
		if payload.FinanceAPR == nil {
			return &APIError{
				Err:            errors.New("value for finance_apr is missing"),
				ErrCode:        db.ErrCodeInvalidRequestParam,
				StatusCode:     http.StatusBadRequest,
				DisplayMessage: "Finance APR is required",
			}
		}
		if payload.FinanceMonthlyPayment.IsZero() {
			return &APIError{
				Err:            errors.New("value for finance_monthly_payment is missing"),
				ErrCode:        db.ErrCodeInvalidRequestParam,
				StatusCode:     http.StatusBadRequest,
				DisplayMessage: "Finance monthly payment is required",
			}
		}
		if !payload.FirstPaymentDate.Valid {
			return &APIError{
				Err:            errors.New("value for first_payment_date is missing"),
				ErrCode:        db.ErrCodeInvalidRequestParam,
				StatusCode:     http.StatusBadRequest,
				DisplayMessage: "First payment date is required",
			}
		}
		if productTypeCode == db.ProductTypeCodeGuaranteedAssetProtection {
			if eSale.FinanceAmount.Valid && payload.FinanceAmount.LessThan(eSale.FinanceAmount.Decimal) {
				return &APIError{
					Err:            errors.New("value for finance_amount is less than e-rating finance_amount"),
					ErrCode:        db.ErrCodeGAPFinanceAmountLess,
					StatusCode:     http.StatusBadRequest,
					DisplayMessage: "Finance Amount less than what was in e-rating",
				}
			}
		}
	}

	if isLenderSPP(&payload) && productTypeCode != db.ProductTypeCodeService && productTypeCode != db.ProductTypeCodeMaintenance {
		return &APIError{
			Err:            errors.New("invalid product type for SPP Lender"),
			ErrCode:        db.ErrCodeInvalidProductTypeForSPP,
			StatusCode:     http.StatusBadRequest,
			DisplayMessage: "Invalid product type for SPP Lender",
		}
	}
	return nil
}

func getESaleForUpdateByID(ctx context.Context, tx *sqlx.Tx, id int) (db.ESale, error) {
	var eSale db.ESale
	query := `select * from e_sales where id = $1
		for update`

	// Using a savepoint to avoid holding onto the lock for the entire transaction if the sale has already been created
	_, err := tx.ExecContext(ctx, "savepoint check_for_sale_by_e_sale")
	if err != nil {
		return db.ESale{}, errors.Wrapf(err, "could not create savepoint check_for_sale_by_e_sale")
	}
	err = tx.Unsafe().GetContext(ctx, &eSale, query, id)
	if err != nil {
		return db.ESale{}, errors.Wrapf(err, "could not get e_sale with id %d", id)
	}
	if eSale.SaleID.Valid {
		// We don't need to hold onto the lock because the sale has already been created for the e_sale
		_, err = tx.ExecContext(ctx, "rollback to savepoint check_for_sale_by_e_sale")
		if err != nil {
			return db.ESale{}, errors.Wrapf(err, "could not rollback to savepoint check_for_sale_by_e_sale")
		}
	}

	return eSale, nil
}

func unvoidedContractsForSaleExists(ctx context.Context, saleID int) (bool, error) {
	query := "select count(*) from contracts where sale_id = ? and status in (?)"

	query, args, err := sqlx.In(query, saleID, db.ContractStatusesActive)
	if err != nil {
		return false, errors.Wrap(err, "database error creating in query for validating active contracts")
	}
	query = db.Get().Rebind(query)
	var count int
	err = db.Get().GetContext(ctx, &count, query, args...)
	if err != nil {
		return false, errors.Wrap(err, "database error in getting active contracts count")
	}
	return (count > 0), nil
}

const apiErrMsg string = "Error generating e-contract"

// GenerateEContract generates eContract for given e_sale and product
func GenerateEContract(_ http.ResponseWriter, req *http.Request, xtkUserCode string) (int, map[string]interface{}) {
	ctx := req.Context()
	var data map[string]interface{}

	var payload EContractPayload
	payload.FinanceAPR = nil
	decoder := json.NewDecoder(req.Body)
	err := decoder.Decode(&payload)
	if err != nil {
		fmt.Printf("Error decoding request body: %v", err)
		return http.StatusBadRequest, ErrorMessage("Unable to parse request", nil)
	}
	payload.Clean()

	// log request body
	logPENRequest(ctx, payload, xtkUserCode, req.URL.Path, "")

	apiErr, responseData := CreateEContractFromPayload(ctx, &payload, xtkUserCode, true)
	if apiErr != nil {
		warningStatuses := []int{http.StatusBadRequest, http.StatusNotFound}
		if !slice.ContainsInt(warningStatuses, apiErr.StatusCode) {
			ReportError(req, apiErr.Err)
		} else {
			util.LogWarning(ctx, apiErr.Err)
		}

		if apiErr.ErrCode != "" {
			data = make(map[string]interface{})
			data["error_code"] = apiErr.ErrCode
		}

		if apiErr.DisplayMessage == "" {
			apiErr.DisplayMessage = apiErrMsg
		}

		return apiErr.StatusCode, ErrorMessage(apiErr.DisplayMessage, data)
	}

	// log response body
	logPENResponse(ctx, responseData, xtkUserCode, req.URL.Path, fmt.Sprintf("%v", responseData["contract_number"]))

	return http.StatusOK, responseData
}

// CreateEContractFromPayload creates an e-contract from a payload
func CreateEContractFromPayload(ctx context.Context, payload *EContractPayload, xtkUserCode string, checkDms bool) (*APIError, map[string]interface{}) {
	var dp db.DealerPlatform
	var paymentTypeChanged bool
	var apiErr *APIError

	// Get the dealer platform record associated with the provided xtkUserCode
	dp, err := getDealerPlatformForXtkCode(ctx, xtkUserCode)
	if err != nil {
		err = errors.WithMessage(err, "error in getting dealer platform")
		apiErr = &APIError{
			Err:        errors.WithMessage(err, "error in getting dealer platform"),
			StatusCode: http.StatusInternalServerError,
		}
		return apiErr, map[string]interface{}{}
	}

	var productTypeCode string
	query := "select code from product_types where id = $1"
	err = db.Get().GetContext(ctx, &productTypeCode, query, payload.Product.ProductTypeID)
	if err != nil {
		apiErr = &APIError{
			Err:        errors.Wrap(err, "error getting product type code"),
			StatusCode: http.StatusInternalServerError,
		}
		return apiErr, map[string]interface{}{}
	}

	apiErr = payload.apiValidate(ctx, dp, productTypeCode)
	if apiErr != nil {
		return apiErr, map[string]interface{}{}
	}

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		apiErr = &APIError{
			Err:        errors.Wrap(err, "could not begin transaction"),
			StatusCode: http.StatusInternalServerError,
		}
		return apiErr, map[string]interface{}{}
	}

	var eSale db.ESale
	eSale, err = getESaleForUpdateByID(ctx, tx, payload.ESaleID)
	if err != nil {
		_ = tx.Rollback()
		if err == sql.ErrNoRows {
			data := make(map[string]interface{})
			data["error_code"] = db.ErrCodeSaleNotFound
			err = errors.Wrap(err, "e_sales not found")
			util.LogWarning(ctx, errors.Wrapf(err, "eSale not found:%d", payload.ESaleID))
			apiErr = &APIError{
				Err:            err,
				StatusCode:     http.StatusNotFound,
				DisplayMessage: "ESale not found. Please provide a valid e-sale ID",
			}
		} else {
			apiErr = &APIError{
				Err:        errors.Wrap(err, "error getting e_sales data"),
				StatusCode: http.StatusInternalServerError,
			}
		}
		return apiErr, map[string]interface{}{}
	}

	dealerIDEnabled, err := isDPAccessibleToStore(ctx, eSale.StoreID, dp.ID)
	if err != nil {
		_ = tx.Rollback()
		apiErr = &APIError{
			Err:        errors.WithMessagef(err, "error in validating store dealer platform for store (%d) dealer(%d)", eSale.StoreID, dp.ID),
			StatusCode: http.StatusInternalServerError,
		}
		return apiErr, map[string]interface{}{}
	}
	if !dealerIDEnabled {
		_ = tx.Rollback()
		apiErr = &APIError{
			Err:            errors.Wrapf(err, "dealer platform ID:%d not enabled for this store:%d", dp.ID, eSale.StoreID),
			StatusCode:     http.StatusBadRequest,
			DisplayMessage: "Dealer platform ID not enabled for this store",
		}
		return apiErr, map[string]interface{}{}
	}

	rq, err := createRateQueryRecord(ctx, &eSale, &dp)
	if err != nil {
		_ = tx.Rollback()
		apiErr = &APIError{
			Err:        err,
			StatusCode: http.StatusInternalServerError,
		}
		return apiErr, map[string]interface{}{}
	}

	var user db.CurrentUser
	err = db.Get().GetContext(ctx, &user, `select * from current_users where first_name='SYSTEM'`)
	if err != nil {
		_ = tx.Rollback()
		apiErr = &APIError{
			Err:        errors.Wrap(err, "could not get system user ID"),
			StatusCode: http.StatusInternalServerError,
		}
		return apiErr, map[string]interface{}{}
	}

	sie, err := getInspectionEligibility(ctx, rq.VINRecord.VIN, user.ID, eSale.ContractDate, true)
	if err != nil {
		_ = tx.Rollback()
		apiErr = &APIError{
			Err:        errors.Wrap(err, "error in getting inspections"),
			StatusCode: http.StatusInternalServerError,
		}
		return apiErr, map[string]interface{}{}
	}

	// We want to ensure that if rounding up is disabled we by pass the rounding up even if
	// the eSale has it set.
	roundUp := eSale.PriceRoundUp
	if !conf.Get().EnablePriceRoundUp {
		roundUp = false
	}

	quoteProducts, err := getQuoteProductData(ctx, &rq, nil, &sie, true, roundUp)
	if err != nil {
		_ = tx.Rollback()
		apiErr = &APIError{
			Err:        errors.Wrap(err, "error in getting quote product data"),
			StatusCode: http.StatusInternalServerError,
		}
		return apiErr, map[string]interface{}{}
	}
	// validate product input
	isValid, errCode := isValidProductDetails(quoteProducts, payload)
	if !isValid {
		_ = tx.Rollback()
		apiErr = &APIError{
			Err:            errors.New("invalid product details"),
			ErrCode:        errCode,
			StatusCode:     http.StatusBadRequest,
			DisplayMessage: "Invalid product details",
		}
		return apiErr, map[string]interface{}{}
	}

	// validate price, it should not be more than calculated max price
	_, qpv, _, _, calcMaxPrice, isFixedRetail, err := getERatingData(
		ctx,
		quoteProducts,
		payload,
		eSale.FinanceAmount.Decimal,
		eSale.StoreID,
		eSale.ContractDate,
		roundUp,
		&dp,
	)
	if err != nil {
		_ = tx.Rollback()
		apiErr = &APIError{
			Err:        errors.Wrap(err, "error in getting e-rating data"),
			StatusCode: http.StatusInternalServerError,
		}
		return apiErr, map[string]interface{}{}
	}
	if isFixedRetail && !payload.Product.Price.RoundBank(2).Equal(calcMaxPrice.RoundBank(2)) {
		_ = tx.Rollback()
		apiErr = &APIError{
			Err:            errors.New("invalid price: e-contract payload price is not equal to connect fixed price"),
			StatusCode:     http.StatusBadRequest,
			DisplayMessage: "Invalid Price: Product price should be same as e-rating price",
		}
		return apiErr, map[string]interface{}{}
	}
	if payload.Product.Price.RoundBank(2).GreaterThan(calcMaxPrice.RoundBank(2)) {
		_ = tx.Rollback()
		apiErr = &APIError{
			Err:            errors.New("invalid price: e-contract payload price is greater than connect price"),
			StatusCode:     http.StatusBadRequest,
			DisplayMessage: "Invalid Price: Product price cannot go above e-rating price",
		}
		return apiErr, map[string]interface{}{}
	}

	// For salesChannel customer, e.g. Clicklane need to validate if there is a GAP license user with the store
	if dp.SalesChannel == db.SalesChannelCustomer {
		if apiErr = validateGAPUser(ctx, rq, qpv, productTypeCode, payload.Product.ProductVariantID); apiErr != nil {
			_ = tx.Rollback()
			return apiErr, map[string]interface{}{}
		}
	}

	//Fetch deal data for stock number and employeeID
	var deal *dms.Deal
	if checkDms {
		deal, err = getDealData(ctx, payload.DMSNumber, eSale.SaleType, rq.Store.ID)
		if err != nil {
			util.LogWarning(ctx, err)
		}
	}

	// Default assigns incoming employeeNumber as the sales person
	salePersonNumber := payload.EmployeeNumber.String
	employeeID, apiErr := getEmployeeID(ctx, salePersonNumber, rq.Store.ID, dp)
	if apiErr != nil {
		_ = tx.Rollback()
		return apiErr, map[string]interface{}{}

	}

	// For salesChannel dealer, e.g. Darwin, need to verify if the employee number from payload has
	// a valid license
	if productTypeCode == db.ProductTypeCodeGuaranteedAssetProtection && dp.SalesChannel == db.SalesChannelDealer {
		checkRequired, err := gapLicenseCheckRequired(ctx, rq, qpv, payload.Product.ProductVariantID)
		if err != nil {
			apiErr = &APIError{
				Err:            errors.WithMessage(err, "error in gap license check"),
				ErrCode:        db.ErrCodeUnexpectedErr,
				StatusCode:     http.StatusInternalServerError,
				DisplayMessage: "Error in GAP license check",
			}
			return apiErr, map[string]interface{}{}
		}
		if checkRequired {
			if employeeID.Valid {
				gapLicense, err := db.FindActiveUserGAPLicense(int(employeeID.Int64), rq.ContractDate)
				if err != nil {
					apiErr = &APIError{
						Err:            errors.WithMessage(err, "error finding GAP license for employee"),
						ErrCode:        db.ErrCodeUnexpectedErr,
						StatusCode:     http.StatusInternalServerError,
						DisplayMessage: "Error finding GAP license for employee",
					}
					return apiErr, map[string]interface{}{}
				}
				if gapLicense == nil {
					apiErr = &APIError{
						Err:            errors.New("missing GAP license for employee"),
						ErrCode:        db.ErrCodeMissingEmployeeGAPLicense,
						StatusCode:     http.StatusBadRequest,
						DisplayMessage: "Missing GAP license for employee",
					}
					return apiErr, map[string]interface{}{}
				}
			} else {
				apiErr = &APIError{
					Err:            errors.New("missing employee number"),
					ErrCode:        db.ErrCodeMissingEmployeeNumber,
					StatusCode:     http.StatusBadRequest,
					DisplayMessage: "Missing employee number",
				}
				return apiErr, map[string]interface{}{}
			}
		}
	}
	var sale *db.Sale
	var customer *db.Customer
	var cobuyer *db.ContractCoBuyer
	var lenderID, lenderMatchStatus int

	if !eSale.SaleID.Valid {
		// We will have lender only for loan and lease deals
		if lenderExists(*payload) && (eSale.PaymentType == dms.PaymentTypeLease || eSale.PaymentType == dms.PaymentTypeLoan) {
			lenderID, lenderMatchStatus, err = getLenderID(ctx, rq.Store.CompanyID, payload)
			if err != nil && err != sql.ErrNoRows {
				_ = tx.Rollback()
				apiErr = &APIError{
					Err:        errors.Wrap(err, "error getting lender ID"),
					StatusCode: http.StatusInternalServerError,
				}
				return apiErr, map[string]interface{}{}
			}
			if err == sql.ErrNoRows {
				err = notifyMissingLender(ctx, payload, "NA", lenderID, lenderMatchStatus)
				if err != nil {
					err = errors.Wrap(err, "error sending lender notification")
					ReportErrorContext(ctx, err)
				}
			}
			if lenderMatchStatus != LenderFound {
				if lenderID != 0 {
					payload.Lender.LenderID.SetValid(int64(lenderID))
				}
				err = pendingLenderInsert(ctx, &payload.Lender)
				if err != nil {
					_ = tx.Rollback()
					apiErr = &APIError{
						Err:        errors.Wrap(err, "error inserting pending lender"),
						StatusCode: http.StatusInternalServerError,
					}
					return apiErr, map[string]interface{}{}
				}
			}
		}

		sale, err = createSaleRecord(&eSale, payload, lenderID, employeeID, deal, roundUp)
		if err != nil {
			_ = tx.Rollback()
			apiErr = &APIError{
				Err:        errors.Wrap(err, "error in creating sale record struct"),
				StatusCode: http.StatusInternalServerError,
			}
			return apiErr, map[string]interface{}{}
		}

		// Passing the eSale record so that the customer will be setup as a business customer
		// if the IsCommercialUse flag was set on the eSale at the time of rating.
		customer = createCustomerRecord(payload, &eSale)
		cobuyer = createCobuyerRecord(payload)

		sale, customer, err = insertNewSaleForESale(ctx, tx, &eSale, sale, customer, cobuyer, &user)
		if err != nil {
			_ = tx.Rollback()
			// TODO: additional info of numeric fields to track numeric overflow errors to be removed once identified and fixed
			str := fmt.Sprintf("Values of APR: %v MonthlyPayment: %v VehiclePrice: %v MSRP: %v",
				sale.FinanceAPR, sale.FinanceMonthlyPayment, sale.VehiclePrice, sale.MSRP)
			apiErr = &APIError{
				Err:        errors.Wrap(err, "error in inserting sale, additionalData"+str),
				StatusCode: http.StatusInternalServerError,
			}
			return apiErr, map[string]interface{}{}
		}
	} else { // Sale record exists for given e-sale
		lenExists := lenderExists(*payload)
		// Validate if change in lender details
		if lenExists && (eSale.PaymentType == dms.PaymentTypeLease || eSale.PaymentType == dms.PaymentTypeLoan) {
			lenderID, _, err = getLenderID(ctx, rq.Store.CompanyID, payload)
			if err != nil && err != sql.ErrNoRows {
				_ = tx.Rollback()
				apiErr = &APIError{
					Err:        errors.Wrap(err, "error getting lender ID"),
					StatusCode: http.StatusInternalServerError,
				}
				return apiErr, map[string]interface{}{}
			}
		}

		var tempSale db.Sale
		err = db.Get().Unsafe().GetContext(ctx, &tempSale, `select * from sales where id = (select sale_id from e_sales where id = $1)`, payload.ESaleID)
		if err != nil {
			_ = tx.Rollback()
			apiErr = &APIError{
				Err:        errors.Wrap(err, "error in getting sale data"),
				StatusCode: http.StatusInternalServerError,
			}
			return apiErr, map[string]interface{}{}
		}
		sale = &tempSale

		// Get whether Lender or Payment Type has changed.
		// Lender changing comes during generating of contracts while payment change
		// happens during rating so the eSale payment type would be differe from the existing
		// sale. We do not want to flag that the lender is changing if the lender on the payload is SPP.
		lenderChanged := lenExists && !isLenderSPP(payload) && sale.LenderID.Valid && int(sale.LenderID.Int64) != lenderID

		// We're checking if the Payment Type on the Sale is different from the eSale to determine if we meed
		// to add payment type change messaging.
		paymentTypeChanged = sale.PaymentType != eSale.PaymentType

		if lenderChanged || paymentTypeChanged {
			messaging := ""
			if lenderChanged && !paymentTypeChanged {
				messaging = "lender"
			} else if paymentTypeChanged && !lenderChanged {
				messaging = "payment type"
			} else {
				messaging = "lender and payment type"
			}

			// Verify that there's no active contracts on the sale. All contracts must be voided in order
			// for the lender and/or payment type to be updated on the sale.
			exists, err := unvoidedContractsForSaleExists(ctx, int(eSale.SaleID.Int64))
			if err != nil {
				_ = tx.Rollback()
				apiErr = &APIError{
					Err:        errors.Wrapf(err, "error in validating %s detail in e-contracting", messaging),
					StatusCode: http.StatusInternalServerError,
				}
				return apiErr, map[string]interface{}{}
			}
			if exists {
				msg := fmt.Sprintf("Could not change %s on sale due to unvoided contracts. Make sure all previous contracts have been voided.", messaging)
				_ = tx.Rollback()
				apiErr = &APIError{
					Err:            errors.Errorf("change in %s on sale where unvoided contract exists for existing lender", messaging),
					StatusCode:     http.StatusBadRequest,
					DisplayMessage: msg,
				}
				return apiErr, map[string]interface{}{}
			}

			var updatedByUserID int
			if employeeID.Valid {
				updatedByUserID = int(employeeID.Int64)
			} else {
				updatedByUserID = user.ID
			}

			// Setup variable for the updated lenderID. This
			// is because if the Lender is being changed, then
			// it should remain the same value that was on the
			// sale originally and this value could have been
			// null previously and that would need to be
			// reflected in the argument value being passed into
			// the update query.
			updatedLenderID := sale.LenderID
			if lenderChanged {
				updatedLenderID.Int64 = int64(lenderID)
			}

			_, err = tx.ExecContext(ctx, `update sales
				set updated_at = now() at time zone 'utc',
					lender_id = $1,
					payment_type = $2,
					updated_by_user_id = $3
				where id = $4`,
				updatedLenderID, eSale.PaymentType, updatedByUserID, int(eSale.SaleID.Int64))
			if err != nil {
				_ = tx.Rollback()
				apiErr = &APIError{
					Err:        errors.Wrapf(err, "error in updating sales with new %s", messaging),
					StatusCode: http.StatusInternalServerError,
				}
				return apiErr, map[string]interface{}{}
			}

			// We need to update the LenderID on the loaded Sale so when it's referenced by later code
			// it'll use the new Lender not the old Lender on the Sale before we updated it in the db.
			sale.LenderID = updatedLenderID
		}

		var tempCust db.Customer
		err = db.Get().Unsafe().GetContext(ctx, &tempCust, `select * from customers where id = $1`, sale.CustomerID)
		if err != nil {
			_ = tx.Rollback()
			apiErr = &APIError{
				Err:        errors.Wrap(err, "error in getting customer data"),
				StatusCode: http.StatusInternalServerError,
			}
			return apiErr, map[string]interface{}{}
		}
		customer = &tempCust

		query := `select count(*) 
			from contracts join product_types on contracts.product_type_position = product_types.position 
			where sale_id = $1 and product_types.id=$2 and status in ($3, $4, $5, $6)`
		var count int
		err = db.Get().GetContext(ctx, &count, query, sale.ID, payload.Product.ProductTypeID,
			db.ContractStatusGenerated, db.ContractStatusRemitted, db.ContractStatusActive, db.ContractStatusPending)
		if err != nil {
			_ = tx.Rollback()
			apiErr = &APIError{
				Err:        errors.Wrap(err, "error getting contracts data"),
				StatusCode: http.StatusInternalServerError,
			}
			return apiErr, map[string]interface{}{}
		}
		if count > 0 {
			_ = tx.Rollback()
			apiErr = &APIError{
				Err:            errors.New("contract already generated for this plan's product type"),
				StatusCode:     http.StatusBadRequest,
				DisplayMessage: "Contract already generated for this plan's product type",
			}
			return apiErr, map[string]interface{}{}
		}
	}

	// Check to see if the Lender is SPP and the eSale is not SPP, then we need to still be able to generate the contract
	// with an SPP payment type instead of the Sale's original payment type.
	// Need to set both the Payment Type and LenderID on the Sale that was loaded so when it's used in the contractCreate
	// function it'll generate the contract with SPP as the payment type and 'Service Payment Plan' as the Lender name.
	if isLenderSPP(payload) && eSale.PaymentType != dms.PaymentTypeSPP {
		sale.PaymentType = dms.PaymentTypeSPP
		lenderID, _, err = getLenderID(ctx, rq.Store.CompanyID, payload)
		if err != nil && err != sql.ErrNoRows {
			_ = tx.Rollback()
			apiErr = &APIError{
				Err:        errors.Wrap(err, "error getting lender ID"),
				StatusCode: http.StatusInternalServerError,
			}
			return apiErr, map[string]interface{}{}
		}
		sale.LenderID.Int64 = int64(lenderID)
		sale.LenderID.Valid = true
	}

	// this will be used only in contracts table not in sales table
	if productTypeCode == db.ProductTypeCodeGuaranteedAssetProtection && dp.SalesChannel != db.SalesChannelDealer {
		if rq.Store.ESaleGapUserID.Valid {
			sale.SalespersonID = rq.Store.ESaleGapUserID
		}
	} else {
		sale.SalespersonID = employeeID
	}

	// generate contract
	contractType := db.ContractTypeEContract
	if employeeID.Valid {
		user.ID = int(employeeID.Int64)
	}
	contract, err := contractCreate(ctx, tx, contractType, nil, payload, &dp, sale, &rq.Store, customer, &rq.VINRecord, quoteProducts, &user)
	if err != nil {
		_ = tx.Rollback()
		ReportErrorContext(ctx, err)

		newErrMsg := apiErrMsg

		// Check if error is contract form related and update the error message being sent
		if strings.Contains(err.Error(), db.ContractFormMissingFormErrorMessage) {
			newErrMsg = "Contract form missing"
		} else if strings.Contains(err.Error(), db.ContractFormCommercialUseNotAvailableErrorMessage) {
			newErrMsg = "Not available for commercial use."

			// TODO: Need to remove this once Commercial Use check at the time of rating is implemented.
			if dp.Name == "Darwin" {
				newErrMsg += " Please remove this product from Darwin for commercial use."
			}
		}
		apiErr = &APIError{
			Err:            errors.WithMessage(err, "error in creating contract"),
			StatusCode:     http.StatusInternalServerError,
			DisplayMessage: newErrMsg,
		}
		return apiErr, map[string]interface{}{}
	}

	// if pending lender was added, it should store the contract_id for which it was created
	if payload.Lender.PendingLenderID != 0 {
		_, err = tx.ExecContext(ctx, `update pending_lenders set contract_id=$1 where id = $2`, contract.ID, payload.Lender.PendingLenderID)
		if err != nil {
			_ = tx.Rollback()
			apiErr = &APIError{
				Err:        errors.Wrap(err, "error in updating pending lenders"),
				StatusCode: http.StatusInternalServerError,
			}
			return apiErr, map[string]interface{}{}
		}
	}

	err = tx.Commit()
	if err != nil {
		_ = tx.Rollback()
		apiErr = &APIError{
			Err:        errors.Wrap(err, "error committing contract save"),
			StatusCode: http.StatusInternalServerError,
		}
		return apiErr, map[string]interface{}{}
	}

	if eSale.PaymentType == dms.PaymentTypeLease || eSale.PaymentType == dms.PaymentTypeLoan {
		if lenderMatchStatus != LenderFound {
			err = notifyMissingLender(ctx, payload, contract.Code, lenderID, lenderMatchStatus)
			if err != nil {
				ReportErrorContext(ctx, err)
			}
		}
	}

	pdfURL, err := s3util.PresignedURL(contract.ContractFormS3Region.String, contract.ContractFormS3Bucket.String, contract.ContractFormFileName.String, "", "application/pdf")
	if err != nil {
		err = errors.Wrap(err, "error getting presigned download URL for contract")
		apiErr = &APIError{
			Err:        errors.Wrap(err, "error getting presigned download URL for contract"),
			StatusCode: http.StatusInternalServerError,
		}
		return apiErr, map[string]interface{}{}
	}

	signatures := []struct {
		Role         string  `db:"role" json:"role"`
		Page         int     `db:"page" json:"page"`
		X            float64 `db:"x" json:"-"`
		Y            float64 `db:"y" json:"-"`
		Width        float64 `db:"width" json:"-"`
		Height       float64 `db:"height" json:"-"`
		Type         string  `db:"-" json:"type"`
		PointsX      int     `db:"-" json:"x"`
		PointsY      int     `db:"-" json:"y"`
		PointsWidth  int     `db:"-" json:"width"`
		PointsHeight int     `db:"-" json:"height"`
	}{}
	err = db.Get().Unsafe().SelectContext(ctx, &signatures, `select * from contract_form_signatures where contract_form_id = $1`, contract.ContractFormID)
	if err != nil {
		apiErr = &APIError{
			Err:        errors.Wrap(err, "error getting contract for signatures"),
			StatusCode: http.StatusInternalServerError,
		}
		return apiErr, map[string]interface{}{}
	}
	// the x,y in contract_forms are w.r.t. left,top origin, PEN requires left,bottom origin
	// also the unit inch needs to converted to pixels/points, 1 inch = 72 points
	const ppi = 72
	const maxY = 11
	for i := range signatures {
		if signatures[i].Y > maxY {
			msg := fmt.Sprintf("can not transform y, value is greater than expected maxY (%d) for contract form: %d", maxY, contract.ContractFormID.Int64)
			ReportErrorContext(ctx, errors.New(msg))
		}
		signatures[i].PointsX = int(math.Round(signatures[i].X * ppi))
		// signatures[i].Y = Top Left corner of the signature
		signatures[i].PointsY = int(math.Round((maxY - signatures[i].Y - signatures[i].Height) * ppi))
		signatures[i].PointsWidth = int(math.Round(signatures[i].Width * ppi))
		signatures[i].PointsHeight = int(math.Round(signatures[i].Height * ppi))
		// Tekion doesn't have the ability to do Initials when singing, so we need to not mark a small
		// signature area as initials if the dealer platform is Tekion.
		if dp.Name != db.DealerSystemTekionPEN && signatures[i].PointsWidth < ppi {
			signatures[i].Type = "initials"
		} else {
			signatures[i].Type = "signature"
		}
	}

	responseData := map[string]interface{}{
		"contract_number": contract.Code,
		"contract_url":    pdfURL,
		"signatures":      signatures,
	}

	return nil, responseData
}

func lenderExists(payload EContractPayload) bool {
	return payload.Lender.Name != "" || payload.Lender.FinanceSource != "" ||
		payload.Lender.Address != "" || payload.Lender.StateCode != "" || payload.Lender.PostalCode != "" ||
		payload.Lender.City != ""
}

func validateVTANumber(ctx context.Context, payload EContractPayload) *APIError {
	var productTypeCode string
	query := "select code from product_types where id = $1"
	err := db.Get().GetContext(ctx, &productTypeCode, query, payload.Product.ProductTypeID)
	if err != nil {
		if err == sql.ErrNoRows {
			err = errors.Wrapf(err, "invalid product type id: %d", payload.Product.ProductTypeID)
			return &APIError{
				Err:            err,
				ErrCode:        db.ErrCodeInvalidProductType,
				StatusCode:     http.StatusBadRequest,
				DisplayMessage: "A valid product type ID is required",
			}
		}
		err = errors.Wrap(err, "failed to query for product type code")
		return &APIError{
			Err:            err,
			ErrCode:        db.ErrCodeUnexpectedErr,
			StatusCode:     http.StatusInternalServerError,
			DisplayMessage: "Error generating e-contract",
		}

	}

	if productTypeCode == db.ProductTypeCodeVehicleTheftAssistance &&
		payload.Product.VTANumber == "" {
		return &APIError{
			Err:            errors.New("vta number is required for VTA products"),
			ErrCode:        db.ErrCodeMissingVTANumber,
			StatusCode:     http.StatusBadRequest,
			DisplayMessage: "VTA number is required for VTA products",
		}
	}
	if productTypeCode != db.ProductTypeCodeVehicleTheftAssistance &&
		payload.Product.VTANumber != "" {
		return &APIError{
			Err:            errors.Errorf("vta number is not supported for %s products", productTypeCode),
			ErrCode:        db.ErrCodeVTANumberNotSupported,
			StatusCode:     http.StatusBadRequest,
			DisplayMessage: "VTA number is not supported for non-VTA products",
		}
	}

	return nil
}

func getDealData(ctx context.Context, dmsNumber, saleType string, storeID int) (*dms.Deal, error) {
	if dmsNumber != "" {
		if saleType == dms.SaleTypeFinanceDeal {
			var store DMSStore
			query := `select s.*, lo.name lender_option_name
			from stores s join companies c on s.company_id = c.id
				left join lender_options lo on lo.id = c.lender_option_id
				where s.id = $1`
			err := db.Get().Unsafe().GetContext(ctx, &store, query, storeID)
			if err != nil {
				util.LogWarning(ctx, errors.Wrapf(err, "error finding store for the %d store.", storeID))
			} else {
				if !store.HasDealIntegration {
					util.LogWarning(ctx, errors.New(fmt.Sprintf("store with DMS provider and has Deal integration not found, for store %d", storeID)))
				} else {
					return dmsfactory.DealOnly(ctx, &store.Store, dmsNumber)
				}
			}
		}
	}
	return nil, nil
}

func getEmployeeID(
	ctx context.Context,
	salePersonNumber string,
	storeID int,
	dealerSystem db.DealerPlatform,
) (null.Int, *APIError) {

	//We dont require employee number for customer sales channels
	if dealerSystem.SalesChannel == db.SalesChannelCustomer {
		return null.Int{}, nil
	}
	// If we have empty salePersonNumber then just return from here
	if salePersonNumber == "" {
		return null.Int{}, nil
	}

	salePersonNumberIsEmail := false
	var userID int
	baseQuery := `select cu.id
		from current_users cu
		join stores_user_versions suv on suv.user_version_id = cu.user_version_id
		join stores s on s.id = suv.store_id
			and s.id = $1
		where cu.active = true
		`
	params := []interface{}{storeID}
	query := baseQuery
	if strings.HasPrefix(salePersonNumber, "email:") {
		query += `and cu.email = $2`
		employeeEmail := salePersonNumber[len("email:"):]
		params = append(params, employeeEmail)
		salePersonNumberIsEmail = true
	} else {
		query += `and cu.dms_employee_number = $2`
		params = append(params, salePersonNumber)
	}
	err := db.Get().GetContext(ctx, &userID, query, params...)
	if err != nil {
		if err == sql.ErrNoRows {
			if !salePersonNumberIsEmail {
				// If no employ is found checking the dms_employee_number, then check
				// for one using the hr_employee_number column
				query = baseQuery
				query += `and cu.hr_employee_number = $2`
				err = db.Get().GetContext(ctx, &userID, query, params...)
				if err != nil && err != sql.ErrNoRows {
					err = errors.Wrapf(err, "error occurred while finding employee (%s)", salePersonNumber)
					return null.Int{}, &APIError{
						Err:            err,
						ErrCode:        db.ErrCodeUnexpectedErr,
						StatusCode:     http.StatusInternalServerError,
						DisplayMessage: "Internal error occurred",
					}
				}
			}
			util.LogWarning(ctx, errors.Wrapf(err, "employee (%s) not found", salePersonNumber))
		} else {
			err = errors.Wrapf(err, "error occurred while finding employee (%s)", salePersonNumber)
			return null.Int{}, &APIError{
				Err:            err,
				ErrCode:        db.ErrCodeUnexpectedErr,
				StatusCode:     http.StatusInternalServerError,
				DisplayMessage: "Internal error occurred",
			}
		}
		return null.Int{}, nil
	}

	return null.IntFrom(int64(userID)), nil
}

func validateGAPUser(
	ctx context.Context,
	rq db.RateQuery,
	qpv db.QuotePlanWithAdjustments,
	productTypeCode string,
	productVariantID int,
) *APIError {
	const errMsg string = "Error generating e-contract"
	var gapUserID int
	if rq.Store.ESaleGapUserID.Valid {
		// this will be used only in contracts table not in sales table
		gapUserID = int(rq.Store.ESaleGapUserID.Int64)
	}

	if gapUserID == 0 {
		if productTypeCode == db.ProductTypeCodeGuaranteedAssetProtection {
			spRequired, err := gapLicenseCheckRequired(ctx, rq, qpv, productVariantID)
			if err != nil {
				apiErr := new(APIError)
				apiErr.Err = errors.WithMessage(err, "error in checking salesperson requirement for GAP")
				apiErr.DisplayMessage = errMsg
				apiErr.ErrCode = db.ErrCodeUnexpectedErr
				apiErr.StatusCode = http.StatusInternalServerError
				return apiErr
			}
			if spRequired {
				apiErr := new(APIError)
				apiErr.Err = errors.New("cannot generate GAP contract without a salesperson")
				apiErr.DisplayMessage = "Cannot generate GAP contract without a salesperson"
				apiErr.ErrCode = db.ErrCodeMissingSalespersonGAP
				apiErr.StatusCode = http.StatusBadRequest
				return apiErr
			}
		}
	}
	return nil
}

func getERatingData(
	ctx context.Context,
	quoteProducts []db.QuoteProductTypeWithData,
	payload *EContractPayload,
	financeAmount decimal.Decimal,
	storeID int,
	contractDate time.Time,
	roundup bool,
	dealerPlatform *db.DealerPlatform,
) (
	db.QuoteProductVariantWithData,
	db.QuotePlanWithAdjustments,
	[]db.QuoteOption,
	[]db.QuoteSurcharge,
	decimal.Decimal,
	bool,
	error,
) {
	var i, j int
	var quote db.QuoteProductTypeWithData
	var err error

	for i, quote = range quoteProducts {
		if quote.ProductTypeID == payload.Product.ProductTypeID {
			break
		}
	}

	var qpv db.QuoteProductVariantWithData
	for j, qpv = range quoteProducts[i].QuoteProductVariants {
		if qpv.ProductVariantID == payload.Product.ProductVariantID {
			break
		}
	}

	var qp db.QuotePlanWithAdjustments
	for _, qp = range quoteProducts[i].QuoteProductVariants[j].QuotePlans {
		if qp.PlanID == payload.Product.PlanID {
			break
		}
	}

	tcaCost := qp.GetTcaTotalCost(qpv)
	dealerCost := qp.GetDealerTotalCost(qpv)
	var price decimal.Decimal
	isFixedRetail := false
	var cap *db.QuoteCap
	priceToCostRatio := db.DefaultPriceToCostRatio
	if len(qpv.QuoteCaps) > 0 {
		price, cap, err = getBestPriceCap(
			qpv.QuoteCaps,
			tcaCost,
			dealerCost,
			payload.FinanceAmount,
			payload.Product.ProductVariantID,
			storeID,
			contractDate,
			roundup,
		)
		if err != nil {
			return db.QuoteProductVariantWithData{}, db.QuotePlanWithAdjustments{}, []db.QuoteOption{}, []db.QuoteSurcharge{}, decimal.Decimal{}, false, err
		}
		if cap != nil {
			isFixedRetail = cap.IsFixedRetailPrice
			priceToCostRatio = cap.PriceToCostRatio
		}
	} else {
		price = dealerCost
	}

	var quoteOptions []db.QuoteOption
	verifyOptions := make(map[int]int)
	verifyOptionGroup := make(map[string]int)
	for _, optionID := range payload.Product.OptionIDs {
		for _, qo := range qpv.QuoteOptions {
			if qo.OptionID == optionID {
				verifyOptions[optionID] = optionID
				if qo.OptionGroup.Valid {
					verifyOptionGroup[qo.OptionGroup.String] = optionID
				}
				quoteOptions = append(quoteOptions, qo)
				optionPrice := qo.Cost.Mul(priceToCostRatio.Decimal)
				// If should be rounding, we want to round the option price to match when we're rounding
				// the option price during eRating to ensure the final price is accurate.
				if roundup {
					optionPrice = optionPrice.Ceil()
				} else {
					optionPrice = optionPrice.RoundBank(2)
				}
				price = price.Add(optionPrice)
			}
		}
	}

	// Add the pre-selected options if not already added e.g. $100 Deductible, Seals and Gasket
	// for options with plan duration the preselected is set on a option with 0 duration
	// hence need to get the option with matching plan duration
	var preSelectedOptionCode, preSelectedOptionGroup string
	for _, qo := range qpv.QuoteOptions {
		var okOptionGroup, okOptionID bool
		if qo.Preselected || preSelectedOptionCode != "" {
			if qo.PlanDuration.Int64 == 0 { // do not add option with 0 duration, store the code, check next option
				preSelectedOptionCode = qo.Code.String
				preSelectedOptionGroup = qo.OptionGroup.String
				continue
			}
			_, okOptionID = verifyOptions[qo.OptionID]
			if !okOptionID {
				if qo.OptionGroup.Valid {
					_, okOptionGroup = verifyOptionGroup[qo.OptionGroup.String]
				}
				if !okOptionID && !okOptionGroup {
					if !qo.PlanDuration.Valid || (qo.PlanDuration.Valid && qo.PlanDuration.Int64 == int64(qp.Duration)) {
						quoteOptions = append(quoteOptions, qo)
						price = price.Add(qo.Cost)
						preSelectedOptionCode = "" // reset the preselected option code
						if qo.OptionGroup.Valid && (preSelectedOptionGroup == qo.OptionGroup.String) {
							preSelectedOptionGroup = ""
						}
					}
				}
			}
		}
	}

	// Add the pre-selected options if not already in above loop for the option with duration
	// this loop will not check for duration
	if len(quoteOptions) == 0 {
		for _, qo := range qpv.QuoteOptions {
			var okOptionGroup, okOptionID bool
			if qo.Preselected {
				_, okOptionID = verifyOptions[qo.OptionID]
				if !okOptionID {
					if qo.OptionGroup.Valid {
						_, okOptionGroup = verifyOptionGroup[qo.OptionGroup.String]
					}
					if !okOptionID && !okOptionGroup {
						quoteOptions = append(quoteOptions, qo)
						if qo.OptionGroup.Valid {
							verifyOptionGroup[qo.OptionGroup.String] = qo.OptionID
						}
						price = price.Add(qo.Cost)
					}
				}
			}
		}
	}

	var quoteSurcharges []db.QuoteSurcharge
	for _, qs := range qpv.QuoteSurcharges {
		if qs.FilteredByProductRuleID.Valid {
			continue
		}
		if !qs.PlanDuration.Valid || (qs.PlanDuration.Valid && qs.PlanDuration.Int64 == int64(qp.Duration)) {
			quoteSurcharges = append(quoteSurcharges, qs)
		}
	}

	return qpv, qp, quoteOptions, quoteSurcharges, price, isFixedRetail, nil
}

func isValidProductDetails(quoteProducts []db.QuoteProductTypeWithData, payload *EContractPayload) (bool, string) {
	var i, j int
	var quote db.QuoteProductTypeWithData

	var validProduct, validPV, validPlan bool
	for i, quote = range quoteProducts {
		if quote.ProductTypeID == payload.Product.ProductTypeID {
			validProduct = true
			break
		}
	}
	if !validProduct {
		return false, db.ErrCodeInvalidProductType
	}

	var qpv db.QuoteProductVariantWithData
	for j, qpv = range quoteProducts[i].QuoteProductVariants {
		if qpv.ProductVariantID == payload.Product.ProductVariantID {
			validPV = true
			break
		}
	}
	if !validPV {
		return false, db.ErrCodeInvalidProductVariant
	}

	var qp db.QuotePlanWithAdjustments
	for _, qp = range quoteProducts[i].QuoteProductVariants[j].QuotePlans {
		if qp.PlanID == payload.Product.PlanID {
			validPlan = true
			break
		}
	}
	if !validPlan {
		return false, db.ErrCodeInvalidPlan
	}

	var quoteOptions []db.QuoteOption
	optionsByGroup := make(map[string]int)
	optionsByNonGroup := make(map[string]int)
	verifyDupOptions := make(map[int]int)

	for _, optionID := range payload.Product.OptionIDs {
		for _, qo := range qpv.QuoteOptions {
			if qo.OptionID == optionID {
				_, ok := verifyDupOptions[optionID]
				if ok {
					return false, db.ErrCodeDuplicateOption
				}
				verifyDupOptions[optionID] = optionID
				if qo.PlanDuration.Valid {
					if qo.PlanDuration.Int64 != int64(qp.Duration) {
						return false, db.ErrCodeInvalidOption
					}
				}
				if qo.OptionGroup.Valid {
					_, ok := optionsByGroup[qo.OptionGroup.String]
					if ok {
						return false, db.ErrCodeMultipleFromOneOptionGroup
					}
					optionsByGroup[qo.OptionGroup.String] = qo.OptionID
				} else { // validate non-grouped options based on plan duration if option duration is set
					if qo.Code.Valid {
						_, ok := optionsByNonGroup[qo.Code.String]
						if ok {
							return false, db.ErrCodeMultipleFromOneNonOptionGroup
						}
						optionsByNonGroup[qo.Code.String] = qo.OptionID
					}
				}
				quoteOptions = append(quoteOptions, qo)
			}
		}
	}

	if len(quoteOptions) != len(payload.Product.OptionIDs) {
		return false, db.ErrCodeInvalidOption
	}
	return true, ""
}

func canDealerAccess(ctx context.Context, xtkUserCode, dealerID string) error {
	var dealerPlatformID null.Int
	query := `select dealer_platform_id
			from xtk_users
			where code = $1 and active=true`
	err := db.Get().GetContext(ctx, &dealerPlatformID, query, xtkUserCode)
	if err != nil {
		if err == sql.ErrNoRows {
			return createCustomError("Unauthorized user",
				http.StatusUnauthorized, nil, db.ErrCodeDealerPlatformNotEnabled)
		}
		return createCustomError("Error voiding contract",
			http.StatusInternalServerError,
			errors.Wrap(err, "error validating xtk_user"), "")
	}
	if !dealerPlatformID.Valid {
		return createCustomError("Dealer Platform ID missing",
			http.StatusBadRequest, nil, db.ErrCodeDealerPlatformNotFound)
	}

	var storeID int
	query = `select id from stores where e_rating_dealer_id = $1`
	err = db.Get().GetContext(ctx, &storeID, query, dealerID)
	if err != nil {
		err = errors.Wrapf(err, "error getting store id for dealer")
		return createCustomError("Dealer Platform ID missing",
			http.StatusInternalServerError, err, db.ErrCodeUnexpectedErr)
	}

	dealerIDEnabled, err := isDPAccessibleToStore(ctx, storeID, int(dealerPlatformID.Int64))
	if err != nil {
		return createCustomError("Error voiding contract",
			http.StatusInternalServerError,
			errors.Wrap(err, "error getting dealer platforms for store"), "")
	}
	if !dealerIDEnabled {
		return createCustomError("Dealer platform ID not enabled for this store",
			http.StatusBadRequest, nil, db.ErrCodeDealerPlatformNotEnabled)
	}

	return nil
}

func getVoidRequestData(req *http.Request) interface{} {
	return struct{ DealerID string }{
		DealerID: req.FormValue("dealer_id"),
	}
}

// VoidEContract voids e-contract for provided contract code
func VoidEContract(_ http.ResponseWriter, req *http.Request, xtkUserCode string) (int, map[string]interface{}) {
	ctx := req.Context()
	statusToReturn := http.StatusInternalServerError
	errorMessage := "Error voiding contract"
	errorCode := ""

	// log request body
	logPENRequest(ctx, getVoidRequestData(req), xtkUserCode, req.URL.Path, chi.URLParam(req, "code"))

	contractCode := chi.URLParam(req, "code")
	if contractCode == "" {
		return http.StatusBadRequest, ErrorMessage("Bad Request: contract_code is missing", db.GetErrorCodeMap(db.ErrCodeContractNotFound))
	}

	dealerID := req.FormValue("dealer_id")
	if dealerID == "" {
		return http.StatusBadRequest, ErrorMessage("Bad Request: dealer_id is missing", db.GetErrorCodeMap(db.ErrCodeDealerNotFound))
	}

	if err := canDealerAccess(ctx, xtkUserCode, dealerID); err != nil {
		if serr, ok := err.(*ContractCustomError); ok {
			err = serr.Err
			statusToReturn = serr.Status
			errorMessage = serr.Error()
			errorCode = serr.ErrorCode
		}
		if err != nil {
			ReportError(req, err)
		}
		return statusToReturn, ErrorMessage(errorMessage, db.GetErrorCodeMap(errorCode))
	}

	// Get E-Contracting details for dealer and contract
	user, storeID, contractID, err := getEContractingDetails(ctx, contractCode, dealerID)
	if err != nil {
		if serr, ok := err.(*ContractCustomError); ok {
			err = serr.Err
			statusToReturn = serr.Status
			errorMessage = serr.Error()
			errorCode = serr.ErrorCode
		}
		if err != nil {
			ReportError(req, err)
		}
		return statusToReturn, ErrorMessage(errorMessage, db.GetErrorCodeMap(errorCode))
	}

	_, err = voidContract(ctx, contractID, user, true, storeID)
	if err != nil {
		if serr, ok := err.(*ContractCustomError); ok {
			err = serr.Err
			statusToReturn = serr.Status
			errorMessage = serr.Error()
			errorCode = serr.ErrorCode
		}
		if err != nil {
			ReportError(req, err)
		}
		return statusToReturn, ErrorMessage(errorMessage, db.GetErrorCodeMap(errorCode))
	}

	resposneData := map[string]interface{}{
		"contract_number": contractCode,
	}

	// log response body
	logPENResponse(ctx, resposneData, xtkUserCode, req.URL.Path, contractCode)

	return http.StatusOK, resposneData
}

func getActivateRequestData(req *http.Request) interface{} {
	return struct {
		DealerID           string
		ContractSignedDate string
	}{
		DealerID:           req.FormValue("dealer_id"),
		ContractSignedDate: req.FormValue("contract_signed_date"),
	}
}

// ActivateEContract activates signed e-contracts
func ActivateEContract(_ http.ResponseWriter, req *http.Request, xtkUserCode string) (int, map[string]interface{}) {
	ctx := req.Context()
	statusToReturn := http.StatusInternalServerError
	errorMessage := "Error Remitting contract"
	errorCode := ""

	// log request body
	logPENRequest(ctx, getActivateRequestData(req), xtkUserCode, req.URL.Path, chi.URLParam(req, "code"))

	contractCode := chi.URLParam(req, "code")
	if contractCode == "" {
		return http.StatusBadRequest, ErrorMessage("Bad Request: contract_code is missing", db.GetErrorCodeMap(db.ErrCodeContractNotFound))
	}

	dealerID := req.FormValue("dealer_id")
	if dealerID == "" {
		return http.StatusBadRequest, ErrorMessage("Bad Request: dealer_id is missing", db.GetErrorCodeMap(db.ErrCodeDealerNotFound))
	}

	signedDateStr := req.FormValue("contract_signed_date")
	if signedDateStr == "" {
		return http.StatusBadRequest, ErrorMessage("Bad Request: contract_signed_date is missing", db.GetErrorCodeMap(db.ErrCodeInvalidContractSignedDate))
	}

	signedDate, err := time.Parse(apiDateFormat, signedDateStr)
	if err != nil {
		return http.StatusBadRequest, ErrorMessage("Bad Request: Invalid contract_signed_date format", db.GetErrorCodeMap(db.ErrCodeInvalidContractSignedDate))
	}

	if signedDate.After(time.Now()) {
		return http.StatusBadRequest, ErrorMessage("Bad Request: Invalid contract_signed_date. Future dates are not allowed", db.GetErrorCodeMap(db.ErrCodeInvalidContractSignedDate))
	}

	if err := canDealerAccess(ctx, xtkUserCode, dealerID); err != nil {
		if serr, ok := err.(*ContractCustomError); ok {
			err = serr.Err
			statusToReturn = serr.Status
			errorMessage = serr.Error()
			errorCode = serr.ErrorCode
		}
		if err != nil {
			ReportError(req, err)
		}
		return statusToReturn, ErrorMessage(errorMessage, db.GetErrorCodeMap(errorCode))
	}

	// Get E-Contracting details for dealer and contract
	userID, _, contractID, err := getEContractingDetails(ctx, contractCode, dealerID)
	if err != nil {
		if serr, ok := err.(*ContractCustomError); ok {
			err = serr.Err
			statusToReturn = serr.Status
			errorMessage = serr.Error()
			errorCode = serr.ErrorCode
		}
		if err != nil {
			ReportError(req, err)
		}
		return statusToReturn, ErrorMessage(errorMessage, db.GetErrorCodeMap(errorCode))
	}

	query := `select created_at, contract_signed_date, status from contracts where id = $1`
	origContract := struct {
		Status             string    `db:"status"`
		CreatedAt          time.Time `db:"created_at"`
		ContractSignedDate null.Time `db:"contract_signed_date"`
	}{}

	err = db.Get().GetContext(ctx, &origContract, query, contractID)
	if err != nil {
		err = errors.Wrap(err, "error getting contract details")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(errorMessage, nil)
	}
	if origContract.Status != db.ContractStatusGenerated {
		return http.StatusBadRequest, ErrorMessage("Bad Request: Invalid status for contract sign", nil)
	}
	// if signed date is already set, don't set again
	if !origContract.ContractSignedDate.Valid {
		createdAtMNT, err := types.InMountainTime(origContract.CreatedAt)
		if err != nil {
			return http.StatusInternalServerError, ErrorMessage("Error in converting generation date from UTC to MDT", nil)
		}

		y, m, d := createdAtMNT.Date()
		contractGenDate := time.Date(y, m, d, 0, 0, 0, 0, time.UTC)
		if signedDate.Before(contractGenDate) {
			return http.StatusBadRequest,
				ErrorMessage("Bad Request: Contract sign date cannot be before generation date",
					db.GetErrorCodeMap(db.ErrCodeInvalidContractSignedDate))
		}
		err = signContract(ctx, contractID, userID, signedDate)
		if err != nil {
			if serr, ok := err.(*ContractCustomError); ok {
				err = serr.Err
				statusToReturn = serr.Status
				errorMessage = serr.Error()
				errorCode = serr.ErrorCode
			}
			if err != nil {
				ReportError(req, err)
			}
			return statusToReturn, ErrorMessage(errorMessage, db.GetErrorCodeMap(errorCode))
		}
	} else {
		signedDate = origContract.ContractSignedDate.Time
	}

	resposneData := map[string]interface{}{
		"contract_number": contractCode,
		"signed_date":     signedDate.Format(db.ShortDateFormat),
	}

	// log response body
	logPENResponse(ctx, resposneData, xtkUserCode, req.URL.Path, contractCode)

	return http.StatusOK, resposneData
}

// AutoActivateEContracts auto activates e contracts after 30 days of a contract is signed
func AutoActivateEContracts(ctx context.Context) error {

	// Get system user to process e contract void
	var user db.CurrentUser
	err := db.Get().Unsafe().GetContext(ctx, &user, `select * from current_users where first_name='SYSTEM'`)
	if err != nil {
		return errors.Wrap(err, "could not query SYSTEM user in autoActivateEContract")
	}

	// get all generated
	q := `select c.id, c.code, c.store_id
	from contracts c join sales s on c.sale_id = s.id
	where (current_date - e_sale_complete_date) >= 30
		and c.is_e_contract = true
		and c.status = $1
		and (s.lender_id is not null or s.payment_type=$2)`

	contracts := []struct {
		ID      int    `db:"id"`
		Code    string `db:"code"`
		StoreID int    `db:"store_id"`
	}{}
	err = db.Get().SelectContext(ctx, &contracts, q, db.ContractStatusGenerated, dms.PaymentTypeCash)
	if err != nil {
		return errors.Wrap(err, "could not query contracts")
	}

	for _, c := range contracts {
		dealNumber := "" // for auto activate, dealNumber cannot be updated
		_, err = RemitContract(ctx, c.ID, dealNumber, user, true, c.StoreID, time.Now(), true)
		if err != nil {
			return errors.Wrap(err, "error in remitting")
		}
		LogMessagef(ctx, "Auto activation of e-contract: %s is done", c.Code)
	}
	return nil
}
