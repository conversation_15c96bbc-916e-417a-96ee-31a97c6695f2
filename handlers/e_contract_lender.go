package handlers

import (
	"bytes"
	"context"
	"database/sql"
	"html/template"
	"net/http"
	"strconv"
	"strings"
	"whiz/conf"
	"whiz/db"
	"whiz/email"

	"github.com/adrg/strutil"
	"github.com/adrg/strutil/metrics"
	newrelic "github.com/newrelic/go-agent"
	"github.com/pkg/errors"
)

const (
	// LenderFound constant for lender details found in the db
	LenderFound = 0
	// LenderMissingFinanceSource constant for lender details missing finance source
	LenderMissingFinanceSource = 1
	// LenderUnexpectedFinanceSource constant for lender details having unexpected finance source
	LenderUnexpectedFinanceSource = 2
	// LenderAddressPartialMatch constant for lender details address partial match
	LenderAddressPartialMatch = 3
	// LenderAddressNotMatch constant for lender details address not match
	LenderAddressNotMatch = 4
	// LenderNameRelaxedMatch constant for lender name relaxed match
	LenderNameRelaxedMatch = 5
	// LenderNotFound constant for lender not found in the db
	LenderNotFound = 6
)

type lenderIDName struct {
	ID   int    `db:"id"`
	Name string `db:"name"`
}

// isLenderSPP returns whether the lender on the eContract Payload
// is SPP or not.
func isLenderSPP(payload *EContractPayload) bool {
	return strings.EqualFold(payload.Lender.Name, db.LenderSPP) ||
		strings.EqualFold(payload.Lender.Name, db.LenderSPPFull) ||
		strings.EqualFold(payload.Lender.Name, db.LenderSPPFullWithInc)
}

// lender match order, stop at where it matches first
// 1. finance_source
// 2. name, address, city, state_code and postal_code
// 3. name, state_code and postal_code
// 4. name and state_code
// 5. name
// Else return NOT FOUND error
func getLenderID(ctx context.Context, companyID int, payload *EContractPayload) (int, int, error) {
	var lenderID int
	var query string
	var lenderOptionName string
	var err error
	var isUnexpectedFinSource bool
	var lenderMatchStatus int

	// handle SPP as lender
	if isLenderSPP(payload) {
		query = `
			select 
				id 
			from lenders 
			where 
				name=$1 
				and deleted_at is null`
		err = db.Get().GetContext(ctx, &lenderID, query, db.LenderSPPFull)
		if err != nil && err != sql.ErrNoRows {
			err = errors.Wrap(err, "database error in getting spp lender for e-sale")
			return lenderID, lenderMatchStatus, err
		}
		lenderMatchStatus = LenderFound
		return lenderID, lenderMatchStatus, err
	}

	query = `
		select lo.name
		from companies c
		join lender_options lo on lo.id = c.lender_option_id
		where c.id = $1`
	err = db.Get().GetContext(ctx, &lenderOptionName, query, companyID)
	if err != nil {
		err = errors.Wrap(err, "database error in getting lender options")
		return lenderID, lenderMatchStatus, err
	}

	// UCS and Tekion lenders won't have a finance source to query, so skip attempting to look up by
	// finance source of UCS lenders or if the finance source is blank from the payload.
	if payload.Lender.FinanceSource != "" && lenderOptionName != db.LenderOptionUCS && lenderOptionName != db.LenderOptionTekion {
		if lenderOptionName == db.LenderOptionLHM {
			query = `
				select 
					id 
				from lenders 
				where 
					upper(lhm_cdk_finance_code) = $1 
					and deleted_at is null`
		} else if lenderOptionName == db.LenderOptionAsbury {
			query = `
				select 
					id 
				from lenders 
				where 
					upper(asbury_cdk_finance_code) = $1 
					and deleted_at is null`
		}

		payload.Lender.FinanceSource = strings.ToUpper(payload.Lender.FinanceSource)
		err = db.Get().GetContext(ctx, &lenderID, query, payload.Lender.FinanceSource)
		if err != nil && err != sql.ErrNoRows {
			err = errors.Wrap(err, "database error in getting lender for e-sale")
			return lenderID, lenderMatchStatus, err
		}
		if err == sql.ErrNoRows {
			isUnexpectedFinSource = true
		} else {
			lenderMatchStatus = LenderFound
			return lenderID, lenderMatchStatus, err
		}
	}

	payload.Lender.Name = strings.ToUpper(payload.Lender.Name)
	payload.Lender.Address = strings.ToUpper(payload.Lender.Address)
	payload.Lender.City = strings.ToUpper(payload.Lender.City)
	payload.Lender.StateCode = strings.ToUpper(payload.Lender.StateCode)

	query = `
		select 
			id
		from lenders 
		where 
			upper(name) = $1 
			and upper(address) = $2 
			and upper(city) = $3 
			and upper(state_code) = $4 
			and upper(postal_code) = $5 
			and deleted_at is null
			and is_active = true`
	err = db.Get().GetContext(ctx, &lenderID, query, payload.Lender.Name, payload.Lender.Address,
		payload.Lender.City, payload.Lender.StateCode, payload.Lender.PostalCode)
	if err != nil && err != sql.ErrNoRows {
		err = errors.Wrap(err, "database error in getting lender for e-sale")
		return lenderID, lenderMatchStatus, err
	}
	if lenderID > 0 {
		if isUnexpectedFinSource {
			lenderMatchStatus = LenderUnexpectedFinanceSource
		}
		return lenderID, lenderMatchStatus, nil
	}

	query = `
		select 
			id
		from lenders 
		where 
			upper(name) = $1 
			and upper(state_code) = $2 
			and upper(postal_code) = $3 
			and deleted_at is null
			and is_active = true
		order by 
			created_at desc 
		limit 1`
	err = db.Get().GetContext(ctx, &lenderID, query, payload.Lender.Name, payload.Lender.StateCode, payload.Lender.PostalCode)
	if err != nil && err != sql.ErrNoRows {
		err = errors.Wrap(err, "database error in getting lender for e-sale")
		return lenderID, lenderMatchStatus, err
	}
	if lenderID > 0 {
		lenderMatchStatus = LenderAddressPartialMatch
		return lenderID, lenderMatchStatus, nil
	}

	query = `
		select 
			id
		from lenders 
		where 
			upper(name) = $1 
			and upper(state_code) = $2 
			and deleted_at is null
			and is_active = true
		order by 
			created_at desc 
		limit 1`
	err = db.Get().GetContext(ctx, &lenderID, query, payload.Lender.Name, payload.Lender.StateCode)
	if err != nil && err != sql.ErrNoRows {
		err = errors.Wrap(err, "database error in getting lender for e-sale")
		return lenderID, lenderMatchStatus, err
	}
	if lenderID > 0 {
		lenderMatchStatus = LenderAddressPartialMatch
		return lenderID, lenderMatchStatus, nil
	}

	query = `
		select 
			id 
		from lenders 
		where 
			upper(name) = $1 
			and deleted_at is null
			and is_active = true
		order by 
			created_at desc 
		limit 1`
	err = db.Get().GetContext(ctx, &lenderID, query, payload.Lender.Name)
	if err != nil && err != sql.ErrNoRows {
		err = errors.Wrap(err, "database error in getting lender for e-sale")
		return lenderID, lenderMatchStatus, err
	}
	if lenderID > 0 {
		lenderMatchStatus = LenderAddressNotMatch
		return lenderID, lenderMatchStatus, nil
	}

	lenderIDNames := []lenderIDName{}
	query = `
		select 
			id,
			name 
		from lenders 
		where 
			deleted_at is null
			and is_active = true
		order by name asc`
	err = db.Get().SelectContext(ctx, &lenderIDNames, query)
	if err != nil {
		err = errors.Wrap(err, "database error in getting lender for e-sale")
		return lenderID, lenderMatchStatus, err
	}
	lenderID = relaxedNameMatch(payload.Lender.Name, lenderIDNames)
	if lenderID > 0 {
		lenderMatchStatus = LenderNameRelaxedMatch
		return lenderID, lenderMatchStatus, err
	}

	lenderMatchStatus = LenderNotFound
	return lenderID, lenderMatchStatus, err
}

const (
	lenderEmailBody = `
<p><a href="{{ .Server }}{{ .LenderLink}}">Lender </a>{{ .EmailFirstLine}}</p>
<p>Contract Number {{ .ContractNumber }}</p>
<p><strong> Provided Lender Details </strong></p>
<p> Name: {{ .LenderName }}</p>
<p> Address: {{ .LenderAddress }}</p>
<p> City: {{ .LenderCity }}</p>
<p> State: {{ .LenderStateCode }} </p>
<p> Postal Code: {{ .LenderPostalCode }} </p>
<p> Finance Source: {{ .FinanceSource }} </p>
<p><strong> Matched Lender Details </strong> </p>
<p> Name: {{ .MatchedLenderName }}</p>
<p> Address: {{ .MatchedLenderAddress }}</p>
<p> City: {{ .MatchedLenderCity }}</p>
<p> State: {{ .MatchedLenderStateCode }} </p>
<p> Postal Code: {{ .MatchedLenderPostalCode }} </p>
<p> Finance Source: {{ .MatchedLenderFinanceSource }} </p>
`
)

var (
	lenderEmailTemplate *template.Template
)

func init() {
	lenderEmailTemplate = template.Must(template.New("lender-email").Parse(lenderEmailBody))
}

func notifyMissingLender(ctx context.Context, payload *EContractPayload, contractNumber string, lenderID, notifyType int) error {

	var emailFirstLine = map[int]string{
		LenderMissingFinanceSource:    " information was missing finance source",
		LenderUnexpectedFinanceSource: " information had unexpected finance source",
		LenderAddressPartialMatch:     " address partially matched. (name matched, but only part of address, city, state, zip matched)",
		LenderAddressNotMatch:         " address did not match. (only name matched)",
		LenderNotFound:                " not found by name",
	}
	var subject, lenderLink string

	switch notifyType {
	case LenderMissingFinanceSource, LenderUnexpectedFinanceSource:
		subject = "Incomplete Lender Match"
		lenderLink = "/admin/lenders/" + strconv.Itoa(lenderID) + "/edit"
	case LenderAddressPartialMatch, LenderAddressNotMatch, LenderNameRelaxedMatch:
		subject = "Incomplete Lender Match"
		lenderLink = "/admin/lenders"
	case LenderNotFound:
		subject = "Missing Lender!"
		lenderLink = "/admin/lenders"
	default:
		return errors.New("invalid Lender missing notification type")
	}

	var lender db.Lender
	var err error
	if notifyType != LenderNotFound {
		query := `select * from lenders where id = $1`
		err = db.Get().GetContext(ctx, &lender, query, lenderID)
		if err != nil {
			return errors.Wrap(err, "database error in getting lender")
		}
	}
	var financeSource string
	if lender.LHMCDKFinanceCode.Valid {
		financeSource = lender.LHMCDKFinanceCode.String
	} else {
		financeSource = lender.AsburyCDKFinanceCode.String
	}

	buf := bytes.NewBuffer([]byte{})
	tmplData := struct {
		ContractNumber             string
		LenderName                 string
		LenderAddress              string
		LenderCity                 string
		LenderStateCode            string
		LenderPostalCode           string
		FinanceSource              string
		MatchedLenderName          string
		MatchedLenderAddress       string
		MatchedLenderCity          string
		MatchedLenderStateCode     string
		MatchedLenderPostalCode    string
		MatchedLenderFinanceSource string
		EmailFirstLine             string
		LenderLink                 string
		Server                     string
	}{
		ContractNumber:             contractNumber,
		LenderName:                 payload.Lender.Name,
		LenderAddress:              payload.Lender.Address,
		LenderCity:                 payload.Lender.City,
		LenderStateCode:            payload.Lender.StateCode,
		LenderPostalCode:           payload.Lender.PostalCode,
		FinanceSource:              payload.Lender.FinanceSource,
		MatchedLenderName:          lender.Name,
		MatchedLenderAddress:       lender.Address,
		MatchedLenderCity:          lender.City,
		MatchedLenderStateCode:     lender.StateCode,
		MatchedLenderPostalCode:    lender.PostalCode,
		MatchedLenderFinanceSource: financeSource,
		EmailFirstLine:             emailFirstLine[notifyType],
		Server:                     conf.Get().Server,
		LenderLink:                 lenderLink,
	}

	err = lenderEmailTemplate.Execute(buf, tmplData)
	if err != nil {
		err = errors.Wrap(err, "could not execute missing lender template to send an email")
		return err
	}

	txn := newrelic.FromContext(ctx)
	sgmt := newrelic.StartSegment(txn, "Send HTML Email lender notification")

	email.SendHTML(ctx, conf.Get().ESale.LenderEmail, subject, buf.Bytes())
	sgmt.End()

	return nil
}

func (el *eContractLender) clean() {
	trim := strings.TrimSpace
	el.Name = trim(el.Name)
	el.Address = trim(el.Address)
	el.City = trim(el.City)
	el.StateCode = trim(el.StateCode)
	el.PostalCode = trim(el.PostalCode)
}

// pendingLenderInsert inserts pending lender record in db
func pendingLenderInsert(ctx context.Context, pendingLender *eContractLender) error {
	pendingLender.clean()
	query := `insert into pending_lenders (
		created_at, name, address, city, state_code, postal_code,
		finance_source, lender_id
	) values (
		now() at time zone 'utc', :name, :address, :city, :state_code, :postal_code,
		:finance_source, :lender_id
	) returning id`
	stmt, err := db.Get().PrepareNamedContext(ctx, query)
	if err != nil {
		return errors.Wrap(err, "error on preparing pending_lenders insert")
	}
	defer func() { _ = stmt.Close() }()
	err = stmt.GetContext(ctx, &pendingLender.PendingLenderID, pendingLender)
	if err != nil {
		return errors.Wrap(err, "error on executing pending_lenders insert")
	}
	return nil
}
func clean(str string) string {
	s := []byte(str)
	j := 0
	for _, b := range s {
		if ('a' <= b && b <= 'z') ||
			('A' <= b && b <= 'Z') ||
			('0' <= b && b <= '9') ||
			b == ' ' {
			s[j] = b
			j++
		}
	}
	return string(s[:j])
}

func matchLenderNamesByDist(name string, allLenders []lenderIDName) *lenderIDName {
	bestMatch := -1
	maxSimilarity := 0.8
	for i, current := range allLenders {
		similarity := strutil.Similarity(name, current.Name, metrics.NewJaroWinkler())
		if similarity >= maxSimilarity {
			maxSimilarity = similarity
			bestMatch = i
		}
	}
	if bestMatch > -1 {
		return &allLenders[bestMatch]
	}
	return nil
}

func relaxedNameMatch(name string, allNames []lenderIDName) int {

	cleanedName := clean(name)
	if cleanedName == "" {
		return 0
	}
	for i, current := range allNames {
		if strings.EqualFold(current.Name, cleanedName) {
			return allNames[i].ID
		}
		if len(cleanedName) <= len(current.Name) {
			if strings.Contains(strings.ToLower(current.Name), strings.ToLower(cleanedName)) {
				return allNames[i].ID
			}
		} else {
			if strings.Contains(strings.ToLower(cleanedName), strings.ToLower(current.Name)) {
				return allNames[i].ID
			}
		}
	}
	matched := matchLenderNamesByDist(name, allNames)
	if matched != nil {
		return matched.ID
	}
	return 0
}

// PENLenders API for returning lenders available for a given dealer_id / store
func PENLenders(w http.ResponseWriter, req *http.Request, xtkUserCode string) (int, map[string]interface{}) {
	ctx := req.Context()
	const errMsg = "Error in getting lenders"

	logPENRequest(ctx, getPenRatesRequestBody(req), xtkUserCode, req.URL.Path, "")

	// Get the dealer platform record associated with the provided xtkUserCode
	var dp db.DealerPlatform
	var err error
	dp, err = getDealerPlatformForXtkCode(ctx, xtkUserCode)
	if err != nil {
		err = errors.WithMessage(err, "error in getting dealer platform")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(errMsg, nil)
	}

	dealerID := req.FormValue("dealer_id")
	if dealerID == "" {
		return http.StatusBadRequest, ErrorMessage("Bad Request: dealer_id is missing", nil)
	}

	var storeID int
	query := `select id from stores where e_rating_dealer_id = $1`
	err = db.Get().GetContext(ctx, &storeID, query, dealerID)
	if err != nil {
		err = errors.Wrapf(err, "error getting store id for dealer")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(errMsg, nil)
	}

	dealerIDEnabled, err := isDPAccessibleToStore(ctx, storeID, dp.ID)
	if err != nil {
		err = errors.WithMessagef(err, "error in validating store dealer platform for store (%d) dealer(%d)", storeID, dp.ID)
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(errMsg, nil)
	}
	if !dealerIDEnabled {
		return http.StatusBadRequest, ErrorMessage("Dealer platform ID not enabled for this store", nil)
	}

	lenders := []struct {
		ID   int    `db:"id" json:"id"`
		Name string `db:"name" json:"name"`
	}{}

	// certifiable_makes from lenders and certifiable_makes from stores are stored as different types and
	// this sql statement is converting them both to text[] and then using the array operator && to
	// check if the two arrays have any elements in common.
	// if certifiable_makes is null in lenders, it's considered available for all
	// using "name" in quotes since some old psql version has name a reserved keyword
	query = `
		select 
			id, 
			e_sale_api_display_name "name"
		from lenders
		where 
			deleted_at is null 
			and is_active = true
			and char_length(e_sale_api_display_name) > 0 
			and (
				(certifiable_makes::text[] && (select akeys(certifiable_makes) from stores where id = $1)) 
				or array_length(certifiable_makes, 1) is null
			)`
	err = db.Get().SelectContext(ctx, &lenders, query, storeID)
	if err != nil {
		err = errors.Wrapf(err, "error in getting available e_sale_api_display_name for store(%d)", storeID)
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(errMsg, nil)
	}

	responseData := map[string]interface{}{"lenders": lenders, "count": len(lenders)}

	// log response body
	logPENResponse(ctx, responseData, xtkUserCode, req.URL.Path, "")

	return http.StatusOK, responseData
}
