package handlers

import (
	"database/sql"
	"encoding/base64"
	"fmt"
	"io"
	"strings"

	"net/http"

	"whiz/db"
	"whiz/util"

	"github.com/pkg/errors"
)

// Configuration
const (
	MaxFileSize = 50 << 20 // 50MB per file
	MaxMemory   = 32 << 20 // 32MB max memory for multipart parsing
)

type asburyCancelPayload struct {
	UserEmail string                          `json:"user_email"`
	Files     []*AICancellationRequestPayload `json:"files"`
}

func isValidExtension(filename string) bool {
	i := strings.LastIndex(filename, ".")
	if i < 1 {
		return false
	}
	ext := filename[i:]
	if len(ext) == 0 {
		return false
	}
	fileExtensions := []string{".pdf", ".jpg", ".jpeg", ".png", ".webp", ".tiff"}
	for _, validExt := range fileExtensions {
		if strings.EqualFold(ext, validExt) {
			return true
		}
	}
	return false
}

// AsburyCancelContracts handles the cancellation of contracts via the Asbury API
func AsburyCancelContracts(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()
	data := make(map[string]interface{})
	const (
		errCodeMissingFiles        = "MISSING_FILES"
		errCodeTooManyFiles        = "TOO_MANY_FILES"
		errCodeInvalidFile         = "INVALID_FILE_TYPE"
		errCodeMissingEmail        = "MISSING_EMAIL"
		errCodeUserNotFound        = "USER_NOT_FOUND"
		errCodeNotAuthorized       = "NOT_AUTHORIZED"
		errCodeCouldNotProcess     = "COULD_NOT_PROCESS_FILES"
		errCodeCouldNotUploadFiles = "COULD_NOT_UPLOAD_FILES"
		maxFileCount               = 5 // Maximum number of files allowed per request
	)
	// MaxFileSize is the maximum allowed file size for uploads)
	// Parse the multipart form data, storing up to 32MB in memory
	err := req.ParseMultipartForm(MaxMemory)
	if err != nil {
		data["error_code"] = errCodeCouldNotProcess
		util.ReportError(ctx, errors.Errorf("failed to parse form data"))
		return http.StatusInternalServerError, ErrorMessage("Failed to parse form data", nil)
	}

	// Access regular form fields
	userEmail := req.FormValue("user_email")
	if len(userEmail) == 0 {
		data["error_code"] = errCodeMissingEmail
		util.ReportError(ctx, errors.New("missing user_email in request"))
		return http.StatusBadRequest, ErrorMessage("Missing user email", data)
	}

	var user db.CurrentUser
	err = db.Get().GetContext(ctx, &user, "select * from current_users where email = $1 and active = true", userEmail)
	if err != nil {
		if err == sql.ErrNoRows {
			data["error_code"] = errCodeUserNotFound
			util.ReportError(ctx, errors.Errorf("user with email %s not found", userEmail))
			return http.StatusNotFound, ErrorMessage(fmt.Sprintf("User with email %s not found", userEmail), data)
		}
		util.ReportError(ctx, errors.Errorf("error fetching user %s: %v", userEmail, err))
		return http.StatusInternalServerError, ErrorMessage(fmt.Sprintf("Error fetching user: %v", err), nil)
	}

	hasPermission := user.HasAnyRole([]string{db.RoleAdminView, db.RoleCancelDashboardManager})
	if !hasPermission {
		data["error_code"] = errCodeNotAuthorized
		util.ReportError(ctx, errors.Errorf("user %s does not have permission to cancel contracts", userEmail))
		return http.StatusUnauthorized, ErrorMessage(fmt.Sprintf("User %s does not have permission to cancel contracts", userEmail), data)
	}

	// Access uploaded files
	var payload asburyCancelPayload
	files := req.MultipartForm.File["files"]
	if len(files) == 0 {
		data["error_code"] = errCodeMissingFiles
		util.ReportError(ctx, errors.New("no files provided for cancellation"))
		return http.StatusBadRequest, ErrorMessage("No Files provided", data)
	}
	if len(files) > maxFileCount {
		data["error_code"] = errCodeTooManyFiles
		util.ReportError(ctx, errors.New("too many files provided for cancellation"))
		return http.StatusBadRequest, ErrorMessage("Too many files provided, maximum is 5", data)
	}

	for _, fileHeader := range files {
		if fileHeader.Size == 0 {
			data["error_code"] = errCodeCouldNotUploadFiles
			util.ReportError(ctx, errors.Errorf("file %s is empty", fileHeader.Filename))
			return http.StatusBadRequest, ErrorMessage(fmt.Sprintf("File %s is empty", fileHeader.Filename), data)
		}
		// Check file size
		if fileHeader.Size > MaxFileSize {
			data["error_code"] = errCodeCouldNotUploadFiles
			util.ReportError(ctx, errors.Errorf("file %s exceeds maximum size of %d bytes", fileHeader.Filename, MaxFileSize))
			return http.StatusInternalServerError, ErrorMessage(fmt.Sprintf("File %s exceeds maximum size of %d bytes", fileHeader.Filename, MaxFileSize), data)
		}
		// Validate file extension
		if !isValidExtension(fileHeader.Filename) {
			data["error_code"] = errCodeInvalidFile
			util.ReportError(ctx, errors.Errorf("file %s has an invalid extension", fileHeader.Filename))
			return http.StatusBadRequest, ErrorMessage(fmt.Sprintf("File %s has an invalid extension", fileHeader.Filename), data)
		}
		file, err := fileHeader.Open()
		if err != nil {
			data["error_code"] = errCodeCouldNotUploadFiles
			util.ReportError(ctx, errors.Errorf("error opening file %s: %v", fileHeader.Filename, err))
			return http.StatusInternalServerError, ErrorMessage(fmt.Sprintf("Error opening file %s: %v", fileHeader.Filename, err), data)
		}
		defer file.Close()

		// Read the file content into a byte slice
		fileContent, err := io.ReadAll(file)
		if err != nil {
			data["error_code"] = errCodeCouldNotUploadFiles
			util.ReportError(ctx, errors.Errorf("Error reading file %s: %v", fileHeader.Filename, err))
			return http.StatusInternalServerError, ErrorMessage(fmt.Sprintf("Error reading file %s: %v", fileHeader.Filename, err), data)
		}
		base64Content := base64.StdEncoding.EncodeToString(fileContent)

		payload.Files = append(payload.Files, &AICancellationRequestPayload{
			Source:      db.APIClientNameAsbury,
			Name:        fileHeader.Filename,
			ContentType: fileHeader.Header.Get("Content-Type"),
			FileContent: base64Content,
		})
	}

	status, result := CancellationRequestCreate(ctx, payload.Files, user)
	if status != http.StatusOK {
		data["error_code"] = errCodeCouldNotProcess
		util.ReportError(ctx, errors.Errorf("Could not process files"))
		return http.StatusInternalServerError, ErrorMessage("Could not process files", data)
	}
	return http.StatusOK, result
}
