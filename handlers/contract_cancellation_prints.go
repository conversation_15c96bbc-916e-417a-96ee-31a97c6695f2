package handlers

import (
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"whiz/db"
	"whiz/pdftk"
	"whiz/types"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"github.com/jung-kurt/gofpdf"
	"github.com/lib/pq"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

const (
	cancelFormCurrent     = "files/Cancel_Request.pdf"
	cancelFormPrevious    = "files/Cancel_Request_old.pdf"
	cancelFormDownPayment = "files/Cancel_Request_Down_Payment.pdf"

	checkboxYes = "Yes"
	checkboxNo  = "No"
)

func cancelEstimationPDF(fpdf *gofpdf.Fpdf, cancelOptions CancelContractOptions, cDetails ContractDetails, ccInfo CancelContractQuote) {
	fpdf.AddPage()
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.CellFormat(0.2, 0.01, time.Now().UTC().Format(db.LongDateFormat), "", 0, "", false, 0, "")
	fpdf.Ln(0.2)
	fpdf.SetFont("Helvetica", "B", 12)
	fpdf.CellFormat(2.5, 0.0, "", "", 0, "", false, 0, "")
	fpdf.CellFormat(2.5, 0.2, "*** Contract Cancellation Quote ***", "", 0, "", false, 0, "")
	fpdf.Ln(0.2)
	fpdf.SetFont("Helvetica", "", 12)
	fpdf.CellFormat(3.5, 0.2, "", "", 0, "", false, 0, "")
	fpdf.CellFormat(3.5, 0.2, ccInfo.ProductTypeName, "", 0, "", false, 0, "")
	fpdf.Ln(0.5)
	customerName := cDetails.CustomerFirstName + " " + cDetails.CustomerLastName
	if cDetails.CustomerIsBusiness {
		customerName = customerName + "/" + cDetails.CustomerBusinessName.String
	}
	lines := fpdf.SplitLines([]byte("Customer Name: "+customerName), 3.5)
	for index, line := range lines {
		fpdf.SetFont("Helvetica", "", 10)
		fpdf.CellFormat(0.25, 0, "", "", 0, "", false, 0, "")
		if index == 0 {
			fpdf.CellFormat(3.5, 0, string(line), "", 0, "", false, 0, "")
			fpdf.SetFont("Helvetica", "B", 10)
			fpdf.CellFormat(0.5, 0, "", "", 0, "", false, 0, "")
			fpdf.CellFormat(3.5, 0, "Contract #: "+cDetails.OriginalCode, "", 0, "", false, 0, "")
		} else {
			fpdf.CellFormat(3.5, 0, string(line), "", 0, "", false, 0, "")
		}
		fpdf.Ln(0.2)
	}
	lines = fpdf.SplitLines([]byte("Store Name: "+cDetails.StoreCode), 3.5)
	for index, line := range lines {
		fpdf.SetFont("Helvetica", "", 10)
		fpdf.CellFormat(0.25, 0, "", "", 0, "", false, 0, "")
		if index == 0 {
			fpdf.CellFormat(3.5, 0, string(line), "", 0, "", false, 0, "")
			fpdf.SetFont("Helvetica", "B", 10)
			fpdf.CellFormat(0.5, 0, "", "", 0, "", false, 0, "")
			fpdf.CellFormat(3.5, 0, "VIN #: "+cDetails.VIN, "", 0, "", false, 0, "")
		} else {
			fpdf.CellFormat(3.5, 0, string(line), "", 0, "", false, 0, "")
		}
		fpdf.Ln(0.2)
	}
	fpdf.SetFont("Helvetica", "", 10)
	fpdf.CellFormat(0.25, 0, "", "", 0, "", false, 0, "")
	fpdf.CellFormat(3.5, 0, "Contract Effective Date: "+cDetails.EffectiveDate.Time.Format(db.LongDateFormat), "", 0, "", false, 0, "")
	fpdf.Ln(0.2)
	fpdf.SetFont("Helvetica", "", 10)
	fpdf.CellFormat(0.25, 0, "", "", 0, "", false, 0, "")
	fpdf.CellFormat(3.5, 0, "Contract Price: $"+cDetails.Price.Round(2).StringFixed(2), "", 0, "", false, 0, "")
	fpdf.SetFont("Helvetica", "B", 10)
	fpdf.CellFormat(0.5, 0, "", "", 0, "", false, 0, "")
	fpdf.CellFormat(3.5, 0, "Cancellation Date: "+cancelOptions.CancelDate.Time.Format(db.LongDateFormat), "", 0, "", false, 0, "")
	fpdf.Ln(0.2)
	fpdf.SetFont("Helvetica", "", 10)
	fpdf.CellFormat(0.25, 0, "", "", 0, "", false, 0, "")
	fpdf.CellFormat(3.5, 0, "Contract Cost: $"+ccInfo.Cost.Round(2).StringFixed(2), "", 0, "", false, 0, "")
	fpdf.SetFont("Helvetica", "B", 10)
	fpdf.CellFormat(0.5, 0, "", "", 0, "", false, 0, "")
	fpdf.CellFormat(3.5, 0, "Cancellation Reason: "+cancelOptions.CancelReasonName, "", 0, "", false, 0, "")
	fpdf.Ln(0.2)
	if cDetails.Lender != "" {
		lines = fpdf.SplitLines([]byte("Lender Name: "+cDetails.Lender), 3.5)
		for index, line := range lines {
			fpdf.SetFont("Helvetica", "", 10)
			fpdf.CellFormat(0.25, 0, "", "", 0, "", false, 0, "")
			if index == 0 {
				fpdf.CellFormat(3.5, 0, string(line), "", 0, "", false, 0, "")
				fpdf.SetFont("Helvetica", "B", 10)
				fpdf.CellFormat(0.5, 0, "", "", 0, "", false, 0, "")
				fpdf.CellFormat(3.5, 0, "Cancellation Mileage: "+strconv.Itoa(cancelOptions.Mileage), "", 0, "", false, 0, "")
			} else {
				fpdf.CellFormat(3.5, 0, string(line), "", 0, "", false, 0, "")
			}
			fpdf.Ln(0.2)
		}
		fpdf.SetFont("Helvetica", "", 10)
		fpdf.CellFormat(0.25, 0, "", "", 0, "", false, 0, "")
		attnTo := cancelOptions.CancelPayeeAttentionTo
		if cancelOptions.CancelPayeeAttentionTo == "" {
			attnTo = cDetails.LenderAttention
		}
		fpdf.CellFormat(3.5, 0, "Lender Attention To: "+attnTo, "", 0, "", false, 0, "")
		fpdf.SetFont("Helvetica", "B", 10)
		fpdf.CellFormat(0.5, 0, "", "", 0, "", false, 0, "")
		fpdf.CellFormat(3.5, 0, "Cancellation Fee: $"+ccInfo.Fee.Round(2).StringFixed(2), "", 0, "", false, 0, "")
		fpdf.Ln(0.2)
		fpdf.SetFont("Helvetica", "", 10)
		fpdf.CellFormat(0.25, 0, "", "", 0, "", false, 0, "")
		city := cancelOptions.CancelPayeeCity
		if cancelOptions.CancelPayeeCity == "" {
			city = cDetails.LenderCity
		}
		fpdf.CellFormat(3.5, 0, "Lender City: "+city, "", 0, "", false, 0, "")
		fpdf.SetFont("Helvetica", "B", 10)
		fpdf.CellFormat(0.5, 0, "", "", 0, "", false, 0, "")
		fpdf.CellFormat(3.5, 0, "Estimated Sales Tax: $"+ccInfo.SalesTax.String(), "", 0, "", false, 0, "")
		fpdf.Ln(0.2)
		fpdf.SetFont("Helvetica", "", 10)
		fpdf.CellFormat(0.25, 0, "", "", 0, "", false, 0, "")
		state := cancelOptions.CancelPayeeState
		if cancelOptions.CancelPayeeState == "" {
			state = cDetails.LenderState
		}
		fpdf.CellFormat(3.5, 0, "Lender State: "+state, "", 0, "", false, 0, "")
		fpdf.Ln(0.2)
	} else {
		fpdf.SetFont("Helvetica", "", 10)
		fpdf.CellFormat(0.25, 0, "", "", 0, "", false, 0, "")
		fpdf.CellFormat(3.5, 0, "", "", 0, "", false, 0, "")
		fpdf.SetFont("Helvetica", "B", 10)
		fpdf.CellFormat(0.5, 0, "", "", 0, "", false, 0, "")
		fpdf.CellFormat(3.5, 0, "Cancellation Mileage: "+strconv.Itoa(cancelOptions.Mileage), "", 0, "", false, 0, "")
		fpdf.Ln(0.2)
		fpdf.SetFont("Helvetica", "", 10)
		fpdf.CellFormat(0.25, 0, "", "", 0, "", false, 0, "")
		fpdf.CellFormat(3.5, 0, "", "", 0, "", false, 0, "")
		fpdf.SetFont("Helvetica", "B", 10)
		fpdf.CellFormat(0.5, 0, "", "", 0, "", false, 0, "")
		fpdf.CellFormat(3.5, 0, "Cancellation Fee: $"+ccInfo.Fee.Round(2).StringFixed(2), "", 0, "", false, 0, "")
		fpdf.Ln(0.2)
		fpdf.CellFormat(4.25, 0, "", "", 0, "", false, 0, "")
		fpdf.CellFormat(3.5, 0, "Estimated Sales Tax: $"+ccInfo.SalesTax.String(), "", 0, "", false, 0, "")
		fpdf.Ln(0.2)
	}

	fpdf.Ln(0.2)
	fpdf.SetFont("Helvetica", "", 10)
	fpdf.CellFormat(3.75, 0, "", "", 0, "", false, 0, "")
	fpdf.SetFont("Helvetica", "B", 10)
	fpdf.CellFormat(0.5, 0, "", "", 0, "", false, 0, "")
	fpdf.CellFormat(3.5, 0, "Days Used: "+ccInfo.PercentMonthsUsed.String()+"%", "", 0, "", false, 0, "")

	if cDetails.ProductTypeName != db.ProductTypeDrivePur &&
		cDetails.ProductTypeName != db.ProductTypeGuaranteedAssetProtection &&
		cDetails.ProductTypeName != db.ProductTypeKeyRemoteReplacement &&
		cDetails.ProductTypeName != db.ProductTypeAppearanceProtection &&
		cDetails.ProductTypeName != db.ProductTypePaintlessDentRepair {
		fpdf.Ln(0.2)
		fpdf.SetFont("Helvetica", "", 10)
		fpdf.CellFormat(3.75, 0, "", "", 0, "", false, 0, "")
		fpdf.SetFont("Helvetica", "B", 10)
		fpdf.CellFormat(0.5, 0, "", "", 0, "", false, 0, "")
		fpdf.CellFormat(3.5, 0, "Miles Used: "+ccInfo.PercentMilesUsed.String()+"%", "", 0, "", false, 0, "")
	}
	labelClaims := "Total Claims: "
	if cDetails.ProductTypeName == db.ProductTypeDrivePur {
		labelClaims = "Applications used: "
	}
	fpdf.Ln(0.4)
	fpdf.SetFont("Helvetica", "", 10)
	fpdf.CellFormat(3.75, 0, "", "", 0, "", false, 0, "")
	fpdf.SetFont("Helvetica", "B", 10)
	fpdf.CellFormat(0.5, 0, "", "", 0, "", false, 0, "")
	fpdf.CellFormat(3.5, 0, labelClaims+strconv.Itoa(ccInfo.ClaimCount), "", 0, "", false, 0, "")
	fpdf.Ln(0.2)
	claimDeducted := "No"
	if ccInfo.ClaimsDeducted {
		claimDeducted = "Yes"
	}
	fpdf.SetFont("Helvetica", "", 10)
	fpdf.CellFormat(3.75, 0, "", "", 0, "", false, 0, "")
	fpdf.SetFont("Helvetica", "B", 10)
	fpdf.CellFormat(0.5, 0, "", "", 0, "", false, 0, "")
	fpdf.CellFormat(3.5, 0, "Claim Deducted: "+claimDeducted, "", 0, "", false, 0, "")
	fpdf.Ln(0.2)
	fpdf.SetFont("Helvetica", "", 10)
	fpdf.CellFormat(3.75, 0, "", "", 0, "", false, 0, "")
	fpdf.SetFont("Helvetica", "B", 10)
	fpdf.CellFormat(0.5, 0, "", "", 0, "", false, 0, "")
	fpdf.CellFormat(3.5, 0, "Total Claims Amount: $"+ccInfo.ClaimTotalAmount.Round(2).StringFixed(2), "", 0, "", false, 0, "")
	fpdf.Ln(0.4)
	fpdf.SetFont("Helvetica", "", 10)
	fpdf.CellFormat(3.75, 0, "", "", 0, "", false, 0, "")
	fpdf.SetFont("Helvetica", "B", 10)
	fpdf.CellFormat(0.5, 0, "", "", 0, "", false, 0, "")
	fpdf.CellFormat(3.5, 0, "Customer Refund: $"+ccInfo.CustomerRefund.Round(2).StringFixed(2), "", 0, "", false, 0, "")
	fpdf.Ln(0.2)
	fpdf.SetFont("Helvetica", "", 10)
	fpdf.CellFormat(3.75, 0, "", "", 0, "", false, 0, "")
	fpdf.SetFont("Helvetica", "B", 10)
	fpdf.CellFormat(0.5, 0, "", "", 0, "", false, 0, "")
	fpdf.CellFormat(3.5, 0, "Store Refund: $"+ccInfo.StoreRefund.Round(2).StringFixed(2), "", 0, "", false, 0, "")
}

// cancelQuotePDF will make a pdf for given contract cancellation quotes
func cancelQuotePDF(ccInfo []CancelContractQuote, cancelOptions CancelContractOptions, customerName string) (*gofpdf.Fpdf, error) {
	fpdf := gofpdf.New("Portrait", "in", "Letter", "")

	fpdf.AddPage()
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.CellFormat(0.2, 0.01, time.Now().UTC().Format(db.LongDateFormat), "", 0, "", false, 0, "")
	fpdf.Ln(0.2)

	fpdf.SetFont("Helvetica", "B", 12)
	fpdf.CellFormat(2.5, 0.0, "", "", 0, "", false, 0, "")
	fpdf.CellFormat(2.5, 0.2, "*** Contract Cancellation Quote ***", "", 0, "", false, 0, "")
	fpdf.Ln(0.6)

	fpdf.SetFont("Helvetica", "B", 10)
	fpdf.CellFormat(2.5, 0, customerName, "", 0, "", false, 0, "")
	fpdf.Ln(0.2)

	fpdf.SetFont("Helvetica", "", 10)
	fpdf.CellFormat(2.5, 0, "Cancel Date: "+cancelOptions.CancelDate.Time.UTC().Format(db.LongDateFormat), "", 0, "", false, 0, "")
	fpdf.Ln(0.2)

	fpdf.SetFont("Helvetica", "", 10)
	fpdf.CellFormat(2.5, 0, "Mileage: "+strconv.Itoa(cancelOptions.Mileage), "", 0, "", false, 0, "")
	fpdf.Ln(0.2)

	fpdf.SetFont("Helvetica", "", 10)
	fpdf.CellFormat(2.5, 0, "Cancel Reason: "+cancelOptions.CancelReasonName, "", 0, "", false, 0, "")
	fpdf.Ln(0.4)

	fpdf.SetFont("Helvetica", "B", 6)
	fpdf.CellFormat(0.80, 0, "Contract", "", 0, "", false, 0, "")
	fpdf.CellFormat(0.70, 0, "Type", "", 0, "", false, 0, "")
	fpdf.CellFormat(0.70, 0, "Issuing", "", 0, "", false, 0, "")
	fpdf.CellFormat(0.70, 0, "Effective", "", 0, "", false, 0, "")
	fpdf.CellFormat(1.5, 0, "Factor", "", 0, "", false, 0, "")
	fpdf.CellFormat(0.75, 0, "Fee", "", 0, "", false, 0, "")
	fpdf.CellFormat(0.50, 0, "Total", "", 0, "", false, 0, "")
	fpdf.CellFormat(0.50, 0, "Claim", "", 0, "", false, 0, "")
	fpdf.CellFormat(0.75, 0, "Total Claim", "", 0, "", false, 0, "")
	fpdf.CellFormat(0.55, 0, "Customer", "", 0, "", false, 0, "")
	fpdf.CellFormat(0.45, 0, "Estimated", "", 0, "", false, 0, "")
	fpdf.Ln(0.1)

	fpdf.CellFormat(1.5, 0, "", "", 0, "", false, 0, "")
	fpdf.CellFormat(0.70, 0, "Dealer", "", 0, "", false, 0, "")
	fpdf.CellFormat(0.70, 0, "Date", "", 0, "", false, 0, "")
	fpdf.CellFormat(2.25, 0, "", "", 0, "", false, 0, "")
	fpdf.CellFormat(0.50, 0, "Claims", "", 0, "", false, 0, "")
	fpdf.CellFormat(0.50, 0, "Deducted", "", 0, "", false, 0, "")
	fpdf.CellFormat(0.75, 0, "Amount", "", 0, "", false, 0, "")
	fpdf.CellFormat(0.55, 0, "Refund", "", 0, "", false, 0, "")
	fpdf.CellFormat(0.45, 0, "Sales", "", 0, "", false, 0, "")
	fpdf.Ln(0.1)

	fpdf.CellFormat(6.15, 0, "", "", 0, "", false, 0, "")
	fpdf.CellFormat(0.75, 0, "Paid", "", 0, "", false, 0, "")
	fpdf.CellFormat(0.55, 0, "", "", 0, "", false, 0, "")
	fpdf.CellFormat(0.45, 0, "Tax", "", 0, "", false, 0, "")
	fpdf.Ln(0.1)

	fpdf.Line(0.25, fpdf.GetY(), 8.25, fpdf.GetY())

	fpdf.Ln(0.1)

	fpdf.SetFont("Helvetica", "", 6)

	grandTotal := decimal.Zero
	salesTaxTotal := decimal.Zero
	for _, q := range ccInfo {
		factor := ""
		warningMessage := ""
		if q.Cancellable {
			var allFactors []string
			for _, v := range q.AllFactors {
				allFactors = append(allFactors, strings.Replace(v, "used", "", 1))
			}
			factor = strings.Join(allFactors, "/") + " used"
			grandTotal = grandTotal.Add(q.CustomerRefund)
			salesTaxTotal = salesTaxTotal.Add(q.SalesTax)
		} else {
			factor = "Not cancellable"
			if len(q.RuleViolations) != 0 {
				var violationMsgs []string
				for _, violation := range q.RuleViolations {
					violationMsgs = append(violationMsgs, violation.ViolationMessage)
				}
				warningMessage = strings.Join(violationMsgs, " ")
			}
		}

		claimDeducted := "No"
		if q.ClaimsDeducted {
			claimDeducted = "Yes"
		}
		if factor != "" {
			lines := fpdf.SplitLines([]byte(factor), 1.5)
			for index, line := range lines {
				contractCode := q.Code
				if q.OriginalCode != "" {
					contractCode = q.OriginalCode
				}

				if index == 0 {
					fpdf.CellFormat(0.8, 0, contractCode, "", 0, "", false, 0, "")
					fpdf.CellFormat(0.70, 0, q.ProductTypeCode, "", 0, "", false, 0, "")
					fpdf.CellFormat(0.70, 0, q.StoreCode, "", 0, "", false, 0, "")
					fpdf.CellFormat(0.70, 0, q.EffectiveDate.Time.UTC().Format(db.LongDateFormat), "", 0, "", false, 0, "")
					fpdf.CellFormat(1.5, 0, string(line), "", 0, "", false, 0, "")
					if warningMessage == "" {
						fpdf.CellFormat(0.75, 0, q.Fee.Round(2).StringFixed(2), "", 0, "", false, 0, "")
						fpdf.CellFormat(0.50, 0, strconv.Itoa(q.ClaimCount), "", 0, "", false, 0, "")
						fpdf.CellFormat(0.50, 0, claimDeducted, "", 0, "", false, 0, "")
						fpdf.CellFormat(0.75, 0, q.ClaimTotalAmount.Round(2).StringFixed(2), "", 0, "", false, 0, "")
						fpdf.CellFormat(0.55, 0, q.CustomerRefund.Round(2).StringFixed(2), "", 0, "", false, 0, "")
						fpdf.CellFormat(0.45, 0, q.SalesTax.Round(2).StringFixed(2), "", 0, "R", false, 0, "")
					} else {
						fpdf.CellFormat(3, 0, warningMessage, "", 0, "", false, 0, "")
					}
				} else {
					fpdf.CellFormat(3.25, 0, "", "", 0, "", false, 0, "")
					fpdf.CellFormat(1.5, 0, string(line), "", 0, "", false, 0, "")
				}

				fpdf.Ln(0.1)
			}
		} else {
			fpdf.CellFormat(0.8, 0, q.Code, "", 0, "", false, 0, "")
			fpdf.CellFormat(0.70, 0, q.ProductTypeCode, "", 0, "", false, 0, "")
			fpdf.CellFormat(0.70, 0, q.StoreCode, "", 0, "", false, 0, "")
			fpdf.CellFormat(0.70, 0, q.EffectiveDate.Time.UTC().Format(db.LongDateFormat), "", 0, "", false, 0, "")
			fpdf.CellFormat(1.5, 0, factor, "", 0, "", false, 0, "")
			if warningMessage == "" {
				fpdf.CellFormat(0.75, 0, q.Fee.Round(2).StringFixed(2), "", 0, "", false, 0, "")
				fpdf.CellFormat(0.50, 0, strconv.Itoa(q.ClaimCount), "", 0, "", false, 0, "")
				fpdf.CellFormat(0.50, 0, claimDeducted, "", 0, "", false, 0, "")
				fpdf.CellFormat(0.75, 0, q.ClaimTotalAmount.Round(2).StringFixed(2), "", 0, "", false, 0, "")
				fpdf.CellFormat(0.55, 0, q.CustomerRefund.Round(2).StringFixed(2), "", 0, "", false, 0, "")
				fpdf.CellFormat(0.45, 0, q.SalesTax.Round(2).StringFixed(2), "", 0, "R", false, 0, "")
			} else {
				fpdf.CellFormat(3, 0, warningMessage, "", 0, "", false, 0, "")
			}
			fpdf.Ln(0.1)
		}

		fpdf.Line(0.25, fpdf.GetY(), 8.25, fpdf.GetY())
		fpdf.Ln(0.1)
	}

	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.CellFormat(7.3, 0, "Total $ "+grandTotal.Round(2).StringFixed(2), "", 0, "R", false, 0, "")
	fpdf.CellFormat(0.6, 0, "$"+salesTaxTotal.Round(2).StringFixed(2), "", 0, "R", false, 0, "")
	fpdf.Ln(0.1)
	fpdf.Line(0.25, fpdf.GetY(), 8.25, fpdf.GetY())
	fpdf.Ln(0.1)
	fpdf.CellFormat(7.3, 0, "Grand Total $ "+grandTotal.Add(salesTaxTotal).Round(2).StringFixed(2), "", 0, "R", false, 0, "")

	return fpdf, nil
}

type cancelContractList []struct {
	Code            string `db:"code"`
	ProductTypeName string `db:"product_type_name"`
	OriginalCode    string `db:"original_code"`
}

// cancellationRequestForm will make a contract request form pdf
func cancellationRequestForm(cancelOptions CancelContractOptions, contractDetails ContractDetails, cList cancelContractList) (*os.File, error) {
	stampedFile, err := ioutil.TempFile("", "temp-contract-cancel")
	if err != nil {
		return nil, errors.Wrap(err, "error creating temp file to stamp contract cancel request form")
	}

	apuContractIDs := []string{}
	pdrContractIDs := []string{}
	serviceContractIDs := []string{}
	keyContractIDs := []string{}
	gapContractIDs := []string{}
	drivePurContractIDs := []string{}
	leaseWearTearContractIDs := []string{}
	maintenanceContractIDs := []string{}
	tireWheelContractIDs := []string{}
	nsdTheftContractIDs := []string{}

	pdrContracts := checkboxNo
	serviceContracts := checkboxNo
	keyContracts := checkboxNo
	gapContracts := checkboxNo
	drivePurContracts := checkboxNo
	leaseWearTearContracts := checkboxNo
	maintenanceContracts := checkboxNo
	tireWheelContracts := checkboxNo
	for _, contract := range cList {
		contractCode := contract.Code
		if contract.OriginalCode != "" {
			contractCode = contract.OriginalCode
		}
		switch contract.ProductTypeName {
		case db.ProductTypeAppearanceProtection:
			apuContractIDs = append(apuContractIDs, contractCode)
		case db.ProductTypePaintlessDentRepair:
			pdrContractIDs = append(pdrContractIDs, contractCode)
			if len(pdrContractIDs) > 0 {
				pdrContracts = checkboxYes
			}
		case db.ProductTypeService:
			serviceContractIDs = append(serviceContractIDs, contractCode)
			if len(serviceContractIDs) > 0 {
				serviceContracts = checkboxYes
			}
		case db.ProductTypeKeyRemoteReplacement:
			keyContractIDs = append(keyContractIDs, contractCode)
			if len(keyContractIDs) > 0 {
				keyContracts = checkboxYes
			}
		case db.ProductTypeGuaranteedAssetProtection:
			gapContractIDs = append(gapContractIDs, contractCode)
			if len(gapContractIDs) > 0 {
				gapContracts = checkboxYes
			}
		case db.ProductTypeDrivePur:
			drivePurContractIDs = append(drivePurContractIDs, contractCode)
			if len(drivePurContractIDs) > 0 {
				drivePurContracts = checkboxYes
			}
		case db.ProductTypeLeaseWearAndTear:
			leaseWearTearContractIDs = append(leaseWearTearContractIDs, contractCode)
			if len(leaseWearTearContractIDs) > 0 {
				leaseWearTearContracts = checkboxYes
			}
		case db.ProductTypeMaintenance:
			maintenanceContractIDs = append(maintenanceContractIDs, contractCode)
			if len(maintenanceContractIDs) > 0 {
				maintenanceContracts = checkboxYes
			}
		case db.ProductTypeTireAndWheel:
			tireWheelContractIDs = append(tireWheelContractIDs, contractCode)
			if len(tireWheelContractIDs) > 0 {
				tireWheelContracts = checkboxYes
			}
		case db.ProductTypeNSDTheft:
			nsdTheftContractIDs = append(nsdTheftContractIDs, contractCode)
		}
	}

	otherContracts := checkboxNo
	otherProducts := []string{}
	if len(apuContractIDs) > 0 {
		otherContracts = checkboxYes
		otherProducts = append(otherProducts, "Appearance Protection")
	}

	if len(nsdTheftContractIDs) > 0 {
		otherContracts = checkboxYes
		otherProducts = append(otherProducts, "NSD Theft")
	}

	isFinanced := checkboxNo
	if contractDetails.Lender != "" {
		isFinanced = checkboxYes
	}
	customerName := contractDetails.CustomerFirstName + " " + contractDetails.CustomerLastName
	if contractDetails.CustomerIsBusiness {
		customerName = customerName + "/" + contractDetails.CustomerBusinessName.String
	}
	customerAddress := cancelOptions.CancelPayeeAddress
	if customerAddress == "" {
		customerAddress = contractDetails.CustomerAddress
	}
	customerCity := cancelOptions.CancelPayeeCity
	if customerCity == "" {
		customerCity = contractDetails.CustomerCity
	}
	customerState := cancelOptions.CancelPayeeState
	if customerState == "" {
		customerState = contractDetails.CustomerStateCode
	}
	customerPostalCode := cancelOptions.CancelPayeePostalCode
	if customerPostalCode == "" {
		customerPostalCode = contractDetails.CustomerPostalCode
	}

	fields := map[string]string{
		"CUST_NAME":                     customerName,
		"CUST_PHONE":                    contractDetails.CustomerPhone,
		"CUST_ADDRESS":                  customerAddress,
		"CUST_CITY":                     customerCity,
		"CUST_STATE":                    customerState,
		"CUST_ZIP":                      customerPostalCode,
		"CUST_EMAIL":                    contractDetails.CustomerEmail,
		"VIN":                           contractDetails.VIN,
		"YEAR":                          strconv.Itoa(contractDetails.VehicleYear),
		"MAKE":                          contractDetails.VehicleMake,
		"MODEL":                         contractDetails.VehicleModel,
		"CURRENT_MILES":                 strconv.Itoa(cancelOptions.Mileage),
		"SERVICE_CONTRACT_ID":           strings.Join(serviceContractIDs, " "),
		"GAP_CONTRACT_ID":               strings.Join(gapContractIDs, " "),
		"KEY_CONTRACT_ID":               strings.Join(keyContractIDs, " "),
		"DRIVEPUR_CONTRACT_ID":          strings.Join(drivePurContractIDs, " "),
		"LEASE_WEAR_TEAR_CONTRACT_ID":   strings.Join(leaseWearTearContractIDs, " "),
		"MAINTENANCE_CONTRACT_ID":       strings.Join(maintenanceContractIDs, " "),
		"TIRE_WHEEL_CONTRACT_ID":        strings.Join(tireWheelContractIDs, " "),
		"PDR_CONTRACT_ID":               strings.Join(pdrContractIDs, " "),
		"OTHER_CONTRACT_ID":             strings.Join(apuContractIDs, " "),
		"OTHER_CONTRACT_ID2":            strings.Join(nsdTheftContractIDs, " "),
		"IS_FINANCE":                    isFinanced,
		"LENDER_NAME":                   contractDetails.Lender,
		"LENDER_ADDRESS":                contractDetails.LenderAddress,
		"REASON":                        cancelOptions.CancelReasonName,
		"SERVICE_CONTRACTS":             serviceContracts,
		"GAP_CONTRACTS":                 gapContracts,
		"TIRE_AND_WHEEL_CONTRACTS":      tireWheelContracts,
		"PDR_CONTRACTS":                 pdrContracts,
		"KEY_CONTRACTS":                 keyContracts,
		"MAINTENANCE_CONTRACTS":         maintenanceContracts,
		"DRIVEPUR_CONTRACTS":            drivePurContracts,
		"LEASE_WEAR_AND_TEAR_CONTRACTS": leaseWearTearContracts,
		"OTHER_CONTRACTS":               otherContracts,
		"OTHER_PRODUCTS":                strings.Join(otherProducts, " "),
		"DATE":                          cancelOptions.CancelDate.Time.Format(db.LongDateFormat),
	}

	// 11/14/2023 There's a new Other option for the Cancel Reasons on the form PDF
	// If the cancel reason is not one of the other options listed on the form, then
	// we need to ensure that the Other box is checked and the reason name is in the
	// text box.
	if cancelOptions.IsCancelReasonOther() {
		fields["REASON"] = "Other"
		fields["REASON_OTHER"] = cancelOptions.CancelReasonName
	}

	cancelFormPath := cancelFormCurrent

	// If the contract is part of a store that requires using the previous
	// version of the cancel form, then update to use the previous version.
	if contractDetails.UsePreviousCancelForm {
		cancelFormPath = cancelFormPrevious

		// If the older cancel form is being used, then we nee do use the
		// old cancel reason name for the Trade In Reason.
		if cancelOptions.CancelReasonName == db.CancelReasonSoldTraded {
			fields["REASON"] = "Trade In"
		}
	}

	// If the cancel reason is "Downpayment" then need to use the downpayment form
	if cancelOptions.CancelReasonName == db.CancelReasonDownPayment {
		cancelFormPath = cancelFormDownPayment
	}

	err = pdftk.FillForm(cancelFormPath, fields, stampedFile.Name())
	if err != nil {
		return nil, errors.Wrap(err, "error stamping SPP quote")
	}

	return stampedFile, nil
}

// CancellationEstimateListAsPdf returns cancellation estimates for given contract list in PDF format
func CancellationEstimateListAsPdf(w http.ResponseWriter, req *http.Request, user db.CurrentUser, validateStore, isAdmin, isManager bool) {
	ctx := req.Context()

	reqPayload := EstimateRequestInfo{}

	query := req.FormValue("q")
	if query == "" {
		_ = r.Text(w, http.StatusBadRequest, "invalid query")
		return
	}

	err := json.Unmarshal([]byte(query), &reqPayload)
	if err != nil {
		_ = r.Text(w, http.StatusBadRequest, "Bad request"+err.Error())
		return
	}

	if err = validateCancellationRequest(ctx, reqPayload, user, db.CancelRequestPrintQuote); err != nil {
		_ = r.Text(w, http.StatusBadRequest, err.Error())
		return
	}

	if len(reqPayload.Contracts) == 0 {
		_ = r.Text(w, http.StatusBadRequest, "Contract list for cancellation is empty")
		return
	}

	cr, err := db.GetCancelReasonByID(ctx, reqPayload.CancelReasonID)
	if err != nil {
		_ = r.Text(w, http.StatusBadRequest, "Unable to load Cancel Reason")
		return
	}

	reqPayload.CancelReasonName = cr.Name
	reqPayload.UserID = user.ID
	reqPayload.CurrentDate = time.Now()
	if cr.IsFlatCancel {
		reqPayload.ManualTaxRate = decimal.Zero
	}

	company, err := GetCompanyForContract(ctx, reqPayload.Contracts[0])
	if err != nil {
		_ = r.Text(w, http.StatusBadRequest, "Unable to load company")
		return
	}

	fpdf := gofpdf.New("Portrait", "in", "Letter", "")
	for _, contractID := range reqPayload.Contracts {
		contract, err := ContractInfo(ctx, contractID, cr)
		if err != nil && !errors.Is(err, ErrDMSLookUpFailed) {
			_ = r.Text(w, http.StatusInternalServerError, "failed to fetch contract details with ID "+strconv.Itoa(contract.ID))
			ReportError(req, errors.Wrapf(err, "failed to fetch contract details with ID %d", contract.ID))
			return
		}
		if contract.Status == db.ContractStatusCancelled {
			continue // skip already cancelled contracts
		}

		// get number of already processed claims
		closedClaims, err := db.GetFilteredClaims(ctx, contract.Code, contract.ProductTypeCode, closedStatuses)
		if err != nil {
			_ = r.Text(w, http.StatusInternalServerError, "Error in getting claims for contract "+contract.Code)
			ReportError(req, err)
			return
		}
		reqPayload.ClosedClaims = closedClaims

		// We will set if contract is of EWU type
		sql := `select Count(pv.id) from products p join product_variants pv on p.id = pv.product_id
			join product_types pt on p.product_type_id = pt.id
			where pt.code = 'LWT' and (p.name ilike '%Toyota%' or p.name ilike '%Lexus%') and pv.id = $1`
		var isEWUToyotaOrLexus int
		err = db.Get().Get(&isEWUToyotaOrLexus, sql, contract.ProductVariantID)
		if err != nil {
			err = errors.Wrap(err, "error getting product variants")
			ReportError(req, err)
			return
		}
		contract.IsEWUToyotaOrLexus = isEWUToyotaOrLexus > 0
		if contract.IsEWUToyotaOrLexus {
			sql := `select sum(ca.cost) from contract_adjustments ca join rate_buckets rb on ca.rate_bucket_id = rb.id
					where ca.contract_id = $1 and rb.name in ('TCA Admin', 'SPIFF')`
			err := db.Get().Get(&contract.AdminCost, sql, contract.ID)
			if err != nil {
				errors.Wrap(err, "error getting admin cost for product")
				ReportError(req, err)
				return
			}
		}

		if cr.IsFlatCancel {
			reqPayload.CancelContractOptions.CancelDate = types.JSPQDate{NullTime: pq.NullTime{Time: contract.EffectiveDate.Time, Valid: true}}
			reqPayload.CancelContractOptions.Mileage = contract.EffectiveMileage
		}
		cancelEstimate, err := EstimateCancellation(contract, reqPayload.CancelContractOptions, validateStore, isAdmin, isManager, user, &company)
		if err != nil {
			_ = r.Text(w, http.StatusInternalServerError, "failed to calculate cancellation estimate with ID "+strconv.Itoa(contract.ID))
			ReportError(req, errors.Wrapf(err, "failed to calculate cancellation estimate with ID %d", contract.ID))
			return
		}
		if !cancelEstimate.Cancellable {
			ReportError(req, errors.Wrap(err, "can not cancel not cancellable contract "+contract.Code))
			continue
		}
		cancelEstimationPDF(fpdf, reqPayload.CancelContractOptions, contract, cancelEstimate)

	}

	w.Header().Set("Content-Type", "application/pdf")
	w.Header().Set("Content-Disposition", "inline; filename=\"Contract_Cancellation_info.pdf\"")
	w.WriteHeader(http.StatusOK)
	err = fpdf.Output(w)
	if err != nil {
		err = errors.Wrap(err, "error writing estimate information to response")
		_ = r.Text(w, http.StatusBadRequest, "error writing estimate information to response")
		ReportError(req, err)
		return
	}
}

// CancellationQuoteListAsPdf returns cancellation estimate quotes for given contract list in PDF format
func CancellationQuoteListAsPdf(w http.ResponseWriter, req *http.Request, user db.CurrentUser, validateStore, isAdmin, isManager bool) {
	ctx := req.Context()

	reqPayload := EstimateRequestInfo{}

	query := req.FormValue("q")
	if query == "" {
		_ = r.Text(w, http.StatusBadRequest, "invalid query")
		return
	}

	err := json.Unmarshal([]byte(query), &reqPayload)
	if err != nil {
		_ = r.Text(w, http.StatusBadRequest, "Bad request"+err.Error())
		return
	}

	if err = validateCancellationRequest(ctx, reqPayload, user, db.CancelRequestPrintQuote); err != nil {
		_ = r.Text(w, http.StatusBadRequest, err.Error())
		return
	}

	if len(reqPayload.Contracts) == 0 {
		_ = r.Text(w, http.StatusBadRequest, "Contract list for cancellation is empty")
		return
	}

	cr, err := db.GetCancelReasonByID(ctx, reqPayload.CancelReasonID)
	if err != nil {
		_ = r.Text(w, http.StatusBadRequest, "Unable to load Cancel Reason")
		return
	}

	reqPayload.CancelReasonName = cr.Name
	reqPayload.UserID = user.ID
	reqPayload.CurrentDate = time.Now()
	if cr.IsFlatCancel {
		reqPayload.ManualTaxRate = decimal.Zero
	}

	company, err := GetCompanyForContract(ctx, reqPayload.Contracts[0])
	if err != nil {
		_ = r.Text(w, http.StatusBadRequest, "Unable to load company")
		return
	}

	fpdf := gofpdf.New("Portrait", "in", "Letter", "")
	cancelEstimates := []CancelContractQuote{}
	customerName := ""
	for _, contractID := range reqPayload.Contracts {
		contract, err := ContractInfo(ctx, contractID, cr)
		if err != nil && !errors.Is(err, ErrDMSLookUpFailed) {
			_ = r.Text(w, http.StatusInternalServerError, "failed to fetch contract details with ID "+strconv.Itoa(contract.ID))
			ReportError(req, errors.Wrapf(err, "failed to fetch contract details with ID %d", contract.ID))
			return
		}
		if contract.Status == db.ContractStatusCancelled {
			continue // skip already cancelled contracts
		}

		// get number of already processed claims
		closedClaims, err := db.GetFilteredClaims(ctx, contract.Code, contract.ProductTypeCode, closedStatuses)
		if err != nil {
			_ = r.Text(w, http.StatusInternalServerError, "Error in getting claims for contract "+contract.Code)
			ReportError(req, err)
			return
		}
		reqPayload.ClosedClaims = closedClaims

		customerName = contract.CustomerFirstName + " " + contract.CustomerLastName
		if contract.CustomerIsBusiness {
			customerName = customerName + "/" + contract.CustomerBusinessName.String
		}
		// We will set if contract is of EWU type
		sql := `select Count(pv.id) from products p join product_variants pv on p.id = pv.product_id
			join product_types pt on p.product_type_id = pt.id
			where pt.code = 'LWT' and (p.name ilike '%Toyota%' or p.name ilike '%Lexus%') and pv.id = $1`
		var isEWUToyotaOrLexus int
		err = db.Get().Get(&isEWUToyotaOrLexus, sql, contract.ProductVariantID)
		if err != nil {
			err = errors.Wrap(err, "error getting product variants")
			ReportError(req, err)
			return
		}
		contract.IsEWUToyotaOrLexus = isEWUToyotaOrLexus > 0

		if contract.IsEWUToyotaOrLexus {
			sql := `select sum(ca.cost) from contract_adjustments ca join rate_buckets rb on ca.rate_bucket_id = rb.id
					where ca.contract_id = $1 and rb.name in ('TCA Admin', 'SPIFF')`
			err := db.Get().Get(&contract.AdminCost, sql, contract.ID)
			if err != nil {
				errors.Wrap(err, "error getting admin cost for product")
				ReportError(req, err)
				return
			}
		}

		if cr.IsFlatCancel {
			reqPayload.CancelContractOptions.CancelDate = types.JSPQDate{NullTime: pq.NullTime{Time: contract.EffectiveDate.Time, Valid: true}}
			reqPayload.CancelContractOptions.Mileage = contract.EffectiveMileage
		}
		cancelEstimate, err := EstimateCancellation(contract, reqPayload.CancelContractOptions, validateStore, isAdmin, isManager, user, &company)
		if err != nil {
			_ = r.Text(w, http.StatusInternalServerError, "failed to calculate cancellation estimate with ID "+strconv.Itoa(contract.ID))
			ReportError(req, errors.Wrapf(err, "failed to calculate cancellation estimate with ID %d", contract.ID))
			return
		}
		cancelEstimates = append(cancelEstimates, cancelEstimate)
	}

	if len(cancelEstimates) == 0 {
		ReportError(req, errors.New("invalid list of contracts to print cancellation quotes - contracts may have already been cancelled"))
		_ = r.Text(w, http.StatusNotFound, "Invalid list of contract to print cancellation quotes. Contracts may have already been cancelled")
		return
	}

	fpdf, err = cancelQuotePDF(cancelEstimates, reqPayload.CancelContractOptions, customerName)
	if err != nil {
		ReportError(req, errors.Wrap(err, "failed to generate pdf file"))
		_ = r.Text(w, http.StatusInternalServerError, "Failed to generate pdf file")
		return
	}

	w.Header().Set("Content-Type", "application/pdf")
	w.Header().Set("Content-Disposition", "inline; filename=\"Contract_Cancellation_Quote.pdf\"")
	w.WriteHeader(http.StatusOK)
	err = fpdf.Output(w)
	if err != nil {
		err = errors.Wrap(err, "error writing estimate information to response")
		ReportError(req, err)
		_ = r.Text(w, http.StatusInternalServerError, "error writing estimate information to response")
		return
	}
}

// CancellationRequestForm returns contract cancellation form given contract list in PDF format
func CancellationRequestForm(w http.ResponseWriter, req *http.Request, user db.CurrentUser) {
	ctx := req.Context()

	contractID, err := strconv.Atoi(chi.URLParam(req, "id"))
	if err != nil {
		_ = r.Text(w, http.StatusBadRequest, "invalid contract ID")
		return
	}

	reqPayload := EstimateRequestInfo{}

	query := req.FormValue("q")
	if query == "" {
		_ = r.Text(w, http.StatusBadRequest, "invalid query")
		return
	}

	err = json.Unmarshal([]byte(query), &reqPayload)
	if err != nil {
		_ = r.Text(w, http.StatusBadRequest, "Bad request"+err.Error())
		return
	}

	if err = validateCancellationRequest(ctx, reqPayload, user, db.CancelRequestPrintQuote); err != nil {
		_ = r.Text(w, http.StatusBadRequest, err.Error())
		return
	}

	if len(reqPayload.Contracts) == 0 {
		_ = r.Text(w, http.StatusBadRequest, "Contract list for cancellation is empty")
		return
	}

	cr, err := db.GetCancelReasonByID(ctx, reqPayload.CancelReasonID)
	if err != nil {
		_ = r.Text(w, http.StatusBadRequest, "Unable to load Cancel Reason")
		return
	}
	reqPayload.CancelReasonName = cr.Name

	// Search contracts by contract ID
	contractDetails, err := ContractInfo(ctx, contractID, cr)
	if err != nil && !errors.Is(err, ErrDMSLookUpFailed) {
		_ = r.Text(w, http.StatusInternalServerError, "failed to fetch contract details")
		ReportError(req, errors.Wrapf(err, "failed to fetch contract details for ID %d", contractID))
		return
	}

	list := cancelContractList{}
	query, args, err := sqlx.In(`select code, product_type_name, original_code from contracts where id in (?)`, reqPayload.Contracts)
	if err != nil {
		_ = r.Text(w, http.StatusInternalServerError, "failed to fetch contract details for given list")
		ReportError(req, errors.Wrapf(err, "ContractCancellationRequestForm :error validating contracts to cancel with ID %d", contractID))
		return
	}
	query = db.Get().Rebind(query)
	err = db.Get().Select(&list, query, args...)
	if err != nil {
		_ = r.Text(w, http.StatusInternalServerError, "database error contract details for given list")
		ReportError(req, errors.Wrapf(err, "ContractCancellationRequestForm :database error contract details for given list with ID %d", contractID))
		return
	}
	if cr.IsFlatCancel {
		reqPayload.CancelContractOptions.CancelDate = types.JSPQDate{NullTime: pq.NullTime{Time: contractDetails.EffectiveDate.Time, Valid: true}}
		reqPayload.CancelContractOptions.Mileage = contractDetails.EffectiveMileage
	}

	stampedFile, err := cancellationRequestForm(reqPayload.CancelContractOptions, contractDetails, list)
	if err != nil {
		_ = r.Text(w, http.StatusInternalServerError, "failed to create contract cancellation form")
		ReportError(req, errors.Wrapf(err, "failed to create contract cancellation form with contract ID %d", contractID))
		return
	}
	defer func() {
		_ = stampedFile.Close()
		_ = os.Remove(stampedFile.Name())
	}()

	contractIDs := []string{}
	for _, contract := range list {
		contractIDs = append(contractIDs, contract.Code)
	}
	w.Header().Set("Content-Type", "application/pdf")
	w.Header().Set("Content-Disposition", fmt.Sprintf("inline; filename=\"cancel_requests/%d/%s/CancelRequestLDCS-{%s}.pdf\"", time.Now().UTC().Year(), time.Now().UTC().Month().String(), strings.Join(contractIDs, "_")))
	w.WriteHeader(http.StatusOK)
	_, err = io.Copy(w, stampedFile)
	if err != nil {
		err = errors.Wrap(err, "error writing estimate information to response")
		_ = r.Text(w, http.StatusInternalServerError, "error writing estimate information to response")
		ReportError(req, err)
		return
	}
}

// CancellationCheckRequestPdf returns cancellation check request for given contract list in PDF format
func CancellationCheckRequestPdf(w http.ResponseWriter, req *http.Request, user db.CurrentUser, validateStore, isAdmin, isManager bool) {
	ctx := req.Context()

	reqPayload := EstimateRequestInfo{}

	query := req.FormValue("q")
	if query == "" {
		_ = r.Text(w, http.StatusBadRequest, "invalid query")
		return
	}

	err := json.Unmarshal([]byte(query), &reqPayload)
	if err != nil {
		_ = r.Text(w, http.StatusBadRequest, "Bad request"+err.Error())
		return
	}

	if err = validateCancellationRequest(ctx, reqPayload, user, db.CancelRequestPrintQuote); err != nil {
		_ = r.Text(w, http.StatusBadRequest, err.Error())
		return
	}

	if len(reqPayload.Contracts) == 0 {
		_ = r.Text(w, http.StatusBadRequest, "Contract list for cancellation is empty")
		return
	}

	cr, err := db.GetCancelReasonByID(ctx, reqPayload.CancelReasonID)
	if err != nil {
		_ = r.Text(w, http.StatusBadRequest, "Unable to load Cancel Reason")
		return
	}
	reqPayload.UserID = user.ID
	reqPayload.CurrentDate = time.Now()
	reqPayload.CancelReasonName = cr.Name

	company, err := GetCompanyForContract(ctx, reqPayload.Contracts[0])
	if err != nil {
		_ = r.Text(w, http.StatusBadRequest, "Unable to load company")
		return
	}

	cancelEstimates := []CancelContractQuote{}
	contractDetails := []ContractDetails{}
	for _, contractID := range reqPayload.Contracts {
		contract, err := ContractInfo(ctx, contractID, cr)
		if err != nil && !errors.Is(err, ErrDMSLookUpFailed) {
			_ = r.Text(w, http.StatusInternalServerError, "failed to fetch contract details with ID "+strconv.Itoa(contract.ID))
			ReportError(req, errors.Wrapf(err, "failed to fetch contract details with ID %d", contract.ID))
			return
		}

		if contract.Status == db.ContractStatusCancelled {
			continue // skip already cancelled contracts
		}

		// get number of already processed claims
		closedClaims, err := db.GetFilteredClaims(ctx, contract.Code, contract.ProductTypeCode, closedStatuses)
		if err != nil {
			_ = r.Text(w, http.StatusInternalServerError, "Error in getting claims for contract "+contract.Code)
			ReportError(req, err)
			return
		}
		reqPayload.ClosedClaims = closedClaims
		contractDetails = append(contractDetails, contract)
		if cr.IsFlatCancel {
			reqPayload.CancelContractOptions.CancelDate = types.JSPQDate{NullTime: pq.NullTime{Time: contract.EffectiveDate.Time, Valid: true}}
			reqPayload.CancelContractOptions.Mileage = contract.EffectiveMileage
		}
		cancelEstimate, err := EstimateCancellation(contract, reqPayload.CancelContractOptions, validateStore, isAdmin, isManager, user, &company)
		if err != nil {
			_ = r.Text(w, http.StatusInternalServerError, "failed to calculate cancellation estimate with ID "+strconv.Itoa(contract.ID))
			ReportError(req, errors.Wrapf(err, "failed to calculate cancellation estimate with ID %d", contract.ID))
			return
		}
		cancelEstimates = append(cancelEstimates, cancelEstimate)
	}

	if len(cancelEstimates) == 0 {
		ReportError(req, errors.Wrap(err, "invalid list of contracts to print check request"))
		_ = r.Text(w, http.StatusNotFound, "invalid list of contract to print check request")
		return
	}

	stampedFile, err := checkRequestForm(contractDetails, cancelEstimates, reqPayload.CancelPayee, user)
	if err != nil {
		_ = r.Text(w, http.StatusInternalServerError, "failed to create contract cancel check request form")
		ReportError(req, errors.Wrap(err, "failed to create contract cancel check request form"))
		return
	}
	defer func() {
		_ = stampedFile.Close()
		_ = os.Remove(stampedFile.Name())
	}()

	w.Header().Set("Content-Type", "application/pdf")
	w.Header().Set("Content-Disposition", fmt.Sprintf("inline; filename=\"cancel_check_requests.pdf\""))
	w.WriteHeader(http.StatusOK)
	_, err = io.Copy(w, stampedFile)
	if err != nil {
		err = errors.Wrap(err, "error writing estimate information to response")
		_ = r.Text(w, http.StatusInternalServerError, "error writing estimate information to response")
		ReportError(req, err)
		return
	}
}

// checkRequestForm will make a contract request form pdf
func checkRequestForm(contractDetails []ContractDetails, ccInfo []CancelContractQuote, payee string, user db.CurrentUser) (*os.File, error) {
	if len(contractDetails) == 0 {
		return nil, errors.New("no contracts for print_check_request")
	}

	checkRequestData := struct {
		storeCode      string
		payeeName      string
		payeeAddress   string
		payeeVen       string
		vscCode        string
		vscAmount      decimal.Decimal
		mntCode        string
		mntAmount      decimal.Decimal
		gapCode        string
		gapAmount      decimal.Decimal
		veroGapCode    string
		veroGapAmount  decimal.Decimal
		tcaVtaCode     string
		tcaVtaAmount   decimal.Decimal
		nsdVtaCode     string
		nsdVtaAmount   decimal.Decimal
		tcaKeyCode     string
		tcaKeyAmount   decimal.Decimal
		nsdKeyCode     string
		nsdKeyAmount   decimal.Decimal
		nsdTwCode      string
		nsdTwAmount    decimal.Decimal
		tcaTwCode      string
		tcaTwAmount    decimal.Decimal
		nsdLwtCode     string
		nsdLwtAmount   decimal.Decimal
		tcaLwtCode     string
		tcaLwtAmount   decimal.Decimal
		drivePurCode   string
		drivePurAmount decimal.Decimal
		appCode        string
		appAmount      decimal.Decimal
		pdrCode        string
		pdrAmount      decimal.Decimal
		totalAmount    decimal.Decimal
		connectUser    string
		customerName   string
	}{
		storeCode:    contractDetails[0].StoreCode,
		connectUser:  user.FirstName + " " + user.LastName,
		customerName: contractDetails[0].CustomerFirstName + " " + contractDetails[0].CustomerLastName,
	}
	if contractDetails[0].CustomerIsBusiness {
		checkRequestData.customerName = checkRequestData.customerName + "/" + contractDetails[0].CustomerBusinessName.String
	}
	if payee == db.CancelPayeeTypeLender {
		for _, c := range contractDetails {
			if c.Lender != "" {
				checkRequestData.payeeName = c.Lender
				break
			}
		}
	} else if payee == db.CancelPayeeTypeCustomer {
		checkRequestData.payeeName = checkRequestData.customerName
		checkRequestData.payeeAddress = contractDetails[0].CustomerAddress + " " +
			contractDetails[0].CustomerCity + " " + contractDetails[0].CustomerPostalCode + " " + contractDetails[0].CustomerStateCode
	}
	var totalAmount decimal.Decimal
	for i, c := range contractDetails {
		switch c.ProductTypeCode {
		case db.ProductTypeCodeService:
			checkRequestData.vscCode += " " + c.OriginalCode
			checkRequestData.vscAmount = checkRequestData.vscAmount.Add(ccInfo[i].CustomerRefund)
		case db.ProductTypeCodeMaintenance:
			checkRequestData.mntCode += " " + c.OriginalCode
			checkRequestData.mntAmount = checkRequestData.mntAmount.Add(ccInfo[i].CustomerRefund)
		case db.ProductTypeCodeGuaranteedAssetProtection:
			if strings.Contains(strings.ToLower(c.ProductName), "vero") {
				checkRequestData.veroGapCode += " " + c.OriginalCode
				checkRequestData.veroGapAmount = checkRequestData.veroGapAmount.Add(ccInfo[i].CustomerRefund)
			} else {
				checkRequestData.gapCode += " " + c.OriginalCode
				checkRequestData.gapAmount = checkRequestData.gapAmount.Add(ccInfo[i].CustomerRefund)
			}
		case db.ProductTypeCodeVehicleTheftAssistance:
			if strings.Contains(strings.ToLower(c.ProductName), "nsd") {
				checkRequestData.nsdVtaCode += " " + c.OriginalCode
				checkRequestData.nsdVtaAmount = checkRequestData.nsdVtaAmount.Add(ccInfo[i].CustomerRefund)
			} else {
				checkRequestData.tcaVtaCode += " " + c.OriginalCode
				checkRequestData.tcaVtaAmount = checkRequestData.tcaVtaAmount.Add(ccInfo[i].CustomerRefund)
			}
		case db.ProductTypeCodeKeyRemoteReplacement:
			if strings.Contains(strings.ToLower(c.ProductName), "nsd") {
				checkRequestData.nsdKeyCode += " " + c.OriginalCode
				checkRequestData.tcaVtaAmount = checkRequestData.tcaVtaAmount.Add(ccInfo[i].CustomerRefund)
			} else {
				checkRequestData.tcaKeyCode += " " + c.OriginalCode
				checkRequestData.tcaKeyAmount = checkRequestData.tcaKeyAmount.Add(ccInfo[i].CustomerRefund)
			}
		case db.ProductTypeCodeTireAndWheel:
			if strings.Contains(strings.ToLower(c.ProductName), "nsd") {
				checkRequestData.nsdTwCode += " " + c.OriginalCode
				checkRequestData.nsdTwAmount = checkRequestData.nsdTwAmount.Add(ccInfo[i].CustomerRefund)
			} else {
				checkRequestData.tcaTwCode += " " + c.OriginalCode
				checkRequestData.tcaTwAmount = checkRequestData.tcaTwAmount.Add(ccInfo[i].CustomerRefund)
			}
		case db.ProductTypeCodeLeaseWearAndTear:
			if strings.Contains(strings.ToLower(c.ProductName), "nsd") {
				checkRequestData.nsdLwtCode += " " + c.OriginalCode
				checkRequestData.nsdLwtAmount = checkRequestData.nsdLwtAmount.Add(ccInfo[i].CustomerRefund)
			} else {
				checkRequestData.tcaLwtCode += " " + c.OriginalCode
				checkRequestData.tcaLwtAmount = checkRequestData.tcaLwtAmount.Add(ccInfo[i].CustomerRefund)
			}
		case db.ProductTypeCodeDrivePur:
			checkRequestData.drivePurCode += " " + c.OriginalCode
			checkRequestData.drivePurAmount = checkRequestData.drivePurAmount.Add(ccInfo[i].CustomerRefund)
		case db.ProductTypeCodeAppearanceProtection:
			checkRequestData.appCode += " " + c.OriginalCode
			checkRequestData.appAmount = checkRequestData.appAmount.Add(ccInfo[i].CustomerRefund)
		case db.ProductTypeCodePaintlessDentRepair:
			checkRequestData.pdrCode += " " + c.OriginalCode
			checkRequestData.pdrAmount = checkRequestData.pdrAmount.Add(ccInfo[i].CustomerRefund)
		}
		totalAmount = totalAmount.Add(ccInfo[i].CustomerRefund)
	}
	checkRequestData.totalAmount = totalAmount

	stampedFile, err := ioutil.TempFile("", "temp-contract-cancel-check-request")
	if err != nil {
		return nil, errors.Wrap(err, "error creating temp file to stamp cancel_check_request form")
	}
	fields := map[string]string{
		"check_request_date": time.Now().UTC().Format(db.LongDateFormat),
		"store_code":         checkRequestData.storeCode,
		"payee_name":         checkRequestData.payeeName,
		"payee_address":      checkRequestData.payeeAddress,
		"payee_ven":          checkRequestData.payeeVen,
		"vsc_code":           checkRequestData.vscCode,
		"mnt_code":           checkRequestData.mntCode,
		"gap_code":           checkRequestData.gapCode,
		"vero_gap_code":      checkRequestData.veroGapCode,
		"tca_vta_code":       checkRequestData.tcaVtaCode,
		"nsd_vta_code":       checkRequestData.nsdVtaCode,
		"tca_key_code":       checkRequestData.tcaKeyCode,
		"nsd_key_code":       checkRequestData.nsdKeyCode,
		"nsd_tw_code":        checkRequestData.nsdTwCode,
		"tca_tw_code":        checkRequestData.tcaTwCode,
		"nsd_lwt_code":       checkRequestData.nsdLwtCode,
		"tca_lwt_code":       checkRequestData.tcaLwtCode,
		"drive_pur_code":     checkRequestData.drivePurCode,
		"app_code":           checkRequestData.appCode,
		"pdr_code":           checkRequestData.pdrCode,
		"vsc_amount":         checkRequestData.vscAmount.StringFixed(2),
		"mnt_amount":         checkRequestData.mntAmount.StringFixed(2),
		"gap_amount":         checkRequestData.gapAmount.StringFixed(2),
		"vero_gap_amount":    checkRequestData.veroGapAmount.StringFixed(2),
		"tca_vta_amount":     checkRequestData.tcaVtaAmount.StringFixed(2),
		"nsd_vta_amount":     checkRequestData.nsdVtaAmount.StringFixed(2),
		"tca_key_amount":     checkRequestData.tcaKeyAmount.StringFixed(2),
		"nsd_key_amount":     checkRequestData.nsdKeyAmount.StringFixed(2),
		"nsd_tw_amount":      checkRequestData.nsdTwAmount.StringFixed(2),
		"tca_tw_amount":      checkRequestData.tcaTwAmount.StringFixed(2),
		"nsd_lwt_amount":     checkRequestData.nsdLwtAmount.StringFixed(2),
		"tca_lwt_amount":     checkRequestData.tcaLwtAmount.StringFixed(2),
		"drive_pur_amount":   checkRequestData.drivePurAmount.StringFixed(2),
		"app_amount":         checkRequestData.appAmount.StringFixed(2),
		"pdr_amount":         checkRequestData.pdrAmount.StringFixed(2),
		"total_amount":       checkRequestData.totalAmount.StringFixed(2),
		"connect_user":       checkRequestData.connectUser,
		"customer_name":      checkRequestData.customerName,
	}
	err = pdftk.FillForm("files/Closed_Store_Check_Request.pdf", fields, stampedFile.Name())
	if err != nil {
		return nil, errors.Wrap(err, "error stamping cancel_check_request")
	}
	return stampedFile, nil
}
