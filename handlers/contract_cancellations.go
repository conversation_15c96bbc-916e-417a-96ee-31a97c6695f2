package handlers

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"whiz/db"
	"whiz/dms"
	"whiz/dmsfactory"
	"whiz/nsd"
	"whiz/randstr"
	"whiz/s3util"
	"whiz/types"
	"whiz/util"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"github.com/lib/pq"
	newrelic "github.com/newrelic/go-agent"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"gopkg.in/guregu/null.v3"
)

const (
	typePrDays        = "PR_DAYS"        // Pro rata based on days remaining
	typePrMiles       = "PR_MILES"       // Pro rata based on miles remaining
	typePrClaims      = "PR_CLAIMS"      // Pro rata based on claims remaining
	type78s           = "RULE_OF_78s"    // Rule of 78s
	typeContractPrice = "CONTRACT_PRICE" // Contract price
)

const (
	claimStatusCheckWritten = "CheckWritten"
	claimStatusCCPaid       = "CCPaid"
	claimStatusDenied       = "Denied"
	claimStausOpen          = "Open"
)

// ErrDMSLookUpFailed represents a DMS Lookup Error
var (
	ErrDMSLookUpFailed = fmt.Errorf("dms lookup failed")
)

const msgCDKLookUpFailed = "Unable to retrieve sales tax, try again later or add sales tax to continue with cancellation."

// CancelContractOptions contains the data entered by the user when canceling a contract
type CancelContractOptions struct {
	CancelDate             types.JSPQDate  `json:"cancel_date"`
	CurrentDate            time.Time       `json:"-"`
	CancelReasonID         int             `json:"cancel_reason_id"`
	CancelReasonName       string          `json:"-"`
	Mileage                int             `json:"mileage"`
	StoreID                int             `json:"store_id"`
	UserID                 int             `json:"user_id"`
	SPPBalance             decimal.Decimal `json:"spp_balance"`
	SPPCustomerPaid        decimal.Decimal `json:"spp_customer_paid"`
	ManualTaxRate          decimal.Decimal `json:"manual_tax_rate"`
	NSDKeyClaims           decimal.Decimal `json:"nsd_key_claims"`
	NSDTireAndWheelClaims  decimal.Decimal `json:"nsd_tire_and_wheel_claims"`
	NSDVTAClaims           decimal.Decimal `json:"nsd_vta_claims"`
	ClosedClaims           []db.Claim      `db:"-" json:"claims"`
	RefundType             string          `db:"refund_type" json:"refund_type"`
	CancelPayee            string          `json:"cancel_payee"`
	CancelPayeeName        string          `json:"cancel_payee_name"`
	CancelPayeeAttentionTo string          `json:"cancel_payee_attention_to"`
	CancelPayeeAddress     string          `json:"cancel_payee_address"`
	CancelPayeeCity        string          `json:"cancel_payee_city"`
	CancelPayeeState       string          `json:"cancel_payee_state"`
	CancelPayeePostalCode  string          `json:"cancel_payee_postal_code"`
	SkipBill               bool            `json:"skip_bill"`
	ApplyFee               bool            `json:"apply_fee"`
	IsElectronicCheck      bool            `json:"is_electronic_check"`
	Email                  string          `json:"email"`
}

// CancelContractQuote contains refund details for a contract
// that will be or has been canceled
type CancelContractQuote struct {
	ID                     int                               `json:"id"`
	Code                   string                            `json:"code"`
	Status                 string                            `json:"status"`
	ProductTypeCode        string                            `json:"product_type_code"`
	ProductTypeName        string                            `json:"product_type_name"`
	ProductTypeID          int                               `json:"product_type_id"`
	StoreCode              string                            `json:"store_code"`
	EffectiveDate          types.JSPQNullDate                `json:"effective_date"`
	PlanCost               decimal.Decimal                   `json:"plan_cost"`
	Cost                   decimal.Decimal                   `json:"cost"`
	Price                  decimal.Decimal                   `json:"price"`
	Factor                 string                            `json:"factor"`
	AllFactors             []string                          `json:"all_factors"`
	FactorPercent          decimal.Decimal                   `json:"-"`
	Ranking                int                               `json:"ranking"`
	Cancellable            bool                              `json:"cancellable"`
	RuleViolations         []stopRuleViolation               `json:"rule_violations"`
	CancelStopRules        []cancelStopRule                  `json:"cancel_stop_rules"`
	Fee                    decimal.Decimal                   `json:"fee"`
	PercentMonthsUsed      decimal.Decimal                   `json:"percent_months_used"`
	PercentMilesUsed       decimal.Decimal                   `json:"percent_miles_used"`
	CustomerRefund         decimal.Decimal                   `json:"customer_refund"`
	SalesTaxRate           decimal.Decimal                   `json:"sales_tax_rate"`
	SalesTax               decimal.Decimal                   `json:"sales_tax"`
	AdjustedCustomerRefund decimal.Decimal                   `json:"adjusted_customer_refund"`
	StoreRefund            decimal.Decimal                   `json:"store_refund"`
	StoreChargeback        decimal.Decimal                   `json:"store_chargeback"`
	ThirdPartyRefund       decimal.Decimal                   `json:"third_party_refund"`
	RSARefund              decimal.Decimal                   `json:"rsa_refund"`
	SPPRefund              decimal.Decimal                   `json:"spp_refund"`
	ClaimCount             int                               `json:"claim_count"`
	ClaimTotalAmount       decimal.Decimal                   `json:"claim_total_amount"`
	ClaimsDeductedAmount   decimal.Decimal                   `json:"claims_amount_deducted"`
	ClaimsDeducted         bool                              `json:"claims_deducted"`
	CancelRuleName         string                            `json:"cancel_rule_name"`
	CancelRuleID           int                               `json:"cancel_rule_id"`
	OriginalCode           string                            `json:"original_code"`
	Breakouts              []db.ContractCancellationBreakout `json:"breakouts"`
	PaymentType            string                            `json:"payment_type"`
}

// ContractDetails contains all of the information about a contract
// that is needed when the contract is canceled
type ContractDetails struct {
	ID                         int                `db:"id" json:"id"`
	Code                       string             `db:"code" json:"code"`
	ContractDate               types.JSPQNullDate `db:"contract_date" json:"contract_date"`
	EffectiveDate              types.JSPQNullDate `db:"effective_date" json:"effective_date"`
	ExpirationDate             types.JSPQNullDate `db:"expiration_date" json:"expiration_date"`
	EffectiveMileage           int                `db:"effective_mileage" json:"effective_mileage"`
	ExpirationMileage          null.Int           `db:"expiration_mileage" json:"expiration_mileage"`
	StoreID                    int                `db:"store_id"`
	StoreCode                  string             `db:"store_code" json:"store_code"`
	Price                      decimal.Decimal    `db:"price" json:"price"`
	PlanCost                   decimal.Decimal    `db:"plan_cost" json:"plan_cost"` // plan cost
	Cost                       decimal.Decimal    `db:"-" json:"cost"`              // total cost included options, surcharges, adjustments
	CustomerID                 int                `db:"customer_id" json:"customer_id"`
	CustomerFirstName          string             `db:"customer_first_name" json:"customer_first_name"`
	CustomerLastName           string             `db:"customer_last_name" json:"customer_last_name"`
	CustomerIsBusiness         bool               `db:"customer_is_business" json:"customer_is_business"`
	CustomerBusinessName       null.String        `db:"customer_business_name" json:"customer_business_name"`
	CustomerPhone              string             `db:"customer_phone" json:"customer_phone"`
	CustomerAddress            string             `db:"customer_address" json:"customer_address"`
	CustomerCity               string             `db:"customer_city" json:"customer_city"`
	CustomerStateCode          string             `db:"customer_state_code" json:"customer_state_code"`
	CustomerPostalCode         string             `db:"customer_postal_code" json:"customer_postal_code"`
	CustomerEmail              string             `db:"customer_email" json:"customer_email"`
	Status                     string             `db:"status" json:"status"`
	FinanceTerm                null.Int           `db:"finance_term" json:"finance_term"`
	ProductTypeCode            string             `db:"product_type_code" json:"product_type_code"`
	ProductTypeName            string             `db:"product_type_name" json:"product_type_name"`
	ProductTypeID              int                `db:"product_type_id" json:"product_type_id"`
	ProductVariantID           null.Int           `db:"product_variant_id" json:"product_variant_id"`
	ProductID                  int                `db:"product_id" json:"product_id"`
	ProductName                string             `db:"product_name" json:"product_name"`
	ProductCancellableByStores bool               `db:"product_cancellable_by_stores" json:"product_cancellable_by_stores"`
	ContractFormID             null.Int           `db:"contract_form_id" json:"contract_form_id"`
	ActiveContractFormID       null.Int           `db:"active_contract_form_id" json:"active_contract_form_id"`
	Lender                     string             `db:"lender" json:"lender"`
	LenderAttention            string             `db:"lender_attention" json:"lender_attention"`
	LenderAddress              string             `db:"lender_address" json:"lender_address"`
	LenderCity                 string             `db:"lender_city" json:"lender_city"`
	LenderState                string             `db:"lender_state" json:"lender_state"`
	LenderVendorID             string             `db:"lender_vendor_id" json:"lender_vendor_id"`
	PaymentType                string             `db:"payment_type" json:"payment_type"`
	StoreStateCode             string             `db:"store_state_code" json:"store_state_code"`
	FirstPaymentDate           types.JSPQNullDate `db:"first_payment_date" json:"first_payment_date"`
	PlanDuration               int                `db:"plan_duration" json:"plan_duration"`
	PlanMileage                null.Int           `db:"plan_mileage" json:"plan_mileage"`
	SoldDate                   types.JSPQNullDate `db:"sold_date" json:"sold_date"`
	VIN                        string             `db:"vin" json:"vin"`
	VehicleYear                int                `db:"vehicle_year" json:"vehicle_year"`
	VehicleMake                string             `db:"vehicle_make" json:"vehicle_make"`
	VehicleModel               string             `db:"vehicle_model" json:"vehicle_model"`
	MaintenanceVisits          null.Int           `db:"maintenance_visits" json:"maintenance_visits"`
	ClaimsCount                int                `db:"-" json:"claims_count"`
	ClaimsAmount               decimal.Decimal    `db:"-" json:"claims_amount"`
	ClaimInProcess             bool               `db:"-" json:"claim_in_process"`
	IsInvoiced                 bool               `db:"-" json:"is_invoiced"`
	IsEWUToyotaOrLexus         bool               `db:"-" json:"-"`
	// TODO: Remove Admin Cost and calculate it from options, adjustments, surcharges
	AdminCost                   decimal.Decimal                 `db:"-" json:"-"`
	Surcharges                  []db.ContractSurcharge          `db:"-" json:"surcharges"`
	Options                     []db.ContractOption             `db:"-" json:"options"`
	Adjustments                 []db.ContractAdjustment         `db:"-" json:"adjustments"`
	RateBuckets                 []db.RateBucket                 `db:"-" json:"rate_buckets"`
	RateBucketRefundSettings    []db.RateBucketRefundSetting    `db:"-" json:"rate_bucket_refund_settings"`
	RateBucketReinstateSettings []db.RateBucketReinstateSetting `db:"-" json:"rate_bucket_reinstate_settings"`
	OriginalCode                string                          `db:"original_code" json:"original_code"`
	ProviderID                  null.Int                        `db:"provider_id" json:"provider_id"`
	ThirdPartyRemit             decimal.Decimal                 `db:"-" json:"third_party_remit"`
	ThirdPartyRateBucketIDs     []int                           `db:"-" json:"third_party_rate_bucket_ids"`
	RefundProviderForCancel     bool                            `db:"-" json:"-"`
	RSARemit                    decimal.Decimal                 `db:"-" json:"rsa_remit"`
	RSAProviderID               null.Int                        `db:"rsa_provider_id" json:"-"`
	CoreRateBucketID            int                             `db:"core_rate_bucket_id" json:"-"`
	DMSNumber                   string                          `db:"dms_number" json:"-"`
	SaleType                    string                          `db:"sale_type" json:"-"`
	SalesTaxRate                decimal.Decimal                 `db:"-" json:"-"`
	UsePreviousCancelForm       bool                            `db:"use_previous_cancel_form" json:"use_previous_cancel_form"`
	// InvoicedAt                 types.JSPQNullDate `db:"-" json:"invoiced_at"`
	// TODO: do we need CaseReserveAmount???
	// TODO: Do we need Issuing Dealer???
}

// EstimateRequestInfo contains the information needed to request a cancellation estimate
type EstimateRequestInfo struct {
	CancelContractOptions
	Contracts []int `json:"contracts"`
	// Add per-contract payee info (contract_id -> payee fields)
	ContractPayees map[int]struct {
		Payee            string `json:"payee"`
		PayeeName        string `json:"payee_name"`
		PayeeAttentionTo string `json:"payee_attention_to"`
		PayeeAddress     string `json:"payee_address"`
		PayeeCity        string `json:"payee_city"`
		PayeeState       string `json:"payee_state"`
		PayeePostalCode  string `json:"payee_postal_code"`
		CancelStoreID    string `json:"cancel_store_id"`
	} `json:"contract_payees"`
	ContractNotes map[int]string `json:"contract_notes"`
}

// Adapted from https://golangr.com/difference-between-two-dates/
func dateDiff(a, b time.Time) (month, day int) {
	if a.Location() != b.Location() {
		b = b.In(a.Location())
	}
	if a.After(b) {
		a, b = b, a
	}
	y1, M1, d1 := a.Date()
	y2, M2, d2 := b.Date()

	h1, m1, s1 := a.Clock()
	h2, m2, s2 := b.Clock()

	year := int(y2 - y1)
	month = int(M2 - M1)
	day = int(d2 - d1)
	hour := int(h2 - h1)
	min := int(m2 - m1)
	sec := int(s2 - s1)

	// Normalize negative values
	if sec < 0 {
		sec += 60
		min--
	}
	if min < 0 {
		min += 60
		hour--
	}
	if hour < 0 {
		hour += 24
		day--
	}
	if day < 0 {
		// days in month:
		t := time.Date(y1, M1, 32, 0, 0, 0, 0, time.UTC)
		day += 32 - t.Day()
		month--
	}
	if month < 0 {
		month += 12
		year--
	}

	month += year * 12

	return
}

type cancelFactor struct {
	percent         decimal.Decimal
	factorType      string
	description     string
	deductClaims    bool
	calculationType string
}

func calcCancelFactors(
	contract ContractDetails,
	cancelOptions CancelContractOptions,
	rule db.CancelRule,
) ([]cancelFactor, error) {
	var factors []cancelFactor
	var err error

	for _, cr := range rule.CancelCalculations {
		var factor cancelFactor
		factor.factorType = cr.Name
		factor.deductClaims = cr.DeductClaims
		factor.calculationType = cr.Name
		switch cr.Name {
		case typePrDays:
			factor.percent, err = calcProRataDaysCancelFactor(
				contract.EffectiveDate.Time, contract.ExpirationDate.Time, cancelOptions.CancelDate.Time)
			if err != nil {
				return nil, errors.Wrap(err, "failed to calculate cancellation with pro rata days")
			}
			factor.description = (decimal.NewFromFloat(1).Sub(factor.percent)).Mul(decimal.NewFromFloat(100)).String() + "% days used"
		case typePrMiles:
			factor.percent, err = calcProRataMilesCancelFactor(
				int64(contract.EffectiveMileage), contract.ExpirationMileage.Int64, int64(cancelOptions.Mileage))
			if err != nil {
				return nil, errors.Wrap(err, "failed to calculate cancellation with pro rata miles")
			}
			factor.description = (decimal.NewFromFloat(1).Sub(factor.percent)).Mul(decimal.NewFromFloat(100)).String() + "% miles used"
		case typePrClaims:
			switch contract.ProductTypeCode {
			case db.ProductTypeCodeDrivePur:
				totalClaims := int64(contract.PlanDuration / 12) // 1 claim per year
				factor.percent, err = calcProRataClaimsCancelFactor(totalClaims, int64(contract.ClaimsCount))
				if err != nil {
					return nil, errors.Wrap(err, "failed to calculate cancellation with pro rata claims")
				}

				factor.description = (decimal.NewFromFloat(1).Sub(factor.percent)).Mul(decimal.NewFromFloat(100)).String() + "% applications used"
			case db.ProductTypeCodeMaintenance:
				factor.percent, err = calcProRataClaimsCancelFactor(int64(contract.MaintenanceVisits.ValueOrZero()), int64(contract.ClaimsCount))
				if err != nil {
					return nil, errors.Wrap(err, "failed to calculate cancellation with pro rata claims")
				}
				factor.description = (decimal.NewFromFloat(1).Sub(factor.percent)).Mul(decimal.NewFromFloat(100)).String() + "% claims used"
			default:
				continue
			}
		case typeContractPrice:
			factor.percent = decimal.NewFromFloat(1.0)
		case type78s:
			expDate := contract.EffectiveDate.Time.AddDate(0, int(contract.FinanceTerm.Int64), 0)
			totalDays := decimal.NewFromFloat(expDate.Sub(contract.EffectiveDate.Time).Hours()).Div(decimal.NewFromFloat(24))
			daysRemaining := decimal.NewFromFloat(expDate.Sub(cancelOptions.CancelDate.Time).Hours()).Div(decimal.NewFromFloat(24))
			factor.percent, err = calcReverse78sCancelFactor(totalDays, daysRemaining)
			if err != nil {
				return nil, errors.Wrap(err, "failed to calculate cancellation with pro rata rule of 78")
			}
		default:
			continue
		}

		factors = append(factors, factor)

	}

	return factors, nil
}

type cancelQuote struct {
	storeRefund            decimal.Decimal
	storeChargeback        decimal.Decimal
	customerRefund         decimal.Decimal
	salesTax               decimal.Decimal
	adjustedCustomerRefund decimal.Decimal
	thirdPartyRefund       decimal.Decimal
	rsaRefund              decimal.Decimal
	sppRefund              decimal.Decimal
	claimsReduction        decimal.Decimal
	factorPercent          decimal.Decimal
	factor                 string
	claimsDeducted         bool
}

func cancellationInfo(
	cancelOptions CancelContractOptions,
	contract ContractDetails,
	rule db.CancelRule,
	isFlatCancellable bool,
	cancellationEstimates *CancelContractQuote,
	company *db.Company,
) error {
	var err error
	refundableCost := contract.Cost

	refundableCost, err = calcRefundableCost(contract)
	if err != nil {
		return errors.Wrapf(err, "unable to calculate refundable cost for contract %s", contract.Code)
	}

	cancellationEstimates.Cost = refundableCost
	cancellationEstimates.Price = contract.Price

	// We will just use admin cost for the EWU ToyotaOrLexus
	if contract.IsEWUToyotaOrLexus {
		// TODO: Is this still necessary now that we are pre-calculating the
		// refundable cost based on rate bucket settings
		cancellationEstimates.Cost = contract.AdminCost
	}

	if contract.ExpirationMileage.Valid {
		milesRemaining, err := calcProRataMilesCancelFactor(
			int64(contract.EffectiveMileage), contract.ExpirationMileage.Int64, int64(cancelOptions.Mileage))
		if err != nil {
			return errors.Wrap(err, "error calculating percent miles used")
		}
		cancellationEstimates.PercentMilesUsed = (decimal.NewFromFloat(1).Sub(milesRemaining)).Mul(decimal.NewFromFloat(100))
	}

	if contract.ExpirationDate.Valid && contract.EffectiveDate.Valid {
		daysRemaining, err := calcProRataDaysCancelFactor(
			contract.EffectiveDate.Time, contract.ExpirationDate.Time, cancelOptions.CancelDate.Time)
		if err != nil {
			return errors.Wrap(err, "error calculating percent miles used")
		}
		cancellationEstimates.PercentMonthsUsed = (decimal.NewFromFloat(1).Sub(daysRemaining)).Mul(decimal.NewFromFloat(100))
	}

	var cfs []cancelFactor
	if isFlatCancellable {
		var cf cancelFactor
		cf.deductClaims = rule.FlatCancelDeductClaims
		cf.percent = decimal.NewFromFloat(1.0) // Full refund
		if rule.FlatCancelDeductClaims && contract.ProductTypeCode == db.ProductTypeCodeDrivePur {
			// Notes : For drive pur contracts, cost and price of the contract is based on number of applications remaining
			// For example : If contract cost was $1000 and price was $2000 with 10 applications and at the time of the cancellation
			// two application is already been used then contract cost would be `Cost - (Cost/Applications)*Applications used`
			// and Price would be `Price - (Price/Applications)*Applications used`
			// Using above formula Cost = 1000 - (1000/10)*2 = 1000 - 200 = 800
			//                     Price = 2000 - (2000/10)*2 = 2000 - 400 = 1600
			// Since the amount deducted from the cost for each claim differs from the amount deducted
			// from the price for each claim, instead of calculating the value of the claims, we build it into
			// the cancel factor.
			totalApplications := int64(contract.PlanDuration / 12) // 1 claim per year
			cf.percent, err = calcProRataClaimsCancelFactor(totalApplications, int64(contract.ClaimsCount))
			if err != nil {
				return errors.Wrap(err, "failed to calculate cancellation with pro rata claims")
			}
		}
		cfs = append(cfs, cf)
	} else {
		cfs, err = calcCancelFactors(contract, cancelOptions, rule)
		if err != nil {
			return errors.Wrap(err, "failed to calculate cancel factors for cancel rule")
		}
	}

	var minRefundQuote *cancelQuote
	var estimates []cancelQuote
	for _, f := range cfs {
		var estimate cancelQuote
		estimate.factor = f.description
		estimate.factorPercent = f.percent

		estimate.claimsReduction = decimal.Zero
		if isFlatCancellable {
			if rule.FlatCancelDeductClaims && contract.ProductTypeCode != db.ProductTypeCodeDrivePur {
				estimate.claimsReduction = cancellationEstimates.ClaimTotalAmount
			}
		} else if f.deductClaims {
			estimate.claimsReduction = cancellationEstimates.ClaimTotalAmount
		}

		estimate.storeRefund = (f.percent.Mul(cancellationEstimates.Cost)).
			Sub(cancellationEstimates.Fee).
			Sub(estimate.claimsReduction).
			Round(2)
		estimate.customerRefund = (f.percent.Mul(cancellationEstimates.Price)).
			Sub(cancellationEstimates.Fee).
			Sub(estimate.claimsReduction).
			Round(2)
		if estimate.claimsReduction.GreaterThan(decimal.Zero) {
			estimate.claimsDeducted = true
		}

		if estimate.storeRefund.LessThan(decimal.Zero) {
			estimate.storeRefund = decimal.Zero
		}
		if estimate.customerRefund.LessThan(decimal.Zero) {
			estimate.customerRefund = decimal.Zero
		}

		var sppRefund decimal.Decimal
		var storeChargeback decimal.Decimal
		estimate.adjustedCustomerRefund = estimate.customerRefund
		var adjustedCustomerRefund = estimate.customerRefund

		// Calculate SPP values if payment type is SPP
		if contract.PaymentType == dms.PaymentTypeSPP &&
			(contract.ProductTypeCode == db.ProductTypeCodeService || contract.ProductTypeCode == db.ProductTypeCodeMaintenance) {
			// If SPP is owed more than the customer is due, then set the customer amount
			// to the SPP balance in order to make SPP whole, and then create a chargeback
			// to the store to cover the extra amount that is going to SPP.
			// The chargeback to the store is automatically handled by the Accounting rules
			// for SPP contracts.
			if cancelOptions.SPPBalance.GreaterThan(adjustedCustomerRefund) {
				adjustedCustomerRefund = cancelOptions.SPPBalance
			}

			// Only allow a refund up to the contract price.  The amount owed SPP could have
			// been greater than the contract price because combined SPP financing
			// is used for service and maintenance.  If the customer amount is > contract price
			// then the rest would need to come from the maintenance contract.
			// TODO: Figure out how to handle SPP refunds that need to come from the maintenance claim.
			// It will probably be done by allowing TCA to manually edit the customer refund amount on
			// the refund page
			if adjustedCustomerRefund.GreaterThan(contract.Price) {
				adjustedCustomerRefund = contract.Price
			}

			sppRefund = adjustedCustomerRefund
			storeChargeback = estimate.storeRefund.Sub(adjustedCustomerRefund)
		}
		estimate.storeChargeback = storeChargeback
		estimate.sppRefund = sppRefund
		estimate.adjustedCustomerRefund = adjustedCustomerRefund
		estimate.rsaRefund = contract.RSARemit

		// Keep track of the factor which results in the smallest refund to the customer
		if minRefundQuote == nil || minRefundQuote.customerRefund.GreaterThan(estimate.customerRefund) {
			minRefundQuote = &estimate
		}

		estimates = append(estimates, estimate)
	}

	if len(estimates) == 0 {
		return errors.New("no rule is applicable for contract")
	}

	for _, f := range estimates {
		cancellationEstimates.AllFactors = append(cancellationEstimates.AllFactors, f.factor)
	}

	cancellationEstimates.StoreRefund = minRefundQuote.storeRefund.Round(2)
	cancellationEstimates.StoreChargeback = minRefundQuote.storeChargeback.Round(2)
	cancellationEstimates.CustomerRefund = minRefundQuote.customerRefund.Round(2)
	cancellationEstimates.SalesTaxRate = contract.SalesTaxRate
	if isTaxApplicable(contract, &db.CancelReason{ID: cancelOptions.CancelReasonID, Name: cancelOptions.CancelReasonName}, company) {
		if cancellationEstimates.SalesTaxRate.Equals(decimal.Zero) {
			cancellationEstimates.SalesTaxRate = cancelOptions.ManualTaxRate
		}
	}

	cancellationEstimates.SalesTax =
		cancellationEstimates.CustomerRefund.Add(
			cancellationEstimates.Fee).Mul(
			cancellationEstimates.SalesTaxRate).Div(
			decimal.NewFromFloat(100)).Round(2)
	cancellationEstimates.AdjustedCustomerRefund = minRefundQuote.adjustedCustomerRefund.Round(2)
	cancellationEstimates.SPPRefund = minRefundQuote.sppRefund.Round(2)
	cancellationEstimates.Factor = minRefundQuote.factor
	cancellationEstimates.FactorPercent = minRefundQuote.factorPercent.Round(6)
	cancellationEstimates.ClaimsDeducted = minRefundQuote.claimsDeducted
	cancellationEstimates.ClaimsDeductedAmount = minRefundQuote.claimsReduction
	cancellationEstimates.Breakouts, err = GetRefundBreakout(
		minRefundQuote.factorPercent, contract, minRefundQuote.storeRefund,
		cancelOptions.CancelDate.NullTime.Time, isFlatCancellable,
		cancellationEstimates.Fee, minRefundQuote.claimsReduction)
	if err != nil {
		return errors.Wrap(err, "error calculating refund rate bucket breakouts")
	}

	// 3rd party refund was calculated while calculating cost breakouts
	// Pull the value out of the breakouts
	for _, rbID := range contract.ThirdPartyRateBucketIDs {
		for _, bo := range cancellationEstimates.Breakouts {
			if bo.RateBucketID == rbID {
				cancellationEstimates.ThirdPartyRefund = cancellationEstimates.ThirdPartyRefund.Add(bo.Amount)
			}
		}
	}

	// RSA remit was calculated while calculating cost breakouts
	// Pull the value out of the breakouts
	rsaRemit := decimal.Zero
	for _, b := range cancellationEstimates.Breakouts {
		if b.RateBucketID == db.RateBucketRsaID {
			rsaRemit = rsaRemit.Add(b.Amount)
		}
	}
	cancellationEstimates.RSARefund = rsaRemit.Round(2)

	return nil
}

// GetRefundBreakout calculates the amount of the refund coming from each rate bucket
func GetRefundBreakout(
	factor decimal.Decimal,
	contract ContractDetails,
	storeRefund decimal.Decimal,
	cancelDate time.Time,
	isFlatCancel bool,
	fee decimal.Decimal,
	claimsReduction decimal.Decimal,
) ([]db.ContractCancellationBreakout, error) {
	var bos []db.ContractCancellationBreakout

	// 1 Apply cancelFactor to all ratebuckets that are refundable and which are
	// refunded as pro-rata (except tca-admin)
	// 2. Refund full amount to rate buckets that are configured as fully refundable
	// 3 NOTE: Some rate buckets may not be refundable.
	// 4. tca-admin = storeRefund - ProrataRefund - FullyRefundablePart

	// Determine the amount of the refund which the cancel factor needs to be applied to
	for _, v := range contract.Adjustments {
		// Exclude TCA-Admin
		if v.RateBucketID == db.RateBucketAdminID {
			continue
		}
		adjustmentCost := v.Cost
		// RSA Cancel refunds have special rules
		// For old RSA Product Prior to 4/8/2024
		// 	1. If canceled <= 120 days past effective date, then refund 100% to RSA bucket
		// 	2. If canceled > 120 days past effective date, then no refund
		//
		// For New RSA Product After 4/8/2024
		//	1. If canceled <= 30 days past effective date, then refund 100% to RSA bucket
		//	2. If canceled > 30 days past effective date, then no refund
		if v.RateBucketID == db.RateBucketRsaID && contract.EffectiveDate.NullTime.Valid {
			newProductDate, err := time.Parse(time.RFC3339, db.NewRSAProductDate)
			if err != nil {
				return nil, errors.Wrapf(err, "error parsing the new product date %s", db.NewRSAProductDate)
			}

			cancelDays := db.ContractProviderRSACancelDays
			if contract.EffectiveDate.Time.After(newProductDate) {
				cancelDays = db.ContractProviderRSACancelDaysNew
			}
			if cancelDate.After(contract.EffectiveDate.NullTime.Time.AddDate(0, 0, cancelDays)) {
				adjustmentCost = decimal.Zero
			}
		}

		bo, err := createBreakout(adjustmentCost, v.RateBucketID, isFlatCancel, contract.ContractDate, contract.RateBuckets, contract.RateBucketRefundSettings)
		if err != nil {
			return nil, errors.Wrap(err, "error creating contract_cancellation_breakout")
		}
		if bo == nil || bo.Amount.Equal(decimal.Zero) {
			continue // Rate bucket was not refundable or the amount was $0.00 so skip it.
		}
		bos = append(bos, *bo)
	}
	for _, v := range contract.Options {
		// Exclude TCA-Admin
		if v.RateBucketID == db.RateBucketAdminID {
			continue
		}
		bo, err := createBreakout(v.Cost, v.RateBucketID, isFlatCancel, contract.ContractDate, contract.RateBuckets, contract.RateBucketRefundSettings)
		if err != nil {
			return nil, errors.Wrap(err, "error creating contract_cancellation_breakout")
		}
		if bo == nil || bo.Amount.Equal(decimal.Zero) {
			continue // Rate bucket was not refundable or the amount was $0.00 so skip it.
		}
		bos = append(bos, *bo)
	}
	for _, v := range contract.Surcharges {
		// Exclude TCA-Admin
		if v.RateBucketID == db.RateBucketAdminID {
			continue
		}
		bo, err := createBreakout(v.Cost, v.RateBucketID, isFlatCancel, contract.ContractDate, contract.RateBuckets, contract.RateBucketRefundSettings)
		if err != nil {
			return nil, errors.Wrap(err, "error creating contract_cancellation_breakout")
		}
		if bo == nil || bo.Amount.Equal(decimal.Zero) {
			continue // Rate bucket was not refundable or the amount was $0.00 so skip it.
		}
		bos = append(bos, *bo)
	}

	// Exclude TCA-Admin
	if contract.CoreRateBucketID != db.RateBucketAdminID {
		bo, err := createBreakout(contract.PlanCost, contract.CoreRateBucketID, isFlatCancel, contract.ContractDate, contract.RateBuckets, contract.RateBucketRefundSettings)
		if err != nil {
			return nil, errors.Wrap(err, "error creating contract_cancellation_breakout")
		}
		if bo != nil && !bo.Amount.Equal(decimal.Zero) {
			bos = append(bos, *bo)
		}
	}

	// Aggregate breakouts by rateBucketID
	bosAgg := make(map[int]*db.ContractCancellationBreakout, len(bos))
	for _, bo := range bos {
		boAgg, ok := bosAgg[bo.RateBucketID]
		if !ok {
			boAgg = &db.ContractCancellationBreakout{
				ContractCancellationID: bo.ContractCancellationID,
				RateBucketID:           bo.RateBucketID,
				RefundRateBucketID:     bo.RefundRateBucketID,
				RefundType:             bo.RefundType,
				Amount:                 decimal.Zero,
			}
			bosAgg[bo.RateBucketID] = boAgg
		}
		boAgg.Amount = boAgg.Amount.Add(bo.Amount)
	}
	// Rebuild bos from bosAgg
	bos = make([]db.ContractCancellationBreakout, 0, len(bosAgg))
	for _, boAgg := range bosAgg {
		bos = append(bos, *boAgg)
	}

	// Apply cancel factor for rate buckets that are configured for prorata refund
	var refund decimal.Decimal
	rateBuckets := generateThirdPartyRemitMap(contract.RateBuckets)

	// Get NSD Rate Bucket
	var nsdRemitRateBucket int
	if contract.ProductName == "NSD Vehicle Theft Assistance" {
		query := `select id from rate_buckets where label ilike 'NSD Remit'`
		err := db.Get().Get(&nsdRemitRateBucket, query)
		if err != nil {
			err = errors.Wrap(err, "error getting nsd remit for rate bucket")
			return nil, err
		}
	}

	for i, bo := range bos {
		if bo.RefundType.ValueOrZero() == db.RefundTypeProRata {
			bos[i].Amount = bos[i].Amount.Mul(factor).Round(2)
		}

		// Apply cancel fee and claims reduction for third-party-remit rate buckets
		if _, ok := rateBuckets[bo.RateBucketID]; ok && contract.RefundProviderForCancel {
			// Don't deduct anything for DrivePur
			if contract.ProductTypeCode != "DP" {
				// Only deduct cancel fee and claims if it is not a DrivePur contract.
				bos[i].Amount = bos[i].Amount.Sub(fee).
					Sub(claimsReduction)
			}
			// Allow negative third-party refund amount for NSD Vehicle Theft Assistance unless the store refund is also negative or $0.
			if contract.ProductName == "NSD Vehicle Theft Assistance" {
				if bos[i].Amount.LessThan(decimal.Zero) && storeRefund.LessThanOrEqual(decimal.Zero) {
					bos[i].Amount = decimal.Zero
				}
			} else if bos[i].Amount.LessThan(decimal.Zero) {
				bos[i].Amount = decimal.Zero
			}
		}
		refund = refund.Add(bos[i].Amount)
	}

	// Calculate TCA-Admin = storeRefund - refundSoFar
	tcaAdminRefund := storeRefund.Sub(refund)
	bo, err := createBreakout(tcaAdminRefund, db.RateBucketAdminID, isFlatCancel, contract.ContractDate, contract.RateBuckets, contract.RateBucketRefundSettings)
	if err != nil {
		return nil, errors.Wrap(err, "error creating contract_cancellation_breakout")
	}
	bos = append(bos, *bo)

	return bos, nil
}

func generateThirdPartyRemitMap(rb []db.RateBucket) map[int]int64 {
	mapToReturn := make(map[int]int64)
	for _, v := range rb {
		if v.ProviderID.Valid {
			mapToReturn[v.ID] = v.ProviderID.Int64
		}
	}
	return mapToReturn
}

func createBreakout(
	amount decimal.Decimal,
	rateBucketID int,
	isFlatCancel bool,
	contractDate types.JSPQNullDate,
	rateBuckets []db.RateBucket,
	rateBucketRefundSettings []db.RateBucketRefundSetting,
) (*db.ContractCancellationBreakout, error) {
	rb := db.RateBucketByID(rateBucketID, rateBuckets)
	if rb == nil {
		return nil, errors.Errorf("unable to find rate bucket with id %d", rateBucketID)
	}
	if !rb.IsRefundable {
		return nil, nil
	}

	rbSettings := db.GetRateBucketRefundSettingsByID(rateBucketRefundSettings, rateBucketID, contractDate)
	var breakout db.ContractCancellationBreakout
	breakout.RateBucketID = rateBucketID
	breakout.RefundRateBucketID = rbSettings.RefundRateBucketID
	breakout.RefundType = null.StringFrom(rbSettings.RefundType)
	breakout.Amount = amount
	return &breakout, nil
}

// ContractInfo loads information about a contract that is needed when canceling the contract
func ContractInfo(ctx context.Context, contractID int, cancelReason *db.CancelReason) (ContractDetails, error) {
	contract := ContractDetails{}
	query := `select c.*,
					cc.state_code,
					cc.first_name as customer_first_name,
					cc.last_name as customer_last_name,
					cc.is_business as customer_is_business,
					cc.business_name as customer_business_name,
					cc.phone as customer_phone,
					cc.address as customer_address,
					cc.city as customer_city,
					cc.state_code as customer_state_code,
					cc.postal_code as customer_postal_code,
					cc.email as customer_email,
					coalesce(s.first_payment_date, sbs.first_payment_date) as first_payment_date, -- get first_payment_date from sb_sales if sales.first_payment_date is null
					coalesce(s.contract_date, sbs.contract_date) as contract_date, -- get contract_date from sb_sales if sales.contract_date is null
					coalesce(s.contract_date, sbs.contract_date) as sold_date, -- get sold_date from sb_sales if sales.contract_date is null
					coalesce(s.dms_number, '') as dms_number,
					coalesce(s.sale_type, '') as sale_type,
					pv.id as product_variant_id,
					p.id as product_id,
					p.name as product_name,
					pt.name as product_type_name,
					p.cancellable_by_stores as product_cancellable_by_stores,
					p.product_type_id,
					prv.id provider_id,
					st.state_code as store_state_code,
					st.code as store_code,
					vr.vin,
					vr.year as vehicle_year,
					vr.make as vehicle_make,
					vr.model as vehicle_model,
					coalesce(cl.name, l.name, '') as lender,
					coalesce(cl.attention,'') as lender_attention,
					coalesce(cl.city,l.city, '') as lender_city,
					coalesce(cl.state_code,l.state_code, '') as lender_state,
					coalesce(cl.address,l.address, '') as lender_address,
					coalesce(l.intacct_vendor_id, '') as lender_vendor_id,
					st.use_previous_cancel_form
			  from contracts c
                left join customers cc on cc.id = c.customer_id
                left join sales s on c.sale_id = s.id
				left join stores st on c.store_id = st.id
				left join lenders l on s.lender_id = l.id
				left join cancellation_lenders cl on l.cancellation_lender_id = cl.id
				left join sb_sale_contracts ssc on ssc.contract_id = c.id
				left join sb_sales sbs on sbs.id = ssc.sb_sale_id
				join vin_records vr on c.vin_record_id = vr.id
				join product_variants pv on pv.id = c.product_variant_id
				join products p on p.id = pv.product_id
				join product_types pt on pt.id = p.product_type_id
				join providers prv on prv.id = p.provider_id
			  where c.id = $1`
	err := db.Get().Unsafe().Get(&contract, query, contractID)
	if err != nil {
		if err == sql.ErrNoRows {
			return contract, errors.Wrap(err, "Contract not found")
		}
		err = errors.Wrap(err, "error finding data")
		return contract, err
	}

	// Get current active form to display
	if contract.ContractFormID.Valid {
		query = `with recursive updated_contract_forms as (
				select id, form_code, created_at, deleted_at
				from contract_forms where id = $1
				union
				select cf.id, cf.form_code, cf.created_at, cf.deleted_at
				from contract_forms cf join updated_contract_forms ucf on cf.created_at = ucf.deleted_at
			)
			select id as active_contract_form_id from updated_contract_forms where deleted_at is null;`

		err = db.Get().Get(&contract.ActiveContractFormID, query, contract.ContractFormID)
		if err != nil && err != sql.ErrNoRows {
			err = errors.Wrap(err, "error finding active contract form")
			return contract, err
		}
	}

	// invoice date from transactions
	query = `select t.invoiced_at 
		from contracts c join transactions t on t.contract_id = c.id
		join invoice_items ii on ii.transaction_id = t.id
		join invoices i on i.id = ii.invoice_id
		where c.id = $1 and t.transaction_type = $2`
	var invoicedAt types.JSPQNullDate
	err = db.Get().Get(&invoicedAt, query, contractID, db.TranTypeNewContract)
	if err != nil && err != sql.ErrNoRows {
		err = errors.Wrap(err, "error finding contract invoice date")
		return contract, err
	}
	contract.IsInvoiced = invoicedAt.Valid

	// total contract cost
	contract.Cost, err = GetContractCost(nil, contract.ID)
	if err != nil {
		return contract, errors.Wrap(err, "error calculating contract cost")
	}

	// contract claim information
	contract.ClaimsCount, contract.ClaimsAmount, contract.ClaimInProcess, err = Claims(ctx, contract.Code, contract.ProductTypeCode)
	if err != nil {
		return contract, errors.Wrap(err, "error finding contract claim details")
	}

	query = `select * from contract_surcharges where contract_id = $1`
	err = db.Get().Unsafe().Select(&contract.Surcharges, query, contract.ID)
	if err != nil {
		return contract, errors.Wrap(err, "error finding contract surcharges")
	}

	query = `select * from contract_options where contract_id = $1`
	err = db.Get().Unsafe().Select(&contract.Options, query, contract.ID)
	if err != nil {
		return contract, errors.Wrap(err, "error finding contract options")
	}

	query = `select * from contract_adjustments where contract_id = $1`
	err = db.Get().Unsafe().Select(&contract.Adjustments, query, contract.ID)
	if err != nil {
		return contract, errors.Wrap(err, "error finding contract adjustments")
	}

	query = `select * from rate_buckets`
	err = db.Get().Unsafe().Select(&contract.RateBuckets, query)
	if err != nil {
		return contract, errors.Wrap(err, "error finding rate buckets")
	}

	query = `select * from rate_bucket_refund_settings`
	err = db.Get().Unsafe().Select(&contract.RateBucketRefundSettings, query)
	if err != nil {
		return contract, errors.Wrap(err, "error finding rate bucket refund settings")
	}

	query = `select * from rate_bucket_reinstate_settings`
	err = db.Get().Unsafe().Select(&contract.RateBucketReinstateSettings, query)
	if err != nil {
		return contract, errors.Wrap(err, "error finding rate bucket reinstate settings")
	}

	contract.RefundProviderForCancel, err = db.ShouldRefundProviderForCancel(nil, contract.ProviderID.ValueOrZero())
	if err != nil {
		return contract, errors.Wrap(err, "error getting cancel on refund setting")
	}

	contract.ThirdPartyRateBucketIDs, err = db.GetContract3rdPartyRateBucketIDs(nil /* tx */, contract.ID)
	if err != nil && err != sql.ErrNoRows {
		return contract, errors.Wrap(err, "3rd-party rateBucketID")
	}

	if contract.PlanDuration == 999 { // lifetime plan
		contract.ExpirationDate.Scan(contract.EffectiveDate.Time.AddDate(5, 0, 0))
	}

	// Set PlanDuration for lifetime contract
	if contract.ProductTypeCode == db.ProductTypeCodeDrivePur && contract.PlanDuration == 999 {
		contract.PlanDuration = 60 // default expiry is 5 years while canceling lifetime plan contracts
	}
	// TODO : What should be the default expiry mileage in case of lifetime plans

	// RSA remit
	query = `select id from providers where name = $1`
	err = db.Get().Get(&contract.RSAProviderID, query, db.ContractProviderRSA)
	if err != nil && err != sql.ErrNoRows {
		return contract, errors.Wrap(err, "error getting RSA provider ID")
	}
	contract.RSARemit, err = db.GetContractRSAAmt(nil, contract.ID)
	if err != nil {
		return contract, errors.Wrap(err, "error getting rsa remit amount")
	}

	store := db.Store{}
	query = `select * from stores where id = $1`
	err = db.Get().Unsafe().GetContext(ctx, &store, query, contract.StoreID)
	if err != nil {
		return contract, errors.Wrap(err, "error finding store")
	}

	company := struct {
		db.Company
		LenderOptionName null.String `db:"lender_option_name"`
	}{}

	query = `select c.*, lo.name lender_option_name
		   from companies c
           inner join stores s on s.company_id = c.id
		   left join lender_options lo on lo.id = c.lender_option_id
           where s.id = $1`
	err = db.Get().Unsafe().GetContext(ctx, &company, query, contract.StoreID)
	if err != nil {
		return contract, errors.Wrap(err, "error finding company")
	}

	if contract.SaleType == dms.SaleTypeFinanceDeal && isTaxApplicable(contract, cancelReason, &company.Company) {
		loName := company.LenderOptionName.ValueOrZero()
		deal, err := dmsfactory.Deal(ctx, &store, contract.DMSNumber, loName)
		if err != nil || deal == nil {
			return contract, errors.WithStack(ErrDMSLookUpFailed)
		}
		contract.SalesTaxRate = calcSalesTaxRate(deal, contract)
	}
	return contract, nil
}

// TestSalesTaxRate is used to test the sales tax rate calculation
func TestSalesTaxRate(ctx context.Context, store db.Store, contract ContractDetails) (decimal.Decimal, error) {

	deal, err := dmsfactory.Deal(ctx, &store, contract.DMSNumber, db.LenderOptionAsbury)
	if err != nil || deal == nil {
		return decimal.Zero, errors.Wrap(err, "error getting deal")
	}
	return calcSalesTaxRate(deal, contract), nil
}

// calcSalesTaxRate calculates the sales tax rate for a contract
func calcSalesTaxRate(deal *dms.Deal, contract ContractDetails) decimal.Decimal {
	switch contract.StoreStateCode {
	case "FL":
		switch contract.ProductTypeCode {
		case db.ProductTypeCodeService, db.ProductTypeCodeMaintenance, db.ProductTypeCodeTireAndWheel:
			if deal.TaxRate.GreaterThan(decimal.Zero) && deal.PurchaseFlexiTaxRate.GreaterThan(decimal.Zero) {
				return deal.TaxRate.Add(deal.PurchaseFlexiTaxRate)
			}
			return deal.TaxRate
		default:
			return deal.TaxRate
		}
	case "VA":
		switch contract.ProductTypeCode {
		case db.ProductTypeCodeService, db.ProductTypeCodeMaintenance, db.ProductTypeCodePaintlessDentRepair:
			if deal.ServiceTaxRate.GreaterThan(decimal.Zero) {
				return deal.ServiceTaxRate
			}
			return deal.TaxRate
		default:
			return deal.TaxRate
		}
	default:
		return deal.TaxRate
	}
}

// Product	GA	MO	IN	FL	VA	TX	CO	NM	UT	ID	AZ	CA	WA
// ------------------------------------------------------------
// VSC		N	N	N	Y	Y	N	N	N	Y	N	N	N	Y
// MNT		N	N	Y	Y	Y	N	N	Y	Y	N	N	N	Y
// GAP		N	N	N	N	N	N	N	N	N	N	N	N	N
// DP 		N	N	N	Y	Y	N	N	N	Y	N	Y	N	Y
// AP 		Y	N	Y	Y	Y	Y	Y	Y	Y	Y	Y	Y	Y
// KEY		N	N	N	Y	Y	N	Y	Y	Y	Y	Y	N	Y
// PDR		N	N	N	Y	Y	N	Y	Y	Y	Y	Y	N	Y
// LWT		N	N	N	Y	Y	N	Y	N	Y	N	Y	N	N
// TW 		N	N	N	Y	Y	N	Y	N	Y	Y	N	N	Y
// ------------------------------------------------------------
// isTaxApplicable determines if tax is applicable for the contract based on state and product type
func isTaxApplicable(contract ContractDetails, cancelReason *db.CancelReason, company *db.Company) bool {
	var ita bool

	// If the contract's payment type is a Lease, then
	// set the is tax applicable to false.
	if contract.PaymentType == dms.PaymentTypeLease {
		ita = false
		return ita
	}

	// If the cancel reason is either Flat Cancel or Unwind, then
	// Sales Tax is not applicable.
	if cancelReason != nil && cancelReason.Name != "" && (cancelReason.IsFlatCancel ||
		cancelReason.Name == db.CancelReasonFlatCancelUnwind) {
		ita = false
		return ita
	}

	if company != nil && !company.IsSalesTaxApplicable {
		return false
	}

	switch contract.ProductTypeCode {
	case db.ProductTypeCodeService:
		switch contract.StoreStateCode {
		case "FL", "VA", "UT", "WA":
			ita = true
		}
	case db.ProductTypeCodeMaintenance:
		switch contract.StoreStateCode {
		case "IN", "FL", "VA", "NM", "UT", "WA":
			ita = true
		}
	case db.ProductTypeCodeGuaranteedAssetProtection:
		// no state has tax on GAP yet
	case db.ProductTypeCodeDrivePur:
		switch contract.StoreStateCode {
		case "FL", "VA", "UT", "AZ", "WA":
			ita = true
		}
	case db.ProductTypeCodeAppearanceProtection:
		switch contract.StoreStateCode {
		case "GA", "IN", "FL", "VA", "TX", "CO", "NM", "UT", "ID", "AZ", "CA", "WA":
			ita = true
		}
	case db.ProductTypeCodeKeyRemoteReplacement:
		switch contract.StoreStateCode {
		case "FL", "VA", "CO", "NM", "UT", "ID", "AZ", "WA":
			ita = true
		}
	case db.ProductTypeCodePaintlessDentRepair:
		switch contract.StoreStateCode {
		case "FL", "VA", "CO", "NM", "UT", "ID", "AZ", "WA":
			ita = true
		}
	case db.ProductTypeCodeLeaseWearAndTear:
		switch contract.StoreStateCode {
		case "FL", "VA", "CO", "UT", "AZ":
			ita = true
		}
	case db.ProductTypeCodeTireAndWheel:
		switch contract.StoreStateCode {
		case "FL", "VA", "CO", "UT", "ID", "WA":
			ita = true
		}
	}

	return ita
}

// contractCancellationFee : calculates cancellation fee for contract
func contractCancellationFee(
	contractFormID int64,
	effectiveDate, expirationDate types.JSPQNullDate,
	state, lender, paymentType string,
	price decimal.Decimal,
	sppCustomerPaid decimal.Decimal,
	cancelDate types.JSPQDate,
	isFlatCancellable bool,
	flatCancelDeductFee bool,
	cancelReasonID int,
) (decimal.Decimal, error) {
	if isFlatCancellable && !flatCancelDeductFee {
		return decimal.Zero, nil
	}

	cancellationFeeRule := struct {
		ID                     int                 `db:"id"`
		CancelFeeType          string              `db:"cancel_fee_type"`
		CancelFeeFixedAmount   types.JSNullDecimal `db:"cancel_fee_fixed_amount"`
		CancelFeePercentAmount types.JSNullDecimal `db:"cancel_fee_percent_amount"`
		CancelFeePercentOf     null.String         `db:"cancel_fee_percent_of"`
	}{}

	query := `select  cr.id, cancel_fee_type, cancel_fee_fixed_amount, cancel_fee_percent_amount, cancel_fee_percent_of
			  from cancel_rules cr join cancel_rule_contract_forms cf on cr.id = cf.cancel_rule_id
              where cf.contract_form_id = $1`
	err := db.Get().Get(&cancellationFeeRule, query, contractFormID)
	if err != nil {
		return decimal.Zero, err
	}

	var cancellationFeeOverrideRules []cancelRuleVariation

	// check for variations
	query = `select
				id,
				cancel_fee_type,
				cancel_fee_fixed_amount,
				cancel_fee_percent_amount,
				cancel_fee_percent_of,
				vary_by_state,
				state,
				vary_by_lender,
				lender,
				vary_by_cancel_reason,
				cancel_reason_ids
			  from cancel_rule_variations
              where cancel_rule_id = $1 and $2 = ANY (overrides)`
	err = db.Get().Select(&cancellationFeeOverrideRules, query, cancellationFeeRule.ID, db.SettingCancelFee)
	if err != nil && err != sql.ErrNoRows {
		return decimal.Zero, errors.Wrap(err, "failed to fetch cancel fee rule overrides")
	}

	overrideRule := selectCRVariation(cancellationFeeOverrideRules, state, lender, cancelReasonID)
	if overrideRule.ID != 0 {
		cancellationFeeRule.CancelFeeType = overrideRule.CancelFeeType
		cancellationFeeRule.CancelFeeFixedAmount = overrideRule.CancelFeeFixedAmount
		cancellationFeeRule.CancelFeePercentAmount = overrideRule.CancelFeePercentAmount
		cancellationFeeRule.CancelFeePercentOf = overrideRule.CancelFeePercentOf
	}

	if cancellationFeeRule.CancelFeeType == db.CancelFeeTypeFixed {
		return cancellationFeeRule.CancelFeeFixedAmount.Decimal, nil
	}

	if cancellationFeeRule.CancelFeeType == db.CanceFeeTypePercent {
		percentOfValue := price
		if cancellationFeeRule.CancelFeePercentOf.String == db.CancelFeePercentOfPaid && paymentType == dms.PaymentTypeSPP {
			percentOfValue = sppCustomerPaid
		}
		if cancellationFeeRule.CancelFeePercentOf.String == db.CancelFeePercentOfDays {
			percentDays, err := calcProRataDaysCancelFactor(
				effectiveDate.Time, expirationDate.Time, cancelDate.Time)
			if err != nil {
				return decimal.Zero, errors.Wrap(err, "failed to calculate cancellation with pro rata days")
			}

			percentOfValue = price.Mul(percentDays)
		}

		return calcPriceCancelFee(cancellationFeeRule.CancelFeePercentAmount.Decimal, percentOfValue), nil
	}

	if cancellationFeeRule.CancelFeeType == db.CanceFeeTypeLesser {
		percentCancelFee := decimal.Zero
		percentOfValue := price
		if cancellationFeeRule.CancelFeePercentOf.ValueOrZero() == db.CancelFeePercentOfPaid && paymentType == dms.PaymentTypeSPP {
			percentOfValue = sppCustomerPaid
		}
		if cancellationFeeRule.CancelFeePercentOf.String == db.CancelFeePercentOfDays {
			percentDays, err := calcProRataDaysCancelFactor(
				effectiveDate.Time, expirationDate.Time, cancelDate.Time)
			if err != nil {
				return decimal.Zero, errors.Wrap(err, "failed to calculate cancellation with pro rata days")
			}
			percentOfValue = price.Mul(percentDays)
		}
		percentCancelFee = calcPriceCancelFee(cancellationFeeRule.CancelFeePercentAmount.Decimal, percentOfValue)

		if percentCancelFee.LessThan(cancellationFeeRule.CancelFeeFixedAmount.Decimal) {
			return percentCancelFee, nil
		}
		return cancellationFeeRule.CancelFeeFixedAmount.Decimal, nil
	}

	return decimal.Zero, errors.New("Error calculating cancel fee : invalid cancel fee type")
}

func cancelCalculation(cancelRule *db.CancelRule, state, lender string, cancelReasonID int) (*db.CancelRule, error) {
	cancelCalculationOverrideRules := []cancelRuleVariation{}

	// check for variations
	query := `select
				id,
				cancel_calculations,
				vary_by_state,
				state,
				vary_by_lender,
				lender,
				vary_by_cancel_reason,
				cancel_reason_ids
			  from cancel_rule_variations
              where cancel_rule_id = $1 and $2 = ANY (overrides)`
	err := db.Get().Select(&cancelCalculationOverrideRules, query, cancelRule.ID, db.SettingCancelCalculation)
	if err != nil && err != sql.ErrNoRows {
		err = errors.Wrap(err, "failed to fetch cancel rule overrides")
		return nil, err
	}

	overrideRule := selectCRVariation(cancelCalculationOverrideRules, state, lender, cancelReasonID)
	if overrideRule.ID != 0 { // variation is found
		cancelRule.CancelCalculations = append(overrideRule.CancelCalculations[:0:0], overrideRule.CancelCalculations...)
	}

	return cancelRule, nil
}

func flatCancellable(effectiveDate types.JSPQNullDate, cancelDate time.Time, contractFormID int64, state, lender string, cancelReasonID int) (bool, bool, bool, bool, error) {
	var flatCancelRule struct {
		ID                     int    `db:"id"`
		FlatCancelDays         int    `db:"flat_cancel_days"`
		FlatCancelOp           string `db:"flat_cancel_op"`
		FlatCancelDeductClaims bool   `db:"flat_cancel_deduct_claims"`
		FlatCancelDeductFee    bool   `db:"flat_cancel_deduct_fee"`
		FlatCancelFeeException bool   `db:"flat_cancel_fee_exception"`
	}
	query := `select
				flat_cancel_days,
				flat_cancel_op,
				flat_cancel_deduct_claims,
				flat_cancel_deduct_fee,
				cancel_rules.id,
				flat_cancel_fee_exception
			  from cancel_rules
				join cancel_rule_contract_forms on cancel_rules.id = cancel_rule_contract_forms.cancel_rule_id
              where cancel_rule_contract_forms.contract_form_id = $1`
	err := db.Get().Get(&flatCancelRule, query, contractFormID)
	if err != nil {
		err = errors.Wrap(err, "failed to fetch contract flat cancel rules")
		return false, false, false, false, err
	}

	var flatCancelOverrideRules []cancelRuleVariation

	// check for variations
	query = `select
				flat_cancel_days,
				flat_cancel_op,
				flat_cancel_deduct_claims,
				flat_cancel_deduct_fee,
				flat_cancel_fee_exception,
				vary_by_state,
				state,
				vary_by_lender,
				lender,
				vary_by_cancel_reason,
				cancel_reason_ids
			  from cancel_rule_variations
              where cancel_rule_id = $1 and $2 = ANY (overrides)`
	err = db.Get().Select(&flatCancelOverrideRules, query, flatCancelRule.ID, db.SettingFlatCancel)
	if err != nil && err != sql.ErrNoRows {
		err = errors.Wrap(err, "failed to fetch cancel fee rule overrides")
		return false, false, false, false, err
	}

	overrideRule := selectCRVariation(flatCancelOverrideRules, state, lender, cancelReasonID)
	if overrideRule.ID != 0 {
		flatCancelRule.FlatCancelDays = overrideRule.FlatCancelDays
		flatCancelRule.FlatCancelOp = overrideRule.FlatCancelOp
		flatCancelRule.FlatCancelDeductClaims = overrideRule.FlatCancelDeductClaims
		flatCancelRule.FlatCancelDeductFee = overrideRule.FlatCancelDeductFee
		flatCancelRule.FlatCancelFeeException = overrideRule.FlatCancelFeeException
	}

	if !effectiveDate.Valid {
		return true, flatCancelRule.FlatCancelDeductClaims, flatCancelRule.FlatCancelDeductFee,
			flatCancelRule.FlatCancelFeeException, nil // Contract not started yet
	}

	flatCancelDate := effectiveDate.Time.AddDate(0, 0, flatCancelRule.FlatCancelDays)

	if flatCancelRule.FlatCancelOp == db.OpLte {
		return cancelDate.Before(flatCancelDate) || cancelDate.Equal(flatCancelDate),
			flatCancelRule.FlatCancelDeductClaims, flatCancelRule.FlatCancelDeductFee,
			flatCancelRule.FlatCancelFeeException, nil
	}

	return cancelDate.Before(flatCancelDate), flatCancelRule.FlatCancelDeductClaims, flatCancelRule.FlatCancelDeductFee, flatCancelRule.FlatCancelFeeException, nil
}

func getRanking(cancellable bool) int {
	if cancellable {
		return 0
	}
	return 2
}

// CancellationEstimateList returns cancellation estimates for all contracts of a VIN
func CancellationEstimateList(w http.ResponseWriter, req *http.Request, user db.CurrentUser, validateStore, isAdmin, isManager bool) (int, map[string]interface{}) {
	ctx := req.Context()
	const maxSalesTax = 11.0
	if chi.URLParam(req, "id") == "" {
		return http.StatusBadRequest, ErrorMessage("Contract ID is empty", nil)
	}

	contractID, err := strconv.Atoi(chi.URLParam(req, "id"))
	if err != nil {
		ReportError(req, errors.Wrapf(err, "failed to parse contract id %s to int", chi.URLParam(req, "id")))
		return http.StatusBadRequest, ErrorMessage("Invalid contract ID", nil)
	}

	if !isAdmin {
		allowed, err := user.CanAccessContract(contractID)
		if err != nil {
			ReportError(req, errors.Wrap(err, "failed to check user authorization"))
			return http.StatusInternalServerError, ErrorMessage("Authorization Error", nil)
		}
		if !allowed {
			return http.StatusUnauthorized, ErrorMessage("Not authorized for this action", nil)
		}
	}

	cancelReasonID, err := strconv.Atoi(req.FormValue("cancel_reason_id"))
	if err != nil {
		return http.StatusBadRequest, ErrorMessage("Empty cancel reason", nil)
	}

	cancelReason, err := db.GetCancelReasonByID(ctx, cancelReasonID)
	if err != nil {
		err = errors.WithMessage(err, "error getting cancel reason")
		ReportError(req, err)
		return http.StatusBadRequest, ErrorMessage("Error getting cancel reason information", nil)
	}

	var cancelDate types.JSPQDate
	var mileage int
	if !cancelReason.IsFlatCancel {
		err = cancelDate.UnmarshalJSON([]byte(req.FormValue("cancel_date")))
		if err != nil {
			return http.StatusBadRequest, ErrorMessage("Cancel Date is not in the proper date format 'YYYY-MM-DD'", nil)
		}

		mileage, err = strconv.Atoi(req.FormValue("mileage"))
		if err != nil {
			return http.StatusBadRequest, ErrorMessage("Empty mileage data", nil)
		}
	}

	var manualTax float64
	// We want to check to see if the manual_tax received was a blank value for use later.
	manualTaxStr := req.FormValue("manual_tax")
	if isAdmin {
		if manualTaxStr != "" {
			manualTax, err = strconv.ParseFloat(manualTaxStr, 64)
			if err != nil {
				return http.StatusBadRequest, ErrorMessage("Invalid manual tax data", nil)
			}
			if manualTax > maxSalesTax {
				errMsg := fmt.Sprintf("Manual tax rate cannot be greater than %f", maxSalesTax)
				return http.StatusBadRequest, ErrorMessage(errMsg, nil)
			}
		}
	}

	requestInfo := EstimateRequestInfo{
		CancelContractOptions: CancelContractOptions{
			CancelDate:             cancelDate,
			CurrentDate:            time.Now(),
			CancelReasonID:         cancelReasonID,
			Mileage:                mileage,
			UserID:                 user.ID,
			CancelPayeeAttentionTo: req.FormValue("cancel_payee_attention_to"),
			ManualTaxRate:          decimal.NewFromFloat(manualTax),
		},
	}

	if isAdmin {
		requestInfo.SPPCustomerPaid, err = decimal.NewFromString(req.FormValue("spp_customer_paid"))
		if err != nil {
			return http.StatusBadRequest, ErrorMessage("invalid spp customer paid value", nil)
		}

		requestInfo.SPPBalance, err = decimal.NewFromString(req.FormValue("spp_balance"))
		if err != nil {
			return http.StatusBadRequest, ErrorMessage("invalid spp balance", nil)
		}

		requestInfo.NSDKeyClaims, err = decimal.NewFromString(req.FormValue("nsd_key_claims"))
		if err != nil {
			return http.StatusBadRequest, ErrorMessage("Invalid NSD Key claims", nil)
		}

		requestInfo.NSDTireAndWheelClaims, err = decimal.NewFromString(req.FormValue("nsd_tire_and_wheel_claims"))
		if err != nil {
			return http.StatusBadRequest, ErrorMessage("Invalid NSD Tire &amp; Wheel claims", nil)
		}

		requestInfo.NSDVTAClaims, err = decimal.NewFromString(req.FormValue("nsd_vta_claims"))
		if err != nil {
			return http.StatusBadRequest, ErrorMessage("Invalid NSD VTA claims", nil)
		}
		requestInfo.ManualTaxRate = decimal.NewFromFloat(manualTax)
	}

	company, err := GetCompanyForContract(ctx, contractID)
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error getting company for contract"))
		return http.StatusInternalServerError, ErrorMessage("Failed to get company for contract", nil)
	}

	// Search for other contracts on the same vin for the customer
	contracts := []struct {
		ID     int    `db:"id"`
		Status string `db:"status"`
	}{}

	query := `select c2.id, c2.status 
		from contracts c
		join vin_records vr on vr.id = c.vin_record_id
		join customers cust on cust.id = c.customer_id
		-- get all vin_records with the same vin
		join vin_records vr2 on vr2.vin = vr.vin
		join contracts c2 on c2.vin_record_id = vr2.id
		join customers cust2 on cust2.id = c2.customer_id
		where c.id = ?
		and c2.status not in (?)
		and c2.source != ?
		and lower(cust.first_name) = lower(cust2.first_name)
		and lower(cust.last_name) = lower(cust2.last_name)
	`
	excludeStatus := []string{
		db.ContractStatusGenerated,
		db.ContractStatusVoided,
	}
	query, args, err := sqlx.In(query, contractID, excludeStatus, "Dealer Portal")
	if err != nil {
		err = errors.Wrap(err, "error getting related contracts")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Failed to get related contracts", nil)
	}
	query = db.Get().Rebind(query)
	err = db.Get().Select(&contracts, query, args...)
	if err != nil {
		ReportError(req, errors.Wrapf(err, "failed to fetch contracts related to contract with ID %d", contractID))
		return http.StatusInternalServerError, ErrorMessage("Failed to fetch related contracts", nil)
	}

	ccInfo := []CancelContractQuote{}
	for _, contract := range contracts {
		// get contract cancellation for all non cancelled and no expired claims
		contract, err := ContractInfo(ctx, contract.ID, cancelReason)
		if err != nil && !errors.Is(err, ErrDMSLookUpFailed) {
			ReportError(req, errors.Wrapf(err, "failed to fetch contract details with ID %d", contract.ID))
			return http.StatusInternalServerError, ErrorMessage("Failed to fetch contract details", nil)
		}
		if isAdmin {
			if isTaxApplicable(contract, cancelReason, &company) && errors.Is(err, ErrDMSLookUpFailed) {
				// We still want to display the error if the manual_tax was blank or if it was 0 but the user doesn't have the
				// appropriate roles to use a 0 tax rate.
				if manualTaxStr == "" || (manualTax == 0 && !user.HasRole(db.RoleAccountRepII) && !user.HasRole(db.RoleAccountRepManager) && !user.HasRole(db.RoleAccountRep)) {
					return http.StatusInternalServerError, ErrorMessage(msgCDKLookUpFailed, nil)
				}
			}
			if isTaxApplicable(contract, cancelReason, &company) &&
				contract.SalesTaxRate.Equals(decimal.Zero) { // DMS did not return tax
				contract.SalesTaxRate = decimal.NewFromFloat(manualTax)
			}
		}
		// We will set if contract is of EWU type
		sql := `select Count(pv.id) from products p join product_variants pv on p.id = pv.product_id
			join product_types pt on p.product_type_id = pt.id
			where pt.code = 'LWT' and (p.name ilike '%Toyota%' or p.name ilike '%Lexus%') and pv.id = $1`
		var isEWUToyotaOrLexus int
		err = db.Get().Get(&isEWUToyotaOrLexus, sql, contract.ProductVariantID)
		if err != nil {
			err = errors.Wrap(err, "error getting product variants")
			ReportError(req, err)
			return http.StatusInternalServerError, ErrorMessage("Error getting product variants.", nil)
		}
		contract.IsEWUToyotaOrLexus = isEWUToyotaOrLexus > 0

		if contract.IsEWUToyotaOrLexus {
			sql := `select sum(ca.cost) from contract_adjustments ca join rate_buckets rb on ca.rate_bucket_id = rb.id
					where ca.contract_id = $1 and rb.name in ('TCA Admin', 'SPIFF')`
			err := db.Get().Get(&contract.AdminCost, sql, contract.ID)
			if err != nil {
				err = errors.Wrap(err, "error getting admin cost for product")
				ReportError(req, err)
				return http.StatusInternalServerError, ErrorMessage("Error getting product variants.", nil)
			}
		}
		requestInfo.CancelContractOptions.CancelReasonName = cancelReason.Name
		if cancelReason.IsFlatCancel {
			requestInfo.CancelContractOptions.CancelDate = types.JSPQDate{NullTime: pq.NullTime{Time: contract.EffectiveDate.Time, Valid: true}}
			requestInfo.CancelContractOptions.Mileage = contract.EffectiveMileage
		}
		cancelEstimate, err := EstimateCancellation(contract, requestInfo.CancelContractOptions, validateStore, isAdmin, isManager, user, &company)
		if err != nil {
			ReportError(req, errors.Wrapf(err, "failed to calculate cancel estimates with ID %d", contract.ID))
			return http.StatusInternalServerError, ErrorMessage("failed to calculate cancel estimates", nil)
		}
		ccInfo = append(ccInfo, cancelEstimate)
	}
	return http.StatusOK, map[string]interface{}{"estimates": ccInfo}
}

func calcRefundableCost(contract ContractDetails) (decimal.Decimal, error) {
	// We don't want to consider surcharge costs which are non refundable
	// For Ex. CUDL surcharges on GAP and VTA OverAllowance on VTA
	refundable := contract.Cost
	for _, surcharge := range contract.Surcharges {
		rb := db.RateBucketByID(surcharge.RateBucketID, contract.RateBuckets)
		if rb == nil {
			return decimal.Zero, errors.Errorf("unable to find rate bucket with id %d", surcharge.RateBucketID)
		}
		if !rb.IsRefundable {
			refundable = refundable.Sub(surcharge.Cost)
		}
	}

	for _, option := range contract.Options {
		rb := db.RateBucketByID(option.RateBucketID, contract.RateBuckets)
		if rb == nil {
			return decimal.Zero, errors.Errorf("unable to find rate bucket with id %d", option.RateBucketID)
		}
		if !rb.IsRefundable {
			refundable = refundable.Sub(option.Cost)
		}
	}

	for _, adj := range contract.Adjustments {
		rb := db.RateBucketByID(adj.RateBucketID, contract.RateBuckets)
		if rb == nil {
			return decimal.Zero, errors.Errorf("unable to find rate bucket with id %d", adj.RateBucketID)
		}
		if !rb.IsRefundable && adj.IsInvoiceable && adj.AffectsContractCost {
			refundable = refundable.Sub(adj.Cost)
		}
	}

	rb := db.RateBucketByID(contract.CoreRateBucketID, contract.RateBuckets)
	if rb == nil {
		return decimal.Zero, errors.Errorf("unable to find rate bucket with id %d", contract.CoreRateBucketID)
	}
	if !rb.IsRefundable {
		refundable = refundable.Sub(contract.PlanCost)
	}

	return refundable, nil
}

// EstimateCancellation calculates cancellation values for a single contract
func EstimateCancellation(
	contract ContractDetails,
	cancelOptions CancelContractOptions,
	validateStore, isAdmin, isManager bool,
	user db.CurrentUser,
	company *db.Company,
) (CancelContractQuote, error) {
	var err error
	var cancelEstimate CancelContractQuote
	cancelEstimate.ID = contract.ID
	cancelEstimate.Code = contract.Code
	cancelEstimate.Status = contract.Status
	cancelEstimate.ProductTypeCode = contract.ProductTypeCode
	cancelEstimate.ProductTypeName = contract.ProductTypeName
	cancelEstimate.ProductTypeID = contract.ProductTypeID
	cancelEstimate.StoreCode = contract.StoreCode
	cancelEstimate.OriginalCode = contract.OriginalCode
	cancelEstimate.PaymentType = contract.PaymentType
	if contract.EffectiveDate.Valid {
		cancelEstimate.EffectiveDate = contract.EffectiveDate
	}

	// Get cancel Rule
	cancelRule, err := db.CancelRuleByContractFormID(contract.ContractFormID.Int64)
	if err != nil && err != sql.ErrNoRows {
		return cancelEstimate, errors.Wrap(err, "failed to get cancel rule for contract")
	}
	if err == sql.ErrNoRows {
		cancelEstimate.Cancellable = false
		cancelEstimate.RuleViolations = []stopRuleViolation{
			{
				Name:             "*No Cancel Rule Defined",
				ViolationMessage: "Missing cancel rule for contract",
			},
		}
		return cancelEstimate, nil
	}

	// Check for cancel rule variations
	rule, err := cancelCalculation(cancelRule, contract.StoreStateCode, contract.Lender, cancelOptions.CancelReasonID)
	if err != nil {
		return cancelEstimate, errors.Wrap(err, "failed to calculate cancel calculation rules")
	}
	cancelEstimate.CancelRuleName = rule.Name
	cancelEstimate.CancelRuleID = rule.ID

	// Check if flat cancellable
	var isFlatCancellable bool
	isFlatCancellable, rule.FlatCancelDeductClaims, rule.FlatCancelDeductFee, rule.FlatCancelFeeException, err = flatCancellable(
		contract.EffectiveDate,
		cancelOptions.CancelDate.Time,
		contract.ContractFormID.Int64,
		contract.StoreStateCode,
		contract.Lender,
		cancelOptions.CancelReasonID)
	if err != nil {
		return cancelEstimate, errors.Wrap(err, "failed to calculate flat cancel rules")
	}

	cancelEstimate.ClaimCount = contract.ClaimsCount
	cancelEstimate.ClaimTotalAmount = contract.ClaimsAmount

	// If NSD product, increase claim amount by NSD*Claims value
	if contract.ProductName == "NSD Key Replacement" || contract.ProductName == "NSD Key" {
		cancelEstimate.ClaimTotalAmount = cancelEstimate.ClaimTotalAmount.Add(cancelOptions.NSDKeyClaims)
		if cancelOptions.NSDKeyClaims.GreaterThan(decimal.Zero) {
			cancelEstimate.ClaimCount++
		}
	} else if contract.ProductName == "NSD Tire & Wheel" {
		cancelEstimate.ClaimTotalAmount = cancelEstimate.ClaimTotalAmount.Add(cancelOptions.NSDTireAndWheelClaims)
		if cancelOptions.NSDTireAndWheelClaims.GreaterThan(decimal.Zero) {
			cancelEstimate.ClaimCount++
		}
	} else if contract.ProductName == "NSD Vehicle Theft Assistance" {
		cancelEstimate.ClaimTotalAmount = cancelEstimate.ClaimTotalAmount.Add(cancelOptions.NSDVTAClaims)
		if cancelOptions.NSDVTAClaims.GreaterThan(decimal.Zero) {
			cancelEstimate.ClaimCount++
		}
	}

	// cancelOptions.NSDKeyClaims
	cancelEstimate.RuleViolations = []stopRuleViolation{}

	// get contract stop rules
	cancelEstimate.CancelStopRules, err = cancellationStopRules(contract, isAdmin, isManager)
	if err != nil {
		return cancelEstimate, errors.Wrap(err, "failed to fetch contact cancellation stop rules")
	}

	cancelEstimate.RuleViolations = applyCancelStopRules(cancelEstimate.CancelStopRules, contract, cancelOptions, user)
	if len(cancelEstimate.RuleViolations) != 0 {
		return cancelEstimate, nil
	}

	// Prevent user from canceling contract if no contract form is associated
	if contract.ContractFormID.Int64 == 0 {
		cancelEstimate.Cancellable = false
		cancelEstimate.RuleViolations = []stopRuleViolation{
			{
				Name:             "No Contract Form Found",
				ViolationMessage: "This contract is non-cancellable",
			},
		}
		return cancelEstimate, nil
	}

	cancelEstimate.Cancellable = true

	// If there is cancel fee deduction exception then check the effective date and cancel date
	if rule.FlatCancelFeeException {
		// We need compare date part only and not the time
		if cancelOptions.CancelDate.Time.Year() == contract.EffectiveDate.Time.Year() &&
			cancelOptions.CancelDate.Time.YearDay() == contract.EffectiveDate.Time.YearDay() {
			// If both the dates are same then we will not deduct cancel fee
			rule.FlatCancelDeductFee = false
		}
	}

	cancelEstimate.Fee, err = contractCancellationFee(
		contract.ContractFormID.Int64,
		contract.EffectiveDate,
		contract.ExpirationDate,
		contract.StoreStateCode,
		contract.Lender,
		contract.PaymentType,
		contract.Price,
		cancelOptions.SPPCustomerPaid,
		cancelOptions.CancelDate,
		isFlatCancellable,
		rule.FlatCancelDeductFee,
		cancelOptions.CancelReasonID,
	)
	if err != nil {
		return cancelEstimate, errors.Wrap(err, "error calculating cancel fee")
	}

	err = cancellationInfo(cancelOptions, contract, *rule, isFlatCancellable, &cancelEstimate, company)
	if err != nil {
		return cancelEstimate, errors.Wrap(err, "failed to calculate contact cancellation")
	}

	if validateStore {
		store, err := userStore(strconv.Itoa(cancelOptions.StoreID), cancelOptions.UserID)
		if err != nil {
			return cancelEstimate, errors.Wrap(err, "store not found")
		}
		if cancelEstimate.StoreCode != store.Code {
			cancelEstimate.Cancellable = false
			cancelEstimate.RuleViolations = append(cancelEstimate.RuleViolations, stopRuleViolation{
				Name:             "*Issuing dealer is not matching",
				ViolationMessage: "Issuing dealer is not matching",
			})
		}
	}
	cancelEstimate.Ranking = getRanking(cancelEstimate.Cancellable)
	return cancelEstimate, nil
}

func validateCancellationRequest(ctx context.Context, req EstimateRequestInfo, user db.CurrentUser, requestType string) error {
	if req.Mileage < 0 {
		return errors.New("invalid current miles")
	}
	if req.CancelReasonID <= 0 {
		return errors.New("invalid cancel reason")
	}
	if !req.CancelDate.Valid {
		return errors.New("invalid cancel date")
	}
	if requestType == db.CancelRequestCancel {
		// Check if role has permission to cancel with the selected reason
		cr, err := db.GetCancelReasonByID(ctx, req.CancelReasonID)
		if err != nil {
			return errors.Wrap(err, "error getting cancel reason")
		}

		switch cr.Name {
		case db.CancelReasonFlatCancelUnwind:
			break
		default: // all other reasons
			if !user.HasAnyRole([]string{db.RoleAdminView, db.RoleLDCSAllCancels}) {
				return errors.New("invalid cancel reason for this role")
			}
		}
	}

	return nil
}
func cleanCancelPayeeInfo(req EstimateRequestInfo) EstimateRequestInfo {
	req.CancelPayeeName = strings.TrimSpace(req.CancelPayeeName)
	req.CancelPayeeAttentionTo = strings.TrimSpace(req.CancelPayeeAttentionTo)
	req.CancelPayeeAddress = strings.TrimSpace(req.CancelPayeeAddress)
	req.CancelPayeeCity = strings.TrimSpace(req.CancelPayeeCity)
	req.CancelPayeeState = strings.TrimSpace(req.CancelPayeeState)
	req.CancelPayeePostalCode = strings.TrimSpace(req.CancelPayeePostalCode)
	return req
}
func validateCancelPayeeInfo(req EstimateRequestInfo) error {
	if req.CancelPayee == db.CancelPayeeTypeCustomer ||
		req.CancelPayee == db.CancelPayeeTypeLender {
		if req.CancelPayeeName == "" {
			return errors.New("invalid cancel payee name")
		}
		if req.CancelPayeeAddress == "" {
			return errors.New("invalid cancel payee address")
		}
		if req.CancelPayeeCity == "" {
			return errors.New("invalid cancel payee city")
		}
		if req.CancelPayeeState == "" {
			return errors.New("invalid cancel payee state")
		}
		if req.CancelPayeePostalCode == "" {
			return errors.New("invalid cancel payee postal code")
		}
	}
	return nil
}

// ContractCancel cancel the given list of contracts
func ContractCancel(
	w http.ResponseWriter,
	r *http.Request,
	user db.CurrentUser,
	validateStore, isAdmin, isManager, isAdminHandler bool,
	tx *sqlx.Tx,
) (int, map[string]interface{}) {
	ctx := r.Context()

	reqPayload := struct {
		EstimateRequestInfo
		Attachments []struct {
			FileContent      string   `json:"file_content"`
			Name             string   `json:"name"`
			Description      string   `json:"description"`
			ContentType      string   `json:"content_type"`
			AttachmentTypeID null.Int `json:"attachment_type_id"`
		} `json:"attachments"`
		HasClaimRequested bool `json:"has_claim_requested"`
	}{}

	dec := json.NewDecoder(r.Body)
	err := dec.Decode(&reqPayload)
	if err != nil {
		return http.StatusBadRequest, ErrorMessage("Bad request"+err.Error(), nil)
	}

	// cancellation request payload
	estimatePayload := EstimateRequestInfo{}
	estimatePayload.StoreID = reqPayload.StoreID
	estimatePayload.Contracts = reqPayload.Contracts
	estimatePayload.CancelDate = reqPayload.CancelDate
	estimatePayload.Mileage = reqPayload.Mileage
	estimatePayload.SPPBalance = reqPayload.SPPBalance
	estimatePayload.SPPCustomerPaid = reqPayload.SPPCustomerPaid
	estimatePayload.NSDKeyClaims = reqPayload.NSDKeyClaims
	estimatePayload.NSDTireAndWheelClaims = reqPayload.NSDTireAndWheelClaims
	estimatePayload.NSDVTAClaims = reqPayload.NSDVTAClaims
	estimatePayload.CancelReasonID = reqPayload.CancelReasonID
	estimatePayload.ManualTaxRate = reqPayload.ManualTaxRate
	estimatePayload.SkipBill = reqPayload.SkipBill
	estimatePayload.ApplyFee = reqPayload.ApplyFee
	estimatePayload.IsElectronicCheck = reqPayload.IsElectronicCheck
	estimatePayload.Email = reqPayload.Email

	cr, err := db.GetCancelReasonByID(ctx, reqPayload.CancelReasonID)
	if err != nil {
		err = errors.WithMessagef(err, "error getting cancel reason with reason ID %d", reqPayload.CancelReasonID)
		ReportError(r, err)
		return http.StatusInternalServerError, ErrorMessage("Error getting cancel reason", nil)
	}
	reqPayload.CancelReasonName = cr.Name
	estimatePayload.CancelReasonName = reqPayload.CancelReasonName
	switch cr.Name {
	case db.CancelReasonFlatCancelUnwind:
		if !isAdminHandler {
			// Unflag the cancellation fee from being applied for this cancellation when cancel is not being
			// done from the Admin Cancel page.
			estimatePayload.ApplyFee = false
		}
	case db.CancelReasonSPPDefault, db.CancelReasonSPPCustomerRequest:
	// do not set payee values

	default:
		estimatePayload.CancelPayee = reqPayload.CancelPayee
		estimatePayload.CancelPayeeName = reqPayload.CancelPayeeName
		estimatePayload.CancelPayeeAttentionTo = reqPayload.CancelPayeeAttentionTo
		estimatePayload.CancelPayeeAddress = reqPayload.CancelPayeeAddress
		estimatePayload.CancelPayeeCity = reqPayload.CancelPayeeCity
		estimatePayload.CancelPayeeState = reqPayload.CancelPayeeState
		estimatePayload.CancelPayeePostalCode = reqPayload.CancelPayeePostalCode
	}

	if err = validateCancellationRequest(ctx, estimatePayload, user, db.CancelRequestCancel); err != nil {
		return http.StatusBadRequest, ErrorMessage(err.Error(), nil)
	}
	estimatePayload = cleanCancelPayeeInfo(estimatePayload)
	if err = validateCancelPayeeInfo(estimatePayload); err != nil {
		return http.StatusBadRequest, ErrorMessage(err.Error(), nil)
	}

	if len(estimatePayload.Contracts) == 0 {
		return http.StatusBadRequest, ErrorMessage("Contract list for cancellation is empty", nil)
	}

	estimatePayload.UserID = user.ID
	estimatePayload.CurrentDate = time.Now()
	estimatePayload.CancelContractOptions.CancelReasonName = cr.Name

	// get store code
	store := db.Store{}
	err = db.Get().Unsafe().Get(&store, `select s.* from stores s join contracts c on c.store_id = s.id where c.id = $1`, reqPayload.Contracts[0])
	if err != nil {
		err = errors.Wrap(err, "error looking up store for contract")
		ReportError(r, err)
		return http.StatusInternalServerError, ErrorMessage("error looking up store for contract", nil)
	}
	// get contract codes
	var contractCodes []string
	for _, contractID := range reqPayload.Contracts {
		var contractCode string
		err = db.Get().Get(&contractCode, `select code from contracts where id = $1`, contractID)
		if err != nil {
			err = errors.Wrap(err, "error looking up code for contract")
			ReportError(r, err)
			return http.StatusInternalServerError, ErrorMessage("error looking up code for contract", nil)
		}
		contractCodes = append(contractCodes, contractCode)
	}

	company, err := GetCompanyForContract(ctx, reqPayload.Contracts[0])
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error getting company for contract"))
		return http.StatusInternalServerError, ErrorMessage("Failed to get company for contract", nil)
	}

	// --- Begin per-contract payee logic ---
	usePerContractPayees := reqPayload.ContractPayees != nil && len(reqPayload.ContractPayees) > 0

	txn := w.(newrelic.Transaction)
	var attachments []db.ContractAttachment
	if !user.HasRole(db.RoleTestAutomation) || len(reqPayload.Attachments) > 0 {
		var ca db.ContractAttachment
		for _, att := range reqPayload.Attachments {
			ca.FileName = strings.TrimSpace(att.Name)
			if ca.FileName == "" {
				continue
			}

			// cancellation attachment payload
			ca.CreatedByUserID = user.ID
			ca.S3Bucket = s3util.Bucket()
			ca.StoreID = store.ID
			ca.S3FileName = fmt.Sprintf("contract-cancellation-attachments/%s/%s_%s_%s", store.Code, strings.Join(contractCodes, "_"), randstr.StringN(6), att.Name)
			if ca.FileName == "" {
				return http.StatusBadRequest, ErrorMessage("attachment file name is required", nil)
			}
			ca.ContentType = att.ContentType
			ca.Description = att.Description
			ca.AttachmentTypeID = att.AttachmentTypeID

			if att.FileContent == "" {
				err := errors.Errorf("attachment missing content. Name: %s Description: %s ContentType: %s AttachmentTypeID: %d", att.Name, att.Description, att.ContentType, att.AttachmentTypeID.ValueOrZero())
				ReportError(r, err)
				return http.StatusInternalServerError, ErrorMessage("attachment is empty", nil)
			}

			// upload attachment to S3 bucket
			data, err := base64.StdEncoding.DecodeString(att.FileContent)
			if err != nil {
				err = errors.Wrap(err, "error base64 decoding cancellation attachment")
				ReportError(r, err)
				return http.StatusBadRequest, ErrorMessage("Error saving cancellation attachment", nil)
			}

			err = s3util.Put(txn, bytes.NewReader(data), s3util.DefaultRegion, ca.S3Bucket, ca.S3FileName)
			if err != nil {
				err = errors.Wrapf(err, "error uploading cancellation attachment file to %s/%s", ca.S3Bucket, ca.S3FileName)
				ReportError(r, err)
				return http.StatusInternalServerError, ErrorMessage("Error saving cancellation attachment", nil)
			}

			attachments = append(attachments, ca)
		}
	}

	if tx == nil {
		tx, err = db.Get().Beginx()
		if err != nil {
			ReportError(r, errors.Wrap(err, "error beginning transaction"))
			return http.StatusInternalServerError, ErrorMessage("transaction error", nil)
		}
	}

	cancelCount := 0
	warnings := []string{}
	for _, contractID := range reqPayload.Contracts {
		// If per-contract payees are present, use them for this contract
		if usePerContractPayees {
			if payee, ok := reqPayload.ContractPayees[contractID]; ok {
				estimatePayload.CancelPayee = payee.Payee
				estimatePayload.CancelPayeeName = payee.PayeeName
				estimatePayload.CancelPayeeAttentionTo = payee.PayeeAttentionTo
				estimatePayload.CancelPayeeAddress = payee.PayeeAddress
				estimatePayload.CancelPayeeCity = payee.PayeeCity
				estimatePayload.CancelPayeeState = payee.PayeeState
				estimatePayload.CancelPayeePostalCode = payee.PayeePostalCode
				// If you need CancelStoreID, add logic here
			} else {
				// If not present for this contract, clear payee fields
				estimatePayload.CancelPayee = ""
				estimatePayload.CancelPayeeName = ""
				estimatePayload.CancelPayeeAttentionTo = ""
				estimatePayload.CancelPayeeAddress = ""
				estimatePayload.CancelPayeeCity = ""
				estimatePayload.CancelPayeeState = ""
				estimatePayload.CancelPayeePostalCode = ""
			}
			// Only save check options for Customer payee
			if estimatePayload.CancelPayee == "Customer" {
				estimatePayload.IsElectronicCheck = reqPayload.IsElectronicCheck
				estimatePayload.Email = reqPayload.Email
			} else {
				estimatePayload.IsElectronicCheck = false
				estimatePayload.Email = ""
			}
			// Validate and clean per-contract payee info
			if err = validateCancelPayeeInfo(estimatePayload); err != nil {
				tx.Rollback()
				return http.StatusBadRequest, ErrorMessage(err.Error(), nil)
			}
			estimatePayload = cleanCancelPayeeInfo(estimatePayload)
		}
		contract, err := ContractInfo(ctx, contractID, cr)
		if err != nil && !errors.Is(err, ErrDMSLookUpFailed) {
			tx.Rollback()
			ReportError(r, errors.Wrapf(err, "failed to fetch contract details with ID %d", contract.ID))
			return http.StatusInternalServerError, ErrorMessage("failed to fetch contract details", nil)
		}
		if contract.Status == db.ContractStatusPending {
			err = contractActivate(tx, contract.ID, user.ID, false)
			if err != nil {
				tx.Rollback()
				ReportError(r, errors.Wrapf(err, "failed to activate contract with ID %d", contract.ID))
				return http.StatusInternalServerError, ErrorMessage("failed to activate contract", nil)
			}
		}
		if contract.Status == db.ContractStatusCancelled {
			warnings = append(warnings, contract.Code+" is already cancelled contract")
			continue // skip already cancelled or expired contracts
		}

		// We will set if contract is of EWU type
		sql := `select Count(pv.id) from products p join product_variants pv on p.id = pv.product_id
			join product_types pt on p.product_type_id = pt.id
			where pt.code = 'LWT' and (p.name ilike '%Toyota%' or p.name ilike '%Lexus%') and pv.id = $1`
		var isEWUToyotaOrLexus int
		err = db.Get().Get(&isEWUToyotaOrLexus, sql, contract.ProductVariantID)
		if err != nil {
			err = errors.Wrap(err, "error getting product variants")
			ReportError(r, err)
			return http.StatusInternalServerError, ErrorMessage("Error getting product variants.", nil)
		}
		contract.IsEWUToyotaOrLexus = isEWUToyotaOrLexus > 0

		if contract.IsEWUToyotaOrLexus {
			sql := `select sum(ca.cost) from contract_adjustments ca join rate_buckets rb on ca.rate_bucket_id = rb.id
					where ca.contract_id = $1 and rb.name in ('TCA Admin', 'SPIFF')`
			err := db.Get().Get(&contract.AdminCost, sql, contract.ID)
			if err != nil {
				err = errors.Wrap(err, "error getting admin cost for product")
				ReportError(r, err)
				return http.StatusInternalServerError, ErrorMessage("Error getting product variants.", nil)
			}
		}
		if cr.IsFlatCancel {
			estimatePayload.CancelContractOptions.CancelDate = types.JSPQDate{NullTime: pq.NullTime{Time: contract.EffectiveDate.Time, Valid: true}}
			estimatePayload.CancelContractOptions.Mileage = contract.EffectiveMileage
		}

		cancelEstimate, err := EstimateCancellation(contract, estimatePayload.CancelContractOptions, validateStore, isAdmin, isManager, user, &company)
		if err != nil {
			tx.Rollback()
			ReportError(r, errors.Wrapf(err, "failed to calculate cancellation estimate with ID %d", contract.ID))
			return http.StatusInternalServerError, ErrorMessage("failed to calculate cancellation estimate", nil)
		}
		if !cancelEstimate.Cancellable {
			warnings = append(warnings, contract.Code+" contract is not cancellable")
			ReportError(r, errors.New("can not cancel not cancellable contract "+contract.Code))
			continue
		}

		// create contract logs
		err = db.CreateContractLog(tx, contract.ID, estimatePayload.UserID)
		if err != nil {
			tx.Rollback()
			ReportError(r, errors.Wrapf(err, "error contract_logs insert for contract ID %d", contract.ID))
			return http.StatusInternalServerError, ErrorMessage("error in create contract logs", nil)
		}

		// Add cancel transaction
		tran := db.Transaction{}
		tran.ContractID = contract.ID
		tran.CreatedByUserID = user.ID
		tran.StoreID = contract.StoreID // don't use store.ID because it is not loaded for each contract and the user could be cancelling multiple contracts from different stores at once.
		tran.VehicleYear = contract.VehicleYear
		tran.VehicleMake = contract.VehicleMake
		tran.VehicleModel = contract.VehicleModel
		tran.TransactionType = db.TranTypeCancel
		tran.Amount = cancelEstimate.StoreRefund
		tran.ContractPrice = contract.Price
		err = db.CreateTransaction(ctx, tx, &tran)
		if err != nil {
			tx.Rollback()
			err = errors.Wrapf(err, "could not create transaction for contract %s", contract.Code)
			ReportError(r, err)
			return http.StatusInternalServerError, ErrorMessage("failed to cancel contract", nil)
		}

		// Add SPP transaction remittal
		var c db.Contract
		err = tx.Unsafe().Get(&c, `select * from contracts where id = $1`, contract.ID)
		if err != nil {
			tx.Rollback()
			err = errors.Wrapf(err, "could not load contract %s", contract.Code)
			ReportError(r, err)
			return http.StatusInternalServerError, ErrorMessage("failed to cancel contract", nil)
		}
		err = db.CreateSPPCancelTransactionRemittal(tx, c, tran, cancelEstimate.SPPRefund)
		if err != nil {
			tx.Rollback()
			err = errors.Wrapf(err, "could not create spp transaction_remittal for contract %s", contract.Code)
			ReportError(r, err)
			return http.StatusInternalServerError, ErrorMessage("failed to cancel contract", nil)
		}

		// Load contract as db.Contract
		dbContract, err := db.GetContractByID(ctx, tx, contract.ID)
		if err != nil {
			_ = tx.Rollback()
			msg := fmt.Sprintf("failed to get contract %s", contract.Code)
			err = errors.WithMessage(err, msg)
			ReportError(r, err)
			return http.StatusInternalServerError, ErrorMessage("Failed to cancel contract", nil)
		}

		// Add 3rd-party transaction remittal
		provider, err := db.GetContractProvider(tx, contract.ID)
		if err != nil {
			tx.Rollback()
			err = errors.Wrapf(err, "error getting provider for contract %s", contract.Code)
			ReportError(r, err)
			return http.StatusInternalServerError, ErrorMessage("Unable to get 3rd-party contract provider", nil)
		}
		if provider.RemitOnCancel {
			var remittal db.TransactionRemittal
			remittal.TransactionID = tran.ID
			remittal.Amount = cancelEstimate.ThirdPartyRefund
			remittal.ProviderID = int(contract.ProviderID.ValueOrZero())
			err = db.CreateTransactionRemittal(tx, dbContract, &remittal)
			if err != nil {
				tx.Rollback()
				err = errors.Wrapf(err, "could not create transaction remittal for contract %s", contract.Code)
				ReportError(r, err)
				return http.StatusInternalServerError, ErrorMessage("failed to cancel contract", nil)
			}
		}

		hasRSA := false
		for _, adj := range contract.Adjustments {
			if adj.RateBucketID == db.RateBucketRsaID {
				hasRSA = true
				break
			}
		}

		// NOTE: If cancel within 120 days, then add transaction remittals (refunds) for any adjustments with rate buckets tied to the RSA provider.
		if contract.EffectiveDate.NullTime.Valid && hasRSA {
			lastAllowedCancelDate := contract.EffectiveDate.NullTime.Time.AddDate(0, 0, db.ContractProviderRSACancelDays)
			cancelDate := estimatePayload.CancelDate.NullTime.Time
			if cancelDate.Before(lastAllowedCancelDate) || cancelDate.Equal(lastAllowedCancelDate) {
				var remittal db.TransactionRemittal
				remittal.TransactionID = tran.ID
				remittal.Amount = cancelEstimate.RSARefund
				remittal.ProviderID = int(contract.RSAProviderID.ValueOrZero())
				err = db.CreateTransactionRemittal(tx, dbContract, &remittal)
				if err != nil {
					tx.Rollback()
					err = errors.Wrapf(err, "could not create RSA transaction remittal for contract %s", contract.Code)
					ReportError(r, err)
					return http.StatusInternalServerError, ErrorMessage("failed to cancel contract", nil)
				}
			}
		}

		// void previous cancellation entries if any
		_, err = tx.Exec(`update contract_cancellations set is_void = true, voided_at = now() at time zone 'utc', voided_by_user_id = $1 where contract_id = $2 and is_void = false`, estimatePayload.UserID, contract.ID)
		if err != nil {
			tx.Rollback()
			ReportError(r, errors.Wrapf(err, "failed to void last cancellation for contract %d", contract.ID))
			return http.StatusInternalServerError, ErrorMessage("failed to void last cancellation", nil)
		}

		mntUnusedVisits := 0
		if cancelEstimate.ProductTypeCode == "MNT" {
			totalVisits := 0
			err = db.Get().Get(&totalVisits, `select maintenance_visits from contracts where id = $1`, contract.ID)
			if err != nil {
				tx.Rollback()
				ReportError(r, errors.Wrapf(err, "error getting maintenance visits for contract %d", contract.ID))
				return http.StatusInternalServerError, ErrorMessage("failed to get maintenance visits", nil)
			}
			mntUnusedVisits = totalVisits - cancelEstimate.ClaimCount
		}

		var claimCount decimal.Decimal
		if contract.ProductName == "NSD Key" {
			claimCount = estimatePayload.NSDKeyClaims
		} else if contract.ProductName == "NSD Tire & Wheel" {
			claimCount = estimatePayload.NSDTireAndWheelClaims
		} else if contract.ProductName == "NSD Vehicle Theft Assistance" {
			claimCount = estimatePayload.NSDVTAClaims
		}

		contractCancellations := struct {
			CreatedByUserID        int                 `db:"created_by_user_id" json:"created_by_user_id"`
			ContractID             int                 `db:"contract_id" json:"contract_id"`
			CancelDate             types.JSPQDate      `db:"cancel_date" json:"cancel_date"`
			CancelMileage          int                 `db:"cancel_mileage" json:"cancel_mileage"`
			CancelReason           string              `db:"cancel_reason" json:"cancel_reason"`
			CancelFee              decimal.Decimal     `db:"cancel_fee" json:"cancel_fee"`
			CancelFactor           decimal.Decimal     `db:"cancel_factor" json:"cancel_factor"`
			CustomerRefund         decimal.Decimal     `db:"customer_refund" json:"customer_refund"`
			SalesTaxRate           decimal.Decimal     `db:"sales_tax_rate" json:"sales_tax_rate"`
			SalesTax               decimal.Decimal     `db:"sales_tax" json:"sales_tax"`
			StoreRefund            decimal.Decimal     `db:"store_refund" json:"store_refund"`
			ClaimsPaidAmount       decimal.Decimal     `db:"claims_paid_amount" json:"claims_paid_amount"`
			ClaimsDeductedAmount   decimal.NullDecimal `db:"claims_deducted_amount" json:"claims_deducted_amount"`
			ThirdPartyRefund       decimal.Decimal     `db:"third_party_refund" json:"third_party_refund"`
			UnusedVisits           int                 `db:"unused_visits" json:"unused_visits"`
			IsVoid                 bool                `db:"is_void" json:"is_void"`
			TransactionID          int                 `db:"transaction_id" json:"transaction_id"`
			SPPAmountPaid          decimal.Decimal     `db:"spp_amount_paid" json:"spp_customer_paid"`
			SPPBalance             decimal.Decimal     `db:"spp_balance" json:"spp_balance"`
			NSDClaims              decimal.Decimal     `db:"nsd_claims" json:"nsd_claims"`
			AdjustedCustomerRefund decimal.Decimal     `db:"adj_customer_refund" json:"adjusted_customer_refund"`
			StoreChargeback        decimal.Decimal     `db:"store_chargeback" json:"store_chargeback"`
			SPPRefund              decimal.Decimal     `db:"spp_refund" json:"spp_refund"`
			RSARefund              decimal.Decimal     `db:"rsa_refund" json:"rsa_refund"`
			RefundType             string              `db:"refund_type" json:"refund_type"`
			PayeeType              string              `db:"payee_type" json:"payee_type"`
			PayeeName              string              `db:"payee_name" json:"payee_name"`
			PayeeAttentionTo       string              `db:"payee_attention_to" json:"payee_attention_to"`
			PayeeAddress           string              `db:"payee_address" json:"payee_address"`
			PayeeCity              string              `db:"payee_city" json:"payee_city"`
			PayeeState             string              `db:"payee_state_code" json:"payee_state_code"`
			PayeePostalCode        string              `db:"payee_postal_code" json:"payee_postal_code"`
			CancelStatus           string              `db:"cancel_status" json:"cancel_status"`
			ApplyFee               bool                `db:"apply_fee" json:"apply_fee"`
			IsElectronicCheck      bool                `db:"is_electronic_check" json:"is_electronic_check"`
			Email                  string              `db:"email" json:"email"`
			CheckApplicable        bool                `db:"check_applicable" json:"check_applicable"`
		}{
			CreatedByUserID:        estimatePayload.UserID,
			ContractID:             contract.ID,
			CancelDate:             estimatePayload.CancelDate,
			CancelMileage:          estimatePayload.Mileage,
			CancelReason:           estimatePayload.CancelReasonName,
			CancelFee:              cancelEstimate.Fee,
			CancelFactor:           cancelEstimate.FactorPercent,
			CustomerRefund:         cancelEstimate.AdjustedCustomerRefund,
			StoreRefund:            cancelEstimate.StoreRefund,
			ThirdPartyRefund:       cancelEstimate.ThirdPartyRefund,
			ClaimsPaidAmount:       cancelEstimate.ClaimTotalAmount,
			ClaimsDeductedAmount:   decimal.NullDecimal{Decimal: cancelEstimate.ClaimsDeductedAmount, Valid: true},
			UnusedVisits:           mntUnusedVisits,
			IsVoid:                 false,
			TransactionID:          tran.ID,
			SPPAmountPaid:          estimatePayload.SPPCustomerPaid,
			SPPBalance:             estimatePayload.SPPBalance,
			NSDClaims:              claimCount,
			AdjustedCustomerRefund: cancelEstimate.AdjustedCustomerRefund,
			StoreChargeback:        cancelEstimate.StoreChargeback,
			SPPRefund:              cancelEstimate.SPPRefund,
			RSARefund:              cancelEstimate.RSARefund,
			RefundType:             reqPayload.RefundType,
			PayeeType:              estimatePayload.CancelPayee,
			PayeeName:              estimatePayload.CancelPayeeName,
			PayeeAttentionTo:       estimatePayload.CancelPayeeAttentionTo,
			PayeeAddress:           estimatePayload.CancelPayeeAddress,
			PayeeCity:              estimatePayload.CancelPayeeCity,
			PayeeState:             estimatePayload.CancelPayeeState,
			PayeePostalCode:        estimatePayload.CancelPayeePostalCode,
			CancelStatus:           db.CancelStatusCanceledPendingInvoicing,
			ApplyFee:               estimatePayload.ApplyFee,
			IsElectronicCheck:      estimatePayload.IsElectronicCheck,
			Email:                  estimatePayload.Email,
			CheckApplicable:        estimatePayload.CancelPayee == db.CancelPayeeTypeCustomer,
		}

		if estimatePayload.SkipBill {
			contractCancellations.CancelStatus = db.CancelStatusBillNotIssued
		}

		if isTaxApplicable(contract, cr, &company) {
			if cancelEstimate.SalesTaxRate.Equals(decimal.Zero) {
				contractCancellations.SalesTaxRate = estimatePayload.ManualTaxRate
			} else {
				contractCancellations.SalesTaxRate = cancelEstimate.SalesTaxRate
			}
			contractCancellations.SalesTax = cancelEstimate.SalesTax
		}

		data := struct {
			StoreCancelRefundByTCA        bool      `db:"store_cancel_refund_by_tca" json:"-"`
			CompanyCancelRefundByTCA      bool      `db:"company_cancel_refund_by_tca" json:"-"`
			CompanyGroupCancelRefundByTCA null.Bool `db:"company_group_cancel_refund_by_tca" json:"-"`
		}{}
		err = db.Get().Get(&data, `
	select s.cancel_refund_by_tca store_cancel_refund_by_tca,
	c.cancel_refund_by_tca company_cancel_refund_by_tca,
	cg.cancel_refund_by_tca company_group_cancel_refund_by_tca
	from stores s
		join companies c on s.company_id = c.id
		left join company_groups cg on c.company_group_id = cg.id
	where s.id = $1`, store.ID)
		if err != nil {
			err = errors.Wrap(err, "error looking up cancel refund by TCA")
			ReportError(r, err)
			return http.StatusInternalServerError, ErrorMessage("Error in getting cancel refund by TCA option", nil)
		}

		if estimatePayload.SkipBill {
			contractCancellations.CancelStatus = db.CancelStatusBillNotIssued
		} else if data.CompanyGroupCancelRefundByTCA.Valid && data.CompanyGroupCancelRefundByTCA.Bool ||
			data.CompanyCancelRefundByTCA || data.StoreCancelRefundByTCA {
			if estimatePayload.CancelPayee == db.CancelPayeeTypeCustomer ||
				estimatePayload.CancelPayee == db.CancelPayeeTypeLender {
				contractCancellations.CancelStatus = db.CancelStatusCanceledPendingInvoicing
			} else {
				contractCancellations.CancelStatus = db.CancelStatusCancelSubmittedPendingInvoice
			}
		} else {
			contractCancellations.CancelStatus = db.CancelStatusCancelSubmittedPendingInvoice
		}

		csQuery := `insert into contract_cancellations(
                created_at,
                created_by_user_id,
                contract_id,
                cancel_date,
                cancel_mileage,
                cancel_reason,
                cancel_fee,
                cancel_factor,
                customer_refund,
				sales_tax_rate,
				sales_tax,
				store_refund,
				third_party_refund,
				claims_paid_amount,
				claims_deducted_amount,
                unused_visits,
				is_void,
				transaction_id,
				spp_amount_paid,
				spp_balance,
				nsd_claims,
				adj_customer_refund,
				store_chargeback,
				spp_refund,
				rsa_refund,
				refund_type,
				payee_type,
				payee_name,
				payee_attention_to,
				payee_address,
				payee_city,
				payee_state_code,
				payee_postal_code,
				cancel_status,
				apply_fee,
				is_electronic_check,
				email,
				check_applicable
			)
            values (now() at time zone 'utc',
				:created_by_user_id,
				:contract_id,
				:cancel_date,
				:cancel_mileage,
				:cancel_reason,
				:cancel_fee,
				:cancel_factor,
				:customer_refund,
				:sales_tax_rate,
				:sales_tax,
				:store_refund,
				:third_party_refund,
				:claims_paid_amount,
				:claims_deducted_amount,
				:unused_visits,
				:is_void,
				:transaction_id,
				:spp_amount_paid,
				:spp_balance,
				:nsd_claims,
				:adj_customer_refund,
				:store_chargeback,
				:spp_refund,
				:rsa_refund,
				:refund_type,
				:payee_type,
				:payee_name,
				:payee_attention_to,
				:payee_address,
				:payee_city,
				:payee_state_code,
				:payee_postal_code,
				:cancel_status,
				:apply_fee,
				:is_electronic_check,
				:email,
				:check_applicable
			) returning id`
		// store details of cancel
		stmt, err := tx.PrepareNamed(csQuery)
		if err != nil {
			tx.Rollback()
			ReportError(r, errors.Wrapf(err, "cancel contract : failed to prepare query to store cancellation for contract %d", contract.ID))
			return http.StatusInternalServerError, ErrorMessage("cancel contract : failed to prepare query to store cancellation", nil)
		}
		defer func() { _ = stmt.Close() }()

		var cancelID int
		err = stmt.Get(&cancelID, contractCancellations)
		if err != nil {
			tx.Rollback()
			ReportError(r, errors.Wrapf(err, "failed to store cancellation information for contract %d", contract.ID))
			return http.StatusInternalServerError, ErrorMessage("failed to store cancellation information", nil)
		}

		if contract.ClaimsCount > 0 {
			_, err = tx.Exec(`insert into contract_flags (flag_reason, contract_id, flagged_at, created_by_user_id) values ($1, $2, now() at time zone 'utc', $3)`, db.ContractFlagClaimFromCancelRefund, contract.ID, estimatePayload.UserID)
			if err != nil {
				_ = tx.Rollback()
				ReportError(r, errors.Wrapf(err, "failed to store contract flag information for contract %d", contract.ID))
				return http.StatusInternalServerError, ErrorMessage("Failed to store contract flag information", nil)
			}
		}

		if !isAdmin && reqPayload.HasClaimRequested {
			_, err = tx.Exec(`insert into contract_flags (flag_reason, contract_id, flagged_at, created_by_user_id) values ($1, $2, now() at time zone 'utc', $3)`, db.ContractFlagPendingCancel, contract.ID, estimatePayload.UserID)
			if err != nil {
				_ = tx.Rollback()
				ReportError(r, errors.Wrapf(err, "failed to store contract flag information for contract %d", contract.ID))
				return http.StatusInternalServerError, ErrorMessage("failed to store contract flag information", nil)
			}
		}

		// Add cancel breakouts
		ctx := context.TODO() // TODO: receive ctx from the calling function
		for _, bo := range cancelEstimate.Breakouts {
			// TODO: Stop saving contract_cancellation_breakout
			bo.CreatedByUserID = user.ID
			bo.ContractCancellationID = cancelID
			err = db.SaveContractCancellationBreakout(ctx, tx, bo)
			if err != nil {
				tx.Rollback()
				err = errors.Wrapf(err, "error adding cancel refund breakouts for contract %s", contract.Code)
				ReportError(r, err)
				return http.StatusInternalServerError, ErrorMessage("failed to cancel contract", nil)
			}

			var tbo db.TransactionBreakout
			tbo.TransactionID = tran.ID
			tbo.CreatedByUserID = user.ID
			tbo.RateBucketID = bo.RateBucketID
			tbo.RefundRateBucketID = null.IntFrom(int64(bo.RefundRateBucketID))
			tbo.Amount = bo.Amount
			tbo.RefundType = bo.RefundType
			err = db.InsertTransactionBreakout(ctx, tx, tbo)
			if err != nil {
				tx.Rollback()
				err = errors.Wrapf(err, "error adding transaction breakouts for contract %s", contract.Code)
				ReportError(r, err)
				return http.StatusInternalServerError, ErrorMessage("failed to cancel contract", nil)
			}
		}

		//update status of the contract with cancelled event
		_, err = tx.Exec(`update contracts
			set version = version + 1, 
			status = $1,
			event = $2,
			event_notes=$3
			where id = $4`, db.ContractStatusCancelled, db.ContractEventCancel, "", contract.ID)
		if err != nil {
			tx.Rollback()
			ReportError(r, errors.Wrapf(err, "failed to update status for contract %d", contract.ID))
			return http.StatusInternalServerError, ErrorMessage("failed to update contract status", nil)
		}

		cancellationEventQ := `insert into contract_events(created_at,created_by_user_id,created_by_name,contract_id,description)values(
			now() at time zone 'utc', $1, $2, $3, $4)`
		_, err = tx.Exec(cancellationEventQ, user.ID, user.FullName(), contract.ID, db.ContractEventCancel)
		if err != nil {
			_ = tx.Rollback()
			err = errors.Wrap(err, "error inserting contract event")
			ReportError(r, err)
			return http.StatusInternalServerError, ErrorMessage("Error while logging contract cancel event", nil)
		}

		// Call NSD Cancellation
		// The NSD logic will determine if the call to NSD is necessary
		// Ignoring the first return parameter as this code doesn't need to know if NSD's api was called or not.
		_, err = nsd.CancelContract(ctx, tx, contract.ID, estimatePayload.CancelDate.Time, user.ID)
		if err != nil {
			_ = tx.Rollback()
			err = errors.WithMessage(err, "error cancelling contract in nsd")
			ReportError(r, err)
			return http.StatusInternalServerError, ErrorMessage("Error cancelling contract in nsd", nil)
		}

		// save attachment
		if !user.HasRole(db.RoleTestAutomation) || len(attachments) > 0 {
			for _, att := range attachments {
				att.ContractID = contractID
				_, err = db.CreateContractAttachment(tx, att)
				if err != nil {
					_ = tx.Rollback()
					err = errors.Wrapf(err, "error saving attachment %s for contract %d", att.FileName, att.ContractID)
					ReportError(r, err)
					return http.StatusInternalServerError, ErrorMessage("error saving attachment for contract cancellation", nil)
				}
			}
		}

		// Save contract note if provided
		if noteText, ok := reqPayload.ContractNotes[contract.ID]; ok && noteText != "" {
			err := saveContractNote(tx, contract.ID, noteText, user.ID)
			if err != nil {
				tx.Rollback()
				ReportError(r, errors.Wrapf(err, "failed to save note for contract %d", contract.ID))
				return http.StatusInternalServerError, ErrorMessage(fmt.Sprintf("Failed to save note for contract %s", contract.Code), nil)
			}
		}

		cancelCount = cancelCount + 1
	}

	err = tx.Commit()
	if err != nil {
		ReportError(r, errors.Wrap(err, "transaction commit error in cancellation"))
		return http.StatusInternalServerError, ErrorMessage("transaction commit error in cancellation", nil)
	}

	return http.StatusOK, map[string]interface{}{"message": "Canceled successfully!", "count": cancelCount, "warnings": warnings}
}

func saveContractNote(tx *sqlx.Tx, contractID int, noteText string, userID int) error {
	note := db.ContractNote{
		ContractID:      contractID,
		NoteText:        noteText,
		CreatedByUserID: userID,
	}

	const q = `insert into contract_notes (contract_id, created_by_user_id, notes_text, created_at)
				values (:contract_id, :created_by_user_id, :notes_text, now() at time zone 'utc')`
	_, err := tx.NamedExec(q, note)
	if err != nil {
		return err
	}
	return nil
}

func validateUndoCancellationRequest(id int) error {
	status := ""
	err := db.Get().Get(&status, `select status from contracts where id = $1`, id)
	if err != nil {
		return err
	}
	if status != db.ContractStatusCancelled {
		return errors.New("can not revert cancellation on non-cancelled contract")
	}
	// Verify cancellation is not invoiced yet
	invoiceID := sql.NullInt64{}
	err = db.Get().Get(&invoiceID,
		`select t.invoice_id from contract_cancellations cc
			join transactions t on cc.transaction_id = t.id
			where cc.contract_id = $1 and cc.is_void = false`,
		id)
	if err != nil && err != sql.ErrNoRows {
		return err
	}
	if invoiceID.Valid {
		return errors.New("contract is invoiced already")
	}
	return nil
}

// ContractUndoCancel re-activates a contract that has been cancelled but not invoiced
func ContractUndoCancel(w http.ResponseWriter, r *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := r.Context()
	id, err := strconv.Atoi(chi.URLParam(r, "id"))
	if err != nil {
		return http.StatusBadRequest, ErrorMessage("invalid contact ID", nil)
	}

	if err := validateUndoCancellationRequest(id); err != nil {
		return http.StatusBadRequest, ErrorMessage(err.Error(), nil)
	}

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		ReportError(r, errors.Wrap(err, "error beginning transaction"))
		return http.StatusInternalServerError, ErrorMessage("transaction error", nil)
	}

	// create contract logs
	err = db.CreateContractLog(tx, id, user.ID)
	if err != nil {
		tx.Rollback()
		ReportError(r, errors.Wrapf(err, "error contract_logs insert for contract ID %d", id))
		return http.StatusInternalServerError, ErrorMessage("error in create contract logs", nil)
	}

	var cancelTransactionID null.Int
	q := `select transaction_id from contract_cancellations where contract_id = $1 and is_void = false`
	err = tx.GetContext(ctx, &cancelTransactionID, q, id)
	if err != nil {
		tx.Rollback()
		ReportError(r, errors.Wrapf(err, "error getting transactionID for contract %d", id))
		return http.StatusInternalServerError, ErrorMessage("Error undoing contract cancel.", nil)
	}

	//The way which we get previous valid invoiced contract is depends on order of
	// Contract cancellation created which seems to be working for now.
	// TODO: Find better way to get last successful valid invoiced cancellation id.
	var previousValidCancellation null.Int
	query := `select cc.id from contract_cancellations cc
			  join transactions t on cc.transaction_id = t.id
			  where cc.contract_id = $1
				and t.invoiced_at is not null
				and t.invoice_id is not null
			  order by voided_at desc limit 1`
	err = tx.GetContext(ctx, &previousValidCancellation, query, id)
	if err != nil && err != sql.ErrNoRows {
		tx.Rollback()
		ReportError(r, errors.Wrapf(err, "error getting cancellation details for contract %d", id))
		return http.StatusInternalServerError, ErrorMessage("Error undoing contract cancel.", nil)
	}

	// void cancellation entry in contract_cancellations
	_, err = tx.ExecContext(ctx, `update contract_cancellations
		set is_void = true,
		voided_at = now() at time zone 'utc',
		voided_by_user_id = $1,
		transaction_id = null
		where contract_id = $2
		and is_void = false`, user.ID, id)
	if err != nil {
		tx.Rollback()
		ReportError(r, errors.Wrapf(err, "failed to void last cancellation for contract %d", id))
		return http.StatusInternalServerError, ErrorMessage("Failed to void last cancellation.", nil)
	}

	// We are un-voiding last successfully invoiced cancellation here
	// Because we want to show the cancellation details if we undo the cancel and we have previous successful
	// invoiced cancel.
	if previousValidCancellation.Valid {
		_, err = tx.ExecContext(ctx, `update contract_cancellations
		set is_void = false,
		voided_at = null,
		voided_by_user_id = null
		where id = $1
		and is_void = true`, previousValidCancellation)
		if err != nil {
			tx.Rollback()
			ReportError(r, errors.Wrapf(err, "failed to un-void last invoiced cancellation for contract %d", id))
			return http.StatusInternalServerError, ErrorMessage("Failed to un-void last invoiced cancellation.", nil)
		}
	}

	// delete transaction and transaction_remittals that were linked to contract_cancellation
	_, err = tx.ExecContext(ctx, "delete from transaction_remittals where transaction_id = $1", cancelTransactionID)
	if err != nil {
		tx.Rollback()
		ReportError(r, errors.Wrapf(err, "error deleting transaction_remittals for contract ID %d", id))
		return http.StatusInternalServerError, ErrorMessage("Failed to void last cancellation.", nil)
	}
	_, err = tx.ExecContext(ctx, "delete from transactions where id = $1", cancelTransactionID)
	if err != nil {
		tx.Rollback()
		ReportError(r, errors.Wrapf(err, "error deleting transaction for contract ID %d", id))
		return http.StatusInternalServerError, ErrorMessage("Failed to void last cancellation.", nil)
	}

	//delete flag entry in contract_flags
	_, err = tx.ExecContext(ctx, `update contract_flags set deleted_at = now() at time zone 'utc', deleted_by_user_id = $1 where contract_id = $2 and flag_reason = $3 and deleted_at is NULL`, user.ID, id, db.ContractFlagPendingCancel)
	if err != nil {
		_ = tx.Rollback()
		ReportError(r, errors.Wrapf(err, "db error while updating contract flags for contract ID %d", id))
		return http.StatusInternalServerError, ErrorMessage("failed to update contract flags", nil)
	}

	//update status of the contract with activate event
	_, err = tx.ExecContext(ctx, `update contracts set status = $1, event = $2, event_notes=$3 where id = $4`, db.ContractStatusActive, db.ContractEventUndoCancel, "", id)
	if err != nil {
		tx.Rollback()
		ReportError(r, errors.Wrapf(err, "failed to update status for contract %d", id))
		return http.StatusInternalServerError, ErrorMessage("failed to update contract status", nil)
	}

	query = `insert into contract_events(created_at,created_by_user_id,created_by_name,contract_id,description)values(
	now() at time zone 'utc', $1, $2, $3, $4)`
	_, err = tx.ExecContext(ctx, query, user.ID, user.FullName(), id, db.ContractEventUndoCancel)
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "error inserting contract event")
		ReportError(r, err)
		return http.StatusInternalServerError, ErrorMessage("Error while logging contract undo cancel event", nil)
	}

	err = tx.Commit()
	if err != nil {
		ReportError(r, errors.Wrap(err, "transaction commit error in undo cancellation"))
		return http.StatusInternalServerError, ErrorMessage("transaction commit error in undo cancellation", nil)
	}

	return http.StatusOK, map[string]interface{}{"message": "Re-activated contract successfully!"}
}

// IsCancelReasonOther returns whether the cancel options' cancel reason is considered
// other cancel reason for the cancel form PDF.
func (co CancelContractOptions) IsCancelReasonOther() bool {
	switch co.CancelReasonName {
	case db.CancelReasonTotalLoss:
		fallthrough
	case db.CancelReasonSoldTraded:
		fallthrough
	case db.CancelReasonLoanPaid:
		return false
	}
	return true
}

type cancelRuleVariation struct {
	ID                     int                          `db:"id"`
	CancelFeeType          string                       `db:"cancel_fee_type"`
	CancelFeeFixedAmount   types.JSNullDecimal          `db:"cancel_fee_fixed_amount"`
	CancelFeePercentAmount types.JSNullDecimal          `db:"cancel_fee_percent_amount"`
	CancelFeePercentOf     null.String                  `db:"cancel_fee_percent_of"`
	CancelCalculations     types.CancelCalculationRules `db:"cancel_calculations"`
	FlatCancelDays         int                          `db:"flat_cancel_days"`
	FlatCancelOp           string                       `db:"flat_cancel_op"`
	FlatCancelDeductClaims bool                         `db:"flat_cancel_deduct_claims"`
	FlatCancelDeductFee    bool                         `db:"flat_cancel_deduct_fee"`
	FlatCancelFeeException bool                         `db:"flat_cancel_fee_exception"`
	VaryByState            null.Bool                    `db:"vary_by_state"`
	State                  null.String                  `db:"state"`
	VaryByLender           null.Bool                    `db:"vary_by_lender"`
	Lender                 null.String                  `db:"lender"`
	VaryByCancelReason     null.Bool                    `db:"vary_by_cancel_reason"`
	CancelReasonIDs        pq.Int64Array                `db:"cancel_reason_ids"`
}

func selectCRVariation(crvs []cancelRuleVariation, state, lender string, cancelReasonID int) cancelRuleVariation {
	// check if all three state, lender and cancel reason match
	for _, crv := range crvs {
		if (crv.VaryByState.Bool && strings.EqualFold(crv.State.String, state)) && //state name matches
			(crv.VaryByLender.Bool && strings.Contains(strings.ToLower(lender), strings.ToLower(crv.Lender.String))) &&
			(crv.VaryByCancelReason.Bool && util.IntSliceContains(crv.CancelReasonIDs, int64(cancelReasonID))) {
			return crv
		}
	}

	//check if state or lender and cancel reason match
	for _, crv := range crvs {
		if ((crv.VaryByState.Bool && strings.EqualFold(crv.State.String, state)) ||
			(crv.VaryByLender.Bool && strings.Contains(strings.ToLower(lender), strings.ToLower(crv.Lender.String)))) &&
			(crv.VaryByCancelReason.Bool && util.IntSliceContains(crv.CancelReasonIDs, int64(cancelReasonID))) {
			return crv
		}
	}

	// remaining cases, state or lender or both
	for _, crv := range crvs {
		if (crv.VaryByState.Bool == false || // vary by state not selected
			(crv.VaryByState.Bool && strings.EqualFold(crv.State.String, state))) && //state name matches
			(crv.VaryByLender.Bool == false || // vary by lender not selected
				(crv.VaryByLender.Bool && strings.Contains(strings.ToLower(lender), strings.ToLower(crv.Lender.String)))) &&
			(crv.VaryByCancelReason.Bool == false || // vary by cancel reason not selected
				(crv.VaryByCancelReason.Bool && util.IntSliceContains(crv.CancelReasonIDs, int64(cancelReasonID)))) {
			return crv
		}
	}
	return cancelRuleVariation{}
}

// GetCompanyForContract returns the company for a contract
func GetCompanyForContract(ctx context.Context, contractID int) (db.Company, error) {
	var company db.Company
	query := `select comp.id, comp.name, comp.is_sales_tax_applicable from companies comp
		inner join stores s on comp.id = s.company_id
		inner join contracts c on c.store_id = s.id
		where c.id = $1`
	err := db.Get().GetContext(ctx, &company, query, contractID)
	if err != nil {
		return company, errors.Wrap(err, "error getting company for contract")
	}
	return company, nil
}

// For some states, the sales tax is manually overridden by the user
// even if the DMS returns a value for sales tax rate.
func isManualTaxOverrideState(stateCode string) bool {
	for _, state := range db.ManualTaxStates {
		if strings.EqualFold(state, stateCode) {
			return true
		}
	}
	return false
}
