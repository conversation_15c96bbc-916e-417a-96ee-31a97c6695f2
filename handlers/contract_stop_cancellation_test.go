package handlers

import (
	"encoding/json"
	"testing"
	"time"
	"whiz/db"
	"whiz/types"

	"github.com/shopspring/decimal"
	"gopkg.in/guregu/null.v3"
)

func Test_applyRule(t *testing.T) {
	effDate := types.JSPQNullDate{}
	effDate.Scan(time.Date(2014, time.March, 29, 0, 0, 0, 0, time.UTC))
	expDate := types.JSPQNullDate{}
	expDate.Scan(time.Date(2020, time.March, 29, 0, 0, 0, 0, time.UTC))
	type args struct {
		rule                rule
		contract            ContractDetails
		cancellationRequest EstimateRequestInfo
	}
	financeTerm := null.Int{}
	financeTerm.Scan(60)
	expirationMileage := null.Int{}
	expirationMileage.Scan(100000)

	cancelDate := types.JSPQDate{}
	cancelDate.Scan(time.Date(2015, time.March, 29, 0, 0, 0, 0, time.UTC))

	nestedRuleString := `
	{
	   "items":[
	      {
	         "property":"cancel_date",
	         "operator":">",
	         "value":"effective_date"
	      },
	      {
	         "logic":"or",
	         "items":[
	            {
	               "property":"cancel_miles",
	               "operator":">",
	               "value":"beginning_miles"
	            },
	            {
	               "property":"payment_type",
	               "operator":"in",
	               "value":[
	                  "Lease"
	               ]
	            }
	         ]
	      },
	      {
	         "property":"cancel_miles",
	         "operator":">",
	         "value":"beginning_miles"
	      },
	      {
	         "property":"cancel_miles",
	         "operator":"<",
	         "value":"expiration_miles"
	      }
	   ],
	   "logic":"and"
	}`
	nestedRule := rule{}
	err := json.Unmarshal([]byte(nestedRuleString), &nestedRule)
	if err != nil {
		t.Errorf("failed to convert rules struct %v", err)
	}

	allAndRuleString := `
	{
	   "items":[
	      {
	         "property":"cancel_date",
	         "operator":">",
	         "value":"effective_date"
	      },
	      {
	         "property":"cancel_miles",
	         "operator":">",
	         "value":"beginning_miles"
	      },
	      {
	         "property":"cancel_reason",
	         "operator":"in",
	         "value":[
	            "1",
	            "2"
	         ]
	      },
	      {
	         "property":"claim_count",
	         "operator":">=",
	         "value":0
	      },
	      {
	         "property":"store_state_code",
	         "operator":"in",
	         "value":[
	            "AK",
	            "UT"
	         ]
	      },
	      {
	         "property":"payment_type",
	         "operator":"in",
	         "value":[
	            "Cash"
	         ]
	      },
	      {
	         "property":"contract_status",
	         "operator":"in",
	         "value":[
	            "Active",
	            "Remitted"
	         ]
	      },
	      {
	         "property":"contract_code",
	         "operator":"starts with",
	         "value":"PPM"
	      },
	      {
	         "property":"issuing_dealer",
	         "operator":"in",
	         "value":[
	            "ACJ",
	            "ACD"
	         ]
	      },
	      {
	         "property":"case_reserve_amount",
	         "operator":">",
	         "value":100
	      },
	      {
	         "property":"is_invoiced",
	         "operator":"is",
	         "value":false
	      },
	      {
	         "property":"claim_in_process",
	         "operator":"is",
	         "value":false
	      }
	   ],
	   "logic":"and"
	}`
	allAndRule := rule{}
	err = json.Unmarshal([]byte(allAndRuleString), &allAndRule)
	if err != nil {
		t.Errorf("failed to convert rules struct %v", err)
	}

	allOrRuleString := `
	{
	   "items":[
	      {
	         "property":"cancel_date",
	         "operator":"<",
	         "value":"effective_date"
	      },
	      {
	         "property":"cancel_miles",
	         "operator":">",
	         "value":"beginning_miles"
	      },
	      {
	         "property":"cancel_reason",
	         "operator":"in",
	         "value":[
	            "1",
	            "2"
	         ]
	      },
	      {
	         "property":"claim_count",
	         "operator":">=",
	         "value":0
	      },
	      {
	         "property":"store_state_code",
	         "operator":"in",
	         "value":[
	            "AK",
	            "UT"
	         ]
	      },
	      {
	         "property":"payment_type",
	         "operator":"in",
	         "value":[
	            "Cash"
	         ]
	      },
	      {
	         "property":"contract_status",
	         "operator":"in",
	         "value":[
	            "Active",
	            "Remitted"
	         ]
	      },
	      {
	         "property":"contract_code",
	         "operator":"starts with",
	         "value":"PPM"
	      },
	      {
	         "property":"issuing_dealer",
	         "operator":"in",
	         "value":[
	            "ACJ",
	            "ACD"
	         ]
	      },
	      {
	         "property":"case_reserve_amount",
	         "operator":">",
	         "value":100
	      },
	      {
	         "property":"is_invoiced",
	         "operator":"is",
	         "value":false
	      },
	      {
	         "property":"claim_in_process",
	         "operator":"is",
	         "value":false
	      }
	   ],
	   "logic":"or"
	}`
	allOrRule := rule{}
	err = json.Unmarshal([]byte(allOrRuleString), &allOrRule)
	if err != nil {
		t.Errorf("failed to convert all OR rules struct %v", err)
	}

	allowStoresToCancelRuleString := `
	{
	   "items":[
	      {
	         "property":"allow_stores_to_cancel",
	         "operator":"is",
	         "value":true
	      }
	   ],
	   "logic":"and"
	}`
	allowStoresToCancelRule := rule{}
	err = json.Unmarshal([]byte(allowStoresToCancelRuleString), &allowStoresToCancelRule)
	if err != nil {
		t.Errorf("failed to convert rules struct %v", err)
	}

	cancelDateAgeCancelRuleString := `
	{
	   "items":[
	      {
	         "property":"cancel_date_age",
	         "operator":">",
	         "value": 90
	      }
	   ],
	   "logic":"and"
	}`
	cancelDateAgeCancelRule := rule{}
	err = json.Unmarshal([]byte(cancelDateAgeCancelRuleString), &cancelDateAgeCancelRule)
	if err != nil {
		t.Errorf("failed to convert rules struct %v", err)
	}

	tests := []struct {
		name            string
		args            args
		wantCancellable bool
	}{
		{name: "Verify apply rule with empty conditions", args: args{
			rule: rule{},
			contract: ContractDetails{
				ID:                1,
				Code:              "PPM32",
				EffectiveDate:     effDate,
				ExpirationDate:    expDate,
				EffectiveMileage:  0,
				ExpirationMileage: expirationMileage,
				StoreCode:         "ACJ",
				Price:             decimal.NewFromFloat(1000),
				PlanCost:          decimal.NewFromFloat(1000),
				CustomerID:        1,
				Status:            "Active",
				FinanceTerm:       financeTerm,
				ProductTypeCode:   "MC",
				ProductTypeName:   "Maintenance",
				Lender:            "",
				PaymentType:       "Cash",
				StoreStateCode:    "AK",
				FirstPaymentDate:  types.JSPQNullDate{},
				PlanDuration:      0,
				ClaimsAmount:      decimal.NewFromFloat(200),
				IsInvoiced:        true,
				ClaimInProcess:    false,
			},
			cancellationRequest: EstimateRequestInfo{
				CancelContractOptions: CancelContractOptions{
					CancelDate:     cancelDate,
					CancelReasonID: 1,
					Mileage:        50000,
				},
				Contracts: []int{},
			},
		}, wantCancellable: true},
		{name: "Verify apply rule with nested conditions", args: args{
			rule: nestedRule,
			contract: ContractDetails{
				ID:                1,
				Code:              "PPM32",
				EffectiveDate:     effDate,
				ExpirationDate:    expDate,
				EffectiveMileage:  0,
				ExpirationMileage: expirationMileage,
				StoreCode:         "ACJ",
				Price:             decimal.NewFromFloat(1000),
				PlanCost:          decimal.NewFromFloat(1000),
				CustomerID:        1,
				Status:            "Active",
				FinanceTerm:       financeTerm,
				ProductTypeCode:   "MC",
				ProductTypeName:   "Maintenance",
				Lender:            "",
				PaymentType:       "Cash",
				StoreStateCode:    "AK",
				FirstPaymentDate:  types.JSPQNullDate{},
				PlanDuration:      0,
				ClaimsAmount:      decimal.NewFromFloat(200),
				IsInvoiced:        true,
				ClaimInProcess:    false,
			},
			cancellationRequest: EstimateRequestInfo{
				CancelContractOptions: CancelContractOptions{
					CancelDate:     cancelDate,
					CancelReasonID: 1,
					Mileage:        50000,
				},
				Contracts: []int{},
			},
		}, wantCancellable: false},
		{name: "Verify apply rule with all 'and' condition", args: args{
			rule: allAndRule,
			contract: ContractDetails{
				ID:                1,
				Code:              "PPM32",
				EffectiveDate:     effDate,
				ExpirationDate:    expDate,
				EffectiveMileage:  0,
				ExpirationMileage: expirationMileage,
				StoreCode:         "ACJ",
				Price:             decimal.NewFromFloat(1000),
				PlanCost:          decimal.NewFromFloat(1000),
				CustomerID:        1,
				Status:            "Active",
				FinanceTerm:       financeTerm,
				ProductTypeCode:   "MC",
				ProductTypeName:   "Maintenance",
				Lender:            "",
				PaymentType:       "Cash",
				StoreStateCode:    "AK",
				FirstPaymentDate:  types.JSPQNullDate{},
				PlanDuration:      0,
				ClaimsAmount:      decimal.NewFromFloat(150),
			},
			cancellationRequest: EstimateRequestInfo{
				CancelContractOptions: CancelContractOptions{
					CancelDate:     cancelDate,
					CancelReasonID: 1,
					Mileage:        50000,
				},
				Contracts: []int{},
			},
		}, wantCancellable: false},
		{name: "Verify apply rule with all 'or' condition", args: args{
			rule: allOrRule,
			contract: ContractDetails{
				ID:                1,
				Code:              "PPM32",
				EffectiveDate:     effDate,
				ExpirationDate:    expDate,
				EffectiveMileage:  0,
				ExpirationMileage: expirationMileage,
				StoreCode:         "ACJ",
				Price:             decimal.NewFromFloat(1000),
				PlanCost:          decimal.NewFromFloat(1000),
				CustomerID:        1,
				Status:            "Active",
				FinanceTerm:       financeTerm,
				ProductTypeCode:   "MC",
				ProductTypeName:   "Maintenance",
				Lender:            "",
				PaymentType:       "Cash",
				StoreStateCode:    "AK",
				FirstPaymentDate:  types.JSPQNullDate{},
				PlanDuration:      0,
				ClaimsAmount:      decimal.NewFromFloat(150),
			},
			cancellationRequest: EstimateRequestInfo{
				CancelContractOptions: CancelContractOptions{
					CancelDate:     cancelDate,
					CancelReasonID: 1,
					Mileage:        50000,
				},
				Contracts: []int{},
			},
		}, wantCancellable: false},
		{name: "Verify apply rule with allow stores to cancel condition", args: args{
			rule: allowStoresToCancelRule,
			contract: ContractDetails{
				ID:                         1,
				Code:                       "PPM32",
				EffectiveDate:              effDate,
				ExpirationDate:             expDate,
				EffectiveMileage:           0,
				ExpirationMileage:          expirationMileage,
				StoreCode:                  "ACJ",
				Price:                      decimal.NewFromFloat(1000),
				PlanCost:                   decimal.NewFromFloat(1000),
				CustomerID:                 1,
				Status:                     "Active",
				FinanceTerm:                financeTerm,
				ProductTypeCode:            "MC",
				ProductTypeName:            "Maintenance",
				Lender:                     "",
				PaymentType:                "Cash",
				FirstPaymentDate:           types.JSPQNullDate{},
				PlanDuration:               0,
				ClaimsAmount:               decimal.NewFromFloat(150),
				ProductCancellableByStores: true,
			},
			cancellationRequest: EstimateRequestInfo{
				CancelContractOptions: CancelContractOptions{
					CancelDate:     cancelDate,
					CancelReasonID: 1,
					Mileage:        50000,
				},
				Contracts: []int{},
			},
		}, wantCancellable: false},
		{name: "Verify apply rule with cancel date age to cancel condition", args: args{
			rule: cancelDateAgeCancelRule,
			contract: ContractDetails{
				ID:                         1,
				Code:                       "PPM32",
				EffectiveDate:              effDate,
				ExpirationDate:             expDate,
				EffectiveMileage:           0,
				ExpirationMileage:          expirationMileage,
				StoreCode:                  "ACJ",
				Price:                      decimal.NewFromFloat(1000),
				PlanCost:                   decimal.NewFromFloat(1000),
				CustomerID:                 1,
				Status:                     "Active",
				FinanceTerm:                financeTerm,
				ProductTypeCode:            "MC",
				ProductTypeName:            "Maintenance",
				Lender:                     "",
				PaymentType:                "Cash",
				FirstPaymentDate:           types.JSPQNullDate{},
				PlanDuration:               0,
				ClaimsAmount:               decimal.NewFromFloat(150),
				ProductCancellableByStores: true,
			},
			cancellationRequest: EstimateRequestInfo{
				CancelContractOptions: CancelContractOptions{
					CancelDate:     cancelDate,
					CurrentDate:    time.Date(2015, time.July, 16, 0, 0, 0, 0, time.UTC),
					CancelReasonID: 1,
					Mileage:        50000,
				},
				Contracts: []int{},
			},
		}, wantCancellable: false},
	}
	user := db.CurrentUser{}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if gotCancellable := applyRule(tt.args.rule, tt.args.contract, tt.args.cancellationRequest.CancelContractOptions, user); gotCancellable != tt.wantCancellable {
				t.Errorf("applyRule() = %v, want %v", gotCancellable, tt.wantCancellable)
			}
		})
	}
}
