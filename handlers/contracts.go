package handlers

import (
	"bytes"
	"context"
	"crypto/sha512"
	"database/sql"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	"whiz/cdkddj"
	"whiz/conf"
	"whiz/db"
	"whiz/dms"
	"whiz/dmsfactory"
	"whiz/ghostscript"
	"whiz/nr"
	"whiz/nsd"
	"whiz/pdftk"
	"whiz/s3util"
	"whiz/slice"
	"whiz/types"
	"whiz/unidata"
	"whiz/util"

	sentry "github.com/getsentry/sentry-go"
	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"github.com/lib/pq"
	newrelic "github.com/newrelic/go-agent"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	null "gopkg.in/guregu/null.v3"
)

const (
	// RateSheetInspection const for insp tag from rate sheet
	RateSheetInspection = "insp"
	// RateSheetInspectionRefund const for insp_ref tag from rate sheet
	RateSheetInspectionRefund = "insp_ref"
)

type newContractPayload struct {
	UserID              int             `json:"-"`
	StoreID             int             `json:"store_id"`
	SaleID              int             `json:"sale_id"`
	QuotePlanID         int             `json:"quote_plan_id"`
	QuoteOptionIDs      []int           `json:"quote_option_ids"`
	QuoteSurchargeIDs   []int           `json:"quote_surcharge_ids"`
	Price               decimal.Decimal `json:"price"`
	QuoteCouponID       *int            `json:"quote_coupon_id"`
	EffectiveDateChange int             `json:"effective_date_change"`
	VehicleTheftNumber  null.String     `json:"vehicle_theft_number"`
}

// ContractEditDetails payload for ContractEditDetails
type ContractEditDetails struct {
	db.ContractWithData
	CurrentCost               decimal.Decimal    `db:"-" json:"current_cost"`
	EffectiveDateChange       int                `db:"-" json:"effective_date_change"`
	EffectiveDateChangeNonMNT null.Time          `db:"-" json:"effective_date_change_non_mnt"`
	ProductType               db.ProductType     `db:"-" json:"product_type"`
	Sale                      db.Sale            `db:"-" json:"sale"`
	Customer                  db.Customer        `db:"-" json:"customer"`
	Store                     db.Store           `db:"-" json:"store"`
	VINRecord                 db.VINRecord       `db:"-" json:"vin_record"`
	Plan                      db.Plan            `db:"-" json:"plan"`
	ProductVariant            db.ProductVariant  `db:"-" json:"product_variant"`
	Classification            *db.Classification `db:"-" json:"-"`
}

func (p *newContractPayload) clean() {
	p.VehicleTheftNumber.String = strings.TrimSpace(p.VehicleTheftNumber.String)
}

func (p newContractPayload) validate() (map[string]string, error) {
	valErrs := map[string]string{}

	if p.Price.Cmp(decimal.Zero) < 0 {
		valErrs["price"] = "Price cannot be negative"
	}

	query := `select s.* from stores s join stores_user_versions suv on s.id = suv.store_id join current_users cu on cu.user_version_id = suv.user_version_id where s.id = $1 and cu.id = $2`
	var store struct {
		AllowMaintenanceEffectiveDateChange bool `db:"allow_maintenance_effective_date_change"`
	}
	err := db.Get().Unsafe().Get(&store, query, p.StoreID, p.UserID)
	if err != nil {
		err = errors.Wrap(err, "error when validating store for user")
		return valErrs, err
	}

	var sale struct {
		FinanceAmount  decimal.NullDecimal `db:"finance_amount"`
		QuoteExpiresAt time.Time           `db:"quote_expires_at"`
		PriceRoundUp   bool                `db:"price_round_up"`
	}
	query = `select finance_amount, quote_expires_at, price_round_up from sales where id = $1 and store_id = $2`
	err = db.Get().Get(&sale, query, p.SaleID, p.StoreID)
	if err != nil {
		if err != sql.ErrNoRows {
			err = errors.Wrap(err, "error when validating sale for store")
			return valErrs, err
		}
		valErrs["sale_id"] = "Invalid sale"
		return valErrs, nil
	}
	if time.Now().After(sale.QuoteExpiresAt) {
		valErrs["sale_id"] = "The sale is no longer active for new contracts"
		return valErrs, nil
	}

	roundup := sale.PriceRoundUp

	if !conf.Get().EnablePriceRoundUp {
		roundup = false
	}

	var tcaCost decimal.Decimal
	var dealerCost decimal.Decimal
	var result struct {
		QuoteProductTypeID    int             `db:"quote_product_type_id"`
		QuoteProductTypeCode  string          `db:"quote_product_type_code"`
		QuoteProductVariantID int             `db:"id"`
		ProductVariantID      int             `db:"product_variant_id"`
		PlanCost              decimal.Decimal `db:"cost"`
		ContractDate          time.Time       `db:"contract_date"`
		PaymentType           string          `db:"payment_type"`
		BestCapPrice          decimal.Decimal `db:"best_cap_price"`
		BestCapID             null.Int        `db:"best_cap_id"`
	}
	query = `select 
		qpv.quote_product_type_id,
		qpt.code quote_product_type_code,
		qpv.id,
		qpv.product_variant_id,
		qp.cost,
		s.contract_date,
		s.payment_type,
		qp.best_cap_price,
		qp.best_cap_id
	from quote_plans qp
	join quote_product_variants qpv on qp.quote_product_variant_id = qpv.id
	join quote_product_types qpt on qpv.quote_product_type_id = qpt.id
	join sales s on qpt.sale_id = s.id
	where qp.id = $1
	and s.id = $2`
	err = db.Get().Get(&result, query, p.QuotePlanID, p.SaleID)
	if err != nil {
		if err != sql.ErrNoRows {
			err = errors.Wrap(err, "error when validating quote plan for sale")
			return valErrs, err
		}
		valErrs["quote_plan_id"] = "Invalid quote plan"
		return valErrs, nil
	}
	tcaCost = result.PlanCost
	dealerCost = result.PlanCost

	query = `select count(*)
	from contracts
	where quote_product_type_id = $1
	and sale_id = $2
	and status in ($3, $4, $5, $6)`
	var count int
	err = db.Get().Get(&count, query, result.QuoteProductTypeID, p.SaleID, db.ContractStatusGenerated, db.ContractStatusRemitted, db.ContractStatusActive, db.ContractStatusPending)
	if err != nil {
		err = errors.Wrap(err, "error when validating quote plan available for sale")
		return valErrs, err
	}
	if count > 0 {
		valErrs["quote_plan_id"] = "Contract already generated for this plan's product type"
		return valErrs, nil
	}

	if result.PaymentType == dms.PaymentTypeSPP && result.QuoteProductTypeCode == "MNT" {
		query = `select c.id, c.transmit_type
		from contracts c
		join quote_product_types qpt on c.quote_product_type_id = qpt.id
		where qpt.code = $1
		and c.sale_id = $2
		and c.status in ($3)`
		type SppContract struct {
			ID           int    `db:"id"`
			TransmitType string `db:"transmit_type"`
		}
		var dbSppContract SppContract
		err = db.Get().Get(&dbSppContract, query, "VSC", p.SaleID, db.ContractStatusGenerated)
		if err != nil && err != sql.ErrNoRows {
			err = errors.Wrap(err, "error when validating service quote plan no already generated for sale on SPP")
			return valErrs, err
		}
		if err == sql.ErrNoRows || dbSppContract.ID == 0 {
			valErrs["quote_plan_id"] = "On SPP, need to generate Service before Maintenance"
			return valErrs, nil
		} else if strings.EqualFold(dbSppContract.TransmitType, db.TransmitTypeAlpha) {
			valErrs["quote_plan_id"] = "On SPP, Maintenance cannot be financed with an Alpha VSC"
			return valErrs, nil
		}
	}

	if _, hasQPErr := valErrs["quote_plan_id"]; p.QuoteCouponID != nil && !hasQPErr {
		query = `select count(*) from quote_plans qp join quote_coupons qcp on qcp.quote_product_variant_id = qp.quote_product_variant_id where qcp.id = $1`
		count = 0
		err = db.Get().Get(&count, query, p.QuoteCouponID)
		if err != nil {
			err = errors.Wrap(err, "error validating coupon coupon")
			return valErrs, err
		}
		if count < 1 {
			valErrs["quote_coupon_id"] = "Invalid coupon"
		}
	}

	adjustmentCosts := []struct {
		Cost          decimal.Decimal `db:"cost"`
		IsInvoiceable bool            `db:"is_invoiceable"`
	}{}
	query = `select cost, is_invoiceable
		from quote_plan_adjustments
		where quote_plan_id = $1`
	err = db.Get().Select(&adjustmentCosts, query, p.QuotePlanID)
	if err != nil {
		err = errors.Wrap(err, "error getting cost of quote plan adjustment for validation")
		return nil, err
	}
	for _, adjCost := range adjustmentCosts {
		if adjCost.IsInvoiceable {
			tcaCost = tcaCost.Add(adjCost.Cost)
		}
		dealerCost = dealerCost.Add(adjCost.Cost)
	}

	ctx := context.Background()

	query = `select * from quote_caps where quote_product_variant_id = $1`
	quoteCaps := []db.QuoteCap{}
	err = db.Get().Unsafe().Select(&quoteCaps, query, result.QuoteProductVariantID)
	if err != nil {
		err = errors.Wrap(err, "error when selecting quote caps for quote product variant")
		return valErrs, err
	}
	getter := db.NewPricingFormulaParameterGetter(
		result.ProductVariantID,
		p.StoreID,
		result.ContractDate,
	)

	qCap, price, err := db.GetBestQuoteCap(ctx, quoteCaps, tcaCost, dealerCost, sale.FinanceAmount, getter, true, roundup)
	if err != nil && err != db.ErrNoPricingFormula {
		err = errors.WithMessage(err, "error when finding best quote cap for quote product variant")
		return valErrs, err
	}

	priceToCostRatio := decimal.NewFromFloat(1.0)

	if qCap != nil && qCap.PriceToCostRatio.Valid {
		priceToCostRatio = qCap.PriceToCostRatio.Decimal
	}
	query = `select cost from quote_options where id = $1 and quote_product_variant_id = $2`
	for _, quoteOptionID := range p.QuoteOptionIDs {
		var optionCost decimal.Decimal
		err = db.Get().Get(&optionCost, query, quoteOptionID, result.QuoteProductVariantID)
		if err != nil {
			if err != sql.ErrNoRows {
				err = errors.Wrap(err, "error when validating quote option for quote product variant")
				return valErrs, err
			}
			valErrs["quote_option_ids"] = "Invalid quote option"
			break
		}
		opPrice := optionCost.Mul(priceToCostRatio)
		if roundup {
			opPrice = opPrice.Ceil()
		} else {
			opPrice = opPrice.RoundBank(2)
		}
		price = price.Add(opPrice)
	}

	query = `select cost from quote_surcharges where id = $1 and quote_product_variant_id = $2`
	for _, quoteSurchargeID := range p.QuoteSurchargeIDs {
		var surchargeCost decimal.Decimal
		err = db.Get().Get(&surchargeCost, query, quoteSurchargeID, result.QuoteProductVariantID)
		if err != nil {
			if err != sql.ErrNoRows {
				err = errors.Wrap(err, "error when validating quote surcharge for quote product variant")
				return valErrs, err
			}
			valErrs["quote_surcharge_ids"] = "Invalid quote surcharge"
			break
		}
		sPrice := surchargeCost.Mul(priceToCostRatio)
		if roundup {
			sPrice = sPrice.Ceil()
		} else {
			sPrice = sPrice.RoundBank(2)
		}
		price = price.Add(sPrice)
	}

	if qCap != nil && p.Price.Cmp(price) > 0 {
		valErrs["price"] = "Price is above cap"
	}

	if result.QuoteProductTypeCode == "MNT" && store.AllowMaintenanceEffectiveDateChange {
		if p.EffectiveDateChange != 0 &&
			p.EffectiveDateChange != 6 &&
			p.EffectiveDateChange != 12 &&
			p.EffectiveDateChange != 18 &&
			p.EffectiveDateChange != 24 &&
			p.EffectiveDateChange != 36 {
			valErrs["effective_date_change"] = "Effective Date Change can only be 0, 6, 12, 18, 24 or 36"
		}
	} else {
		if p.EffectiveDateChange != 0 {
			valErrs["effective_date_change"] = "Effective Date Change is not allowed"
		}
	}

	// Require Vehicle Theft Number for Vehicle Theft Assistance contracts
	if result.QuoteProductTypeCode == "VTA" && (p.VehicleTheftNumber.String == "" || !p.VehicleTheftNumber.Valid) {
		valErrs["vehicle_theft_number"] = "VTA Number is required"
	}

	return valErrs, nil
}

const insertContractBankCodeQuery = `insert into contracts (
	"created_at", "created_by_user_id", "updated_at", "updated_by_user_id",
	"store_id", "sale_id", "quote_product_type_id", "code", "contract_form_id",
	"contract_form_s3_region", "contract_form_s3_bucket", "contract_form_file_name",
	"quote_coupon_id", "transmit_type", "unidata_code", "customer_id", "quote_plan_id",
	"vin_record_id", "effective_date", "effective_mileage", "expiration_date", "expiration_mileage",
	"price", "plan_cost", "core_rate_bucket_id", "status", "event", "event_notes",
	"finance_term", "finance_amount", "product_name", "plan_name",
	"plan_duration", "plan_mileage", "remitted_at", "remitted_by_user_id",
	"spp_company", "product_type_id", "plan_id", "coupon_id",
	"product_type_name", "product_type_code", "product_type_position",
	"classification_id", "product_variant_name", "product_variant_display_name",
	"additive_mileage", "plan_code", "maintenance_visits", "maintenance_visit_value",
	"dms_number", "sale_type", "is_dms_deal", "salesperson_id", "coupon_code", "coupon_amount",
	"lender", "product_variant_id", "vehicle_theft_number", "payment_type", "original_code", "is_e_contract",
	"dealer_platform_id", "e_contract_employee_number"
) values (
	now() at time zone 'utc', :created_by_user_id, now() at time zone 'utc', :updated_by_user_id,
	:store_id, :sale_id, :quote_product_type_id, :contract_code_bank_code, :contract_form_id,
	:contract_form_s3_region, :contract_form_s3_bucket, 'contract-forms-stamped/' || :contract_code_bank_code || '.pdf',
	:quote_coupon_id, :transmit_type, :unidata_code, :customer_id, :quote_plan_id,
	:vin_record_id, :effective_date, :effective_mileage, :expiration_date, :expiration_mileage,
	:price, :plan_cost, :core_rate_bucket_id, :status, :event, :event_notes,
	:finance_term, :finance_amount, :product_name, :plan_name,
	:plan_duration, :plan_mileage, :remitted_at, :remitted_by_user_id,
	:spp_company, :product_type_id, :plan_id, :coupon_id,
	:product_type_name, :product_type_code, :product_type_position,
	:classification_id, :product_variant_name, :product_variant_display_name,
	:additive_mileage, :plan_code, :maintenance_visits, :maintenance_visit_value,
	:dms_number, :sale_type, :is_dms_deal, :salesperson_id, :coupon_code, :coupon_amount,
	:lender, :product_variant_id, :vehicle_theft_number, :payment_type, :contract_code_bank_code, :is_e_contract, 
	:dealer_platform_id, :e_contract_employee_number
) returning "id", "created_at", "updated_at", "code", "contract_form_file_name", "original_code"`

const insertContractPrefixQuery = `insert into contracts (
	"created_at", "created_by_user_id", "updated_at", "updated_by_user_id",
	"store_id", "sale_id", "quote_product_type_id", "code", "contract_form_id",
	"contract_form_s3_region", "contract_form_s3_bucket", "contract_form_file_name",
	"quote_coupon_id", "transmit_type", "unidata_code", "customer_id", "quote_plan_id",
	"vin_record_id", "effective_date", "effective_mileage", "expiration_date", "expiration_mileage",
	"price", "plan_cost", "core_rate_bucket_id", "status", "event", "event_notes",
	"finance_term", "finance_amount", "product_name", "plan_name",
	"plan_duration", "plan_mileage", "remitted_at", "remitted_by_user_id",
	"spp_company", "product_type_id", "plan_id", "coupon_id",
	"product_type_name", "product_type_code", "product_type_position",
	"classification_id", "product_variant_name", "product_variant_display_name",
	"additive_mileage", "plan_code", "maintenance_visits", "maintenance_visit_value",
	"dms_number", "sale_type", "is_dms_deal", "salesperson_id", "coupon_code", "coupon_amount",
	"lender", "product_variant_id", "vehicle_theft_number", "payment_type", "original_code", "is_e_contract",
	"dealer_platform_id", "e_contract_employee_number"
) values (
	now() at time zone 'utc', :created_by_user_id, now() at time zone 'utc', :updated_by_user_id,
	:store_id, :sale_id, :quote_product_type_id, :code_prefix || currval('contracts_id_seq') || :code_suffix, :contract_form_id,
	:contract_form_s3_region, :contract_form_s3_bucket, 'contract-forms-stamped/' || :code_prefix || currval('contracts_id_seq') || :code_suffix || '.pdf',
	:quote_coupon_id, :transmit_type, :unidata_code, :customer_id, :quote_plan_id,
	:vin_record_id, :effective_date, :effective_mileage, :expiration_date, :expiration_mileage,
	:price, :plan_cost, :core_rate_bucket_id, :status, :event, :event_notes,
	:finance_term, :finance_amount, :product_name, :plan_name,
	:plan_duration, :plan_mileage, :remitted_at, :remitted_by_user_id,
	:spp_company, :product_type_id, :plan_id, :coupon_id,
	:product_type_name, :product_type_code, :product_type_position,
	:classification_id, :product_variant_name, :product_variant_display_name,
	:additive_mileage, :plan_code, :maintenance_visits, :maintenance_visit_value,
	:dms_number, :sale_type, :is_dms_deal, :salesperson_id, :coupon_code, :coupon_amount,
	:lender, :product_variant_id, :vehicle_theft_number, :payment_type, :code_prefix || currval('contracts_id_seq') || :code_suffix, :is_e_contract,
	:dealer_platform_id, :e_contract_employee_number
) returning "id", "created_at", "updated_at", "code", "contract_form_file_name", "original_code"`

const insertContractAdjustmentQuery = `insert into contract_adjustments (
	"created_at", "contract_id", "quote_adjustment_id",
	"rate_bucket_id", "is_invoiceable", "cost", "adjustment_id",
	"affects_contract_cost"
) values (
	now() at time zone 'utc', :contract_id, :quote_adjustment_id,
	:rate_bucket_id, :is_invoiceable, :cost, :adjustment_id,
	true
) returning "id", "created_at"`

const insertContractOptionQuery = `insert into contract_options (
	"created_at", "contract_id", "quote_option_id",
	"name", "code", "cost", "option_id", "rate_bucket_id"
) values (
	now() at time zone 'utc', :contract_id, :quote_option_id,
	:name, :code, :cost, :option_id, :rate_bucket_id
) returning "id", "created_at"`

const insertContractSurchargeQuery = `insert into contract_surcharges (
	"created_at", "contract_id", "quote_surcharge_id",
	"name", "code", "cost", "surcharge_id", "rate_bucket_id"
) values (
	now() at time zone 'utc', :contract_id, :quote_surcharge_id,
	:name, :code, :cost, :surcharge_id, :rate_bucket_id
) returning "id", "created_at"`

// ContractCreate will create a contract
func ContractCreate(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()
	txn := newrelic.FromContext(ctx)

	var payload newContractPayload
	sgmt := newrelic.StartSegment(txn, "json decode")
	decoder := json.NewDecoder(req.Body)
	err := decoder.Decode(&payload)
	sgmt.End()
	if err != nil {
		return http.StatusBadRequest, ErrorMessage("Bad request.", nil)
	}

	payload.clean()
	payload.UserID = user.ID
	sgmt = newrelic.StartSegment(txn, "validate contract")
	valErrs, err := payload.validate()
	sgmt.End()
	if err != nil {
		err = errors.Wrap(err, "error during new contract validation")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error validating the new contract.", nil)
	}

	if len(valErrs) > 0 {
		return http.StatusBadRequest, ErrorMessage("Validation errors", map[string]interface{}{"form_errors": valErrs})
	}

	var store db.Store
	sgmt = newrelic.StartSegment(txn, "select store")
	err = db.Get().Unsafe().Get(&store, `select * from stores where id = $1`, payload.StoreID)
	sgmt.End()
	if err != nil {
		err = errors.Wrap(err, "error finding store")
		ReportError(req, err)
		return http.StatusBadRequest, ErrorMessage("Error finding store", nil)
	}

	var sale db.Sale
	sgmt = newrelic.StartSegment(txn, "select sale")
	err = db.Get().Unsafe().Get(&sale, `select * from sales where id = $1`, payload.SaleID)
	sgmt.End()
	if err != nil {
		err = errors.Wrap(err, "error finding sale")
		ReportError(req, err)
		return http.StatusBadRequest, ErrorMessage("Error finding sale", nil)
	}

	var customer db.Customer
	sgmt = newrelic.StartSegment(txn, "select customers")
	err = db.Get().Unsafe().Get(&customer, `select * from customers where id = $1`, sale.CustomerID)
	sgmt.End()
	if err != nil {
		err = errors.Wrap(err, "error finding customer")
		ReportError(req, err)
		return http.StatusBadRequest, ErrorMessage("Error finding customer", nil)
	}

	var vinRecord db.VINRecord
	sgmt = newrelic.StartSegment(txn, "select vin_records")
	err = db.Get().Unsafe().Get(&vinRecord, `select * from vin_records where id = $1`, sale.VINRecordID)
	sgmt.End()
	if err != nil {
		err = errors.Wrap(err, "error finding vin record")
		ReportError(req, err)
		return http.StatusBadRequest, ErrorMessage("Error finding vin record", nil)
	}

	// Removed previous checks for whether to do price rounding since now will be using the
	// value on the Sale record when rounding was determined to be done and that should
	// continue during the eContract generation process.

	sgmt = newrelic.StartSegment(txn, "begin transaction")
	tx, err := db.Get().Beginx()
	sgmt.End()
	if err != nil {
		err = errors.Wrap(err, "error beginning transaction for contract save")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error in contract creation", nil)
	}

	contract, err := contractCreate(ctx, tx, db.ContractTypeConnect, &payload, nil, nil, &sale, &store, &customer, &vinRecord, nil, &user)
	if err != nil {
		err = errors.WithMessage(err, "error in contract creation")
		ReportError(req, err)

		errMsg := "Error in contract creation"

		// Check if error is contract form related and update the error message sent
		if strings.Contains(err.Error(), "missing contract form") {
			errMsg = "Contract form missing"
		} else if strings.Contains(err.Error(), "not available for business use") {
			errMsg = "Not available for commercial use"
		}
		return http.StatusInternalServerError, ErrorMessage(errMsg, nil)
	}

	sgmt = newrelic.StartSegment(txn, "transaction commit")
	err = tx.Commit()
	sgmt.End()
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "error committing contract save")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error in contract creation", nil)
	}

	return http.StatusOK, map[string]interface{}{"contract_id": contract.ID, "contract_code": contract.Code}
}

func getQuoteProductDataForNonESale(ctx context.Context, payload *newContractPayload) (
	*productDataForContractGen, error) {
	var pdfc productDataForContractGen

	txn := newrelic.FromContext(ctx)
	sgmt := newrelic.StartSegment(txn, "select quote_plans")
	err := db.Get().Unsafe().Get(&pdfc.quotePlan, `select * from quote_plans where id = $1`, payload.QuotePlanID)
	sgmt.End()
	if err != nil {
		err = errors.Wrap(err, "error finding quote plan")
		return nil, err
	}
	sgmt = newrelic.StartSegment(txn, "select quote_plan_adjustments")
	err = db.Get().Unsafe().Select(&pdfc.quotePlan.Adjustments, `select * from quote_plan_adjustments where quote_plan_id = $1`, pdfc.quotePlan.ID)
	sgmt.End()
	if err != nil {
		err = errors.Wrap(err, "error finding quote plan adjustments")
		return nil, err
	}

	sgmt = newrelic.StartSegment(txn, "select quote_adjustments")
	err = db.Get().Unsafe().Select(&pdfc.quoteAdjustments, `select * from quote_adjustments where quote_product_variant_id = $1`, pdfc.quotePlan.QuoteProductVariantID)
	sgmt.End()
	if err != nil {
		err = errors.Wrap(err, "error finding quote adjustments")
		return nil, err
	}

	quoteOptions := make([]db.QuoteOption, len(payload.QuoteOptionIDs))
	for i, quoteOptionID := range payload.QuoteOptionIDs {
		sgmt = newrelic.StartSegment(txn, "select quote_options")
		err = db.Get().Unsafe().Get(&quoteOptions[i], `select * from quote_options where id = $1`, quoteOptionID)
		sgmt.End()
		if err != nil {
			err = errors.Wrap(err, "error finding quote option")
			return nil, err
		}
	}
	pdfc.quoteOptions = quoteOptions

	quoteSurcharges := make([]db.QuoteSurcharge, len(payload.QuoteSurchargeIDs))
	for i, quoteSurchargeID := range payload.QuoteSurchargeIDs {
		sgmt = newrelic.StartSegment(txn, "select quote_surcharges")
		err = db.Get().Unsafe().Get(&quoteSurcharges[i], `select * from quote_surcharges where id = $1`, quoteSurchargeID)
		sgmt.End()
		if err != nil {
			err = errors.Wrap(err, "error finding quote surcharge")
			return nil, err
		}
	}
	pdfc.quoteSurcharges = quoteSurcharges

	sgmt = newrelic.StartSegment(txn, "select quote_product_variants")
	err = db.Get().Unsafe().Get(&pdfc.quoteProductVariant, `select * from quote_product_variants where id = $1`, pdfc.quotePlan.QuoteProductVariantID)
	sgmt.End()
	if err != nil {
		err = errors.Wrap(err, "error finding quote product variant")
		return nil, err
	}

	sgmt = newrelic.StartSegment(txn, "select quote_product_types")
	err = db.Get().Unsafe().Get(&pdfc.quoteProductType, `select * from quote_product_types where id = $1`, pdfc.quoteProductVariant.QuoteProductTypeID)
	sgmt.End()
	if err != nil {
		err = errors.Wrap(err, "error finding quote product type")
		return nil, err
	}
	return &pdfc, err
}

type productDataForContractGen struct {
	quoteProductType    db.QuoteProductType
	quoteProductVariant db.QuoteProductVariant
	quotePlan           db.QuotePlanWithAdjustments
	quoteAdjustments    []db.QuoteAdjustment
	quoteOptions        []db.QuoteOption
	quoteSurcharges     []db.QuoteSurcharge
}

func getQuoteProductDataForESale(
	ctx context.Context,
	quoteProducts []db.QuoteProductTypeWithData,
	payload *EContractPayload,
	financeAmount decimal.Decimal,
	storeID int,
	contractDate time.Time,
	roundup bool,
) (*productDataForContractGen, error) {

	var pdfc productDataForContractGen
	var qpv db.QuoteProductVariantWithData
	var err error

	qpv, pdfc.quotePlan, pdfc.quoteOptions, pdfc.quoteSurcharges, _, _, err = getERatingData(
		ctx,
		quoteProducts,
		payload,
		financeAmount,
		storeID,
		contractDate,
		roundup,
		nil,
	)
	if err != nil {
		return nil, err
	}

	pdfc.quoteAdjustments = qpv.QuoteAdjustments

	err = db.Get().Unsafe().GetContext(ctx, &pdfc.quoteProductVariant,
		`select pv.*, p.additive_mileage, p.position, p.transmit_type, p.unidata_code, p.spp_company
		from product_variants pv join products p on pv.product_id = p.id
		where pv.id = $1`,
		payload.Product.ProductVariantID)
	pdfc.quoteProductVariant.ClassificationID = qpv.ClassificationID

	if err != nil {
		err = errors.Wrap(err, "error finding quote product variant")
		return &pdfc, err
	}

	err = db.Get().Unsafe().GetContext(ctx, &pdfc.quoteProductType, `select * from product_types where id = $1`, payload.Product.ProductTypeID)
	if err != nil {
		err = errors.Wrap(err, "error finding quote product type")
		return &pdfc, err
	}
	return &pdfc, nil
}

func contractCreate(
	ctx context.Context,
	tx *sqlx.Tx,
	contractType int,
	payload *newContractPayload,
	ePayload *EContractPayload,
	dp *db.DealerPlatform,
	sale *db.Sale,
	store *db.Store,
	customer *db.Customer,
	vinRecord *db.VINRecord,
	quoteProducts []db.QuoteProductTypeWithData,
	user *db.CurrentUser,
) (*db.ContractWithData, error) {
	txn := newrelic.FromContext(ctx)

	// Get contract generation timestamp in local time
	loc, err := time.LoadLocation(store.TimeZone)
	if err != nil {
		err = errors.Wrap(err, "error getting location from store timezone for "+store.TimeZone)
		return nil, err
	}

	sale.ContractGeneratedAt = time.Now().In(loc).Format("02 Jan 2006 03:04:05 PM")

	var lender db.Lender
	if sale.LenderID.Valid {
		sgmt := newrelic.StartSegment(txn, "select lender")
		err = db.Get().Unsafe().GetContext(ctx, &lender, `select * from lenders where id = $1`, sale.LenderID.Int64)
		sgmt.End()
		if err != nil {
			err = errors.Wrap(err, "error finding lender")
			return nil, err
		}
	}

	// We want to ensure that If rounding is disabled, we also bypass whether rounding was done on the sale
	roundUp := sale.PriceRoundUp
	if !conf.Get().EnablePriceRoundUp {
		roundUp = false
	}

	var pdfc *productDataForContractGen
	if contractType == db.ContractTypeEContract {
		pdfc, err = getQuoteProductDataForESale(ctx, quoteProducts, ePayload, sale.FinanceAmount.Decimal, store.ID, sale.ContractDate, roundUp)
		// TODO: This is temprory fix for the TS-7842 need to change this after right fix
		if ePayload.FinanceAmount.GreaterThan(decimal.Zero) {
			pdfc, err = getQuoteProductDataForESale(ctx, quoteProducts, ePayload, ePayload.FinanceAmount, store.ID, sale.ContractDate, roundUp)
		}

		// Add the cast from int64 to int because at this point the pdfc.quoteProductVariant.ID is
		// the ID of the Product Variant from the product_variants table which is an int.
		pdfc.quoteProductVariant.ProductVariantID = int(pdfc.quoteProductVariant.ID)
		pdfc.quoteProductType.ProductTypeID = pdfc.quoteProductType.ID
	} else {
		pdfc, err = getQuoteProductDataForNonESale(ctx, payload)
	}
	if err != nil {
		err = errors.Wrap(err, "error in getting quote data")
		return nil, err
	}
	quotePlan := pdfc.quotePlan
	quoteOptions := pdfc.quoteOptions
	quoteProductVariant := pdfc.quoteProductVariant
	quoteSurcharges := pdfc.quoteSurcharges
	quoteAdjustments := pdfc.quoteAdjustments
	quoteProductType := pdfc.quoteProductType

	var product struct {
		ID   int    `db:"id" json:"id"`
		Name string `db:"name" json:"name"`
	}
	sgmt := newrelic.StartSegment(txn, "select product_variants")
	err = db.Get().Unsafe().GetContext(ctx, &product, `select p.id, p.name
		from product_variants pv join products p on pv.product_id = p.id where pv.id = $1`,
		pdfc.quoteProductVariant.ProductVariantID)
	sgmt.End()
	if err != nil {
		err = errors.Wrap(err, "error finding product")
		return nil, err
	}

	// Load the Options that are referenced by the loaded Quote Options.
	// This is needed so we can get the Rate Bucket ID from the associated
	// Option when creating the Contract Option
	optionCache := make(map[int]db.Option)
	if len(quoteOptions) > 0 {
		var options []db.Option
		var optionIDs []int64
		for _, o := range quoteOptions {
			optionIDs = append(optionIDs, int64(o.OptionID))
		}
		sgmt = newrelic.StartSegment(txn, "select options")
		query, args, err := sqlx.In(`select * from options where id in (?)`, optionIDs)
		if err != nil {
			sgmt.End()
			err = errors.Wrap(err, "error preparing to getting options")
			return nil, err
		}
		query = db.Get().Rebind(query)
		err = db.Get().Unsafe().SelectContext(ctx, &options, query, args...)
		sgmt.End()
		if err != nil && err != sql.ErrNoRows {
			err = errors.Wrap(err, "error getting options")
			return nil, err
		}

		for _, o := range options {
			optionCache[o.ID] = o
		}
	}

	// Load the Surcharges that are referenced by the loaded Quote Surcharges.
	// This is needed so we can get the Rate Bucket ID from the associated
	// Surcharge when creating the Contract Surcharge
	surchargeCache := make(map[int]db.Surcharge)
	if len(quoteSurcharges) > 0 {
		var surcharges []db.Surcharge
		var surchargeIDs []int64
		for _, s := range quoteSurcharges {
			surchargeIDs = append(surchargeIDs, int64(s.SurchargeID))
		}
		sgmt = newrelic.StartSegment(txn, "select surcharges")
		query, args, err := sqlx.In(`select * from surcharges where id in (?)`, surchargeIDs)
		if err != nil {
			sgmt.End()
			err = errors.Wrap(err, "error preparing to get surcharges")
			return nil, err
		}
		query = db.Get().Rebind(query)
		err = db.Get().Unsafe().SelectContext(ctx, &surcharges, query, args...)
		sgmt.End()
		if err != nil && err != sql.ErrNoRows {
			err = errors.Wrap(err, "error getting surcharges")
			return nil, err
		}

		for _, s := range surcharges {
			surchargeCache[s.ID] = s
		}
	}

	contractDate := types.JSPQDate{
		NullTime: pq.NullTime{
			Valid: true,
			Time:  sale.ContractDate,
		},
	}
	sgmt = newrelic.StartSegment(txn, "find contract form")
	contractForm, err := db.FindContractForm(quoteProductVariant.ProductVariantID, store.ID, contractDate, sale.IsCUDL, customer.IsBusiness, null.Int{sale.LenderID})
	sgmt.End()
	if err != nil {
		// If the error is the "missing contract form" or "not available for business use" then we want to return those errors as is
		if strings.Contains(err.Error(), db.ContractFormMissingFormErrorMessage) || strings.Contains(err.Error(), db.ContractFormCommercialUseNotAvailableErrorMessage) {
			return nil, err
		}

		// Return all other errors with this message attached
		err = errors.WithMessage(err, "error finding the first matching contract form")
		return nil, err
	}

	if contractForm == nil {
		return nil, errors.New("missing contract form")
	}

	sgmt = newrelic.StartSegment(txn, "generate s3 presigned URL")
	url, err := s3util.PresignedURL(s3util.DefaultRegion, contractForm.S3Bucket, contractForm.FileName, "contract-form.pdf", "application/pdf")
	sgmt.End()
	if err != nil {
		err = errors.Wrap(err, fmt.Sprintf("error generating presigned URL for contract form '%s'", contractForm.FormCode))
		return nil, err
	}
	// TODO: change to 15 before deploying, 45 is for local dev test
	client := http.Client{Timeout: time.Second * 45}
	clientReq, err := http.NewRequest("GET", url, nil)
	if err != nil {
		err = errors.Wrap(err, fmt.Sprintf("error creating request to download contract form '%s'", contractForm.FormCode))
		return nil, err
	}
	resp, err := nr.External(txn, client, clientReq)
	if err != nil {
		err = errors.Wrap(err, fmt.Sprintf("error downloading contract form '%s'", contractForm.FormCode))
		return nil, err
	}
	defer func() { _ = resp.Body.Close() }()
	if resp.StatusCode != http.StatusOK {
		err = errors.Errorf("error download contract form '%s': not 200 status ('%d')", contractForm.FormCode, resp.StatusCode)
		errors.Wrap(err, fmt.Sprintf("error downloading contract form %s : CODE FMDWN03", contractForm.FormCode))
		return nil, err
	}
	originalFile, err := ioutil.TempFile("", "contract-form")
	if err != nil {
		err = errors.Wrap(err, fmt.Sprintf("error creating temp file when downloading contract form '%s'", contractForm.FormCode))
		errors.Wrap(err, fmt.Sprintf("Error downloading contract for %s : CODE FMDWN04", contractForm.FormCode))
		return nil, err
	}
	defer func() {
		_ = originalFile.Close()
		_ = os.Remove(originalFile.Name())
	}()
	sgmt = newrelic.StartSegment(txn, "copy downloaded s3 file to temp file")
	_, err = io.Copy(originalFile, resp.Body)
	sgmt.End()
	if err != nil {
		err = errors.Wrap(err, fmt.Sprintf("error writing temp file when downloading contract form '%s'", contractForm.FormCode))
		err = errors.Wrap(err, fmt.Sprintf("Error downloading contract form %s : CODE FMDWN05", contractForm.FormCode))
		return nil, err
	}
	_ = originalFile.Close()

	var quoteCoupon struct {
		ID       null.Int            `db:"id"`
		CouponID null.Int            `db:"coupon_id"`
		Code     null.String         `db:"code"`
		Amount   decimal.NullDecimal `db:"amount"`
	}
	if payload != nil && payload.QuoteCouponID != nil {
		sgmt = newrelic.StartSegment(txn, "select quote_coupons")
		err = db.Get().Unsafe().GetContext(ctx, &quoteCoupon, `select * from quote_coupons where id = $1`, payload.QuoteCouponID)
		sgmt.End()
		if err != nil {
			err = errors.Wrap(err, "error finding quote coupon")
			return nil, err
		}
	}

	sgmt = newrelic.StartSegment(txn, "map to contract struct")
	var effDate time.Time
	if contractType == db.ContractTypeEContract {
		effDate = sale.ContractDate
	} else {
		effDate = sale.ContractDate.AddDate(0, payload.EffectiveDateChange, 0)
	}

	// If a VTA number was provided for a VTA E-Sales contract, then save the number in the contract.
	var vtaNumber null.String
	if ePayload != nil && ePayload.Product.VTANumber != "" && quoteProductType.Code == db.ProductTypeCodeVehicleTheftAssistance {
		vtaNumber = null.StringFrom(ePayload.Product.VTANumber)
	}
	contract := db.ContractWithData{
		Contract: db.Contract{
			CreatedByUserID: user.ID,
			UpdatedByUserID: user.ID,
			StoreID:         store.ID,
			SaleID:          null.IntFrom(int64(sale.ID)),

			ContractFormID:            null.IntFrom(int64(contractForm.ID)),
			ContractFormS3Region:      null.StringFrom(s3util.DefaultRegion),
			ContractFormS3Bucket:      null.StringFrom(s3util.Bucket()),
			TransmitType:              null.String{NullString: quoteProductVariant.TransmitType},
			UnidataCode:               null.String{NullString: quoteProductVariant.UnidataCode},
			SPPCompany:                quoteProductVariant.SPPCompany,
			CustomerID:                sale.CustomerID,
			VINRecordID:               sale.VINRecordID,
			VehicleTheftNumber:        vtaNumber,
			EffectiveDate:             effDate,
			EffectiveMileage:          sale.Odometer,
			ExpirationDate:            quotePlan.ExpirationDate(effDate),
			ExpirationMileage:         quotePlan.ExpirationMileage(sale.Odometer, quoteProductVariant.AdditiveMileage),
			PlanCost:                  quotePlan.Cost,
			CoreRateBucketID:          quoteProductVariant.CoreRateBucketID,
			Status:                    db.ContractStatusGenerated,
			Event:                     db.ContractEventGenerate,
			EventNotes:                "",
			FinanceTerm:               null.Int{NullInt64: sale.FinanceTerm},
			FinanceAmount:             sale.FinanceAmount,
			ProductName:               product.Name,
			PlanName:                  quotePlan.Name,
			PlanDuration:              quotePlan.Duration,
			PlanMileage:               null.Int{NullInt64: quotePlan.Mileage},
			QuoteCouponID:             quoteCoupon.ID,
			ProductTypeID:             null.IntFrom(int64(quoteProductType.ProductTypeID)),
			PlanID:                    null.IntFrom(int64(quotePlan.PlanID)),
			CouponID:                  quoteCoupon.CouponID,
			ProductTypeName:           quoteProductType.Name,
			ProductTypeCode:           quoteProductType.Code,
			ProductTypePosition:       quoteProductType.Position,
			ClassificationID:          null.Int{NullInt64: quoteProductVariant.ClassificationID},
			ProductVariantName:        quoteProductVariant.Name,
			ProductVariantDisplayName: quoteProductVariant.DisplayName,
			ProductVariantID:          null.IntFrom(int64(quoteProductVariant.ProductVariantID)),
			AdditiveMileage:           quoteProductVariant.AdditiveMileage,
			PlanCode:                  null.String{NullString: quotePlan.Code},
			MaintenanceVisits:         null.Int{NullInt64: quotePlan.MaintenanceVisits},
			MaintenanceVisitValue:     quotePlan.MaintenanceVisitValue,
			SaleType:                  sale.SaleType,
			IsDMSDeal:                 sale.IsDMSDeal,
			DMSNumber:                 sale.DMSNumber,
			SalespersonID:             sale.SalespersonID,
			CouponCode:                quoteCoupon.Code,
			CouponAmount:              quoteCoupon.Amount,
			Lender:                    lender.Name,
			PaymentType:               sale.PaymentType,
		},
		Adjustments: []db.ContractAdjustment{},
		Options:     []db.ContractOption{},
		Surcharges:  []db.ContractSurcharge{},
	}
	if sale.QuoteVersion == 2 || sale.QuoteVersion == 3 { // TODO: Remove if check when quote version 1 is done
		query := `select classification_code from plans where id = $1`
		var classificationCode null.String
		err = db.Get().GetContext(ctx, &classificationCode, query, quotePlan.PlanID)
		if err != nil {
			err = errors.Wrapf(err, "could not query plans for plan_id (%d)", quotePlan.PlanID)
			return nil, err
		}

		for _, adj := range quoteAdjustments {
			if !adj.Tag.Valid || quotePlan.Tags.Map[adj.Tag.String].Valid {

				// Dont process generator type adjustments
				if adj.AdjustmentType.String == db.AdjustmentTypeGenerator {
					continue
				}
				// If adjustment is generated type and product type is in AdjustmentGeneratorProductTypes then validate the adjustment for matching values
				if adj.AdjustmentType.String == db.AdjustmentTypeGenerated &&
					slice.ContainsString(db.AdjustmentGeneratorProductTypes, quoteProductType.Code) {
					// If duration is not match then continue
					if adj.PlanDuration.Valid && adj.PlanDuration.Int64 != int64(quotePlan.Duration) {
						continue
					}

					// For MNT if Code, MaintenanceVisits or MaintenanceVisitValue is mismatch then continue
					if quoteProductType.Code == db.ProductTypeCodeMaintenance &&
						(quotePlan.Code.String != adj.PlanCode.String ||
							quotePlan.MaintenanceVisits.Int64 != adj.MaintenanceVisits.Int64 ||
							!quotePlan.MaintenanceVisitValue.Decimal.Equal(adj.MaintenanceVisitValue.Decimal)) {
						continue
					}

					// For TW if classification is mismatch then continue
					if quoteProductType.Code == db.ProductTypeCodeTireAndWheel && classificationCode.String != adj.VehicleClassificationCode.String {
						continue
					}
				}

				adjCost, err := db.CalculateAdjustmentCost(adj.CalculationType,
					adj.Amount, quotePlan.Cost, quotePlan.PlanID,
					contract.EffectiveMileage,
					adj.RateBucketID,
					adj.ClpRateID,
					adj.IsFirstDollarCLP)
				if err != nil {
					err = errors.Wrapf(err, "could not calculate adjustment (%d) cost for quote plan (%d)", adj.ID, quotePlan.ID)
					return nil, err
				}
				curAdj := db.ContractAdjustment{
					QuoteAdjustmentID:   null.IntFrom(int64(adj.ID)),
					RateBucketID:        adj.RateBucketID,
					IsInvoiceable:       adj.IsInvoiceable,
					Cost:                adjCost,
					AdjustmentID:        null.IntFrom(int64(adj.AdjustmentID)),
					AffectsContractCost: true, // TODO: backport this setting back to quotes
				}
				if contractType == db.ContractTypeEContract {
					curAdj.QuoteAdjustmentID = null.Int{}
				}
				contract.Adjustments = append(contract.Adjustments, curAdj)
			}
		}
	} else { // TODO: Remove else block when quote version 1 is done
		for _, adj := range quotePlan.Adjustments {
			contract.Adjustments = append(contract.Adjustments, db.ContractAdjustment{
				RateBucketID:        adj.RateBucketID,
				IsInvoiceable:       adj.IsInvoiceable,
				Cost:                adj.Cost,
				AffectsContractCost: true,
			})
		}
	}
	for _, quoteOption := range quoteOptions {
		curOpt := db.ContractOption{
			QuoteOptionID: null.IntFrom(int64(quoteOption.ID)),
			Name:          null.String{NullString: quoteOption.Name},
			Code:          null.String{NullString: quoteOption.Code},
			Cost:          quoteOption.Cost,
			OptionGroup:   null.String{NullString: quoteOption.OptionGroup},
			OptionID:      null.IntFrom(int64(quoteOption.OptionID)),
		}
		if contractType == db.ContractTypeEContract {
			curOpt.QuoteOptionID = null.Int{}
		}
		contract.Options = append(contract.Options, curOpt)
	}
	for _, quoteSurcharge := range quoteSurcharges {
		curSur := db.ContractSurcharge{
			QuoteSurchargeID: null.IntFrom(int64(quoteSurcharge.ID)),
			Name:             null.String{NullString: quoteSurcharge.Name},
			Code:             null.String{NullString: quoteSurcharge.Code},
			Cost:             quoteSurcharge.Cost,
			SurchargeID:      null.IntFrom(int64(quoteSurcharge.SurchargeID)),
		}
		if contractType == db.ContractTypeEContract {
			curSur.QuoteSurchargeID = null.Int{}
		}
		contract.Surcharges = append(contract.Surcharges, curSur)
	}
	sgmt.End()
	var code string
	if contractForm.ContractCodeBankID.Valid {
		sgmt = newrelic.StartSegment(txn, "get next contract code bank code")
		code, err = db.GetNextContractCodeBankCode(int(contractForm.ContractCodeBankID.Int64))
		sgmt.End()
		if err != nil {
			err = errors.Wrap(err, "error getting contract code from contract bank")
			return nil, err
		}
	}

	var eContractCodeSuffix string
	if contractType == db.ContractTypeEContract {
		contract.IsEContract = true
		contract.DealerPlatformID = null.IntFrom(int64(dp.ID))
		eContractCodeSuffix = "E"
		// We need to ensure that the Lender Name on the contract is set if there's
		// either a Pending Lender ID or if the Lender on the ePayload is SPP
		if ePayload.Lender.PendingLenderID != 0 || isLenderSPP(ePayload) {
			contract.Lender = ePayload.Lender.Name
		}
		contract.Price = ePayload.Product.Price
		contract.EContractEmployeeNumber = ePayload.EmployeeNumber.ValueOrZero()
	} else {
		contract.VehicleTheftNumber = payload.VehicleTheftNumber
		contract.QuotePlanID = null.IntFrom(int64(quotePlan.ID))
		contract.QuoteProductTypeID = null.IntFrom(int64(quoteProductVariant.QuoteProductTypeID))
		contract.Price = payload.Price
	}

	if contractForm.ContractCodeBankID.Valid {
		contractWithCode := struct {
			db.ContractWithData
			ContractCodeBankCode string `db:"contract_code_bank_code"`
		}{
			ContractWithData:     contract,
			ContractCodeBankCode: code + eContractCodeSuffix,
		}
		sgmt = newrelic.StartSegment(txn, "prepare insert contracts")
		contractStmt, err := tx.PrepareNamedContext(ctx, insertContractBankCodeQuery)
		sgmt.End()
		if err != nil {
			_ = tx.Rollback()
			err = errors.Wrap(err, "error preparing insert contract")
			return nil, err
		}
		defer func() { _ = contractStmt.Close() }()
		sgmt = newrelic.StartSegment(txn, "get insert contracts")
		err = contractStmt.GetContext(ctx, &contract, contractWithCode)
		sgmt.End()
		if err != nil {
			_ = tx.Rollback()
			err = errors.Wrap(err, "error inserting contract")
			return nil, err
		}
	} else if contractForm.ContractCodePrefix.Valid {
		contractWithPrefix := struct {
			db.ContractWithData
			CodePrefix string `db:"code_prefix"`
			CodeSuffix string `db:"code_suffix"`
		}{
			ContractWithData: contract,
			CodePrefix:       contractForm.ContractCodePrefix.String,
			CodeSuffix:       contractForm.ContractCodeSuffix.String + eContractCodeSuffix,
		}
		sgmt = newrelic.StartSegment(txn, "prepare insert contracts")
		contractStmt, err := tx.PrepareNamedContext(ctx, insertContractPrefixQuery)
		sgmt.End()
		if err != nil {
			_ = tx.Rollback()
			err = errors.Wrap(err, "error preparing insert contract")
			return nil, err
		}
		defer func() { _ = contractStmt.Close() }()
		sgmt = newrelic.StartSegment(txn, "get insert contracts")
		err = contractStmt.GetContext(ctx, &contract, contractWithPrefix)
		sgmt.End()
		if err != nil {
			_ = tx.Rollback()
			err = errors.Wrap(err, "error inserting contract")
			return nil, err
		}
	} else {
		_ = tx.Rollback()
		err = errors.New("error inserting contract when no code prefix or code bank")
		return nil, err
	}

	sgmt = newrelic.StartSegment(txn, "prepare insert contract_adjustments")
	adjustmentStmt, err := tx.PrepareNamedContext(ctx, insertContractAdjustmentQuery)
	sgmt.End()
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "error preparing insert contract adjustments")
		return nil, err
	}
	defer func() { _ = adjustmentStmt.Close() }()
	for i := range contract.Adjustments {
		contract.Adjustments[i].ContractID = contract.ID
		sgmt = newrelic.StartSegment(txn, "get insert contract_adjustments")
		err = adjustmentStmt.GetContext(ctx, &contract.Adjustments[i], contract.Adjustments[i])
		sgmt.End()
		if err != nil {
			_ = tx.Rollback()
			err = errors.Wrap(err, "error executing insert contract adjustment")
			return nil, err
		}
	}

	sgmt = newrelic.StartSegment(txn, "prepare insert contract_options")
	optionStmt, err := tx.PrepareNamedContext(ctx, insertContractOptionQuery)
	sgmt.End()
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "error preparing insert contract options")
		return nil, err
	}
	defer func() { _ = optionStmt.Close() }()
	for i := range contract.Options {
		contract.Options[i].ContractID = contract.ID
		contract.Options[i].RateBucketID = int(optionCache[int(contract.Options[i].OptionID.Int64)].RateBucketID.Int64)
		sgmt = newrelic.StartSegment(txn, "get insert contract_options")
		err = optionStmt.GetContext(ctx, &contract.Options[i], contract.Options[i])
		sgmt.End()
		if err != nil {
			_ = tx.Rollback()
			err = errors.Wrap(err, "error executing insert contract option")
			return nil, err
		}
	}

	sgmt = newrelic.StartSegment(txn, "prepare insert contract_surcharges")
	surchargeStmt, err := tx.PrepareNamedContext(ctx, insertContractSurchargeQuery)
	sgmt.End()
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "error preparing insert contract surcharges")
		return nil, err
	}
	defer func() { _ = surchargeStmt.Close() }()
	for i := range contract.Surcharges {
		contract.Surcharges[i].ContractID = contract.ID
		contract.Surcharges[i].RateBucketID = int(surchargeCache[int(contract.Surcharges[i].SurchargeID.Int64)].RateBucketID.Int64)
		sgmt = newrelic.StartSegment(txn, "get insert contract_surcharges")
		err = surchargeStmt.GetContext(ctx, &contract.Surcharges[i], contract.Surcharges[i])
		sgmt.End()
		if err != nil {
			_ = tx.Rollback()
			err = errors.Wrap(err, "error executing insert contract surcharge")
			return nil, err
		}
	}

	sgmt = newrelic.StartSegment(txn, "get PDF field data")
	fillFormData := map[string]string{}
	sale.PDFFieldData(fillFormData)
	if sale.PaymentType == dms.PaymentTypeSPP {
		// TODO: Move SPP lender to database?
		lender := db.Lender{
			Name:       "SPP",
			Address:    "303 East Wacker Drive, Suite 230",
			City:       "Chicago",
			StateCode:  "IL",
			PostalCode: "60601",
		}
		lender.PDFFieldData(fillFormData)
	} else if contractType == db.ContractTypeEContract {
		lender := db.Lender{
			Name:       ePayload.Lender.Name,
			Address:    ePayload.Lender.Address,
			City:       ePayload.Lender.City,
			StateCode:  ePayload.Lender.StateCode,
			PostalCode: ePayload.Lender.PostalCode,
		}
		lender.PDFFieldData(fillFormData)
	} else if sale.LenderID.Valid {
		lender.PDFFieldData(fillFormData)
	}

	var salesperson db.CurrentUser
	var query string
	if sale.SalespersonID.Valid {
		query = `select * from current_users where id = $1`
		sgmt2 := newrelic.StartSegment(txn, "select current_users (salesperson)")
		err = db.Get().Unsafe().GetContext(ctx, &salesperson, query, sale.SalespersonID)
		sgmt2.End()
		if err != nil {
			_ = tx.Rollback()
			err = errors.Wrap(err, "error finding salesperson user")
			return nil, err
		}
		salesperson.PDFFieldData(fillFormData)
	}
	store.PDFFieldData(fillFormData)

	var coBuyerName string
	if contract.IsEContract && ePayload.Cobuyer.FirstName != "" {
		coBuyerName = strings.Join([]string{ePayload.Cobuyer.FirstName, ePayload.Cobuyer.LastName}, " ")
	} else {
		if sale.ContractCoBuyerID.Valid {
			err = db.Get().GetContext(ctx, &coBuyerName, `select first_name || ' ' || last_name
			from contract_cobuyers where id = $1`, sale.ContractCoBuyerID)
			if err != nil {
				_ = tx.Rollback()
				err = errors.Wrap(err, "error getting cobuyer")
				return nil, err
			}
		}
	}
	customer.PDFFieldData(fillFormData, coBuyerName)
	vinRecord.PDFFieldData(fillFormData)
	contract.PDFFieldData(fillFormData)
	quotePlan.PDFFieldData(fillFormData)
	sgmt.End()

	stampedFile, err := ioutil.TempFile("", "stamped-contract-form")
	if err != nil {
		err = errors.Wrap(err, fmt.Sprintf("error creating temp file to stamp contract form '%s'", contractForm.FormCode))
		return nil, err
	}
	defer func() {
		_ = stampedFile.Close()
		_ = os.Remove(stampedFile.Name())
	}()
	sgmt = newrelic.StartSegment(txn, "pdftk fill form")
	err = pdftk.FillForm(originalFile.Name(), fillFormData, stampedFile.Name())
	sgmt.End()
	if err != nil {
		err = errors.Wrap(err, fmt.Sprintf("error filling contract form '%s'", contractForm.FormCode))
		return nil, err
	}

	compressedFile, err := ioutil.TempFile("", "compressed-contract")
	if err != nil {
		err = errors.Wrapf(err, "error CODE CFCMP01: creating temp file to compress contract form '%s'", contractForm.FormCode)
		return nil, err
	}
	defer func() {
		_ = compressedFile.Close()
		_ = os.Remove(compressedFile.Name())
	}()
	sgmt = newrelic.StartSegment(txn, "ghostscript compress contract")
	err = ghostscript.CompressPDF(stampedFile.Name(), compressedFile.Name())
	sgmt.End()
	if err != nil {
		err = errors.Wrapf(err, "error CODE CFCMP02: compressing contract form '%s'", contractForm.FormCode)
		return nil, err
	}

	stampedStat, err := stampedFile.Stat()
	if err != nil {
		err = errors.Wrapf(err, "error CODE CFSTMP01: getting stamped contract size form '%s'", contractForm.FormCode)
		return nil, err
	}
	compressedStat, err := compressedFile.Stat()
	if err != nil {
		err = errors.Wrapf(err, "error CODE CFCMP03: getting compressed contract form '%s'", contractForm.FormCode)
		return nil, err
	}
	outputFile := compressedFile
	if stampedStat.Size() < compressedStat.Size() {
		// If the compressed file ended up being larger, then used the stamped file instead
		util.LogMessagef(ctx, "%s: compressed contract size (%d) was larger than stamped contract size (%d). Using stamped contract instead.",
			sentry.LevelWarning, compressedStat.Size(), stampedStat.Size())
		outputFile = stampedFile
	}

	sgmt = newrelic.StartSegment(txn, "s3 upload contract")
	err = s3util.Put(txn, outputFile, contract.ContractFormS3Region.String, contract.ContractFormS3Bucket.String, contract.ContractFormFileName.String)
	sgmt.End()
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "error uploading stamped contract form to s3")
		return nil, err
	}

	userName := user.FullName()
	if contractType == db.ContractTypeEContract {
		userName = dp.Name
	}
	query = `insert into contract_events(created_at,created_by_user_id,created_by_name,contract_id,description)values(
	now() at time zone 'utc', $1, $2, $3, $4)`
	_, err = tx.ExecContext(ctx, query, user.ID, userName, contract.ID, contract.Event)
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "error inserting contract event")
		return nil, err
	}

	query = `update contract_cobuyers set contract_id = $1 where id in (select contract_cobuyer_id from sales where id = $2)`
	_, err = tx.ExecContext(ctx, query, contract.ID, contract.SaleID)
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "error updating contract_cobuyers in contract save")
		return nil, err
	}

	return &contract, err
}

// ContractRemitList will show a list of contracts for the remit process given parameters
func ContractRemitList(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	contractArgs := struct {
		StoreID         int    `db:"store_id"`
		StatusGenerated string `db:"status_generated"`
		StatusRemitted  string `db:"status_remitted"`
		StatusActive    string `db:"status_active"`
		StatusPending   string `db:"status_pending"`
		StatusVoided    string `db:"status_voided"`
		DMSNumber       string `db:"dms_number"`
		ContractCode    string `db:"contract_code"`
	}{
		StatusGenerated: db.ContractStatusGenerated,
		StatusRemitted:  db.ContractStatusRemitted,
		StatusActive:    db.ContractStatusActive,
		StatusPending:   db.ContractStatusPending,
		StatusVoided:    db.ContractStatusVoided,
	}
	query := `select suv.store_id from stores_user_versions suv join current_users cu on cu.user_version_id = suv.user_version_id where suv.user_version_id = $1 and suv.store_id = $2`
	err := db.Get().Get(&contractArgs.StoreID, query, user.UserVersionID, req.FormValue("store_id"))
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, ErrorMessage("Not found", nil)
		}
		err = errors.Wrap(err, "error validating store/user for contract remit list")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error finding contracts", nil)
	}

	query = `select c.*, s.sale_type, s.dms_number, s.dms_number_updated_at, s.lender_id as lender_id, s.id as sale_id, s.inspection_liability,
	c.product_variant_display_name product_variant_display_name, case when c.is_e_contract then c.payment_type else s.payment_type end as payment_type,
	case when cc.is_business and (cc.first_name!='' or cc.last_name!='') then cc.last_name || ',' || cc.first_name || '/' || cc.business_name
	when cc.is_business and (cc.first_name='' and cc.last_name='' and ccb.first_name!='') then cc.business_name || '/' || ccb.last_name || ',' || ccb.first_name
	when cc.is_business and cc.first_name='' and cc.last_name='' then cc.business_name
	when cc.is_business=false and ccb.first_name!='' then cc.last_name || ',' || cc.first_name || '/' || ccb.last_name || ',' || ccb.first_name
	else cc.last_name || ',' || cc.first_name end customer,
	u.first_name created_by_user_first_name, u.last_name created_by_user_last_name,
	v.vin, v.year vehicle_year, v.make vehicle_make, v.model vehicle_model, v.trim vehicle_trim,
	s.contract_date as sale_contract_date,
	coalesce(dppv.display_name,'') dealer_pv_display_name
	from contracts c
	join sales s on c.sale_id = s.id
	left join contract_cobuyers ccb on s.contract_cobuyer_id = ccb.id
	join customers cc on c.customer_id = cc.id
	join current_users u on c.created_by_user_id = u.id
	join vin_records v on c.vin_record_id = v.id
	left join dealer_platform_product_variants dppv on
		(c.product_variant_id = dppv.product_variant_id and dppv.dealer_platform_id = c.dealer_platform_id)
	where s.store_id = :store_id`
	contractArgs.DMSNumber = req.FormValue("dms_number")
	contractArgs.ContractCode = req.FormValue("contract_code")
	if contractArgs.DMSNumber == "" && contractArgs.ContractCode == "" {
		query += ` and (c.status = :status_generated)`
	} else {
		query += ` and (c.status = :status_generated or c.status = :status_remitted or c.status = :status_voided or c.status = :status_active or c.status = :status_pending)`
		if contractArgs.DMSNumber != "" {
			query = query + ` and s.dms_number = :dms_number`
		}
		if contractArgs.ContractCode != "" {
			query = query + ` and c.code = :contract_code`
		}
	}
	query = query + ` order by c.created_at desc limit 50`

	stmt, err := db.Get().Unsafe().PrepareNamed(query)
	if err != nil {
		err = errors.Wrap(err, "error preparing select for current contracts")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error finding contracts", nil)
	}
	defer func() { _ = stmt.Close() }()
	var contracts []struct {
		ID                        int                 `db:"id" json:"id"`
		Code                      string              `db:"code" json:"code"`
		EffectiveDate             time.Time           `db:"effective_date" json:"effective_date"`
		EffectiveMileage          int                 `db:"effective_mileage" json:"effective_mileage"`
		Price                     decimal.Decimal     `db:"price" json:"price"`
		PlanCost                  decimal.Decimal     `db:"plan_cost" json:"-"`
		TotalCost                 decimal.Decimal     `db:"-" json:"total_cost"`
		Status                    string              `db:"status" json:"status"`
		FinanceTerm               null.Int            `db:"finance_term" json:"finance_term"`
		FinanceAmount             decimal.NullDecimal `db:"finance_amount" json:"finance_amount"`
		InspectionLiability       null.String         `db:"inspection_liability" json:"inspection_liability"`
		EligibilityLevel          null.String         `db:"eligibility_level" json:"eligibility_level"`
		IsCertified               bool                `db:"is_certified" json:"is_certified"`
		PlanName                  string              `db:"plan_name" json:"plan_name"`
		LenderID                  null.Int            `db:"lender_id" json:"lender_id"`
		SaleID                    int                 `db:"sale_id" json:"sale_id"`
		SaleType                  string              `db:"sale_type" json:"sale_type"`
		DMSNumber                 string              `db:"dms_number" json:"dms_number"`
		ProductVariantDisplayName string              `db:"product_variant_display_name" json:"product_variant_display_name"`
		DealerPVDisplayName       string              `db:"dealer_pv_display_name" json:"dealer_pv_display_name"`
		PaymentType               string              `db:"payment_type" json:"payment_type"`
		Customer                  string              `db:"customer" json:"customer"`
		CreatedByUserFirstName    string              `db:"created_by_user_first_name" json:"created_by_user_first_name"`
		CreatedByUserLastName     string              `db:"created_by_user_last_name" json:"created_by_user_last_name"`
		VIN                       string              `db:"vin" json:"vin"`
		VehicleYear               int                 `db:"vehicle_year" json:"vehicle_year"`
		VehicleMake               string              `db:"vehicle_make" json:"vehicle_make"`
		VehicleModel              string              `db:"vehicle_model" json:"vehicle_model"`
		VehicleTrim               string              `db:"vehicle_trim" json:"vehicle_trim"`
		ProductTypeCode           string              `db:"product_type_code" json:"product_type_code"`
		ProductTypeID             int                 `db:"product_type_id" json:"product_type_id"`
		SaleContractDate          time.Time           `db:"sale_contract_date" json:"sale_contract_date"`
		RemittedAt                null.Time           `db:"remitted_at" json:"remitted_at"`
		OriginalCode              string              `db:"original_code" json:"original_code"`
		IsLHMStore                bool                `db:"-" json:"is_lhm_store"`
		IsVTAOverAllowanceApplied bool                `db:"-" json:"is_vta_overallowance_applied"`
		AllSPPFilesAttached       bool                `db:"-" json:"all_spp_files_attached"`
		IsEContract               bool                `db:"is_e_contract" json:"is_e_contract"`
		DMSNumberUpdatedAt        pq.NullTime         `db:"dms_number_updated_at" json:"-"`
		IsDealNumberNeedUpdate    bool                `db:"-" json:"is_deal_number_need_update"`
		HideVoidDealerContract    bool                `db:"-" json:"hide_void_dealer_contract"`
	}
	err = stmt.Unsafe().Select(&contracts, contractArgs)
	if err != nil {
		err = errors.Wrap(err, "error executing select for current contracts")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error finding contracts", nil)
	}

	for i, contract := range contracts {
		err = db.Get().Get(&contracts[i], `select eligibility_level, is_certified from sale_inspection_eligibilities where sale_id = $1 order by created_at desc limit 1`, contract.SaleID)
		if err != nil && err != sql.ErrNoRows {
			err = errors.Wrap(err, "error select for sale_inspection_eligibilities")
			ReportError(req, err)
			return http.StatusInternalServerError, ErrorMessage("Error finding contracts", nil)
		}

		totalCost := contract.PlanCost
		var newCost decimal.Decimal
		err = db.Get().Get(&newCost, `select coalesce(sum(cost), 0)
			from contract_adjustments
			where contract_id = $1 and is_invoiceable=true and affects_contract_cost=true`, contract.ID)
		if err != nil {
			err = errors.Wrap(err, "error select for contract_adjustments")
			ReportError(req, err)
			return http.StatusInternalServerError, ErrorMessage("Error finding contracts", nil)
		}
		totalCost = totalCost.Add(newCost)
		err = db.Get().Get(&newCost, `select coalesce(sum(cost), 0) from contract_options where contract_id = $1`, contract.ID)
		if err != nil {
			err = errors.Wrap(err, "error select for contract_options")
			ReportError(req, err)
			return http.StatusInternalServerError, ErrorMessage("Error finding contracts", nil)
		}
		totalCost = totalCost.Add(newCost)
		err = db.Get().Get(&newCost, `select coalesce(sum(cost), 0) from contract_surcharges where contract_id = $1`, contract.ID)
		if err != nil {
			err = errors.Wrap(err, "error select for contract_surcharges")
			ReportError(req, err)
			return http.StatusInternalServerError, ErrorMessage("Error finding contracts", nil)
		}
		totalCost = totalCost.Add(newCost)
		contracts[i].TotalCost = totalCost

		isLHMStore, err := isLHMStore(contract.ID)
		if err != nil {
			err = errors.Wrapf(err, "error getting store for contract %s", contract.Code)
			ReportError(req, err)
			return http.StatusInternalServerError, ErrorMessage("Error getting store for contract", nil)
		}

		contracts[i].IsVTAOverAllowanceApplied = false
		if isLHMStore && contract.ProductTypeCode == db.ProductTypeCodeVehicleTheftAssistance {
			var rateBuckets []string
			err = db.Get().Select(&rateBuckets, `select rb.name from contract_surcharges cs 
												join surcharges s on cs.surcharge_id = s.id 
												join rate_buckets rb on s.rate_bucket_id = rb.id where cs.contract_id = $1`, contract.ID)
			if err != nil {
				err = errors.Wrap(err, "error selecting rate bucket name")
				ReportError(req, err)
				return http.StatusInternalServerError, ErrorMessage("Error finding contracts", nil)
			}

			for _, v := range rateBuckets {
				if v == "VTA Overallowance" {
					contracts[i].IsVTAOverAllowanceApplied = true
					break
				}
			}
		}

		contracts[i].IsLHMStore = isLHMStore

		if contract.PaymentType == dms.PaymentTypeSPP {
			var files []int
			query := `select at.id from contracts c
				join contract_attachments ca on c.id = ca.contract_id
				join attachment_types at on ca.attachment_type_id = at.id
			where code = $1
				and at.name in ($2, $3)
			group by at.id`

			err := db.Get().Select(&files, query, contract.Code, db.AttachmentTypeSPPTCAContract, db.AttachmentTypeSPPRetailInstallmentContract)
			if err != nil && err != sql.ErrNoRows {
				err = errors.Wrap(err, "error getting contract attachment")
				ReportError(req, err)
				return http.StatusInternalServerError, ErrorMessage("Error getting contract attachment", nil)
			}

			if len(files) == 2 {
				contracts[i].AllSPPFilesAttached = true
			}
		}
		if contract.IsEContract {
			if !contract.DMSNumberUpdatedAt.Valid {
				var requireDNUpdate bool
				err := db.Get().Get(&requireDNUpdate, `select require_deal_number_remit
			from dealer_platforms dp
			join e_sales es on es.dealer_platform_id = dp.id
			where es.sale_id = $1`, contract.SaleID)
				if err != nil && err != sql.ErrNoRows {
					err = errors.Wrap(err, "error getting dealer platform")
					ReportError(req, err)
					return http.StatusInternalServerError, ErrorMessage("Error finding contracts", nil)
				}
				if requireDNUpdate {
					contracts[i].IsDealNumberNeedUpdate = true
				}
			}
			query := `select cp.hide_void_dealer_contract
				from contracts ct
				join sales s
						on ct.sale_id = s.id
				join stores st
						on st.id = ct.store_id
				join companies cp
						on cp.id = st.company_id
				join dealer_platforms dp
						on ct.dealer_platform_id = dp.id
				where s.id = $1 and
						dp.sales_channel = $2
				limit 1`
			err := db.Get().Get(&contracts[i].HideVoidDealerContract, query, contract.SaleID, db.SalesChannelDealer)
			if err != nil && err != sql.ErrNoRows {
				ReportError(req, errors.Wrap(err, "error finding hide void for dealer contract"))
				return http.StatusInternalServerError, ErrorMessage("Error finding contracts", nil)
			}
		}
	}

	return http.StatusOK, map[string]interface{}{"contracts": contracts}
}

type contractView struct {
	ID                        int                    `db:"id" json:"id"`
	CreatedAt                 time.Time              `db:"created_at" json:"created_at"`
	CreatedByUserID           int                    `db:"created_by_user_id" json:"created_by_user_id"`
	UpdatedAt                 time.Time              `db:"updated_at" json:"updated_at"`
	UpdatedByUserID           int                    `db:"updated_by_user_id" json:"updated_by_user_id"`
	SaleID                    int                    `db:"sale_id" json:"sale_id"`
	ProductTypeID             int                    `db:"product_type_id" json:"product_type_id"`
	QuoteProductTypeID        null.Int               `db:"quote_product_type_id" json:"quote_product_type_id"`
	Code                      string                 `db:"code" json:"code"`
	CustomerID                int                    `db:"customer_id" json:"customer_id"`
	QuotePlanID               sql.NullInt64          `db:"quote_plan_id" json:"quote_plan_id"`
	VINRecordID               int                    `db:"vin_record_id" json:"vin_record_id"`
	EffectiveDate             time.Time              `db:"effective_date" json:"effective_date"`
	EffectiveMileage          int                    `db:"effective_mileage" json:"effective_mileage"`
	ExpirationDate            time.Time              `db:"expiration_date" json:"expiration_date"`
	ExpirationMileage         sql.NullInt64          `db:"expiration_mileage" json:"expiration_mileage"`
	Price                     decimal.Decimal        `db:"price" json:"price"`
	PlanCost                  decimal.Decimal        `db:"plan_cost" json:"-"`
	TotalCost                 decimal.Decimal        `db:"-" json:"total_cost"`
	Status                    string                 `db:"status" json:"status"`
	FinanceTerm               sql.NullInt64          `db:"finance_term" json:"finance_term"`
	FinanceAmount             decimal.NullDecimal    `db:"finance_amount" json:"finance_amount"`
	ProductName               string                 `db:"product_name" json:"product_name"`
	PlanName                  string                 `db:"plan_name" json:"plan_name"`
	PlanDuration              int                    `db:"plan_duration" json:"plan_duration"`
	PlanMileage               sql.NullInt64          `db:"plan_mileage" json:"plan_mileage"`
	QuoteCouponID             sql.NullInt64          `db:"quote_coupon_id" json:"quote_coupon_id"`
	QuoteCoupon               *db.QuoteCoupon        `db:"-" json:"quote_coupon"`
	RemittedAt                pq.NullTime            `db:"remitted_at" json:"remitted_at"`
	RemittedByUserID          sql.NullInt64          `db:"remitted_by_user_id" json:"remitted_by_user_id"`
	ProductVariantDisplayName string                 `db:"product_variant_display_name" json:"product_variant_display_name"`
	DealerPVDisplayName       string                 `db:"dealer_pv_display_name" json:"dealer_pv_display_name"`
	ClassificationCode        sql.NullString         `db:"classification_code" json:"classification_code"`
	VehicleTheftNumber        null.String            `db:"vehicle_theft_number" json:"vehicle_theft_number"`
	Options                   []db.ContractOption    `db:"-" json:"options"`
	Surcharges                []db.ContractSurcharge `db:"-" json:"surcharges"`
	OriginalCode              string                 `db:"original_code" json:"original_code"`
}

// ContractFind finds a contract in generated status based on sale_id and quote_product_type_id
func ContractFind(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	var contractIDs []int
	query := `select c.id
		from contracts c
		join sales s on c.sale_id = s.id
		join stores st on s.store_id = st.id
		join stores_user_versions suv on st.id = suv.store_id
		where c.sale_id = $1 and c.product_type_id = $2 and suv.user_version_id = $3`
	err := db.Get().Select(&contractIDs, query, chi.URLParam(req, "sale_id"), chi.URLParam(req, "product_type_id"), user.UserVersionID)
	if err != nil {
		err = errors.Wrap(err, "could not query contracts.id")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error finding contract", nil)
	}
	if len(contractIDs) < 1 {
		return http.StatusNotFound, ErrorMessage("Contract not found", nil)
	}

	var contract contractView
	eSaleID := 0
	err = db.Get().Get(&eSaleID, `select id from e_sales where sale_id = $1`, chi.URLParam(req, "sale_id"))
	if err != nil && err != sql.ErrNoRows {
		err = errors.Wrap(err, "could not query e_sales")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Contract not found", nil)
	}

	if eSaleID == 0 {
		query = `select c.*,
		qpv.display_name as product_variant_display_name,
		coalesce(dppv.display_name,'') as dealer_pv_display_name,
		cc.code as classification_code
		from contracts c
		join quote_plans qp on c.quote_plan_id = qp.id
		join quote_product_variants qpv on qp.quote_product_variant_id = qpv.id
		left join classifications cl on qpv.classification_id = cl.id
		left join classification_codes cc on cl.classification_code_id = cc.id
		left join dealer_platform_product_variants dppv
			on (c.product_variant_id = dppv.product_variant_id and dppv.dealer_platform_id = c.dealer_platform_id)
		where c.id in (?) and c.status in (?)`
	} else {
		query = `select c.*,                   
                cc.code as classification_code,
				coalesce(dppv.display_name,'') as dealer_pv_display_name
                from contracts c
                left join classifications cl on c.classification_id = cl.id
                left join classification_codes cc on cl.classification_code_id = cc.id
				left join dealer_platform_product_variants dppv
					on (c.product_variant_id = dppv.product_variant_id and dppv.dealer_platform_id = c.dealer_platform_id)
                where c.id in (?) and c.status in (?)`
	}

	var args []interface{}
	query, args, err = sqlx.In(query, contractIDs, db.ContractStatusesActive)
	if err != nil {
		err = errors.Wrap(err, "could not setup In contracts")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error finding contract", nil)
	}
	query = db.Get().Rebind(query)
	err = db.Get().Unsafe().Get(&contract, query, args...)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, ErrorMessage("Contract not found", nil)
		}
		err = errors.Wrap(err, "could not query contracts")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error finding contract", nil)
	}

	var adjustmentsCost decimal.Decimal
	query = `select coalesce(sum(cost), 0.0) from contract_adjustments where contract_id = $1`
	err = db.Get().Get(&adjustmentsCost, query, contract.ID)
	if err != nil {
		err = errors.Wrap(err, "could not query contract_adjustments")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error finding contract", nil)
	}
	contract.TotalCost = contract.PlanCost.Add(adjustmentsCost)

	query = `select * from contract_options where contract_id = $1`
	err = db.Get().Unsafe().Select(&contract.Options, query, contract.ID)
	if err != nil {
		err = errors.Wrap(err, "could not query contract_options")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error finding contract", nil)
	}

	query = `select * from contract_surcharges where contract_id = $1`
	err = db.Get().Unsafe().Select(&contract.Surcharges, query, contract.ID)
	if err != nil {
		err = errors.Wrap(err, "could not query contract_surcharges")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error finding contract", nil)
	}

	if contract.QuoteCouponID.Valid {
		query = `select * from quote_coupons where id = $1`
		contract.QuoteCoupon = &db.QuoteCoupon{}
		err = db.Get().Unsafe().Get(contract.QuoteCoupon, query, contract.QuoteCouponID.Int64)
		if err != nil {
			err = errors.Wrap(err, "could not query quote_coupons")
			ReportError(req, err)
			return http.StatusInternalServerError, ErrorMessage("Error finding contract", nil)
		}
	}

	return http.StatusOK, map[string]interface{}{"contract": contract}
}

// ContractFindVoided finds voided contracts for a sale and quote product type
func ContractFindVoided(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	var contractIDs []int
	query := `select c.id
		from contracts c
		join sales s on c.sale_id = s.id
		join stores st on s.store_id = st.id
		join stores_user_versions suv on st.id = suv.store_id
		where c.sale_id = $1 and c.product_type_id = $2 and suv.user_version_id = $3`
	err := db.Get().Select(&contractIDs, query, chi.URLParam(req, "sale_id"), chi.URLParam(req, "product_type_id"), user.UserVersionID)
	if err != nil {
		err = errors.Wrap(err, "could not query contracts.id")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error finding voided contracts", nil)
	}

	var contracts []struct {
		ID           int    `db:"id" json:"id"`
		Code         string `db:"code" json:"code"`
		OriginalCode string `db:"original_code" json:"original_code"`
	}
	if len(contractIDs) > 0 {
		query = `select id, code, original_code from contracts where id in (?) and status = ? order by code`
		var args []interface{}
		query, args, err = sqlx.In(query, contractIDs, db.ContractStatusVoided)
		if err != nil {
			err = errors.Wrap(err, "could not setup In contracts")
			ReportError(req, err)
			return http.StatusInternalServerError, ErrorMessage("Error finding voided contracts", nil)
		}
		query = db.Get().Rebind(query)
		err := db.Get().Select(&contracts, query, args...)
		if err != nil {
			err = errors.Wrap(err, "could not query contracts")
			ReportError(req, err)
			return http.StatusInternalServerError, ErrorMessage("Error finding voided contracts", nil)
		}
	}

	return http.StatusOK, map[string]interface{}{"contracts": contracts}
}

const updateContractUnvoidQuery = `update contracts
set version = version + 1,
updated_at = now() at time zone 'utc', updated_by_user_id = :updated_by_user_id,
status = :status, event = :event, event_notes = :event_notes
where id = :id
returning updated_at`

// ContractUnvoid unvoid a contract
func ContractUnvoid(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	contractID, err := strconv.Atoi(chi.URLParam(req, "id"))
	if err != nil {
		ReportError(req, errors.Wrapf(err, "failed to parse contract id %s to int", chi.URLParam(req, "id")))
		return http.StatusBadRequest, ErrorMessage("Invalid contract ID", nil)
	}

	allowed, err := user.CanAccessContract(contractID)
	if err != nil {
		ReportError(req, errors.Wrap(err, "failed to check user authorization"))
		return http.StatusInternalServerError, ErrorMessage("Authorization Error", nil)
	}
	if !allowed {
		return http.StatusUnauthorized, ErrorMessage("Not authorized for this action", nil)
	}

	var contract struct {
		db.ContractWithData
	}
	query := `select c.*, s.payment_type
		from contracts c
		join sales s on c.sale_id = s.id
		join stores_user_versions suv on s.store_id = suv.store_id
		join current_users cu on cu.user_version_id = suv.user_version_id
		where c.id = $1 and c.status = $2 and suv.user_version_id = $3`
	err = db.Get().Unsafe().Get(&contract, query, contractID, db.ContractStatusVoided, user.UserVersionID)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, ErrorMessage("Contract not found", nil)
		}
		err = errors.Wrap(err, "could not query contracts")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error unvoiding contract", nil)
	}

	query, args, err := sqlx.In("select count(*) from contracts where sale_id = ? and product_type_id = ? and status in (?)",
		contract.SaleID, contract.ProductTypeID, db.ContractStatusesActive)
	if err != nil {
		err = errors.Wrap(err, "could not setup In contracts query to validate no active contracts")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error unvoiding contract", nil)
	}
	var foundContracts int
	query = db.Get().Rebind(query)
	err = db.Get().Get(&foundContracts, query, args...)
	if err != nil {
		err = errors.Wrap(err, "could not query contracts to validate no active contracts")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error unvoiding contract", nil)
	}
	if foundContracts > 0 {
		return http.StatusBadRequest, ErrorMessage("There is already an active contract for that product type on the sale", nil)
	}

	if !user.HasRole(db.RoleRemit) {
		if contract.SaleType == dms.SaleTypeFinanceDeal && !user.HasRole(db.RoleFinanceDeal) {
			return http.StatusForbidden, ErrorMessage("Not allowed", nil)
		}
		if contract.SaleType == dms.SaleTypeServiceRO && (!user.HasRole(db.RoleServiceRO) || !user.HasRole(db.RoleBDCRO)) {
			return http.StatusForbidden, ErrorMessage("Not allowed", nil)
		}
	}

	if contract.PaymentType == dms.PaymentTypeSPP && contract.ProductTypeCode == "MNT" {
		query = `select count(*)
		from contracts
		where product_type_code = $1
		and sale_id = $2
		and status in ($3,$4)`
		var count int
		err = db.Get().Get(&count, query, "VSC", contract.SaleID, db.ContractStatusGenerated, db.ContractStatusActive)
		if err != nil {
			err = errors.Wrap(err, "error checking if Service contract is generated for SPP")
			ReportError(req, err)
			return http.StatusInternalServerError, ErrorMessage("Error unvoiding contract", nil)
		}
		if count == 0 {
			return http.StatusBadRequest, ErrorMessage("On SPP, need to have generated Service before unvoiding Maintenance", nil)
		}
	}

	query = `select * from contract_adjustments where contract_id = $1`
	err = db.Get().Unsafe().Select(&contract.Adjustments, query, contract.ID)
	if err != nil {
		err = errors.Wrap(err, "could not query contract_adjustments")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error unvoiding contract", nil)
	}

	query = `select * from contract_options where contract_id = $1`
	err = db.Get().Unsafe().Select(&contract.Options, query, contract.ID)
	if err != nil {
		err = errors.Wrap(err, "could not query contract_options")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error unvoiding contract", nil)
	}

	query = `select * from contract_surcharges where contract_id = $1`
	err = db.Get().Unsafe().Select(&contract.Surcharges, query, contract.ID)
	if err != nil {
		err = errors.Wrap(err, "could not query contract_surcharges")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error unvoiding contract", nil)
	}

	contract.UpdatedByUserID = user.ID
	contract.Status = db.ContractStatusGenerated
	contract.Event = db.ContractEventUnvoid
	contract.EventNotes = ""

	tx, err := db.Get().Beginx()
	if err != nil {
		err = errors.Wrap(err, "error beginning transaction")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error unvoiding contract", nil)
	}

	err = db.CreateContractLog(tx, contract.ID, user.ID)
	if err != nil {
		tx.Rollback()
		err = errors.Wrap(err, "error contract_logs insert")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error unvoiding contract", nil)
	}
	contractStmt, err := tx.PrepareNamed(updateContractUnvoidQuery)
	if err != nil {
		tx.Rollback()
		err = errors.Wrap(err, "error preparing contracts update")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error unvoiding contract", nil)
	}
	err = contractStmt.Get(&contract, contract)
	if err != nil {
		tx.Rollback()
		err = errors.Wrap(err, "error executing contracts update")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error unvoiding contract", nil)
	}

	query = `insert into contract_events(created_at,created_by_user_id,created_by_name,contract_id,description)values(
	now() at time zone 'utc', $1, $2, $3, $4)`
	_, err = tx.Exec(query, user.ID, user.FullName(), contract.ID, contract.Event)
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "error inserting contract event")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error while logging contract unvoid event", nil)
	}

	err = tx.Commit()
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "error committing")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error unvoiding contract", nil)
	}

	return http.StatusOK, map[string]interface{}{"contract": contract}
}

// ContractFormDownload redirects the user to a contract's PDF in S3
func ContractFormDownload(w http.ResponseWriter, req *http.Request, user db.CurrentUser) {
	contractID, err := strconv.Atoi(chi.URLParam(req, "id"))
	if err != nil {
		ReportError(req, errors.Wrapf(err, "failed to parse contract id %s to int", chi.URLParam(req, "id")))
		w.WriteHeader(http.StatusBadRequest)
		return
	}

	allowed, err := user.CanAccessContract(contractID)
	if err != nil {
		ReportError(req, errors.Wrap(err, "failed to check user authorization"))
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
	if !allowed {
		w.WriteHeader(http.StatusUnauthorized)
		fmt.Fprint(w, "Unauthorized")
	}

	var contract struct {
		Code                 string `db:"code"`
		ContractFormS3Region string `db:"contract_form_s3_region"`
		ContractFormS3Bucket string `db:"contract_form_s3_bucket"`
		ContractFormFileName string `db:"contract_form_file_name"`
		IsEContract          bool   `db:"is_e_contract"`
		ContractFormID       int    `db:"contract_form_id"`
	}
	query := `select c.code, c.is_e_contract, c.contract_form_s3_region, 
       	c.contract_form_s3_bucket, c.contract_form_file_name, c.contract_form_id
		from contracts c
		join contract_forms cf on c.contract_form_id = cf.id
		join stores st on c.store_id = st.id
		join stores_user_versions suv on st.id = suv.store_id
		where c.id = ? 
	    and c.status in (?) 
	    and suv.user_version_id = ?
		and c.contract_form_s3_region is not null 
	    and c.contract_form_s3_bucket is not null 
	    and c.contract_form_file_name is not null`

	var args []interface{}
	query, args, err = sqlx.In(query, contractID, db.ContractStatusesDashboardViewable, user.UserVersionID)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		fmt.Fprint(w, "Error finding contract")
		err = errors.Wrap(err, "error preparing In to find contract")
		ReportError(req, err)
	}
	query = db.Get().Rebind(query)
	err = db.Get().Unsafe().Get(&contract, query, args...)
	if err != nil {
		if err == sql.ErrNoRows {
			w.WriteHeader(http.StatusNotFound)
			fmt.Fprint(w, "Not Found")
		} else {
			w.WriteHeader(http.StatusInternalServerError)
			fmt.Fprint(w, "Error finding contract")
			err = errors.Wrap(err, "error loading contract form data for PDF download")
			ReportError(req, err)
		}
		return
	}

	if !contract.IsEContract {
		reverseProxy := s3util.GetS3ReverseProxy()
		signedURL, err := reverseProxy.GetSecureURL(
			contract.ContractFormS3Region, contract.ContractFormS3Bucket,
			contract.ContractFormFileName, filepath.Base(contract.ContractFormFileName),
			"application/pdf", user, time.Minute*conf.Get().S3ReverseProxy.DefaultLinkTimeoutMinutes)
		if err != nil {
			w.WriteHeader(http.StatusInternalServerError)
			fmt.Fprint(w, "Error loading PDF")
			err = errors.Wrap(err, "error making contract form presigned download URL")
			ReportError(req, err)
			return
		}
		http.Redirect(w, req, signedURL, http.StatusTemporaryRedirect)
	}

	ctx := req.Context()
	txn := newrelic.FromContext(ctx)
	urlF, err := s3util.PresignedURL(
		contract.ContractFormS3Region,
		contract.ContractFormS3Bucket,
		contract.ContractFormFileName,
		filepath.Base(contract.ContractFormFileName),
		"application/pdf")
	if err != nil {
		_ = r.Text(w, http.StatusInternalServerError, "Error getting contract")
		err = errors.Wrap(err, fmt.Sprintf(
			"error making contract (%s) form presigned download URL", contract.Code))
		ReportError(req, err)
		return
	}

	req, err = http.NewRequest("GET", urlF, nil)
	if err != nil {
		_ = r.Text(w, http.StatusInternalServerError, "Error getting contract")
		err = errors.Wrap(err, fmt.Sprintf(
			"error creating request to download contract '%s'", contract.Code))
		ReportError(req, err)
		return
	}
	client := http.Client{Timeout: time.Second * 15}
	resp, err := nr.External(txn, client, req)
	if err != nil {
		_ = r.Text(w, http.StatusInternalServerError, "Error getting contract")
		err = errors.Wrap(err, fmt.Sprintf("error downloading contract '%s'", contract.Code))
		ReportError(req, err)
		return
	}

	// check for valid response
	if resp.StatusCode != http.StatusOK {
		_ = r.Text(w, resp.StatusCode, resp.Status+": Access denied / Request has expired")
		err = errors.New(fmt.Sprintf("error downloading contract '%s'", contract.Code))
		ReportError(req, err)
		return
	}

	defer func() { _ = resp.Body.Close() }()

	originalFile, err := ioutil.TempFile("", "contract-form")
	if err != nil {
		_ = r.Text(w, http.StatusInternalServerError, "Error getting contract")
		err = errors.Wrap(err, fmt.Sprintf(
			"error creating temp file for contract '%s'", contract.Code))
		ReportError(req, err)
		return
	}
	defer func() {
		_ = originalFile.Close()
		_ = os.Remove(originalFile.Name())
	}()

	var pdfBytes []byte

	// Read the contents of the original PDF into a byte array. This will be used
	// if there's no signature pages defined for the form that the contract used when
	// the stamped contract form was created.
	pdfBytes, err = io.ReadAll(resp.Body)
	if err != nil {
		_ = r.Text(w, http.StatusInternalServerError, "Error getting contract pdf")
		err = errors.Wrapf(err, "error getting pdf contents for contract '%s'", contract.Code)
		ReportError(req, err)
		return
	}

	// We need to create a new reader as the contents of the resp.Body has already been read
	// so that we can copy the original pdf contents to a file.
	pdfContentsReader := bytes.NewReader(pdfBytes)
	_, err = io.Copy(originalFile, pdfContentsReader)
	if err != nil {
		_ = r.Text(w, http.StatusInternalServerError, "Error getting contract")
		err = errors.Wrap(err, fmt.Sprintf(
			"error writing temp file for contract '%s'", contract.Code))
		ReportError(req, err)
		return
	}
	defer originalFile.Close()

	signaturePageQuery := `select distinct cfs.page as sign 
						   from contract_forms cf 
    					   join contract_form_signatures cfs on cfs.contract_form_id = cf.id
						   where cf.id = $1 order by sign`

	var signPages []int
	err = db.Get().SelectContext(ctx, &signPages, signaturePageQuery, contract.ContractFormID)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		fmt.Fprint(w, "Error loading PDF")
		err = errors.Wrap(err, "error fetching distinct signature page numbers  for the contract")
		ReportError(req, err)
		return
	}

	// There are cases where some contracts used forms that didn't have signature pages setup
	// in the database, so we don't want to try to create duplicate signature pages if there's
	// no signature pages defined to duplicate.
	if len(signPages) > 0 {
		dupFilenames := make(map[int][]string)
		for _, signPageNum := range signPages {
			additionalSignatureFile, err := getAdditionalFile(
				ctx, originalFile, strconv.Itoa(signPageNum))
			if err != nil {
				_ = r.Text(w, http.StatusInternalServerError, "Error getting contract")
				return
			}
			defer func() {
				_ = additionalSignatureFile.Close()
				_ = os.Remove(additionalSignatureFile.Name())
			}()

			// add two duplicate signature page only
			dupFilenames[signPageNum] = append(
				dupFilenames[signPageNum],
				additionalSignatureFile.Name(),
				additionalSignatureFile.Name())
		}

		var finalFileNames []string
		lastSignPageNo := 1

		// get additional file with signature pages
		for signPage, dupFiles := range dupFilenames {
			if signPage != 1 && lastSignPageNo == 1 {
				additionalSignatureFile, err := getAdditionalFile(
					ctx, originalFile,
					strconv.Itoa(lastSignPageNo)+"-"+strconv.Itoa(signPage))
				if err != nil {
					_ = r.Text(w, http.StatusInternalServerError, "Error getting contract")
					return
				}
				defer func() {
					_ = additionalSignatureFile.Close()
					_ = os.Remove(additionalSignatureFile.Name())
				}()
				finalFileNames = append(finalFileNames, additionalSignatureFile.Name())
			} else if len(finalFileNames) != 0 && signPage-lastSignPageNo != 0 {
				additionalSignatureFile, err := getAdditionalFile(
					ctx, originalFile,
					strconv.Itoa(lastSignPageNo)+"-"+strconv.Itoa(signPage))
				if err != nil {
					_ = r.Text(w, http.StatusInternalServerError, "Error getting contract")
					return
				}
				defer func() {
					_ = additionalSignatureFile.Close()
					_ = os.Remove(additionalSignatureFile.Name())
				}()
				finalFileNames = append(finalFileNames, additionalSignatureFile.Name())
			}

			finalFileNames = append(finalFileNames, dupFiles...)
			lastSignPageNo = signPage
		}

		if len(dupFilenames) == 1 {
			finalFileNames = append(finalFileNames, originalFile.Name())
		}

		// replace the pdfBytes with the bytes of the newly concatenated pdf with the duplicated
		// signature pages added to the original contractg form PDF.
		pdfBytes, err = pdftk.Cat(finalFileNames...)
		if err != nil {
			_ = r.Text(w, http.StatusInternalServerError, "Error getting contract")
			err = errors.Wrap(err, "error executing pdftk cat")
			ReportError(req, err)
			return
		}
	}

	w.Header().Set("Content-Type", "application/pdf")
	w.Header().Set("Content-Disposition", fmt.Sprintf("inline; filename="+contract.Code+".pdf"))
	w.WriteHeader(http.StatusOK)
	_, err = io.Copy(w, bytes.NewReader(pdfBytes))
	if err != nil {
		err = errors.Wrap(err, "error writing contractsPDF to response")
		ReportError(req, err)
		return
	}
}

func getAdditionalFile(ctx context.Context, originalFile *os.File, conCatPage string) (*os.File, error) {
	additionalSignatureFile, err := ioutil.TempFile("", "additional-signature-pages")
	if err != nil {
		err = errors.Wrap(err, "error creating temp file for additional signature pages on contract")
		util.ReportError(ctx, err)
		return nil, err
	}
	err = pdftk.CatPages(originalFile.Name(), additionalSignatureFile.Name(), conCatPage)
	if err != nil {
		err = errors.Wrap(err, "error creating additional signature pages on contrac")
		util.ReportError(ctx, err)
		return nil, err
	}
	return additionalSignatureFile, nil
}

const updateContractVoidQuery = `update contracts
set version = version + 1,
updated_at = now() at time zone 'utc', updated_by_user_id = :updated_by_user_id,
status = :status, event = :event, event_notes = :event_notes
where id = :id
returning updated_at`

type contractData struct {
	db.ContractWithData
}

// ContractCustomError is the custom error from contract
type ContractCustomError struct {
	ErrorMessage string
	Err          error
	Status       int
	ErrorCode    string
}

// Error returns the error message
func (e *ContractCustomError) Error() string {
	return fmt.Sprintf("Error: %s", e.ErrorMessage)
}

func createCustomError(message string, status int, err error, errorCode string) *ContractCustomError {
	return &ContractCustomError{
		ErrorMessage: message,
		Status:       status,
		Err:          err,
		ErrorCode:    errorCode,
	}
}

type vtaOverAllowanceDetails struct {
	IsVTAOverallowance           bool
	VTAOverallowanceAmount       decimal.Decimal
	VTAOverallowanceRateBucketID int64
}

// ContractVoid voids the contract
func ContractVoid(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()

	statusToReturn := http.StatusInternalServerError
	errorMessage := "Error voiding contract"
	contractID, err := strconv.Atoi(chi.URLParam(req, "id"))
	if err != nil {
		util.LogWarning(ctx, errors.Wrap(err, "error parsing contract id"))
		return http.StatusBadRequest, ErrorMessage("Invalid contract id", nil)
	}

	contract, err := voidContract(ctx, contractID, user, false, 0)
	if err != nil {
		if serr, ok := err.(*ContractCustomError); ok {
			err = serr.Err
			statusToReturn = serr.Status
			errorMessage = serr.Error()
		}
		if err != nil {
			ReportError(req, err)
		}
		return statusToReturn, ErrorMessage(errorMessage, nil)
	}

	return http.StatusOK, map[string]interface{}{"contract": contract}
}

func voidContract(ctx context.Context, contractID int, user db.CurrentUser, isESaleContract bool, storeID int) (contractData, error) {
	if ok, err := canAccessContract(ctx, isESaleContract, contractID, storeID, user); !ok {
		return contractData{}, err
	}

	contract, err := getContract(ctx, contractID, storeID, user, isESaleContract)
	if err != nil {
		return contract, err
	}

	err = loadContractData(ctx, &contract)
	if err != nil {
		return contract, err
	}

	contract, err = processVoidContract(ctx, contract, user)
	if err != nil {
		return contract, err
	}
	return contract, nil
}

func canAccessContract(ctx context.Context, isESaleContract bool, contractID int, storeID int, user db.CurrentUser) (bool, error) {
	if isESaleContract {
		allowed, err := db.CanStoreAccessContract(ctx, contractID, storeID)
		if err != nil {
			return false, createCustomError("Authorization Error", http.StatusInternalServerError,
				errors.Wrapf(err, "failed to check store authorization for contract %d", contractID), "")
		}
		if !allowed {
			return false, createCustomError("Access Denied: Store not authorized for this action", http.StatusForbidden, nil, db.ErrCodeAccessDenied)
		}
		return true, nil
	}

	allowed, err := user.CanAccessContract(contractID)
	if err != nil {
		return false, createCustomError("Authorization Error", http.StatusInternalServerError,
			errors.Wrapf(err, "failed to check user authorization for contract %d", contractID), "")
	}
	if !allowed {
		return false, createCustomError("Access Denied: User not authorized for this action", http.StatusForbidden, nil, db.ErrCodeAccessDenied)
	}
	return true, nil
}

func getContract(ctx context.Context, contractID int, storeID int, user db.CurrentUser, isESaleContract bool) (contractData, error) {
	var contract contractData
	var err error

	if isESaleContract {
		query := `select c.*, s.payment_type
				from contracts c
         			join sales s on c.sale_id = s.id
					join stores st on c.store_id = st.id
				where c.id = $1 and st.id = $2 and c.is_e_contract = true`
		err = db.Get().Unsafe().GetContext(ctx, &contract, query, contractID, storeID)
	} else {
		query := `select c.*, s.payment_type
		from contracts c
		join sales s on c.sale_id = s.id
		join stores_user_versions suv on s.store_id = suv.store_id
		join current_users cu on cu.user_version_id = suv.user_version_id
		where c.id = $1 and c.status = $2 and suv.user_version_id = $3`
		err = db.Get().Unsafe().GetContext(ctx, &contract, query, contractID, db.ContractStatusGenerated, user.UserVersionID)
	}
	if err != nil {
		if err == sql.ErrNoRows {
			return contract, &ContractCustomError{
				ErrorMessage: "Contract not found, please provide valid contract code",
				Status:       http.StatusNotFound,
				Err:          nil,
				ErrorCode:    db.ErrCodeContractNotFound,
			}
		}
		return contract, &ContractCustomError{
			ErrorMessage: "Error finding contract",
			Status:       http.StatusInternalServerError,
			Err:          errors.Wrap(err, "could not query contracts"),
		}
	}

	if isESaleContract && contract.Status != db.ContractStatusGenerated {
		return contract, &ContractCustomError{
			ErrorMessage: "Only contracts with a status of 'Generated' can be voided",
			Status:       http.StatusBadRequest,
			Err:          nil,
			ErrorCode:    db.ErrCodeInvalidContractStatus,
		}
	}

	if !isESaleContract {
		if !user.HasRole(db.RoleRemit) {
			if contract.SaleType == dms.SaleTypeFinanceDeal && !user.HasRole(db.RoleFinanceDeal) {
				return contract, &ContractCustomError{
					ErrorMessage: "Not allowed",
					Status:       http.StatusForbidden,
					Err:          nil,
				}
			}
			if contract.SaleType == dms.SaleTypeServiceRO && !user.HasRole(db.RoleServiceRO) {
				return contract, &ContractCustomError{
					ErrorMessage: "Not allowed",
					Status:       http.StatusForbidden,
					Err:          nil,
				}
			}
		}
	}

	if !isESaleContract {
		if contract.PaymentType == dms.PaymentTypeSPP && contract.ProductTypeCode == "VSC" {
			query := `select count(*)
				from contracts
				where product_type_code = $1
				and sale_id = $2
				and status in ($3, $4, $5, $6)`
			var count int
			err = db.Get().GetContext(ctx, &count, query, "MNT", contract.SaleID, db.ContractStatusGenerated, db.ContractStatusRemitted, db.ContractStatusPending, db.ContractStatusActive)
			if err != nil {
				return contract, &ContractCustomError{
					ErrorMessage: "Error finding contract",
					Status:       http.StatusInternalServerError,
					Err:          errors.Wrap(err, "error checking if Maintenance contract is still active before voiding Service on SPP"),
				}
			}
			if count > 0 {
				return contract, &ContractCustomError{
					ErrorMessage: "Need to void Maintenance contract for SPP before voiding Service contract",
					Status:       http.StatusBadRequest,
					Err:          nil,
				}
			}
		}
	}

	contract.UpdatedByUserID = user.ID
	contract.Status = db.ContractStatusVoided
	contract.Event = db.ContractEventVoid
	contract.EventNotes = ""

	return contract, nil
}

func loadContractData(ctx context.Context, contract *contractData) error {
	query := `select * from contract_adjustments where contract_id = $1`
	err := db.Get().Unsafe().SelectContext(ctx, &contract.Adjustments, query, contract.ID)
	if err != nil {
		return createCustomError("Error loading contract details",
			http.StatusInternalServerError,
			errors.Wrap(err, "could not query contract_adjustments"), "")
	}

	query = `select * from contract_options where contract_id = $1`
	err = db.Get().Unsafe().SelectContext(ctx, &contract.Options, query, contract.ID)
	if err != nil {
		return createCustomError("Error loading contract details",
			http.StatusInternalServerError,
			errors.Wrap(err, "could not query contract_options"), "")
	}

	query = `select * from contract_surcharges where contract_id = $1`
	err = db.Get().Unsafe().SelectContext(ctx, &contract.Surcharges, query, contract.ID)
	if err != nil {
		return createCustomError("Error loading contract details",
			http.StatusInternalServerError,
			errors.Wrap(err, "could not query contract_surcharges"), "")
	}
	return nil
}

func processVoidContract(ctx context.Context, contract contractData, user db.CurrentUser) (contractData, error) {
	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		return contract, createCustomError("Error voiding contract",
			http.StatusInternalServerError,
			errors.Wrap(err, "error beginning transaction"), "")
	}

	err = db.CreateContractLog(tx, contract.ID, user.ID)
	if err != nil {
		return contract, createCustomError("Error voiding contract",
			http.StatusInternalServerError,
			errors.Wrap(err, "error contract_logs insert"), "")
	}
	contractStmt, err := tx.PrepareNamedContext(ctx, updateContractVoidQuery)
	if err != nil {
		return contract, createCustomError("Error voiding contract",
			http.StatusInternalServerError,
			errors.Wrap(err, "error preparing contracts update"), "")
	}
	err = contractStmt.GetContext(ctx, &contract, contract)
	if err != nil {
		return contract, createCustomError("Error voiding contract",
			http.StatusInternalServerError,
			errors.Wrap(err, "error executing contracts update"), "")
	}

	query := `insert into contract_events(created_at,created_by_user_id,created_by_name,contract_id,description)values(
	now() at time zone 'utc', $1, $2, $3, $4)`
	_, err = tx.ExecContext(ctx, query, user.ID, user.FullName(), contract.ID, contract.Event)
	if err != nil {
		return contract, createCustomError("Error voiding contract",
			http.StatusInternalServerError,
			errors.Wrap(err, "error inserting contract event"), "")
	}

	err = tx.Commit()
	if err != nil {
		return contract, createCustomError("Error voiding contract",
			http.StatusInternalServerError,
			errors.Wrap(err, "error committing"), "")
	}

	return contract, nil
}

// ContractRemitSupportingData returns the data needed contract remitting functions
func ContractRemitSupportingData(h http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {

	var lenders []struct {
		ID               int    `db:"id" json:"id"`
		Name             string `db:"name" json:"name"`
		Address          string `db:"address" json:"address"`
		City             string `db:"city" json:"city"`
		StateCode        string `db:"state_code" json:"state_code"`
		PostalCode       string `db:"postal_code" json:"postal_code"`
		VTAOverallowance bool   `json:"add_vta_overallownce" db:"add_vta_overallownce"`
	}
	ctx := req.Context()
	query := `select id, name, address, city, state_code, postal_code, add_vta_overallownce
		from lenders where deleted_at is null and is_active = true order by name`

	err := db.Get().SelectContext(ctx, &lenders, query)
	if err != nil {
		err = errors.Wrap(err, "error getting lenders list for remit supporting data")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error getting supporting data from remitting contracts", nil)
	}

	var attachmentTypes []struct {
		ID   int    `json:"id" db:"id"`
		Name string `json:"name" db:"name"`
	}
	err = db.Get().SelectContext(ctx, &attachmentTypes, `select id, name from attachment_types order by id asc`)
	if err != nil {
		err = errors.Wrap(err, "error getting attachment_types list for remit supporting data")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error getting supporting data from remitting contracts", nil)
	}

	return http.StatusOK, map[string]interface{}{
		"lenders":         lenders,
		"attachmentTypes": attachmentTypes,
	}
}

// ContractUpdateLender updates the lender for the contract's sale
func ContractUpdateLender(h http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	contractID, err := strconv.Atoi(chi.URLParam(req, "id"))
	if err != nil {
		ReportError(req, errors.Wrapf(err, "failed to parse contract id %s to int", chi.URLParam(req, "id")))
		return http.StatusBadRequest, ErrorMessage("Invalid contract ID", nil)
	}

	allowed, err := user.CanAccessContract(contractID)
	if err != nil {
		ReportError(req, errors.Wrap(err, "failed to check user authorization"))
		return http.StatusInternalServerError, ErrorMessage("Authorization Error", nil)
	}
	if !allowed {
		return http.StatusUnauthorized, ErrorMessage("Not authorized for this action", nil)
	}

	var payload struct {
		LenderID int `json:"lender_id"`
	}

	dec := json.NewDecoder(req.Body)
	err = dec.Decode(&payload)
	if err != nil {
		return http.StatusBadRequest, ErrorMessage("Bad request, decoding error", nil)
	}
	if payload.LenderID == 0 {
		return http.StatusBadRequest, ErrorMessage("Lender ID is required", nil)
	}
	ctx := req.Context()

	var saleID int
	query := `select s.id from contracts c join sales s on s.id = c.sale_id join stores_user_versions suv on suv.store_id = s.store_id join current_users cu on cu.user_version_id = suv.user_version_id where c.id = $1 and c.status = $2 and suv.user_version_id = $3 and s.payment_type != $4`
	err = db.Get().GetContext(ctx, &saleID, query, contractID, db.ContractStatusGenerated, user.UserVersionID, dms.PaymentTypeCash)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, ErrorMessage("Not found", nil)
		}
		err = errors.Wrap(err, "error looking up contract to add lender")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error setting lender", nil)
	}

	query = `select count(*) from lenders where id = $1`
	var count int
	err = db.Get().GetContext(ctx, &count, query, payload.LenderID)
	if err != nil {
		err = errors.Wrap(err, `error validating lender ID for remit set lender`)
		ReportError(req, err)
		return http.StatusBadRequest, ErrorMessage("Invalid Lender", nil)
	}

	query = `select id from pending_lenders where contract_id = $1`
	var plID int
	err = db.Get().GetContext(ctx, &plID, query, contractID)
	if err != nil && err != sql.ErrNoRows {
		err = errors.Wrap(err, `error checking pending lender for contract`)
		ReportError(req, err)
		return http.StatusBadRequest, ErrorMessage("Error updating lender", nil)
	}

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		err = errors.Wrap(err, `error beginning transaction in updating lender`)
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error updating lender", nil)
	}

	query = `update sales set lender_id = $1 where id = $2`
	_, err = tx.ExecContext(ctx, query, payload.LenderID, saleID)
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrapf(err, "error updating sale ID %d to lender ID %d by user ID %d", saleID, payload.LenderID, user.ID)
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error updating lender", nil)
	}
	if plID != 0 {
		query = `update pending_lenders set
			confirmed_at = now() at time zone 'utc',
			lender_id = $1
			where id = $2`
		_, err = tx.ExecContext(ctx, query, payload.LenderID, plID)
		if err != nil {
			_ = tx.Rollback()
			err = errors.Wrapf(err, "error updating pending lender for contract %d", contractID)
			ReportError(req, err)
			return http.StatusInternalServerError, ErrorMessage("Error updating lender", nil)
		}
	}
	err = tx.Commit()
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrapf(err, "error updating sale ID %d to lender ID %d by user ID %d", saleID, payload.LenderID, user.ID)
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error updating lender", nil)
	}
	return http.StatusOK, map[string]interface{}{}
}

// ContractType represents a Contract with additional information that is used for remitting
type ContractType struct {
	db.ContractWithData
	VIN                 string       `db:"vin" json:"vin"`
	LenderID            null.Int     `db:"lender_id" json:"lender_id"`
	ContractDate        time.Time    `db:"contract_date" json:"contract_date"`
	VINRecord           db.VINRecord `db:"-" json:"vin_record"`
	RemittedByUserName  string       `db:"remitted_by_user_name" json:"-"`
	InspectionLiability null.String  `db:"inspection_liability" json:"inspection_liability"`
}

// inspectionsActiveByVIN returns count of inspections against vin returns -1 if error and 0 if no record found
func inspectionsActiveByVIN(isESaleContract bool, storeID int, user db.CurrentUser, vin string, date time.Time) (int, error) {
	var companyID int
	var inspectionCount int
	if isESaleContract {
		query := `select count(1) from inspections i
		where i.vin = $1 and i.deactivated_at is null and i.expires_on >= $2 and i.store_id = $3`
		err := db.Get().Get(&inspectionCount, query, vin, date, storeID)
		if err != nil {
			if err == sql.ErrNoRows {
				return 0, err
			}
			err = errors.Wrap(err, "error looking up inspection by vin")
			return -1, err
		}
		return inspectionCount, nil
	}
	query := `select s.company_id from stores_user_versions suv 
		join stores s on suv.store_id = s.id where suv.store_id = $1 and suv.user_version_id = $2`
	err := db.Get().Get(&companyID, query, storeID, user.UserVersionID)
	if err != nil {
		if err == sql.ErrNoRows {
			err = errors.Wrap(err, "invalid store id")
		} else {
			err = errors.Wrap(err, "error looking up company from given store")
		}
		return -1, err
	}

	query = `select count(1) from inspections i join stores s on i.store_id = s.id
		where i.vin = $1 and i.deactivated_at is null and i.expires_on >= $2 and s.company_id = $3 limit 1`
	err = db.Get().Get(&inspectionCount, query, vin, date, companyID)
	if err != nil {
		if err == sql.ErrNoRows {
			return 0, err
		}
		err = errors.Wrap(err, "error looking up inspection by vin")
		return -1, err
	}
	return inspectionCount, nil
}

// setContractFlags Method will set the flags which are related to the contract
func setContractFlags(contract *ContractType, user db.CurrentUser) error {
	query := `insert into contract_flags (flag_reason, contract_id, flagged_at, created_by_user_id) 
                values ($1, $2, now() at time zone 'utc', $3)`

	if !contract.IsEContract {
		// Inserting a flag when payment type is SPP
		if contract.PaymentType == dms.PaymentTypeSPP {
			_, err := db.Get().Exec(query, db.ContractFlagNewSPPContract, contract.ID, user.ID)
			if err != nil {
				return err
			}
		}
	}

	// Get minimum inspection level for the product
	minInspQ := `select products.minimum_inspection_level
	from contracts join product_variants on contracts.product_variant_id = product_variants.id
	join products on product_variants.product_id = products.id where contracts.id = $1`

	var minInspLevel null.String
	err := db.Get().Get(&minInspLevel, minInspQ, contract.ID)
	if err != nil {
		err = errors.Wrap(err, "db error getting minimum inspection level")
		return err
	}

	// Inserting flag when same product type contract exist for against same vin number.
	// Create query to get product types against same vin number
	selectQuery, args, err := sqlx.In(`select count(1) as productType 
		from contracts c join vin_records vr on c.vin_record_id = vr.id 
		where vr.vin = ? and c.id != ? and c.product_type_id = ? 
		and c.status in (?) and c.status not in (?)`, contract.VIN, contract.ID, contract.ContractWithData.Contract.ProductTypeID, db.ContractStatusesActive, db.ContractStatusGenerated)

	if err != nil {
		err = errors.Wrap(err, "db error creating product type count query")
		return err
	}
	var productType int
	selectQuery = db.Get().Rebind(selectQuery)
	err = db.Get().Get(&productType, selectQuery, args...)
	if err != nil {
		err = errors.Wrap(err, "db error getting product type count")
		return err
	}
	if productType > 0 {
		// Insert flag if same product types found
		_, err = db.Get().Exec(query, db.ContactFlagActiveContractSameProductType, contract.ID, user.ID)
		if err != nil {
			err = errors.Wrap(err, "db error inserting active contract for same product type flag")
			return err
		}
	}

	// Inserting flag when VW Gap contract dont have VW Credit as lender
	// Fetch lender and product variant type for the contract
	var lenderDetails struct {
		ProductVariant string `db:"product_variant"`
		Lender         string `db:"lender"`
	}
	selectQuery = `select pv.name as product_variant,l.name as lender from contracts c 
		join sales s on s.id = c.sale_id join lenders l on s.lender_id = l.id 
		join product_variants pv on c.product_variant_id = pv.id where c.id = $1`
	err = db.Get().Get(&lenderDetails, selectQuery, contract.ID)
	if err != nil && err != sql.ErrNoRows {
		err = errors.Wrap(err, "db error getting lender details")
		return err
	}

	const ProductPrefix = "VW GAP"
	const LenderPrefix = "VW CREDIT"
	if strings.HasPrefix(lenderDetails.ProductVariant, ProductPrefix) && (!strings.HasPrefix(lenderDetails.Lender, LenderPrefix)) {
		_, err = db.Get().Exec(query, db.ContractFlagVWGapNoVWCreditLender, contract.ID, user.ID)
		if err != nil {
			err = errors.Wrap(err, "db error inserting VW gap with no VW credit flag")
			return err
		}
	}

	return nil
}

// ContractRemit remits the contract
func ContractRemit(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()
	statusToReturn := http.StatusInternalServerError
	errorMessage := "Error remitting contract"

	contractID, err := strconv.Atoi(chi.URLParam(req, "id"))
	if err != nil {
		util.LogWarning(ctx, errors.Wrap(err, "error parsing contract id"))
		return http.StatusBadRequest, ErrorMessage("Invalid contract id", nil)
	}

	var payload struct {
		DealNumber string `json:"deal_number"`
	}
	dec := json.NewDecoder(req.Body)
	err = dec.Decode(&payload)
	if err != nil {
		return http.StatusBadRequest, ErrorMessage("Bad request", nil)
	}
	contract, err := RemitContract(ctx, contractID, payload.DealNumber, user, false, 0, time.Now().UTC(), true)
	if err != nil {
		if serr, ok := err.(*ContractCustomError); ok {
			err = serr.Err
			statusToReturn = serr.Status
			errorMessage = serr.Error()
		}
		if err != nil {
			ReportError(req, err)
		}
		return statusToReturn, ErrorMessage(errorMessage, nil)
	}
	return http.StatusOK, map[string]interface{}{"contract": contract.ContractWithData}
}

// ContractRemitAll remits the list of contract ids
func ContractRemitAll(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()
	statusToReturn := http.StatusInternalServerError
	errorMessage := "Error remitting contract"

	var payload struct {
		DealNumber  string `json:"deal_number"`
		ContractIDs []int  `json:"contract_ids"`
	}
	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&payload)
	if err != nil {
		return http.StatusBadRequest, ErrorMessage("Bad request", nil)
	}
	for _, contractID := range payload.ContractIDs {
		_, err := RemitContract(ctx, contractID, payload.DealNumber, user, false, 0, time.Now().UTC(), true)
		if err != nil {
			if serr, ok := err.(*ContractCustomError); ok {
				err = serr.Err
				statusToReturn = serr.Status
				errorMessage = serr.Error()
			}
			if err != nil {
				ReportError(req, err)
			}
			return statusToReturn, ErrorMessage(errorMessage, nil)
		}
	}
	return http.StatusOK, map[string]interface{}{}
}

// RemitContract function process remit steps which is called from multiple places
func RemitContract(ctx context.Context,
	contractID int, dealNumber string, user db.CurrentUser,
	isESaleContract bool, storeID int, signedDate time.Time,
	checkDms bool) (ContractType, error) {
	txn := newrelic.FromContext(ctx)

	if ok, err := canAccessContract(ctx, isESaleContract, contractID, storeID, user); !ok {
		return ContractType{}, err
	}

	contract, err := getContractForRemit(ctx, txn, contractID, storeID, user, isESaleContract)
	if err != nil {
		return contract, err
	}

	if checkDms && dealNumber != "" && contract.IsEContract {
		store := struct {
			db.Store
			LenderOptionName null.String `db:"lender_option_name"`
		}{}
		query := `select s.*, lo.name lender_option_name
			from stores s join companies c on s.company_id = c.id
				left join lender_options lo on lo.id = c.lender_option_id
				where s.id = $1`
		err = db.Get().Unsafe().GetContext(ctx, &store, query, contract.StoreID)
		if err != nil {
			return contract, errors.Wrapf(err, "error finding store for the %d store.", storeID)
		}
		if store.HasDealIntegration {
			_, err := dmsfactory.DealOnly(ctx, &store.Store, dealNumber)
			if err != nil {
				if nfErr, ok := err.(notFoundError); ok && nfErr.IsNotFound() {
					return contract, createCustomError(fmt.Sprintf("Deal Number %s not found in CDK", dealNumber), http.StatusNotFound,
						errors.Wrapf(err, "deal Number not found for %s deal", dealNumber), "")
				}
				if userErr, ok := err.(userError); ok {
					return contract, createCustomError(fmt.Sprintf("Deal Number %s not found in CDK", dealNumber), http.StatusInternalServerError,
						errors.Wrapf(err, "%s error for %s deal", userErr.UserErrorMessage(), dealNumber), "")
				}
				return contract, createCustomError(fmt.Sprintf("Error Lookup deal details for %s deal", dealNumber), http.StatusInternalServerError,
					errors.Wrapf(err, "deal Number not found for %s deal", dealNumber), "")
			}
		}
	}

	err = validateRemitContract(ctx, txn, contract, user)
	if err != nil {
		return contract, err
	}

	err = loadRemitContractDetails(ctx, txn, &contract)
	if err != nil {
		return contract, err
	}

	// We need to create transaction here becuase
	// we want to track and commit all inserts and updates if there are no error from anywhere
	sgmt := newrelic.StartSegment(txn, "begin db transaction")
	tx, err := db.Get().BeginTxx(ctx, nil)
	sgmt.End()
	if err != nil {
		return contract, createCustomError("Error remitting contract", http.StatusInternalServerError,
			errors.Wrapf(err, "error starting transaction for contract remit for contract %s", contract.Code), "")
	}

	err = processContractInspections(ctx, txn, tx, contract, user)
	if err != nil {
		_ = tx.Rollback()
		return contract, err
	}

	contract, err = updateRemitContract(ctx, txn, tx, contract, dealNumber, user)
	if err != nil {
		_ = tx.Rollback()
		return contract, err
	}

	// As per latest we are no longer using VTA overallowance but we still want to keep it for connect contracts
	var vtaOverAllowance vtaOverAllowanceDetails
	if !contract.IsEContract {
		vtaOverAllowance, contract, err = processVTAOverallowance(ctx, tx, contract, user)
		if err != nil {
			_ = tx.Rollback()
			return contract, err
		}
	}

	isToyotaEWU, contractCostEWU, err := processToyotaEWUAdjustment(ctx, tx, contract)
	if err != nil {
		_ = tx.Rollback()
		return contract, err
	}

	tran, err := getRemitalTransaction(tx, contract, isToyotaEWU, contractCostEWU, user)
	if err != nil {
		_ = tx.Rollback()
		return contract, err
	}

	err = db.CreateTransaction(ctx, tx, &tran)
	if err != nil {
		_ = tx.Rollback()
		return contract, createCustomError("Error remitting contract", http.StatusInternalServerError,
			errors.Wrapf(err, "could not create transaction for contract %s", contract.Code), "")
	}

	// Reload contract surcharges because they could have been changed during the remit.
	contract.Surcharges, err = db.GetContractSurcharges(ctx, tx, contract.ID)
	if err != nil {
		_ = tx.Rollback()
		return contract, createCustomError("Error remitting contract", http.StatusInternalServerError,
			errors.Wrapf(err, "failed to reload contract surcharges for %s", contract.Code), "")

	}

	breakouts := db.GetContractCostBreakouts(contract.ContractWithData, contract.CreatedByUserID, tran.ID)
	for _, bo := range breakouts {
		err = db.InsertTransactionBreakout(ctx, tx, bo)
		if err != nil {
			_ = tx.Rollback()
			return contract, createCustomError("Error remitting contract", http.StatusInternalServerError,
				errors.Wrapf(err, "could not create transaction breakout for contract %s", contract.Code), "")
		}
	}

	// Create SPP transaction_remittals entry
	// E-Contracting does not support SPP so skip the code execution
	if !contract.IsEContract {
		err = db.CreateSPPNewTransactionRemittal(tx, contract.Contract, tran)
		if err != nil {
			_ = tx.Rollback()
			return contract, createCustomError("Error remitting contract", http.StatusInternalServerError,
				errors.Wrapf(err, "could not create SPP transaction_remittal for contract %s", contract.Code), "")
		}
	}

	err = processThirdPartyRemittal(tx, contract, tran)
	if err != nil {
		_ = tx.Rollback()
		return contract, err
	}

	// Add transaction remittal for the VTA Overallowance
	// As per latest we are no longer using VTA overallowance but we still want to keep it for connect contracts
	if !isESaleContract {
		err = processVTAOverallowanceRemittal(ctx, tx, contract, tran, vtaOverAllowance)
		if err != nil {
			_ = tx.Rollback()
			return contract, err
		}
	}

	// Add transaction remittal for the GAP CUDL
	err = processGapCUDLRemittal(ctx, tx, contract, tran)
	if err != nil {
		_ = tx.Rollback()
		return contract, err
	}

	// Add RSA Remit amount
	err = processRSARemittal(ctx, tx, contract, tran)
	if err != nil {
		_ = tx.Rollback()
		return contract, err
	}

	err = createRemitContractEvents(ctx, tx, contract, user)
	if err != nil {
		_ = tx.Rollback()
		return contract, err
	}

	// Method to set the all flags which are related to contract at the time of remit.
	err = setContractFlags(&contract, user)
	if err != nil {
		_ = tx.Rollback()
		return contract, createCustomError("Error remitting contract", http.StatusInternalServerError,
			errors.Wrapf(err, "error inserting contract flags for contract %s", contract.Code), "")
	}

	if contract.IsEContract {
		if strings.EqualFold(contract.Lender, db.LenderSPP) || strings.EqualFold(contract.Lender, db.LenderSPPFull) {
			updateSPPQuery := `update sales set payment_type = $1 where id = $2`
			if _, err = tx.ExecContext(ctx, updateSPPQuery, dms.PaymentTypeSPP, contract.SaleID); err != nil {
				_ = tx.Rollback()
				return contract, createCustomError("Error remitting contract", http.StatusInternalServerError,
					errors.Wrapf(err, "error updating sales payment type for spp contract %s", contract.Code), "")
			}
			updateSPPContractQuery := `update contracts set payment_type = $1 where id = $2`
			if _, err = tx.ExecContext(ctx, updateSPPContractQuery, dms.PaymentTypeSPP, contract.ID); err != nil {
				_ = tx.Rollback()
				return contract, createCustomError(
					"Error remitting contract", http.StatusInternalServerError,
					errors.Wrapf(err,
						"error updating contracts payment type for spp contract %s",
						contract.Code), "")
			}
		}
		if dealNumber != "" { //e-sale contract can update deal number while remitting
			query := `update sales set dms_number = $1, dms_number_updated_at=now() at time zone 'utc' where id = $2`
			if _, err = tx.ExecContext(ctx, query, dealNumber, contract.SaleID); err != nil {
				_ = tx.Rollback()
				return contract, createCustomError("Error remitting contract", http.StatusInternalServerError,
					errors.Wrapf(err, "error updating sales dms_number contract %s", contract.Code), "")
			}
			// update other contracts linked to this sale
			query = `update contracts set dms_number = $1 where sale_id = $2 and id!=$3`
			if _, err = db.Get().ExecContext(ctx, query, dealNumber, contract.SaleID, contract.ID); err != nil {
				return contract, createCustomError("Error updating dms_number of contracts", http.StatusInternalServerError,
					errors.Wrapf(err, "error updating contract dms_number for saleID %d", contract.SaleID.Int64), "")
			}
		}
	}

	// Send to NSD. NSD logic will determine if the contract needs to be sent to NSD
	// Ignoring the first return parameter as this code doesn't need to know if NSD's api was called or not.
	_, err = nsd.SendContractToNSD(ctx, tx, &contract.ContractWithData, user.ID)
	if err != nil {
		_ = tx.Rollback()
		return contract, createCustomError("Error remitting contract", http.StatusInternalServerError,
			errors.WithMessage(err, "error sending contract to NSD"), "")
	}
	sgmt = newrelic.StartSegment(txn, "commit db transaction")
	err = tx.Commit()
	sgmt.End()
	if err != nil {
		return contract, createCustomError("Error remitting contract", http.StatusInternalServerError,
			errors.Wrapf(err, "error committing transaction for contract_remit for contract %s", contract.Code), "")
	}

	return contract, nil
}

// getContractForRemit function gets the contract which is currently being remitted
func getContractForRemit(ctx context.Context, txn newrelic.Transaction, contractID int, storeID int, user db.CurrentUser, isESaleContract bool) (ContractType, error) {
	var contract ContractType
	var err error
	if isESaleContract {
		query := `select c.*, vr.vin, s.lender_id, s.contract_date, s.inspection_liability
					from contracts c
					join sales s on c.sale_id = s.id
					join stores st on s.store_id = st.id
					join vin_records vr on s.vin_record_id = vr.id
					where c.id = $1 and c.status = $2 and st.id = $3 and c.is_e_contract = true`
		sgmt := newrelic.StartSegment(txn, "select contract")
		err = db.Get().Unsafe().GetContext(ctx, &contract, query, contractID, db.ContractStatusGenerated, storeID)
		sgmt.End()
	} else {
		query := `select c.*, case when c.is_e_contract then c.payment_type else s.payment_type end as payment_type, vr.vin, s.lender_id, s.contract_date,
				(cu.first_name || ' ' || cu.last_name) remitted_by_user_name, s.inspection_liability
				from contracts c
				join sales s on c.sale_id = s.id
				join stores_user_versions suv on s.store_id = suv.store_id
				join current_users cu on cu.user_version_id = suv.user_version_id
				join vin_records vr on s.vin_record_id = vr.id
				where c.id = $1 and c.status = $2 and suv.user_version_id = $3`
		sgmt := newrelic.StartSegment(txn, "select contract")
		err = db.Get().Unsafe().GetContext(ctx, &contract, query, contractID, db.ContractStatusGenerated, user.UserVersionID)
		sgmt.End()
	}
	if err != nil {
		if err == sql.ErrNoRows {
			return contract, createCustomError("Contract not found, please provide valid contract code", http.StatusNotFound,
				errors.Wrapf(err, "contract not found for contract id %d", contractID), db.ErrCodeContractNotFound)
		}
		return contract, createCustomError("Error finding contract", http.StatusInternalServerError,
			errors.Wrapf(err, "could not query contracts for contract id %d", contractID), "")
	}

	// Fill other details for remit contract information
	contract.UpdatedByUserID = user.ID
	if contract.PaymentType == dms.PaymentTypeSPP && !contract.IsEContract {
		contract.Status = db.ContractStatusPending
		contract.Event = db.ContractEventPending
	} else {
		contract.Status = db.ContractStatusActive
		contract.Event = db.ContractEventActivate
	}
	contract.EventNotes = ""
	contract.RemittedByUserID = null.IntFrom(int64(user.ID))
	if isESaleContract {
		contract.RemittedByUserName = fmt.Sprintf("%s %s", user.FirstName, user.LastName)
	}

	return contract, nil
}

// validateRemitContract function validates contract if its eligible for remit
func validateRemitContract(ctx context.Context, txn newrelic.Transaction, contract ContractType, user db.CurrentUser) error {
	if !contract.IsEContract {
		if (contract.ProductTypeCode != "MNT" || contract.SaleType != dms.SaleTypeServiceRO) && !user.HasRole(db.RoleRemit) {
			return createCustomError("Not authorized", http.StatusForbidden, nil, "")
		}

		if contract.PaymentType == dms.PaymentTypeSPP && contract.ProductTypeCode == "MNT" {
			query := `select count(*)
				from contracts
				where product_type_code = $1
				and sale_id = $2
				and status in ($3, $4, $5)`
			var count int
			sgmt := newrelic.StartSegment(txn, "select service contract when maintenance")
			err := db.Get().GetContext(ctx, &count, query, "VSC", contract.SaleID, db.ContractStatusRemitted, db.ContractStatusActive, db.ContractStatusPending)
			sgmt.End()
			if err != nil {
				return createCustomError("Error finding contract", http.StatusInternalServerError,
					errors.Wrapf(err, "error checking if Service contract is already remitted for SPP Maintenance for contract %s", contract.Code), "")
			}
			if count == 0 {
				return createCustomError("Need to remit Service contract for SPP before Maintenance contract", http.StatusBadRequest, nil, "")
			}
		}

		maxContractAgeDays := 75
		if user.HasRole(db.RoleAccountRepManager) || user.HasRole(db.RoleAccountRepII) {
			// Allow account rep manager to remit contracts that are up to 150 days old.
			maxContractAgeDays = 150
		}
		if contract.PaymentType == dms.PaymentTypeSPP && contract.ContractDate.Add(time.Hour*time.Duration(24*maxContractAgeDays)).Before(time.Now()) {
			return createCustomError(fmt.Sprintf("Cannot remit an SPP contract %d days after contract date", maxContractAgeDays), http.StatusBadRequest, errors.New("cannot remit an SPP contract"), "")
		}

		if contract.PaymentType != dms.PaymentTypeCash && contract.PaymentType != dms.PaymentTypeSPP && !contract.LenderID.Valid {
			return createCustomError("Need to set a lender before remitting", http.StatusBadRequest, errors.New("need to set a lender before remitting"), "")
		}

		if contract.PaymentType == dms.PaymentTypeSPP {
			var files []int
			query := `select at.id from contracts c
						join contract_attachments ca on c.id = ca.contract_id
						join attachment_types at on ca.attachment_type_id = at.id
					where code = $1
						and at.name in ($2, $3)
					group by at.id`

			err := db.Get().SelectContext(ctx, &files, query, contract.Code, db.AttachmentTypeSPPTCAContract, db.AttachmentTypeSPPRetailInstallmentContract)
			if err != nil && err != sql.ErrNoRows {
				return createCustomError("Error getting contract attachment", http.StatusInternalServerError,
					errors.Wrapf(err, "error getting contract attachment for contract %s", contract.Code), "")
			}

			if len(files) <= 1 {
				return createCustomError("The SPP contract requires attachments",
					http.StatusBadRequest, nil, "")
			}
		}
	}

	if contract.PaymentType != dms.PaymentTypeCash && contract.PaymentType != dms.PaymentTypeSPP && !contract.LenderID.Valid {
		return createCustomError("Need to set a lender before remitting", http.StatusInternalServerError, nil, db.ErrCodeInvalidLender)
	}
	return nil
}

// loadRemitContractDetails function loads the contract details from database
func loadRemitContractDetails(ctx context.Context, txn newrelic.Transaction, contract *ContractType) error {
	err := db.Get().Unsafe().GetContext(ctx, &contract.VINRecord, "select * from vin_records where id = $1", contract.VINRecordID)
	if err != nil {
		return createCustomError("Error remitting contract", http.StatusInternalServerError,
			errors.Wrapf(err, "error getting vin records for contract %s", contract.Code), "")
	}

	query := `select * from contract_adjustments where contract_id = $1`
	sgmt := newrelic.StartSegment(txn, "select contract adjustments")
	err = db.Get().Unsafe().SelectContext(ctx, &contract.Adjustments, query, contract.ID)
	sgmt.End()
	if err != nil {
		return createCustomError("Error remitting contract", http.StatusInternalServerError,
			errors.Wrapf(err, "error getting contract_adjustments for contract %s", contract.Code), "")
	}

	query = `select * from contract_options where contract_id = $1`
	sgmt = newrelic.StartSegment(txn, "select contract options")
	err = db.Get().Unsafe().SelectContext(ctx, &contract.Options, query, contract.ID)
	sgmt.End()
	if err != nil {
		return createCustomError("Error remitting contract", http.StatusInternalServerError,
			errors.Wrapf(err, "error getting contract_options for contract %s", contract.Code), "")
	}

	query = `select cs.*, s.is_cudl from contract_surcharges cs join surcharges s on cs.surcharge_id = s.id where cs.contract_id = $1`
	sgmt = newrelic.StartSegment(txn, "select contract surcharges")
	err = db.Get().Unsafe().SelectContext(ctx, &contract.Surcharges, query, contract.ID)
	sgmt.End()
	if err != nil {
		return createCustomError("Error remitting contract", http.StatusInternalServerError,
			errors.Wrapf(err, "error getting contract_surcharges for contract %s", contract.Code), "")
	}
	return nil
}

// processContractInspections function process the contract inspection before remitting
func processContractInspections(ctx context.Context, txn newrelic.Transaction, tx *sqlx.Tx, contract ContractType, user db.CurrentUser) error {
	var sie db.SaleInspectionEligibility
	var inspection db.Inspection
	query := `select * from inspections where vin = $1 and deactivated_at is null and expires_on >= $2::date`

	sgmt := newrelic.StartSegment(txn, "select active inspection")
	err := db.Get().Unsafe().GetContext(ctx, &inspection, query, contract.VIN, contract.EffectiveDate)
	sgmt.End()
	if err != nil {
		if err != sql.ErrNoRows {
			return createCustomError("Error remitting contract", http.StatusInternalServerError,
				errors.Wrapf(err, "error getting inspection for contract %s", contract.Code), "")
		}
	} else {
		sie.Valid = true
		sie.SaleID = int(contract.SaleID.Int64)
		sie.CreatedByUserID = user.ID
		sie.EligibilityLevel = inspection.EligibilityLevel
		sie.InspectionID = inspection.ID
		sie.IsCertified = inspection.IsCertified
	}

	if contract.ProductTypeCode == "VSC" {
		if sie.Valid {
			query = `insert into sale_inspection_eligibilities 
						(sale_id, created_at, created_by_user_id, eligibility_level, inspection_id, is_certified)
						values (:sale_id, now() at time zone 'utc', :created_by_user_id, :eligibility_level, :inspection_id, :is_certified)`
			sgmt = newrelic.StartSegment(txn, "prepare insert sale_inspection_eligibilities")
			sieStmt, err := tx.PrepareNamedContext(ctx, query)
			sgmt.End()
			if err != nil {
				return createCustomError("Error remitting contract", http.StatusInternalServerError,
					errors.Wrapf(err, "error preparing insert sale_inspection_eligibilities for contract %s", contract.Code), "")
			}
			defer sieStmt.Close()
			sgmt = newrelic.StartSegment(txn, "exec insert sale_inspection_eligibilities")
			_, err = sieStmt.ExecContext(ctx, sie)
			sgmt.End()
			if err != nil {
				return createCustomError("Error remitting contract", http.StatusInternalServerError,
					errors.Wrapf(err, "could not exec insert sale_inspection_eligibilities for contract %s", contract.Code), "")
			}
		}
	}
	return nil
}

const remitQuery = `update contracts
set version = version + 1,
updated_at = now() at time zone 'utc', updated_by_user_id = :updated_by_user_id,
status = :status, event = :event, event_notes = :event_notes,
remitted_at = now() at time zone 'utc',
activated_on = now() at time zone 'utc',
remitted_by_user_id = :remitted_by_user_id
%s
%s
where id = :id
returning updated_at, remitted_at`

const remitEConSPPPart = `,payment_type = :payment_type`
const remitEConDMSPart = `,dms_number = :dms_number`

// updateRemitContract function udpates the databse with remit info for the contract
func updateRemitContract(ctx context.Context, txn newrelic.Transaction,
	tx *sqlx.Tx, contract ContractType, dealNumber string, user db.CurrentUser) (ContractType, error) {
	sgmt := newrelic.StartSegment(txn, "insert contract_logs")
	err := db.CreateContractLog(tx, contract.ID, user.ID)
	sgmt.End()
	if err != nil {
		return contract, createCustomError("Error remitting contract", http.StatusInternalServerError,
			errors.Wrapf(err, "error contract_logs insert for contract %s", contract.Code), "")
	}

	query := remitQuery
	contract.DMSNumber = dealNumber
	if contract.PaymentType == dms.PaymentTypeSPP {
		query = fmt.Sprintf(query, remitEConSPPPart, "")
		if dealNumber != "" {
			query = fmt.Sprintf(query, remitEConSPPPart, remitEConDMSPart)
		}
	} else {
		if dealNumber != "" {
			query = fmt.Sprintf(query, remitEConDMSPart, "")
		}
	}
	if query == remitQuery {
		query = fmt.Sprintf(query, "", "")
	}

	sgmt = newrelic.StartSegment(txn, "prepare update contracts")
	contractStmt, err := tx.PrepareNamedContext(ctx, query)
	sgmt.End()
	if err != nil {
		return contract, createCustomError("Error remitting contract", http.StatusInternalServerError,
			errors.Wrapf(err, "error preparing contracts update for contract %s", contract.Code), "")
	}

	sgmt = newrelic.StartSegment(txn, "execute update contracts")
	err = contractStmt.GetContext(ctx, &contract, contract)
	sgmt.End()
	if err != nil {
		return contract, createCustomError("Error remitting contract", http.StatusInternalServerError,
			errors.Wrapf(err, "error executing contracts update for contract %s", contract.Code), "")
	}
	return contract, nil
}

// processVTAOverallowance function finds if vta contract has any overallowance on it
func processVTAOverallowance(ctx context.Context, tx *sqlx.Tx, contract ContractType, user db.CurrentUser) (vtaOverAllowanceDetails, ContractType, error) {
	vtaOverallowance := vtaOverAllowanceDetails{
		IsVTAOverallowance:           false,
		VTAOverallowanceAmount:       decimal.Zero,
		VTAOverallowanceRateBucketID: int64(0),
	}
	isLHMStore, err := isLHMStore(contract.ID)
	if err != nil {
		return vtaOverallowance, contract, createCustomError("Error remitting contract", http.StatusInternalServerError,
			errors.Wrapf(err, "error getting store for contract %s", contract.Code), "")
	}

	// For now we are applying VTA Allowance to VTA so we will check if contract is of VTA type.
	if isLHMStore && contract.ProductTypeCode == db.ProductTypeCodeVehicleTheftAssistance {
		// We will flip this flag so we can use it again
		vtaOverallowance.IsVTAOverallowance = false

		query := `select add_vta_overallownce, id from lenders where id = $1 and deleted_at is null`
		var lenderDetails struct {
			LenderID         int  `db:"id"`
			VTAOverAllowance bool `db:"add_vta_overallownce"`
		}
		err = tx.GetContext(ctx, &lenderDetails, query, contract.LenderID.Int64)
		if err != nil && err != sql.ErrNoRows {
			return vtaOverallowance, contract, createCustomError("Error remitting contract", http.StatusInternalServerError,
				errors.Wrapf(err, "error getting lender detail for contract %s", contract.Code), "")
		}

		if lenderDetails.VTAOverAllowance {
			var customer db.Customer
			err = tx.Unsafe().GetContext(ctx, &customer, `select * from customers where id = $1`, contract.CustomerID)
			if err != nil {
				return vtaOverallowance, contract, createCustomError("Error remitting contract", http.StatusInternalServerError,
					errors.Wrapf(err, "error fetching customer details for contract %s", contract.Code), "")
			}

			var sale db.Sale
			err = tx.Unsafe().GetContext(ctx, &sale, `select * from sales where id = $1`, contract.SaleID)
			if err != nil {
				return vtaOverallowance, contract, createCustomError("Error remitting contract", http.StatusInternalServerError,
					errors.Wrapf(err, "error fetching sale details for contract %s", contract.Code), "")
			}

			var classification = &db.Classification{}
			err = tx.Unsafe().GetContext(ctx, classification, `select * from classifications where id = $1`, contract.ClassificationID)
			if err != nil {
				if err != sql.ErrNoRows {
					return vtaOverallowance, contract, createCustomError("Error remitting contract", http.StatusInternalServerError,
						errors.Wrapf(err, "error fetching classification details for contract %s", contract.Code), "")
				}
				classification = nil
			}

			rateQuery, err := db.RateQueryData(contract.CreatedByUserID, contract.Contract, sale, customer)
			if err != nil {
				return vtaOverallowance, contract, createCustomError("Error remitting contract", http.StatusInternalServerError,
					errors.Wrapf(err, "error fetching rate query for contract %s", contract.Code), "")
			}

			var rateSheetID int
			err = tx.GetContext(ctx, &rateSheetID, `select p.rate_sheet_id from plans p
				join contracts c on c.plan_id = p.id
				where c.id = $1`, contract.ID)
			if err != nil {
				return vtaOverallowance, contract, createCustomError("Error remitting contract", http.StatusInternalServerError,
					errors.Wrapf(err, "error getting ratesheet ID for contract %s", contract.Code), "")
			}

			surcharges, err := db.FindSurcharges(rateSheetID, rateQuery, classification)
			if err != nil {
				return vtaOverallowance, contract, createCustomError("Error remitting contract", http.StatusInternalServerError,
					errors.Wrapf(err, "error getting surcharges for contract %s", contract.Code), "")
			}

			for _, surcharge := range surcharges {
				var rateBucketName string
				err = tx.GetContext(ctx, &rateBucketName, `select name from rate_buckets where id = $1`, surcharge.RateBucketID)
				if err != nil {
					if err == sql.ErrNoRows {
						continue
					}
					return vtaOverallowance, contract, createCustomError("Error remitting contract", http.StatusInternalServerError,
						errors.Wrapf(err, "error fetching rate bucket details for contract %s", contract.Code), "")
				}

				if rateBucketName == "VTA Overallowance" {
					var vtaSurcharge db.ContractSurcharge
					overallowanceAlreadyAdded := true
					err = db.Get().Unsafe().GetContext(ctx, &vtaSurcharge, `select * from contract_surcharges where surcharge_id = $1 and contract_id = $2`, surcharge.ID, contract.ID)
					if err != nil {
						if err != sql.ErrNoRows {
							return vtaOverallowance, contract, createCustomError("Error checking for existance of VTA Overallowance surcharge", http.StatusInternalServerError,
								errors.Wrapf(err, "error checking for existance of vta overallowance surcharge for contract %s", contract.Code), "")
						}

						overallowanceAlreadyAdded = false
					}

					if !overallowanceAlreadyAdded {
						// Add a record in contract_surcharges table
						addQ := `insert into contract_surcharges (created_at, contract_id, name, code, cost, surcharge_id, rate_bucket_id) 
								values(now() at time zone 'utc', $1, $2, $3, $4, $5, $6)`
						addQ, args, err := sqlx.In(addQ, contract.ID, surcharge.Name, surcharge.Code, surcharge.Rateable.Cost, surcharge.ID, surcharge.RateBucketID.Int64)
						if err != nil {
							return vtaOverallowance, contract, createCustomError("Error remitting contract", http.StatusInternalServerError,
								errors.Wrapf(err, "could not add surcharges for contract %s", contract.Code), "")
						}

						addQ = tx.Rebind(addQ)
						_, err = tx.ExecContext(ctx, addQ, args...)
						if err != nil {
							return vtaOverallowance, contract, createCustomError("Error remitting contract", http.StatusInternalServerError,
								errors.Wrapf(err, "could not add surcharges for contract %s", contract.Code), "")
						}
					}
					vtaOverallowance.VTAOverallowanceAmount = surcharge.Rateable.Cost
					vtaOverallowance.VTAOverallowanceRateBucketID = surcharge.RateBucketID.Int64
					vtaOverallowance.IsVTAOverallowance = true
				}
			}
		} else {
			// The current vendor does not allow for VTA Overallowance, so we
			// need to see if the VTA Overallowance surcharge was previously
			// added, and if so, remove it.
			for i, s := range contract.Surcharges {
				if s.RateBucketID == db.RateBucketVtaOverallowanceID {
					_, err = tx.ExecContext(ctx, `delete from contract_surcharges where id = $1`, s.ID)
					if err != nil {
						return vtaOverallowance, contract, createCustomError("Error remitting contract", http.StatusInternalServerError,
							errors.Wrapf(err, "could not remove vta overallowance surcharge for contract %s", contract.Code), "")
					}

					// Remove the surcharge from the current slice. We do this by
					// shifting the last item to the current index and set that last
					// item to a nil value then truncate the slice by removing the
					// last element. This is done to help ensure that any pointer
					// references that might be in the stuct will properly get
					// unreferenced so it can be garbage collected and not cause
					// a potential memory leak.
					if i < len(contract.Surcharges)-1 {
						copy(contract.Surcharges[i:], contract.Surcharges[i+1:])
					}
					contract.Surcharges[len(contract.Surcharges)-1] = db.ContractSurcharge{}
					contract.Surcharges = contract.Surcharges[:len(contract.Surcharges)-1]

					// We no longer need to process the contract's surcharges.
					break
				}
			}
		}
	}
	return vtaOverallowance, contract, nil
}

// processToyotaEWUAdjustment function finds if there is EWU adjustment if we need to include that cost
func processToyotaEWUAdjustment(ctx context.Context, tx *sqlx.Tx, contract ContractType) (bool, decimal.Decimal, error) {
	var isToyotaEWU int
	var contractCostEWU decimal.Decimal

	query := `select count(1) from contracts c 
				join product_variants pv on c.product_variant_id = pv.id 
				join products p on pv.product_id = p.id 
				join product_types pt on p.product_type_id = pt.id
			where pt.code = 'LWT'
				and (p.name ilike '%Toyota%' or p.name ilike '%Lexus%')
				and c.id = $1;`

	err := tx.GetContext(ctx, &isToyotaEWU, query, contract.ID)
	if err != nil && err != sql.ErrNoRows {
		return false, contractCostEWU, createCustomError("Error remitting contract", http.StatusInternalServerError,
			errors.Wrapf(err, "error getting product details for contract %s", contract.Code), "")
	}

	if isToyotaEWU > 0 {
		var rateSheetID int
		err = tx.GetContext(ctx, &rateSheetID, `select p.rate_sheet_id from plans p
			join contracts c on c.plan_id = p.id where c.id = $1`, contract.ID)
		if err != nil {
			return false, contractCostEWU, createCustomError("Error remitting contract", http.StatusInternalServerError,
				errors.Wrapf(err, "error getting ratesheet ID for contract %s", contract.Code), "")
		}

		var sale db.Sale
		// TODO: This is temprory fix for the TS-7842 need to change this after right fix
		getSaleQuery := `select * from sales where id = $1`
		if contract.IsEContract {
			getSaleQuery = `select s.*, es.finance_amount
				from sales s
				left join e_sales es on s.id = es.sale_id
				where s.id = $1`
		}

		err = tx.Unsafe().GetContext(ctx, &sale, getSaleQuery, contract.SaleID)
		if err != nil {
			return false, contractCostEWU, createCustomError("Error remitting contract", http.StatusInternalServerError,
				errors.Wrapf(err, "error getting sale details for contract %s", contract.Code), "")
		}

		var customer db.Customer
		err = tx.Unsafe().GetContext(ctx, &customer, `select * from customers where id = $1`, contract.CustomerID)
		if err != nil {
			return false, contractCostEWU, createCustomError("Error remitting contract", http.StatusInternalServerError,
				errors.Wrapf(err, "error getting customer details for contract %s", contract.Code), "")
		}

		rateQuery, err := db.RateQueryData(contract.CreatedByUserID, contract.Contract, sale, customer)
		if err != nil {
			return false, contractCostEWU, createCustomError("Error remitting contract", http.StatusInternalServerError,
				errors.Wrapf(err, "error creating a rate query for contract %s", contract.Code), "")
		}

		adjustments, err := db.FindAdjustments(rateSheetID, rateQuery)
		if err != nil {
			return false, contractCostEWU, createCustomError("Error remitting contract", http.StatusInternalServerError,
				errors.Wrapf(err, "error finding adjustments for contract %s", contract.Code), "")
		}

		for _, v := range adjustments {
			var rateBucketLabel string
			err = tx.GetContext(ctx, &rateBucketLabel, `select label from rate_buckets where id = $1`, v.RateBucketID)
			if err != nil {
				if err == sql.ErrNoRows {
					continue
				}
				return false, contractCostEWU, createCustomError("Error remitting contract", http.StatusInternalServerError,
					errors.Wrapf(err, "error fetching rate bucket details for contract %s", contract.Code), "")
			}
			if rateBucketLabel == "Admin" || rateBucketLabel == "SPIFF" {
				contractCostEWU = contractCostEWU.Add(v.Amount)
			}
		}
	}
	return isToyotaEWU > 0, contractCostEWU, nil
}

// getRemitalTransaction function genrates the struct for the remittal transaction
func getRemitalTransaction(tx *sqlx.Tx, contract ContractType, isToyotaEWU bool, contractCostEWU decimal.Decimal, user db.CurrentUser) (db.Transaction, error) {
	var err error
	tran := db.Transaction{}
	tran.ContractID = contract.ID
	tran.CreatedByUserID = user.ID
	tran.StoreID = contract.StoreID
	tran.VehicleYear = contract.VINRecord.Year
	tran.VehicleMake = contract.VINRecord.Make
	tran.VehicleModel = contract.VINRecord.Model
	tran.TransactionType = db.TranTypeNewContract
	tran.TransactionSubtype = null.String{}
	tran.ContractPrice = contract.Price

	if isToyotaEWU {
		tran.Amount = contractCostEWU
		return tran, nil
	}

	tran.Amount, err = GetContractCost(tx, contract.ID)
	if err != nil {
		return tran, createCustomError("Error remitting contract", http.StatusInternalServerError,
			errors.Wrapf(err, "could not create transaction for contract %s", contract.Code), "")
	}
	return tran, nil
}

// processThirdPartyRemittal function checks if contract has third party provider then it creates third party remittal
func processThirdPartyRemittal(tx *sqlx.Tx, contract ContractType, tran db.Transaction) error {
	remit, err := db.GetContractThirdPartyAmt(tx, contract.ID)
	if err != nil {
		return createCustomError("Error remitting contract", http.StatusInternalServerError,
			errors.Wrapf(err, "error getting 3rd-party amount for contract %s", contract.Code), "")
	}

	provider, err := db.GetContractProvider(tx, contract.ID)
	if err != nil {
		return createCustomError("Error remitting contract", http.StatusInternalServerError,
			errors.Wrapf(err, "error getting provider for contract %s", contract.Code), "")
	}

	if provider.Name != "TCA" {
		var tr db.TransactionRemittal
		tr.TransactionID = tran.ID
		tr.Amount = remit
		tr.ProviderID = provider.ID
		err = db.CreateTransactionRemittal(tx, contract.Contract, &tr)
		if err != nil {
			return createCustomError("Error remitting contract", http.StatusInternalServerError,
				errors.Wrapf(err, "could not create transaction remittal for contract %s", contract.Code), "")
		}
	}
	return nil
}

// processVTAOverallowanceRemittal function creates remittal transaction for vta overallowance if there is any
func processVTAOverallowanceRemittal(ctx context.Context, tx *sqlx.Tx, contract ContractType, tran db.Transaction, vtaOverAllowance vtaOverAllowanceDetails) error {
	if vtaOverAllowance.IsVTAOverallowance {
		query := `select provider_id from rate_buckets where id = $1 and provider_id is not null`
		var tr db.TransactionRemittal
		tr.TransactionID = tran.ID
		tr.Amount = vtaOverAllowance.VTAOverallowanceAmount
		tr.Type = db.TransactionRemittalVTAOverallowance

		err := tx.GetContext(ctx, &tr.ProviderID, query, vtaOverAllowance.VTAOverallowanceRateBucketID)
		if err != nil {
			return createCustomError("Error remitting contract", http.StatusInternalServerError,
				errors.Wrapf(err, "could not get provider for rate bucket %d", vtaOverAllowance.VTAOverallowanceRateBucketID), "")
		}

		err = db.CreateTransactionRemittal(tx, contract.Contract, &tr)
		if err != nil {
			return createCustomError("Error remitting contract", http.StatusInternalServerError,
				errors.Wrapf(err, "could not create transaction remittal for contract %s", contract.Code), "")
		}
	}
	return nil
}

// processGapCUDLRemittal function creates remittal transaction for gap cudl if there is any
func processGapCUDLRemittal(ctx context.Context, tx *sqlx.Tx, contract ContractType, tran db.Transaction) error {
	var isGapCUDLApplicable bool
	query := `select count(1) from product_types where code in ('GAP') and id = $1 `

	err := tx.GetContext(ctx, &isGapCUDLApplicable, query, contract.ProductTypeID.Int64)
	if err != nil && err != sql.ErrNoRows {
		return createCustomError("Error remitting contract", http.StatusInternalServerError,
			errors.Wrapf(err, "error getting product type for contract %s", contract.Code), "")
	}

	if isGapCUDLApplicable && len(contract.Surcharges) > 0 {
		for _, v := range contract.Surcharges {
			if v.IsCUDL.Valid && v.IsCUDL.Bool {
				var tr db.TransactionRemittal
				tr.TransactionID = tran.ID
				tr.Amount = v.Cost
				tr.Type = db.TransactionRemittalGAPCUDLSurcharge

				err = tx.GetContext(ctx, &tr.ProviderID, "select provider_id from rate_buckets where id = $1", v.RateBucketID)
				if err != nil {
					return createCustomError("Error remitting contract", http.StatusInternalServerError,
						errors.Wrapf(err, "could not get provider for rate bucket %d", v.RateBucketID), "")
				}

				err = db.CreateTransactionRemittal(tx, contract.Contract, &tr)
				if err != nil {
					return createCustomError("Error remitting contract", http.StatusInternalServerError,
						errors.Wrapf(err, "could not create transaction remittal for contract %s", contract.Code), "")
				}
				break
			}
		}
	}
	return nil
}

// processRSARemittal function creates remittal transaction for RSA if there is any
func processRSARemittal(ctx context.Context, tx *sqlx.Tx, contract ContractType, tran db.Transaction) error {
	var providerID int
	query := `select id from providers where name = $1`
	err := tx.GetContext(ctx, &providerID, query, db.ContractProviderRSA)
	if err != nil {
		return createCustomError("Error remitting contract", http.StatusInternalServerError,
			errors.Wrapf(err, "could not get RSA provider for contract %s", contract.Code), "")
	}

	rsaCost, err := db.GetContractRSAAmt(tx, contract.ID)
	if err != nil {
		return createCustomError("Error remitting contract", http.StatusInternalServerError,
			errors.Wrapf(err, "could not sum RSA costs for contract %s", contract.Code), "")
	}

	if rsaCost.GreaterThan(decimal.Zero) {
		var tr db.TransactionRemittal
		tr.TransactionID = tran.ID
		tr.ProviderID = providerID
		tr.Amount = rsaCost
		err = db.CreateTransactionRemittal(tx, contract.Contract, &tr)
		if err != nil {
			return createCustomError("Error remitting contract", http.StatusInternalServerError,
				errors.Wrapf(err, "could not insert RSA remittal for contract %s", contract.Code), "")
		}
	}
	return nil
}

// createRemitContractEvents function creates the contract event for remit event
func createRemitContractEvents(ctx context.Context, tx *sqlx.Tx, contract ContractType, user db.CurrentUser) error {
	query := `insert into contract_events(created_at,created_by_user_id,created_by_name,contract_id,description)
	values(now() at time zone 'utc', $1, $2, $3, $4)`
	_, err := tx.ExecContext(ctx, query, user.ID, contract.RemittedByUserName, contract.ID, contract.Event)
	if err != nil {
		return createCustomError("Error remitting contract", http.StatusInternalServerError,
			errors.Wrapf(err, "error inserting contract event for contract %s", contract.Code), "")
	}
	return nil
}

func isLHMStore(contractID int) (bool, error) {
	var isLHMStore bool
	query := `select count(1) from contracts where id = $1
			and store_id in
				(select id from stores where company_id =
					(select id from companies where code = 'LHM')
				)`
	err := db.Get().Get(&isLHMStore, query, contractID)
	if err != nil {
		err = errors.Wrapf(err, "error getting store for contract")
		return false, err
	}
	return isLHMStore, nil
}

// ContractCDKDDJSend remits the contract
func ContractCDKDDJSend(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	contractID, err := strconv.Atoi(chi.URLParam(req, "id"))
	if err != nil {
		ReportError(req, errors.Wrapf(err, "failed to parse contract id %s to int", chi.URLParam(req, "id")))
		return http.StatusBadRequest, ErrorMessage("Invalid contract ID", nil)
	}

	allowed, err := user.CanAccessContract(contractID)
	if err != nil {
		ReportError(req, errors.Wrap(err, "failed to check user authorization"))
		return http.StatusInternalServerError, ErrorMessage("Authorization Error", nil)
	}
	if !allowed {
		return http.StatusUnauthorized, ErrorMessage("Not authorized for this action", nil)
	}

	var contract db.Contract
	query := `select c.* from contracts c
		join sales s on c.sale_id = s.id
		join stores_user_versions suv on s.store_id = suv.store_id
		join current_users cu on cu.user_version_id = suv.user_version_id
		where c.id = $1 and c.status = $2 and suv.user_version_id = $3`
	err = db.Get().Unsafe().Get(&contract, query, contractID, db.ContractStatusGenerated, user.UserVersionID)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, ErrorMessage("Contract not found", nil)
		}
		err = errors.Wrap(err, "could not query contracts")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error finding contract", nil)
	}

	var storeDealerID string
	query = `select st.dms_provider_parameters -> 'dealer_id' from sales s
		join stores st on s.store_id = st.id
		where s.id = $1 and st.dms_provider = $2
		and (st.dms_provider_parameters -> 'ddj') = '1'`
	err = db.Get().Get(&storeDealerID, query, contract.SaleID, db.DMSProviderCDK)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, ErrorMessage("Store does not send to CDK DDJ", nil)
		}
		err = errors.Wrap(err, "could not query stores")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error finding store", nil)
	}

	err = cdkddj.Send(req.Context(), storeDealerID, contract)
	if err != nil {
		err = errors.Wrap(err, "could not send contract to CDK DDJ")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error sending contract to CDK DDJ:"+err.Error(), nil)
	}

	return http.StatusOK, map[string]interface{}{}
}

type contractList []unidata.Contract

func (a contractList) Len() int           { return len(a) }
func (a contractList) Swap(i, j int)      { a[i], a[j] = a[j], a[i] }
func (a contractList) Less(i, j int) bool { return a[i].CustomerName < a[j].CustomerName }

type sorterFunc func(p1, p2 *unidata.Contract) bool

type contractListSorter struct {
	contracts contractList
	sortBy    func(p1, p2 *unidata.Contract) bool // Closure used in the Less method.
}

func (sortBy sorterFunc) sort(contracts contractList, orderBy string) {
	ps := &contractListSorter{
		contracts: contracts,
		sortBy:    sortBy, // The sort method's receiver is the function (closure) that defines the sort order.
	}
	if orderBy == "desc" {
		sort.Sort(sort.Reverse(ps))
	} else {
		sort.Sort(ps)
	}
}

// Len is part of sort.Interface.
func (s *contractListSorter) Len() int {
	return len(s.contracts)
}

// Swap is part of sort.Interface.
func (s *contractListSorter) Swap(i, j int) {
	s.contracts[i], s.contracts[j] = s.contracts[j], s.contracts[i]
}

// Less is part of sort.Interface. It is implemented by calling the "sorterFunc" closure in the sorter.
func (s *contractListSorter) Less(i, j int) bool {
	return s.sortBy(&s.contracts[i], &s.contracts[j])
}

// ContractIndex looks up all contracts for a given VIN or Name or Contract ID
func ContractIndex(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {

	storeID := req.FormValue("store_id")
	if storeID == "" {
		return http.StatusBadRequest, ErrorMessage("storeID is missing", nil)
	}
	return ContractIndexCommon(w, req, user)
}

// ContractIndexExt wrapper function for ContractIndexCommon to be used in ext for phizz calling
func ContractIndexExt(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	return ContractIndexCommon(w, req, db.CurrentUser{})
}

// ContractIndexCommon ContractIndex for handlers and adminhandlers
func ContractIndexCommon(_ http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()
	searchQ := req.FormValue("search")
	searchQ = strings.ToUpper(searchQ)
	searchQ, err := url.QueryUnescape(searchQ)
	if err != nil {
		err = errors.Wrap(err, "error in decoding input for contract search")
		ReportError(req, err)
		return http.StatusBadRequest, ErrorMessage("Error finding contracts", nil)
	}
	// need this check to flag message if contract is voided/generated
	// the message should be displayed if given contract number is found but has status voided / generated
	id := 0
	err = db.Get().GetContext(ctx, &id, "select id from contracts where (code = $1 or original_code = $1) and status in ($2,$3) ", searchQ, db.ContractStatusGenerated, db.ContractStatusVoided)
	if err != nil && err != sql.ErrNoRows {
		err = errors.Wrap(err, "error in checking if contract is voided or generated")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error finding contracts", nil)
	}
	voidedOrGenerated := false
	if id != 0 {
		voidedOrGenerated = true
	}

	// The Regular Expression should allow for the start of the search
	// expression to be a number instead of just a letter because the first
	// 2 characters of a the last 8 of a VIN can be a number, not a letter.
	var contractCodeOrVIN = regexp.MustCompile(`[A-Z\d][0-9]+`)

	selectClause := `c.*, c.product_type_code product_code, c.dms_number deal_number,
	case when cc.is_business and (cc.first_name!='' or cc.last_name!='') then cc.last_name || ',' || cc.first_name || '/' || cc.business_name
	when cc.is_business and (cc.first_name='' and cc.last_name='' and ccb.first_name!='') then cc.business_name || '/' || ccb.last_name || ',' || ccb.first_name
	when cc.is_business and cc.first_name='' and cc.last_name='' then cc.business_name
	when cc.is_business=false and ccb.first_name!='' then cc.last_name || ',' || cc.first_name || '/' || ccb.last_name || ',' || ccb.first_name
	else cc.last_name || ',' || cc.first_name end customer_name,
	c.product_name product_name,
    v.vin, v.year vehicle_year, v.make vehicle_make, v.model vehicle_model, v.trim vehicle_trim,
    st.code store_code, st.store_number, s.sale_type, rb.name rate_bucket_name, cg.name company_group`

	fromClause := `contracts c
	join stores st on c.store_id = st.id
	join companies co on co.id = st.company_id
	join customers cc on c.customer_id = cc.id
	join vin_records v on c.vin_record_id = v.id
	join rate_buckets rb on rb.id = c.core_rate_bucket_id
	left join company_groups cg on cg.id = co.company_group_id
	left join sales s on s.id = c.sale_id
	left join contract_cobuyers ccb on s.contract_cobuyer_id = ccb.id
	left join product_types pt on pt.id = c.product_type_id`

	var whereClause string
	args := []interface{}{}

	if strings.Contains(req.URL.Path, "/admin/") && user.HasRole(db.RoleProductManager) {
		whereClause = " true "
		// for admin search with PM role the voided or generated contract will be shown in search hence no warning needed
		voidedOrGenerated = false
	} else {
		whereClause = " c.status not in (?, ?) "
		args = append(args, db.ContractStatusGenerated, db.ContractStatusVoided)
	}
	if searchQ != "" {
		if contractCodeOrVIN.MatchString(searchQ) { // ContractID or VIN
			// get vin for given contractID or VIN and then get contracts for given VIN
			// using pattern match we can probably detect if it's contractID or VIN, but it's not foolproof
			var vinRecordID []string
			err := db.Get().SelectContext(ctx, &vinRecordID, `select distinct vin_record_id from contracts where code = $1 or original_code = $1`, searchQ)
			if err != nil {
				err = errors.Wrap(err, "error in getting vin record for find contracts")
				ReportError(req, err)
				return http.StatusInternalServerError, ErrorMessage("Error finding contracts", nil)
			}

			var vin []string
			if len(vinRecordID) != 0 {
				vinQuery, vinArgs, err := sqlx.In(`select vin from vin_records where id in (?)`, vinRecordID)
				if err != nil {
					ReportError(req, errors.Wrap(err, "error preparing IN select for find vin"))
					return http.StatusInternalServerError, ErrorMessage("Error finding contracts", nil)
				}
				vinQuery = db.Get().Rebind(vinQuery)
				err = db.Get().SelectContext(ctx, &vin, vinQuery, vinArgs...)
				if err != nil {
					err = errors.Wrap(err, "error in getting vin for find contracts")
					ReportError(req, err)
					return http.StatusInternalServerError, ErrorMessage("Error finding contracts", nil)
				}
			}
			if len(vinRecordID) == 0 {
				// given string did not match as contractID, so try that as vin
				whereClause = whereClause + " and v.vin ilike ? "
				args = append(args, "%"+searchQ+"%")
			} else {
				whereClause = whereClause + " and v.vin in ( "
				for _, v := range vin {
					whereClause += "?,"
					args = append(args, v)
				}
				whereClause = strings.TrimSuffix(whereClause, ",")
				whereClause += ")"
			}
		} else { // customer name
			whereClause = whereClause + `and (concat(cc.first_name, ' ', cc.last_name) ilike ?
			or concat(cc.last_name, ' ', cc.first_name) ilike ? or cc.business_name ilike ?)`
			pattern := "%" + strings.Join(strings.Fields(strings.TrimSpace(searchQ)), " ") + "%"
			args = append(args, pattern, pattern, pattern)
		}
	}

	storeID := req.FormValue("store_id")
	if storeID != "" {
		var companyID int
		err = db.Get().GetContext(ctx, &companyID, "select company_id from stores where id = $1", storeID)
		if err != nil {
			if err == sql.ErrNoRows {
				return http.StatusBadRequest, ErrorMessage("Given store is invalid", nil)
			}
			err = errors.Wrap(err, "error getting company from given store_id")
			ReportError(req, err)
			return http.StatusInternalServerError, ErrorMessage("Error finding contracts", nil)
		}
		companyIDs := []int{}
		err = db.Get().SelectContext(ctx, &companyIDs, "select readable_company_id from company_readable_companies where company_id = $1", companyID)
		if err != nil {
			ReportError(req, errors.Wrap(err, "error getting readable companies from given store_id"))
			return http.StatusInternalServerError, ErrorMessage("Error finding contracts", nil)
		}

		storeIDs := []int{}
		err = db.Get().SelectContext(ctx, &storeIDs, "select readable_store_id from company_readable_stores where company_id = $1", companyID)
		if err != nil {
			ReportError(req, errors.Wrap(err, "error getting readable company stores from given store_id"))
			return http.StatusInternalServerError, ErrorMessage("Error finding contracts", nil)
		}
		var storeIDn int
		storeIDn, err = strconv.Atoi(storeID)
		if err != nil {
			ReportError(req, errors.Wrap(err, "error converting store_id to number"))
			return http.StatusInternalServerError, ErrorMessage("Error finding contracts", nil)
		}

		if len(storeIDs) > 0 {
			whereClause = whereClause + " and st.id in (?)"
			args = append(args, append([]int{storeIDn}, storeIDs...))
		} else {
			whereClause = whereClause + " and st.company_id in (?)"
			args = append(args, append([]int{companyID}, companyIDs...))
		}

	}

	inputSort := req.FormValue("sort_by")
	sortBy := ""
	switch inputSort {
	case "status":
		sortBy = "status"
	case "name":
		sortBy = "customer_name"
	case "contract":
		sortBy = "code, original_code"
	case "store":
		sortBy = "store_code"
	case "date":
		sortBy = "effective_date"
	}

	sortOrder := ""
	if sortBy == "" {
		sortBy = "customer_name, v.vin, pt.position"
		sortOrder = "asc"
	} else {
		sortOrder = req.FormValue("sort_order")
	}

	listQuery, countQuery := ListQueries(
		selectClause,
		fromClause,
		" where "+whereClause,
		" order by "+sortBy+" "+sortOrder,
		GetPageSize(req, 20),
		GetPage(req),
	)
	type warrantyInfo struct {
		BasicWarrantyMiles       int `json:"basic_warranty_miles" db:"basic_warranty_miles"`
		BasicWarrantyMonths      int `json:"basic_warranty_months" db:"basic_warranty_months"`
		DrivetrainWarrantyMiles  int `json:"drivetrain_warranty_miles" db:"drivetrain_warranty_miles"`
		DrivetrainWarrantyMonths int `json:"drivetrain_warranty_months" db:"drivetrain_warranty_months"`
	}

	var contracts []struct {
		ID                 int                 `json:"id" db:"id"`
		ProductCode        string              `json:"product_code" db:"product_code"`
		Code               string              `json:"code" db:"code"`
		Status             string              `json:"status" db:"status"`
		EffectiveDate      null.Time           `json:"effective_date" db:"effective_date"`
		ExpirationMileage  null.Int            `json:"expiration_mileage" db:"expiration_mileage"`
		PlanName           string              `json:"plan_name" db:"plan_name"`
		CustomerName       string              `json:"customer_name" db:"customer_name"`
		VIN                string              `json:"vin" db:"vin"`
		DealNumber         string              `json:"deal_number" db:"deal_number"`
		VehicleYear        string              `json:"vehicle_year" db:"vehicle_year"`
		VehicleMake        string              `json:"vehicle_make" db:"vehicle_make"`
		VehicleModel       string              `json:"vehicle_model" db:"vehicle_model"`
		StoreCode          string              `json:"store_code" db:"store_code"`
		CompanyGroup       null.String         `json:"company_group" db:"company_group"`
		SaleType           null.String         `json:"-" db:"sale_type"`
		Source             string              `json:"source" db:"source"`
		EditWithRole       []string            `json:"edit_with_roles" db:"-"`
		ProductName        string              `json:"product_name" db:"product_name"`
		ProductVariantName string              `json:"product_variant_name" db:"product_variant_name"`
		RateBucketName     string              `json:"rate_bucket_name" db:"rate_bucket_name"`
		OriginalCode       string              `json:"original_code" db:"original_code"`
		RemitAmount        string              `json:"remit_amount" db:"-"`
		UpdatedAt          time.Time           `json:"updated_at" db:"updated_at"`
		Options            []db.ContractOption `json:"options" db:"-"`
		Warranty           warrantyInfo        `json:"warranty" db:"_"`
		StoreNumber        string              `json:"store_number" db:"store_number"`
	}
	count := 0

	var listArgs []interface{}
	listQuery, listArgs, err = sqlx.In(listQuery, args...)
	if err != nil {
		ReportError(req, errors.Wrap(err, "error preparing IN select for find contracts"))
		return http.StatusInternalServerError, ErrorMessage("Error finding contracts", nil)
	}
	listQuery = db.Get().Rebind(listQuery)

	err = db.Get().Unsafe().SelectContext(ctx, &contracts, listQuery, listArgs...)
	if err != nil {
		err = errors.Wrap(err, "error executing select for find contracts")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error finding contracts", nil)
	}

	var countArgs []interface{}
	countQuery, countArgs, err = sqlx.In(countQuery, args...)
	if err != nil {
		ReportError(req, errors.Wrap(err, "error preparing IN count for find contracts"))
		return http.StatusInternalServerError, ErrorMessage("Error finding contracts", nil)
	}
	countQuery = db.Get().Rebind(countQuery)

	err = db.Get().GetContext(ctx, &count, countQuery, countArgs...)
	if err != nil {
		err = errors.Wrap(err, "error executing get count for find contracts")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error finding contracts", nil)
	}

	// Configure which roles can make changes to the contract terms
	for i, c := range contracts {
		// Only the account rep manager can edit most contracts
		editWithRoles := []string{db.RoleAccountRepManager}

		// MOD sales can be edited by account reps and account rep managers
		if c.ProductCode == "MNT" && c.SaleType.String == dms.SaleTypeServiceRO {
			editWithRoles = []string{db.RoleAccountRep, db.RoleAccountRepManager}
		}
		contracts[i].EditWithRole = editWithRoles

		remitAmount, err := db.GetContractThirdPartyAmt(nil, contracts[i].ID)
		if err != nil {
			ReportError(req, errors.Wrap(err, "error getting 3rd party remit amount"))
			return http.StatusInternalServerError, ErrorMessage("Error getting 3rd party remit amount", nil)
		}
		contracts[i].RemitAmount = remitAmount.String()

		query := `select * from contract_options where contract_id = $1`
		err = db.Get().Unsafe().SelectContext(ctx, &contracts[i].Options, query, contracts[i].ID)
		if err != nil {
			err = errors.Wrap(err, "could not query contract_options")
			ReportError(req, err)
			return http.StatusInternalServerError, ErrorMessage("Error loading contract data", nil)
		}

		query = `select basic_warranty_miles, basic_warranty_months, drivetrain_warranty_miles, drivetrain_warranty_months from vin_records where vin = $1 order by id desc limit 1`
		err = db.Get().GetContext(ctx, &contracts[i].Warranty, query, contracts[i].VIN)
		if err != nil {
			err = errors.Wrap(err, "error fetching vin information")
			ReportError(req, err)
			return http.StatusInternalServerError, ErrorMessage("Error fetching vin information", nil)
		}
	}

	return http.StatusOK, map[string]interface{}{"contracts": contracts, "count": count, "voided_or_generated": voidedOrGenerated}
}

// ContractShowByID returns contract details for contract_id
func ContractShowByID(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	contractID, err := strconv.Atoi(chi.URLParam(req, "id"))
	if err != nil {
		ReportError(req, errors.Wrapf(err, "failed to parse contract id %s to int", chi.URLParam(req, "id")))
		return http.StatusBadRequest, ErrorMessage("Invalid contract ID", nil)
	}

	allowed, err := user.CanAccessContract(contractID)
	if err != nil {
		ReportError(req, errors.Wrap(err, "failed to check user authorization"))
		return http.StatusInternalServerError, ErrorMessage("Authorization Error", nil)
	}
	if !allowed {
		return http.StatusUnauthorized, ErrorMessage("Not authorized for this action", nil)
	}

	// TODO if required by business need to handle changes based on logged in store user
	return ContractShowCommon(w, req)
}

func getContractQuery(req *http.Request) (string, string) {
	if value := chi.URLParam(req, "code"); value == "" {
		return `select c.*
			, pt.name product_type_name
			, dppv.display_name dealer_pv_display_name
			from contracts c
			left join product_types pt on pt.id = c.product_type_id
			left join dealer_platform_product_variants dppv
				on (dppv.dealer_platform_id = c.dealer_platform_id and dppv.product_variant_id = c.product_variant_id)
			where c.id = $1`,
			chi.URLParam(req, "id")
	}
	return `select c.*
		, pt.name product_type_name
		, dppv.display_name dealer_pv_display_name
		from contracts c
		left join product_types pt on pt.id = c.product_type_id
		left join dealer_platform_product_variants dppv
			on (dppv.dealer_platform_id = c.dealer_platform_id and dppv.product_variant_id = c.product_variant_id)
		where c.code = $1 or c.original_code = $1`,
		chi.URLParam(req, "code")
}

var appearanceOptionsRegex = regexp.MustCompile(`(?m)Appearance Protection \((P)?(F)?(L)?(D)?\)`)

// ContractShowCommon returns contract details for give contract number and product code
func ContractShowCommon(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()

	data := struct {
		Contract struct {
			ID                        int                    `json:"id" db:"id"`
			Code                      string                 `json:"code"`
			ProductName               string                 `json:"product_name" db:"product_name"`
			ProductVariantName        string                 `json:"product_variant_name" db:"product_variant_name"`
			ProductVariantDisplayName string                 `json:"product_variant_display_name" db:"product_variant_display_name"`
			DealerPVDisplayName       null.String            `json:"dealer_pv_display_name" db:"dealer_pv_display_name"`
			ProductTypeName           null.String            `json:"product_type_name" db:"product_type_name"`
			EffectiveDate             types.JSPQNullDate     `json:"effective_date" db:"effective_date"`
			UpdatedAt                 time.Time              `json:"updated_at" db:"updated_at"`
			ExpirationDate            types.JSPQNullDate     `json:"expiration_date" db:"expiration_date"`
			EffectiveMileage          int                    `json:"effective_mileage" db:"effective_mileage"`
			ExpirationMileage         null.Int               `json:"expiration_mileage" db:"expiration_mileage"`
			AdditiveMileage           bool                   `json:"additive_mileage" db:"additive_mileage"`
			PlanName                  string                 `json:"plan_name" db:"plan_name"`
			IssuingDealer             string                 `json:"issuing_dealer"`
			IssuingDealerNumber       string                 `json:"issuing_dealer_number"`
			IssuingDealerCompanyGroup null.String            `json:"issuing_dealer_company_group" db:"issuing_dealer_company_group"`
			BookDate                  types.JSPQNullDate     `json:"book_date" db:"book_date"`
			Price                     decimal.Decimal        `json:"price" db:"price"`
			PlanCost                  decimal.Decimal        `json:"plan_cost" db:"plan_cost"` // plan cost
			Cost                      decimal.Decimal        `json:"cost" db:"-"`
			InvoicedAt                null.Time              `json:"invoiced_at" db:"invoiced_at"`
			InvoiceNumber             string                 `json:"invoice_number" db:"invoice_number"`
			CustomerID                int                    `json:"-" db:"customer_id"`
			VINRecordID               int                    `json:"-" db:"vin_record_id"`
			Options                   []db.ContractOption    `json:"options" db:"-" `
			Surcharges                []db.ContractSurcharge `json:"surcharges" db:"-" `
			StoreID                   int                    `json:"-" db:"store_id"`
			StoreStateCode            string                 `json:"store_state_code" db:"store_state_code"`
			StoreCode                 string                 `json:"store_code" db:"store_code"`
			StoreNumber               string                 `json:"store_number" db:"store_number"`
			SaleID                    null.Int               `json:"-" db:"sale_id"`
			MaintenanceVisits         null.Int               `json:"maintenance_visits" db:"maintenance_visits"`
			MaintenanceVisitValue     types.JSNullDecimal    `json:"maintenance_visit_value" db:"maintenance_visit_value"`
			MinorMaintenanceVisits    null.Int               `json:"minor_coupons_purchased" db:"minor_coupons_purchased"`
			LenderID                  null.Int               `json:"lender_id" db:"lender_id"`
			Lender                    string                 `json:"-" db:"lender"`
			SalespersonID             null.Int               `json:"-" db:"salesperson_id"`
			Status                    string                 `json:"status" db:"status"`
			FinanceTerm               null.Int               `json:"-" db:"finance_term"`
			FinanceAmount             types.JSNullDecimal    `json:"-" db:"finance_amount"`
			PaymentType               string                 `json:"-" db:"payment_type"`
			Source                    string                 `json:"source" db:"source"`
			Fabric                    null.Bool              `json:"fabric" db:"-"`
			LeatherOrVinyl            null.Bool              `json:"leather_or_vinyl" db:"-"`
			Paint                     null.Bool              `json:"paint" db:"-"`
			DentAndDing               null.Bool              `json:"dent_and_ding" db:"-"`
			ProductTypeCode           string                 `json:"product_type_code" db:"product_type_code"`
			DealNumber                string                 `json:"deal_number" db:"dms_number"`
			OriginalCode              string                 `json:"original_code" db:"original_code"`
			SurchargeCost             decimal.Decimal        `json:"surcharge_cost" db:"cost"`
			InspectionLiability       sql.NullString         `json:"inspection_liability" db:"inspection_liability"`
			EligibilityLevel          string                 `json:"eligibility_level" db:"eligibility_level" `
			RemitAmount               string                 `json:"remit_amount" db:"-"`
			Editable                  bool                   `json:"editable" db:"-"`
			PlanID                    null.Int               `json:"-" db:"plan_id"`
			EligibleForClaim          bool                   `json:"eligible_for_claim" db:"-"`
			VTANumber                 null.String            `json:"vehicle_theft_number" db:"vehicle_theft_number"`
			DealerPlatformID          null.Int               `json:"dealer_platform_id" db:"dealer_platform_id"`
			DealerPlatform            db.DealerPlatform      `json:"dealer_platform" db:"_"`
			IsManuallyExpired         bool                   `json:"is_manually_expired" db:"-"`
			IsUndoTransfer            bool                   `json:"is_undo_transfer" db:"is_undo_transfer"`
			IsEContract               bool                   `json:"is_e_contract" db:"is_e_contract"`
		} `json:"contract"`
		CustomerDetails struct {
			ID                int    `json:"id" db:"id"`
			IsBusiness        bool   `json:"is_business" db:"is_business"`
			BusinessName      string `json:"business_name" db:"business_name"`
			FirstName         string `json:"first_name" db:"first_name"`
			LastName          string `json:"last_name" db:"last_name"`
			Address           string `json:"address" db:"address"`
			City              string `json:"city" db:"city"`
			StateCode         string `json:"state_code" db:"state_code"`
			PostalCode        string `json:"postal_code" db:"postal_code"`
			Phone             string `json:"phone" db:"phone"`
			AlternatePhone    string `json:"alternate_phone" db:"alternate_phone"`
			Email             string `json:"email" db:"email"`
			BestContactMethod string `json:"best_contact_method" db:"best_contact_method"`
		} `json:"customer_details"`
		VehicleDetails struct {
			VIN      string `json:"vin" db:"vin"`
			Year     string `json:"year" db:"year"`
			Make     string `json:"make" db:"make"`
			Model    string `json:"model" db:"model"`
			IsNew    bool   `json:"is_new" db:"is_new"`
			Odometer string `json:"odometer" db:"-"`
		} `json:"vehicle_details"`
		FinancingDetails struct {
			PaymentType           string              `json:"payment_type" db:"payment_type"`
			LenderName            string              `json:"lender_name" db:"lender_name"`
			LenderAddress         string              `json:"lender_address" db:"lender_address"`
			LenderCity            string              `json:"lender_city" db:"lender_city"`
			LenderState           string              `json:"lender_state" db:"lender_state"`
			LenderZip             string              `json:"lender_zip" db:"lender_zip"`
			SaleType              string              `json:"sale_type" db:"sale_type"`
			VehiclePrice          types.JSNullDecimal `json:"vehicle_price" db:"vehicle_price"`
			FinanceAmount         types.JSNullDecimal `json:"finance_amount" db:"finance_amount"`
			FinanceApr            types.JSNullDecimal `json:"finance_apr" db:"finance_apr"`
			FinanceMonthlyPayment types.JSNullDecimal `json:"finance_monthly_payment" db:"finance_monthly_payment"`
			FirstPaymentDate      null.Time           `json:"first_payment_date" db:"first_payment_date"`
			ContractDate          types.JSPQDate      `json:"contract_date" db:"contract_date"`
			MSRP                  types.JSNullDecimal `json:"msrp" db:"msrp"`
			SalesMan              string              `json:"sales_man" db:"-"`
			Term                  int                 `json:"term"`
			IsNew                 bool                `json:"-" db:"is_new"`
			Odometer              string              `json:"-" db:"odometer"`
			SalespersonID         null.Int            `json:"-" db:"salesperson_id"`
			LenderID              null.Int            `json:"lender_id" db:"lender_id"`
			SBLenderID            null.Int            `json:"-" db:"sb_lender_id"`
			KeysRemotes           int                 `json:"keys_remotes" db:"keys_remotes"`
			ERatingFinanceAmount  decimal.NullDecimal `db:"-" json:"e_rating_finance_amount"`
		} `json:"financing_details"`
		CoBuyer struct {
			ID         int         `json:"id" db:"id"`
			FirstName  string      `json:"first_name" db:"first_name"`
			LastName   string      `json:"last_name" db:"last_name"`
			Address    null.String `json:"address" db:"address"`
			City       null.String `json:"city" db:"city"`
			StateCode  null.String `json:"state_code" db:"state_code"`
			PostalCode null.String `json:"postal_code" db:"postal_code"`
			HomePhone  null.String `json:"home_phone" db:"home_phone"`
			AltPhone   null.String `json:"alt_phone" db:"alt_phone"`
			Email      null.String `json:"email" db:"email"`
		} `json:"cobuyer"`
		Claims         []claimSummary `json:"claims"`
		ContractEvents []struct {
			db.ContractEvent
			CreatedByUserName string `db:"created_by_user_name" json:"created_by_user_name"`
		} `db:"-" json:"contract_events"`
		Cancellation struct {
			ID                     int                 `db:"id" json:"id"`
			CancelDate             time.Time           `db:"cancel_date" json:"cancel_date"`
			CancelMileage          int                 `db:"cancel_mileage" json:"cancel_mileage"`
			CancelReason           string              `db:"cancel_reason" json:"cancel_reason"`
			CustomerRefund         decimal.Decimal     `db:"customer_refund" json:"customer_refund"`
			SalesTax               decimal.Decimal     `db:"sales_tax" json:"sales_tax"`
			SalesTaxRate           decimal.Decimal     `db:"sales_tax_rate" json:"sales_tax_rate"`
			StoreRefund            decimal.Decimal     `db:"store_refund" json:"store_refund"`
			CancelFee              decimal.Decimal     `db:"cancel_fee" json:"cancel_fee"`
			CancelFactor           decimal.Decimal     `db:"cancel_factor" json:"cancel_factor"`
			InvoiceID              int                 `db:"invoice_id" json:"invoice_id"`
			ReviewedAt             types.JSPQNullDate  `db:"reviewed_at" json:"reviewed_at"`
			ReviewedByUserID       int                 `db:"reviewed_by_user_id" json:"reviewed_by_user_id"`
			ClaimsDeducted         bool                `db:"-" json:"claims_deducted"`
			ClaimsPaidAmount       decimal.Decimal     `db:"claims_paid_amount" json:"claims_paid_amount"`
			ClaimsDeductedAmount   decimal.NullDecimal `db:"claims_deducted_amount" json:"claims_deducted_amount"`
			CancelFactorUsed       string              `db:"-" json:"cancel_factor_used"`
			InvoicedAt             null.Time           `db:"invoiced_at" json:"invoiced_at"`
			InvoiceNumber          string              `db:"invoice_number" json:"invoice_number"`
			ThirdPartyRemitAmount  decimal.Decimal     `db:"remit_amount" json:"third_party_remit"`
			SPPAmountPaid          decimal.Decimal     `db:"spp_amount_paid" json:"spp_customer_paid"`
			SPPBalance             decimal.Decimal     `db:"spp_balance" json:"spp_balance"`
			NSDClaims              decimal.Decimal     `db:"nsd_claims" json:"nsd_claims"`
			AdjustedCustomerRefund decimal.Decimal     `db:"adj_customer_refund" json:"adjusted_customer_refund"`
			StoreChargeback        decimal.Decimal     `db:"store_chargeback" json:"store_chargeback"`
			SPPRefund              decimal.Decimal     `db:"spp_refund" json:"spp_refund"`
			RSARefund              decimal.Decimal     `db:"rsa_refund" json:"rsa_refund"`
			TransactionID          null.Int            `db:"transaction_id" json:"-"`
			PayeeType              string              `db:"payee_type" json:"payee_type"`
			PayeeName              string              `db:"payee_name" json:"payee_name"`
			PayeeAttentionTo       string              `db:"payee_attention_to" json:"payee_attention_to"`
			PayeeAddress           string              `db:"payee_address" json:"payee_address"`
			PayeeCity              string              `db:"payee_city" json:"payee_city"`
			PayeeState             string              `db:"payee_state" json:"payee_state"`
			PayeePostalCode        string              `db:"payee_postal_code" json:"payee_postal_code"`
			CancelStatus           string              `db:"cancel_status" json:"cancel_status"`
			CheckNumber            string              `db:"check_number" json:"check_number"`
			PaidDate               null.String         `db:"paid_date" json:"paid_date"`
			BillNumber             string              `db:"bill_number" json:"bill_number"`
			CheckAmount            string              `db:"check_amount" json:"check_amount"`
			ManualUpdateNotes      string              `db:"manual_update_notes" json:"manual_update_notes"`
			IsElectronicCheck      bool                `db:"is_electronic_check" json:"is_electronic_check"`
			Email                  string              `db:"email" json:"email"`
			CheckApplicable        bool                `db:"check_applicable" json:"check_applicable"`
			DaysUsed               decimal.Decimal     `db:"-" json:"days_used"`
			MilesUsed              decimal.Decimal     `db:"-" json:"miles_used"`
		} `json:"cancellation"`
		Transfers []struct {
			ID                       int         `db:"id" json:"id"`
			TransferredAt            time.Time   `db:"transferred_at" json:"transferred_at"`
			TransferredBy            int         `db:"transferred_by_user_id" json:"transferred_by_user_id"`
			TransferType             string      `db:"transfer_type" json:"transfer_type"`
			PreviousVin              null.String `db:"previous_vin" json:"previous_vin"`
			PreviousEffectiveMileage null.String `db:"previous_effective_mileage" json:"previous_effective_mileage"`
			PreviousCustomerID       null.String `db:"previous_customer_id" json:"previous_customer_id"`
			FeeReceived              bool        `db:"fee_received" json:"fee_received"`
		} `json:"transfers"`
		Reinstate struct {
			ID              int       `db:"id" json:"id"`
			ContractID      int       `db:"contract_id" json:"contract_id"`
			CreatedAt       time.Time `db:"created_at" json:"created_at"`
			ReinstateDate   null.Time `db:"reinstate_date" json:"reinstate_date"`
			CreatedByUserID int       `db:"created_by_user_id" json:"created_by_user_id"`
			InvoiceDate     null.Time `db:"invoice_date" json:"invoice_date"`
			InvoiceNumber   string    `db:"invoice_number" json:"invoice_number"`
			TransactionID   int       `db:"transaction_id" json:"transaction_id"`
			IsVoid          bool      `db:"is_void" json:"is_void"`
		} `json:"reinstate"`
	}{}

	query, contractID := getContractQuery(req)

	err := db.Get().Unsafe().GetContext(ctx, &data.Contract, query, contractID)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, ErrorMessage("Contract not found", nil)
		}
		err = errors.Wrap(err, "error finding data")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error loading contract data", nil)
	}

	err = db.Get().Unsafe().GetContext(ctx, &data.Contract.DealerPlatform, `select * from dealer_platforms where id = $1`, data.Contract.DealerPlatformID)
	if err != nil && err != sql.ErrNoRows {
		err = errors.Wrap(err, "error finding dealer platform details")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error finding dealer platform", nil)
	}

	// Set the Editable property if the contract is not an SB Created Contract and
	// th`e contract has an associtad plan.
	data.Contract.Editable = data.Contract.Source != "SB" && data.Contract.PlanID.Valid

	data.FinancingDetails.LenderName = data.Contract.Lender
	data.FinancingDetails.PaymentType = data.Contract.PaymentType
	if data.Contract.FinanceTerm.Valid {
		data.FinancingDetails.Term = int(data.Contract.FinanceTerm.Int64)
	}
	if data.Contract.FinanceAmount.Valid {
		data.FinancingDetails.FinanceAmount = data.Contract.FinanceAmount
	}

	if data.Contract.IsEContract {
		query = `select es.finance_amount
				from e_sales es
					join sales s 
						on es.sale_id = s.id
				where sale_id = $1`
		if err := db.Get().GetContext(ctx, &data.FinancingDetails.ERatingFinanceAmount, query, data.Contract.SaleID); err != nil {
			ReportError(req, errors.Wrap(err, "error finding e rating finance amount"))
			return http.StatusInternalServerError, ErrorMessage("Error finding contract data", nil)
		}
	}

	var storeDetails struct {
		Code        string `db:"code"`
		StateCode   string `db:"state_code"`
		StoreNumber string `db:"store_number"`
	}

	err = db.Get().GetContext(ctx, &storeDetails, `select code, state_code, store_number from stores where id = $1`, data.Contract.StoreID)
	if err != nil {
		err = errors.Wrap(err, "error finding store details")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error finding store details", nil)
	}
	data.Contract.StoreCode = storeDetails.Code
	data.Contract.StoreStateCode = storeDetails.StateCode
	data.Contract.StoreNumber = storeDetails.StoreNumber

	if data.Contract.ProductTypeCode == "VSC" {
		var surchargeCost = decimal.Zero
		query := `select coalesce(sum(cs.cost), 0.0)
			from contract_surcharges cs
				join surcharges s on s.id = cs.surcharge_id
				join contracts c on c.id = cs.contract_id
			where c.id = $1
				and s.tags ?| ARRAY['insp', 'insp_ref']`
		err := db.Get().GetContext(ctx, &surchargeCost, query, data.Contract.ID)
		if err != nil && err != sql.ErrNoRows {
			err = errors.Wrap(err, "error finding inspection amount")
			ReportError(req, err)
			return http.StatusInternalServerError, ErrorMessage("Error finding inspection amount", nil)
		}
		data.Contract.SurchargeCost = surchargeCost

		var inspectionLiability string
		query = `select coalesce(inspection_liability, '') from contracts c join sales s on c.sale_id = s.id where c.id = $1`
		err = db.Get().GetContext(ctx, &inspectionLiability, query, data.Contract.ID)
		if err != nil && err != sql.ErrNoRows {
			err = errors.Wrap(err, "error finding inspection liability")
			ReportError(req, err)
			return http.StatusInternalServerError, ErrorMessage("Error finding inspection liability", nil)
		}
		data.Contract.InspectionLiability.String = inspectionLiability

		eligibilityLevel := ""
		query = `select eligibility_level from inspections where vin = $1 and deactivated_at is null and expires_on >= (now() at time zone 'utc')::date`
		err = db.Get().GetContext(ctx, &eligibilityLevel, query, data.Contract.VINRecordID)
		if err != nil && err != sql.ErrNoRows {
			err = errors.Wrap(err, "error finding eligibility level")
			ReportError(req, err)
			return http.StatusInternalServerError, ErrorMessage("Error finding eligibility level", nil)
		}
		data.Contract.EligibilityLevel = eligibilityLevel
	}

	// fabric, paint, leather
	if data.Contract.ProductTypeCode == "AP" {
		matches := appearanceOptionsRegex.FindStringSubmatch(data.Contract.ProductName)

		if len(matches) > 0 {
			for _, m := range matches {
				if m == "P" {
					data.Contract.Paint = null.BoolFrom(true)
				} else if m == "F" {
					data.Contract.Fabric = null.BoolFrom(true)
				} else if m == "L" {
					data.Contract.LeatherOrVinyl = null.BoolFrom(true)
				} else if m == "D" {
					data.Contract.DentAndDing = null.BoolFrom(true)
				}
			}
		} else {
			data.Contract.Fabric.Valid = true
			data.Contract.Fabric.Bool = true
			data.Contract.LeatherOrVinyl.Valid = true
			data.Contract.LeatherOrVinyl.Bool = true
			data.Contract.Paint.Valid = true
			data.Contract.Paint.Bool = true
		}
	}

	if data.Contract.ProductTypeCode == "PDR" {
		data.Contract.DentAndDing.Valid = true
		data.Contract.DentAndDing.Bool = true
	}

	// invoice date from transactions
	var invoiceData struct {
		InvoicedAt    null.Time `db:"invoiced_at"`
		InvoiceNumber string    `db:"invoice_number"`
	}
	query = `select i.invoice_date invoiced_at, i.invoice_number
		from contracts c join transactions t on t.contract_id = c.id
		join invoice_items ii on ii.transaction_id = t.id
		join invoices i on i.id = ii.invoice_id
		where c.id = $1 and t.transaction_type = $2`
	err = db.Get().GetContext(ctx, &invoiceData, query, data.Contract.ID, db.TranTypeNewContract)
	if err != nil && err != sql.ErrNoRows {
		err = errors.Wrap(err, "error finding contract invoice date")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error loading contract data", nil)
	}
	data.Contract.InvoicedAt = invoiceData.InvoicedAt
	data.Contract.InvoiceNumber = invoiceData.InvoiceNumber

	query = `select * from customers where id = $1`
	err = db.Get().Unsafe().GetContext(ctx, &data.CustomerDetails, query, data.Contract.CustomerID)
	if err != nil {
		err = errors.Wrap(err, "error finding customer")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error loading contract data", nil)
	}

	query = `select * from contract_cobuyers where contract_id = $1`
	err = db.Get().Unsafe().GetContext(ctx, &data.CoBuyer, query, data.Contract.ID)
	if err != nil && err != sql.ErrNoRows { // cobuyer is optional, as of now only GAP contract has cobuyer
		err = errors.Wrap(err, "error finding cobuyer")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error loading contract data", nil)
	}

	if data.CoBuyer.ID == 0 { // cobuyer could be created with New Sale
		query = `select * from contract_cobuyers where id = (select contract_cobuyer_id from sales where id = $1)`
		err = db.Get().Unsafe().GetContext(ctx, &data.CoBuyer, query, data.Contract.SaleID)
		if err != nil && err != sql.ErrNoRows { // cobuyer is optional
			err = errors.Wrap(err, "error finding cobuyer")
			ReportError(req, err)
			return http.StatusInternalServerError, ErrorMessage("Error loading contract data", nil)
		}
	}

	query = `select * from vin_records where id = $1`
	err = db.Get().Unsafe().GetContext(ctx, &data.VehicleDetails, query, data.Contract.VINRecordID)
	if err != nil {
		err = errors.Wrap(err, "error finding vin_record")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error loading contract data", nil)
	}

	if data.Contract.LenderID.Valid && data.Contract.LenderID.Int64 != 0 {
		err = db.Get().GetContext(ctx, &data.FinancingDetails, `
			select id lender_id, name lender_name, address lender_address,
			city lender_city, state_code lender_state, postal_code lender_zip 
			from lenders 
			where id = $1`, data.Contract.LenderID.Int64)
		if err != nil {
			err = errors.Wrap(err, "error finding lender")
			ReportError(req, err)
			return http.StatusBadRequest, ErrorMessage("Error finding lender record", nil)
		}
	}

	// SaleID can be null for migrated contracts
	if data.Contract.SaleID.Valid {
		saleSelectQ := `sale_type, vehicle_price, finance_amount, 
		finance_apr, finance_monthly_payment, first_payment_date, 
		contract_date, msrp, is_new, 
		odometer, salesperson_id, 
		keys_remotes`
		if data.FinancingDetails.PaymentType == "" {
			saleSelectQ += `, payment_type`
		}
		if !data.FinancingDetails.LenderID.Valid || data.FinancingDetails.LenderID.Int64 == 0 {
			saleSelectQ += `, lender_id`
		}
		query = `select ` + saleSelectQ + ` from sales where id = $1`

		err = db.Get().Unsafe().GetContext(ctx, &data.FinancingDetails, query, data.Contract.SaleID)
		if err != nil {
			err = errors.Wrap(err, "could not query sales")
			ReportError(req, err)
			return http.StatusInternalServerError, ErrorMessage("Error loading contract data", nil)
		}
		data.VehicleDetails.IsNew = data.FinancingDetails.IsNew
		data.VehicleDetails.Odometer = data.FinancingDetails.Odometer

		if data.FinancingDetails.SalespersonID.Valid {
			query = `select last_name || ', ' || first_name sales_man
		from current_users join sales on current_users.id = sales.salesperson_id
		where salesperson_id = $1`
			err = db.Get().GetContext(ctx, &data.FinancingDetails.SalesMan, query, data.FinancingDetails.SalespersonID)
			if err != nil {
				err = errors.Wrap(err, "could not query salesperson from users")
				ReportError(req, err)
				return http.StatusInternalServerError, ErrorMessage("Error loading contract data", nil)
			}
		}

		if data.FinancingDetails.LenderID.Valid {
			err = db.Get().GetContext(ctx, &data.FinancingDetails,
				`select id lender_id, name lender_name, address lender_address,
			           city lender_city, state_code lender_state, postal_code lender_zip 
			           from lenders 
			           where id = $1`,
				data.FinancingDetails.LenderID.Int64)
			if err != nil {
				err = errors.Wrap(err, "error finding lender")
				ReportError(req, err)
				return http.StatusBadRequest, ErrorMessage("Error finding lender record", nil)
			}
		}
	} else {
		query = `select sbs.*
					from sb_sales sbs
						join sb_sale_contracts ssc on ssc.sb_sale_id = sbs.id
					where ssc.contract_id = $1`
		err = db.Get().Unsafe().GetContext(ctx, &data.FinancingDetails, query, data.Contract.ID)
		if err != nil && err != sql.ErrNoRows {
			err = errors.Wrap(err, "could not query sb_sales")
			ReportError(req, err)
			return http.StatusInternalServerError, ErrorMessage("Error fetching sales data", nil)
		}

		if err != sql.ErrNoRows {
			data.VehicleDetails.IsNew = data.FinancingDetails.IsNew
			data.VehicleDetails.Odometer = data.FinancingDetails.Odometer

			if data.FinancingDetails.SalespersonID.Valid {
				query = `select last_name || ', ' || first_name sales_man
			from current_users join sales on current_users.id = sales.salesperson_id
			where salesperson_id = $1`
				err = db.Get().GetContext(ctx, &data.FinancingDetails.SalesMan, query, data.FinancingDetails.SalespersonID)
				if err != nil && err != sql.ErrNoRows {
					err = errors.Wrap(err, "could not query salesperson from users")
					ReportError(req, err)
					return http.StatusInternalServerError, ErrorMessage("Error fetching salesperson data", nil)
				}
			}

			if data.FinancingDetails.SBLenderID.Valid {
				err = db.Get().GetContext(ctx, &data.FinancingDetails,
					`select id lender_id, name lender_name, address lender_address,
			               city lender_city, state_code lender_state, postal_code lender_zip
			               from sb_lenders 
			               where id = $1`,
					data.FinancingDetails.SBLenderID.Int64)
				if err != nil {
					err = errors.Wrap(err, "error finding lender")
					ReportError(req, err)
					return http.StatusBadRequest, ErrorMessage("Error finding lender record", nil)
				}
			}
		}
	}

	var adjustmentsCost decimal.Decimal
	query = `select
		coalesce(
			sum(
				case
					when (is_invoiceable = true) and (affects_contract_cost = true) then cost
					else 0.0
				end
			),
			0.0
		)
		from contract_adjustments
		where contract_id = $1`
	err = db.Get().GetContext(ctx, &adjustmentsCost, query, data.Contract.ID)
	if err != nil {
		err = errors.Wrap(err, "could not query contract_adjustments")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error loading contract data", nil)
	}
	data.Contract.Cost = data.Contract.PlanCost.Add(adjustmentsCost)

	query = `select * from contract_options where contract_id = $1`
	err = db.Get().Unsafe().SelectContext(ctx, &data.Contract.Options, query, data.Contract.ID)
	if err != nil {
		err = errors.Wrap(err, "could not query contract_options")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error loading contract data", nil)
	}

	var optionsCost decimal.Decimal
	query = `select coalesce(sum(cost), 0.0) from contract_options where contract_id = $1`
	err = db.Get().GetContext(ctx, &optionsCost, query, data.Contract.ID)
	if err != nil {
		err = errors.Wrap(err, "could not query contract_options")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error loading contract data", nil)
	}
	data.Contract.Cost = data.Contract.Cost.Add(optionsCost)

	query = `select cs.*, rb.name as rate_bucket_name
			from contract_surcharges cs
				join surcharges s on s.id = cs.surcharge_id
				join contracts c on c.id = cs.contract_id
				left join rate_buckets rb on s.rate_bucket_id = rb.id
			where c.id = $1
				and (s.tags is null
				OR (not (s.tags ?| ARRAY['insp','insp_ref'])))`
	err = db.Get().Unsafe().SelectContext(ctx, &data.Contract.Surcharges, query, data.Contract.ID)
	if err != nil {
		err = errors.Wrap(err, "could not query contract_surcharges")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error loading contract data", nil)
	}
	var surchargeCost decimal.Decimal
	query = `select coalesce(sum(cost), 0.0) from contract_surcharges where contract_id = $1`
	err = db.Get().GetContext(ctx, &surchargeCost, query, data.Contract.ID)
	if err != nil {
		err = errors.Wrap(err, "could not query contract_options")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error loading contract data", nil)
	}
	data.Contract.Cost = data.Contract.Cost.Add(surchargeCost)

	var postSaleAdjustmentsCost decimal.Decimal
	query = `select coalesce(sum(amount), 0.0) from contract_post_sale_adjustments where contract_id = $1`
	err = db.Get().GetContext(ctx, &postSaleAdjustmentsCost, query, data.Contract.ID)
	if err != nil {
		err = errors.Wrap(err, "could not query contract_adjustments")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error loading contract data", nil)
	}
	data.Contract.Cost = data.Contract.Cost.Add(postSaleAdjustmentsCost)

	query = `select code issuing_dealer from stores where id = $1`
	err = db.Get().GetContext(ctx, &data.Contract.IssuingDealer, query, data.Contract.StoreID)
	if err != nil {
		err = errors.Wrap(err, "could not query store code")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error loading contract data", nil)
	}

	query = `select store_number issuing_dealer_number from stores where id = $1`
	err = db.Get().GetContext(ctx, &data.Contract.IssuingDealerNumber, query, data.Contract.StoreID)
	if err != nil {
		err = errors.Wrap(err, "could not query store code")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error loading contract data", nil)
	}

	query = `select cg.name issuing_dealer_company_group
		from companies c
		left join company_groups cg on cg.id = c.company_group_id
		where c.id = (select company_id from stores where id = $1)`
	err = db.Get().Get(&data.Contract.IssuingDealerCompanyGroup, query, data.Contract.StoreID)
	if err != nil {
		err = errors.Wrap(err, "could not query company group")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error loading contract data", nil)
	}

	query = `select ce.*,case when cu.id is not null then trim(both from (coalesce(cu.first_name, '') || ' ' || coalesce(cu.last_name, '')))
		else ce.created_by_name
		end created_by_user_name
		from contract_events ce
		left join current_users cu ON cu.id = ce.created_by_user_id
		where contract_id = $1
		order by ce.created_at desc`
	err = db.Get().Unsafe().SelectContext(ctx, &data.ContractEvents, query, data.Contract.ID)
	if err != nil && err != sql.ErrNoRows {
		err = errors.Wrap(err, "error finding contract events")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error loading contract data", nil)
	}

	query = `select ce.description from
	contracts c join contract_events ce on c.id = ce.contract_id
	where contract_id = $1 and c.status=$2 and (ce.description=$3 or ce.description=$4)
	order by ce.created_at desc limit 1`

	expDesc := ""
	err = db.Get().GetContext(ctx, &expDesc, query,
		data.Contract.ID, db.ContractStatusExpired, db.ContractEventExpire, db.ContractEventExpireManually)
	if err != nil && err != sql.ErrNoRows {
		err = errors.Wrap(err, "error finding contract events")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error loading contract data", nil)
	}
	if expDesc == db.ContractEventExpireManually {
		data.Contract.IsManuallyExpired = true
	}

	query = `select cc.id,
				cc.cancel_date,
				cc.cancel_mileage,
				cc.cancel_reason,
				cc.customer_refund,
				cc.sales_tax,
				cc.sales_tax_rate,
				cc.store_refund,
				cc.cancel_fee,
				cc.cancel_factor,
				cc.reviewed_at,
				coalesce(cc.reviewed_by_user_id, 0) as reviewed_by_user_id,
				coalesce(t.invoice_id, 0) as invoice_id,
				coalesce(tr.amount, 0) as remit_amount,
				cc.claims_deducted_amount,
				cc.spp_amount_paid,
				cc.spp_balance,
				cc.nsd_claims,
				cc.adj_customer_refund,
				cc.store_chargeback,
				cc.spp_refund,
				cc.rsa_refund,
				t.id as transaction_id,
				cc.payee_type,
				cc.payee_name,
				cc.payee_attention_to,
				cc.payee_address,
				cc.payee_city,
				cc.payee_state_code as payee_state,
				cc.payee_postal_code,
				cc.cancel_status,
				coalesce(ccib.check_number, '') as check_number,
				to_char(ccib.payment_date, 'MM/DD/YYYY') paid_date,
				coalesce(ccib.bill_number, '') as bill_number,
				coalesce(ccib.check_amount, 0) as check_amount,
				coalesce(ccib.manual_update_notes,'') as manual_update_notes,
				cc.is_electronic_check,
				coalesce(cc.email, '') as email,
				cc.check_applicable
			from contract_cancellations cc join contracts c on cc.contract_id = c.id
				left join cancel_contract_intacct_bills ccib on ccib.contract_cancellation_id = cc.id
				left join product_variants pv on c.product_variant_id = pv.id
				left join products p on pv.product_id = p.id
				left join providers pr on pr.id = p.provider_id
				left join transactions t on t.id = cc.transaction_id
				left join transaction_remittals tr on t.id = tr.transaction_id and pr.id = tr.provider_id
			where cc.contract_id = $1 and cc.is_void = $2`
	err = db.Get().GetContext(ctx, &data.Cancellation, query, data.Contract.ID, false)
	if err != nil && err != sql.ErrNoRows {
		ReportError(req, errors.Wrap(err, "error select contract_cancellations"))
		return http.StatusBadRequest, ErrorMessage("Error finding contract", nil)
	}

	// Calculate MilesUsed
	if data.Contract.ExpirationMileage.Valid {
		milesRemaining, err := calcProRataMilesCancelFactor(
			int64(data.Contract.EffectiveMileage), data.Contract.ExpirationMileage.Int64, int64(data.Cancellation.CancelMileage))
		if err != nil {
			return http.StatusInternalServerError, ErrorMessage("Error calculating miles used", nil)
		}
		data.Cancellation.MilesUsed = (decimal.NewFromFloat(1).Sub(milesRemaining)).Mul(decimal.NewFromFloat(100))
	}

	// Calculate DaysUsed
	if data.Contract.ExpirationDate.Valid && data.Contract.EffectiveDate.Valid {
		daysRemaining, err := calcProRataDaysCancelFactor(
			data.Contract.EffectiveDate.Time, data.Contract.ExpirationDate.Time, data.Cancellation.CancelDate)
		if err != nil {
			return http.StatusInternalServerError, ErrorMessage("Error calculating days used", nil)
		}
		data.Cancellation.DaysUsed = (decimal.NewFromFloat(1).Sub(daysRemaining)).Mul(decimal.NewFromFloat(100))
	}

	// If we find cancellation then we need to set the used factor string to display on UI.
	if err != sql.ErrNoRows {
		data.Cancellation.CancelFactorUsed = (decimal.NewFromFloat(1).Sub(data.Cancellation.CancelFactor)).Mul(decimal.NewFromFloat(100)).String()
	}

	query = `select id, transferred_at, transferred_by_user_id, transfer_type, previous_vin, previous_effective_mileage
		from contract_transfers where contract_id = $1`
	err = db.Get().SelectContext(ctx, &data.Transfers, query, data.Contract.ID)
	if err != nil && err != sql.ErrNoRows {
		err = errors.Wrap(err, "error finding contract transfers")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error loading contract data", nil)
	}

	query = `select cr.id, cr.contract_id, cr.created_at, cr.reinstate_date, cr.created_by_user_id, cr.transaction_id, cc.is_void
			from contract_reinstatements cr 
				join contracts c on cr.contract_id = c.id
				join contract_cancellations cc on cc.id = cr.contract_cancellation_id
			where c.id = $1 and cr.is_void = false`
	err = db.Get().GetContext(ctx, &data.Reinstate, query, data.Contract.ID)
	if err != nil && err != sql.ErrNoRows {
		err = errors.Wrap(err, "error finding contract re-instate")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error loading contract data", nil)
	}

	if err != sql.ErrNoRows {
		// invoice date from transactions
		invoiceDataReinstate := struct {
			InvoicedAt    null.Time `db:"invoice_date"`
			InvoiceNumber string    `db:"invoice_number"`
		}{}
		query = `select i.invoice_date, i.invoice_number
		from contracts c join transactions t on t.contract_id = c.id
		join invoice_items ii on ii.transaction_id = t.id
		join invoices i on i.id = ii.invoice_id
		where c.id = $1 and t.transaction_type = $2 and t.id = $3`
		err = db.Get().GetContext(ctx, &invoiceDataReinstate, query, data.Contract.ID, db.TranTypeReinstatement, data.Reinstate.TransactionID)
		if err != nil && err != sql.ErrNoRows {
			err = errors.Wrap(err, "error finding contract invoice date")
			ReportError(req, err)
			return http.StatusInternalServerError, ErrorMessage("Error loading contract data", nil)
		}

		if invoiceDataReinstate.InvoiceNumber != "" {
			data.Reinstate.InvoiceDate = invoiceDataReinstate.InvoicedAt
			data.Reinstate.InvoiceNumber = invoiceDataReinstate.InvoiceNumber
		}
	}

	// if the request is from phizz, don't call claims, which is a call to phizz service
	// phizz contract details does not need claim data and if needed that can be queried locally
	if !strings.Contains(req.RequestURI, "ext") {
		filterStatuses := []string{
			db.AutoClaimStatusOpen,
			db.AutoClaimStatusPayable,
			db.AutoClaimStatusApproved,
			db.AutoClaimStatusWaitingForCheck,
			db.AutoClaimStatusWaitingForReversed,
			db.AutoClaimStatusWaitingForChargeback,
			db.AutoClaimStatusAdjusted,
			db.AutoClaimStatusCheckWritten,
			db.AutoClaimStatusCCPaid,
			db.AutoClaimStatusInvoiceSent,
			db.AutoClaimStatusReversed,
			db.AutoClaimStatusDenied,
			db.AutoClaimStatusNeedRentalBill,
			db.AutoClaimStatusNeedSubletBill,
			db.AutoClaimStatusNeedSMToCall,
			db.AutoClaimStatusNeedClosedAccountingRO,
			db.AutoClaimStatusNeedProofOfDeductibleReimbursement,
			db.AutoClaimStatusAdjusted,
		}
		if data.Contract.ProductTypeCode == db.ProductTypeCodeLeaseWearAndTear {
			filterStatuses = []string{
				db.LwtClaimStatusInquiry,
				db.LwtClaimStatusClaimInProcess,
				db.LwtClaimStatusPendingApproval,
				db.LwtClaimStatusPendingDenial,
				db.LwtClaimStatusApproved,
				db.LwtClaimStatusWaitingForCheck,
				db.LwtClaimStatusCheckWritten,
				db.LwtClaimStatusReturnedClaim,
				db.LwtClaimStatusDenied,
				db.LwtClaimStatusCheckVoided,
				db.LwtClaimStatusAdjusted,
			}
		} else { // return all for GAP, VTA etc.
			filterStatuses = []string{}
		}
		claims, err := db.GetFilteredClaims(ctx, data.Contract.Code, data.Contract.ProductTypeCode, filterStatuses)
		if err != nil {
			ReportError(req, err)
			return http.StatusInternalServerError, ErrorMessage("Error in getting claims, loading data", nil)
		}

		if data.Contract.ProductTypeCode == db.ProductTypeCodeLeaseWearAndTear {
			data.Claims = summarizeLWTClaims(claims)
		} else {
			data.Claims = summarizeClaims(claims)
		}
	}

	// Calculate claims amount and set if claim is deducted
	data.Cancellation.ClaimsDeducted = false
	if data.Cancellation.ClaimsDeductedAmount.Valid && data.Cancellation.ClaimsDeductedAmount.Decimal.GreaterThan(decimal.Zero) {
		data.Cancellation.ClaimsDeducted = true
	}
	// Older cancellations will not have a value for claims_deducted_amount, so fall back to the old broken way of providing this information.
	// It is correct most of the time and Dava and Jade decided they would rather see the information that is right most of the time
	// than not see it for any old cancels
	if !data.Cancellation.ClaimsDeductedAmount.Valid {
		data.Cancellation.ClaimsDeductedAmount = decimal.NullDecimal{Decimal: decimal.Zero, Valid: true}
		if len(data.Claims) > 0 {
			data.Cancellation.ClaimsDeducted = true
			var totalClaimAmount decimal.Decimal
			for _, claim := range data.Claims {
				totalClaimAmount = totalClaimAmount.Add(claim.ClaimAmount)
			}
			data.Cancellation.ClaimsDeductedAmount = decimal.NullDecimal{Decimal: totalClaimAmount, Valid: true}
		}
	}

	if data.Contract.ProductTypeCode == db.ProductTypeCodeMaintenance && data.Contract.Status == db.ContractStatusExpired {
		isEligibleForClaim := true
		// check if a claim is already created in grace period
		for _, claim := range data.Claims {
			if claim.ClaimDate.Time.After(data.Contract.ExpirationDate.Time) {
				// If claim is already made in grace period then it is not eligible for claim
				isEligibleForClaim = false
				break
			}
		}

		if len(data.Claims) == 0 || isEligibleForClaim {
			gracePeriod := 0
			err = db.Get().GetContext(ctx, &gracePeriod,
				`select grace_period_months from companies where id = (select company_id from stores where id = $1) and allow_grace_period = true`,
				data.Contract.StoreID)
			if err != nil && err != sql.ErrNoRows {
				ReportError(req, errors.Wrap(err, "error in getting grace period"))
				return http.StatusInternalServerError, ErrorMessage("Error in creating maintenance claim", nil)
			}
			// If its in grace period its eligible for at least one claim.
			if data.Contract.ExpirationDate.Time.AddDate(0, gracePeriod, 0).Before(time.Now()) {
				isEligibleForClaim = false
			}
		}
		data.Contract.EligibleForClaim = isEligibleForClaim
	}

	query = `select
			   i.invoice_date invoiced_at
			 , i.invoice_number
		from contracts c
			join transactions t on t.contract_id = c.id
			join invoice_items ii on ii.transaction_id = t.id
			join invoices i on i.id = ii.invoice_id
			join contract_cancellations cc on c.id = cc.contract_id
		where c.id = $1
		  and t.transaction_type = $2
		  and cc.is_void = false
		  and t.id = $3`
	err = db.Get().GetContext(ctx, &invoiceData, query, data.Contract.ID, db.TranTypeCancel, data.Cancellation.TransactionID)
	if err != nil && err != sql.ErrNoRows {
		err = errors.Wrap(err, "error finding cancel contract invoice data")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error loading contract cancellation data", nil)
	}

	if err != sql.ErrNoRows {
		data.Cancellation.InvoicedAt = invoiceData.InvoicedAt
		data.Cancellation.InvoiceNumber = invoiceData.InvoiceNumber
	}

	remitAmount, err := db.GetContractThirdPartyAmt(nil, data.Contract.ID)
	if err != nil {
		ReportError(req, errors.Wrap(err, "error getting 3rd party remit amount"))
		return http.StatusInternalServerError, ErrorMessage("Error getting 3rd party remit amount", nil)
	}
	data.Contract.RemitAmount = remitAmount.String()
	return http.StatusOK, map[string]interface{}{"data": data}
}

type claimSummary struct {
	ClaimDate         types.JSPQNullDate   `json:"claim_date"`
	ClaimStatus       string               `json:"claim_status"`
	Odometer          int                  `json:"odometer"`
	FailedComponents  string               `json:"failed_components"`
	RepairOrderNumber string               `json:"repair_order_number"`
	CheckNumber       []string             `json:"check_number"`
	ClaimAmount       decimal.Decimal      `json:"claim_amount"`
	ClaimPaidDate     []types.JSPQNullDate `json:"claim_paid_date"`
	AuthorizedAmount  decimal.Decimal      `json:"authorized_amount" db:"authorized_amount"`
	ClaimDOL          types.JSPQNullDate   `json:"claim_dol"`
	PaymentID         int                  `json:"payment_id"`
}

func summarizeClaims(claims []db.Claim) []claimSummary {
	temp := make(map[int]claimSummary)
	for _, c := range claims {
		cs := claimSummary{
			ClaimDate:         c.ClaimDate,
			ClaimStatus:       c.ClaimStatus,
			Odometer:          c.Odometer,
			FailedComponents:  c.FailedComponents,
			RepairOrderNumber: c.RepairOrderNumber,
			CheckNumber:       []string{c.CheckNumber},
			ClaimAmount:       c.ClaimAmount.Decimal,
			AuthorizedAmount:  c.AuthorizedAmount,
			ClaimDOL:          c.ClaimDOL,
			PaymentID:         int(c.PaymentID.Int64),
		}
		if c.ClaimPaidDate.Valid {
			cs.ClaimPaidDate = append(cs.ClaimPaidDate, c.ClaimPaidDate)
		}
		if v, ok := temp[c.ID]; ok {
			currentPaymentID := cs.PaymentID
			cs = claimSummary(v)
			if c.ClaimAmount.Valid && c.ClaimAmount.Decimal.LessThan(decimal.Zero) && cs.PaymentID != int(c.PaymentID.Int64) {
				cs.ClaimAmount = cs.ClaimAmount.Add(c.AuthorizedAmount)
			}
			cs.CheckNumber = append(cs.CheckNumber, c.CheckNumber)
			if c.ClaimPaidDate.Valid {
				cs.ClaimPaidDate = append(cs.ClaimPaidDate, c.ClaimPaidDate)
			}
			cs.PaymentID = currentPaymentID
		} else {
			if !c.AuthorizedAmount.Equals(decimal.Zero) {
				cs.ClaimAmount = c.AuthorizedAmount
			}
		}
		temp[c.ID] = cs
	}

	var summary []claimSummary
	for _, v := range temp {
		summary = append(summary, v)
	}
	sort.Slice(summary, func(i, j int) bool {
		return summary[i].ClaimDate.Time.Before(summary[j].ClaimDate.Time)
	})

	return summary
}

func summarizeLWTClaims(claims []db.Claim) []claimSummary {
	temp := make(map[int]claimSummary)
	for _, c := range claims {
		cs := claimSummary{
			ClaimDate:         c.ClaimDate,
			ClaimStatus:       c.ClaimStatus,
			Odometer:          c.Odometer,
			FailedComponents:  c.FailedComponents,
			RepairOrderNumber: c.RepairOrderNumber,
			CheckNumber:       []string{c.CheckNumber},
			ClaimAmount:       c.ClaimAmount.Decimal,
			AuthorizedAmount:  c.AuthorizedAmount,
		}
		if c.ClaimPaidDate.Valid {
			cs.ClaimPaidDate = append(cs.ClaimPaidDate, c.ClaimPaidDate)
		}
		if v, ok := temp[c.ID]; ok {
			cs = v
			cs.CheckNumber = append(cs.CheckNumber, c.CheckNumber)
			if c.ClaimPaidDate.Valid {
				cs.ClaimPaidDate = append(cs.ClaimPaidDate, c.ClaimPaidDate)
			}
		}
		temp[c.ID] = cs
	}

	var summary []claimSummary
	for _, v := range temp {
		summary = append(summary, v)
	}

	sort.Slice(summary, func(i, j int) bool {
		return summary[i].ClaimDate.Time.Before(summary[j].ClaimDate.Time)
	})
	return summary
}

// Claims function search in phizz, on contract number
func Claims(ctx context.Context, code, productType string) (int, decimal.Decimal, bool, error) {

	claims := struct {
		ClaimCount        int             `json:"claim_count"`
		ClaimTotal        decimal.Decimal `json:"claim_total"`
		HasClaimInProcess bool            `json:"has_claim_in_process"`
	}{}

	if _, ok := db.ProductCodeWhizToPhizz[productType]; !ok {
		err := errors.Errorf("product code not mapped in phizz: %s", productType)
		return claims.ClaimCount, claims.ClaimTotal, claims.HasClaimInProcess, err
	}

	url := conf.Get().Phizz.BaseURL + "/ext/claims/" + code + "/" + productType

	if conf.Get().Phizz.Log {
		util.LogMessagef(ctx, "[Phizz-claims-code] request URL: %s", url)
	}

	phizzReq, err := http.NewRequest("GET", url, nil)
	if err != nil {
		err = errors.Wrap(err, "could not create Phizz-claims-code request")
		return claims.ClaimCount, claims.ClaimTotal, claims.HasClaimInProcess, errors.Wrap(err, "could not create Phizz-claims-code request")
	}

	phizzReq.Header.Set("Content-Type", "application/json")
	phizzReq.Header.Set("Accept", "application/json")
	salt := []byte(conf.Get().Phizz.AuthSalt)
	checksum := sha512.Sum512(salt)
	phizzReq.Header.Set("Phizz-Checksum", hex.EncodeToString(checksum[:]))

	client := http.Client{Timeout: time.Second * 30}
	resp, err := nr.External(newrelic.FromContext(ctx), client, phizzReq)
	if err != nil {
		err = errors.Wrap(err, "Invalid response for Phizz-claims-code")
		return claims.ClaimCount, claims.ClaimTotal, claims.HasClaimInProcess, errors.Wrap(err, "Invalid response for Phizz-claims-code")
	}
	defer resp.Body.Close()

	bodyBytes, _, err := util.ReadResponse(resp.Body)
	if err != nil && err != io.EOF && err != io.ErrUnexpectedEOF {
		err = errors.Wrap(err, "Invalid response for Phizz-claims-code")
		return claims.ClaimCount, claims.ClaimTotal, claims.HasClaimInProcess, errors.Wrap(err, "Invalid response for Phizz-claims-code")
	}
	if conf.Get().Phizz.Log {
		util.LogMessagef(ctx, "[Phizz-Auth] response status: %s  body: %s", resp.Status, string(bodyBytes))
	}

	if resp.StatusCode != http.StatusOK {
		err = errors.Wrap(err, "Invalid response for Phizz-claims-code")
		return claims.ClaimCount, claims.ClaimTotal, claims.HasClaimInProcess, errors.Wrap(err, "Invalid response for Phizz-claims-code")
	}

	err = json.Unmarshal(bodyBytes, &claims)
	if err != nil {
		err = errors.Wrap(err, "Invalid response for Phizz-auto-claims-code")
		return claims.ClaimCount, claims.ClaimTotal, claims.HasClaimInProcess, errors.Wrap(err, "Invalid response for Phizz-auto-claims-code")
	}
	return claims.ClaimCount, claims.ClaimTotal, claims.HasClaimInProcess, nil
}

// GetCancellationReasonsLogic contains the core logic for fetching cancellation reasons.
// This function is used by multiple handlers.
func GetCancellationReasonsLogic(ctx context.Context) ([]db.CancelReason, error) {
	reasons, err := db.GetCancelReasons(ctx)
	if err != nil {
		return nil, errors.WithMessage(err, "error getting cancel reasons list")
	}
	return reasons, nil
}

// ContractCancelReasonList returns list of available cancel reasons for session-authenticated users.
// Signature changed to AuthenticatedAPIHandlerFunc
func ContractCancelReasonList(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	// user db.CurrentUser is available here if needed for authorization or specific logic for session users
	// For now, it's unused by getCancellationReasonsLogic itself.
	ctx := req.Context()
	reasons, err := GetCancellationReasonsLogic(ctx) // Calls the new logic function
	if err != nil {
		ReportError(req, err) // Assumes ReportError handles logging/reporting appropriately
		return http.StatusInternalServerError, ErrorMessage("Error getting cancellation reasons", nil)
	}
	return http.StatusOK, map[string]interface{}{"reasons": reasons}
}

// ContractCoverageDetails returns list of coverage for given contract
func ContractCoverageDetails(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	return CoverageDetails(w, req)
}

// CoverageDetails returns list of coverage for given contract
func CoverageDetails(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	code := chi.URLParam(req, "code")

	if code == "" {
		return http.StatusBadRequest, ErrorMessage("Contract code is missing", nil)
	}

	var contractFormID sql.NullInt64
	query := `select contract_form_id  from contracts where code = $1`
	err := db.Get().Get(&contractFormID, query, code)
	if err != nil {
		if err == sql.ErrNoRows {
			err = errors.Wrap(err, "error getting contract")
			ReportError(req, err)
			return http.StatusNotFound, ErrorMessage("Error getting contract", nil)
		}
		err = errors.Wrap(err, "error getting contract form")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error getting contract form", nil)
	}

	var currentContractFormID sql.NullInt64
	query = `with recursive updated_contract_forms as (
			select id, form_code, created_at, deleted_at from contract_forms where id = $1
			union
			select cf.id, cf.form_code, cf.created_at, cf.deleted_at
			from contract_forms cf
			join updated_contract_forms ucf on cf.created_at = ucf.deleted_at
		) select id from updated_contract_forms where deleted_at is null`
	err = db.Get().Get(&currentContractFormID, query, contractFormID)
	if err != nil && err != sql.ErrNoRows {
		ReportError(req, errors.Wrap(err, "error getting current contract form data"))
		return http.StatusInternalServerError, ErrorMessage("Error getting current contract form data", nil)
	}

	if currentContractFormID.Int64 == 0 {
		return http.StatusOK, map[string]interface{}{"coverageDetails": nil, "contractFormID": currentContractFormID.Int64}
	}

	coverageDetails := []struct {
		Code              int         `db:"code" json:"code"`
		Covered           string      `db:"covered" json:"covered"`
		ProductTypeID     int         `db:"product_type_id" json:"product_type_id"`
		GroupName         string      `db:"group_name" json:"group_name"`
		Description       string      `db:"description" json:"description"`
		ProductName       string      `db:"name" json:"product_name"`
		OptionCode        null.String `db:"option_code" json:"option_code"`
		UpdatedOptionCode null.String `db:"updated_option_code" json:"updated_option_code"`
	}{}

	query = `select vc.code
       , pvc.covered
       , product_type_id
       , pt.name
       , vc.group_name
       , vc.description
	   , pvc.option_code
       , CASE WHEN pvc.option_code = 'SG' THEN 'SGC'
            WHEN pvc.option_code = 'HT' THEN 'HTC'
            WHEN pvc.option_code = 'ST' THEN 'STC'
            ELSE pvc.option_code
         END as updated_option_code
	from product_vehicle_components pvc
		join vehicle_components vc on pvc.vehicle_component_code = vc.code
		join product_types pt on vc.product_type_id = pt.id
		join product_vehicle_component_lists pvcl on pvc.product_vehicle_component_list_id = pvcl.id
		where product_vehicle_component_list_id in
			(
			   select product_vehicle_component_list_id
			   from contract_forms_product_vehicle_component_lists
			   where contract_form_id = $1
			   and product_id in
				(
					select p.id from contracts c
						join product_variants pv on c.product_variant_id = pv.id
						join products p on pv.product_id = p.id
					where c.code = $2
				)
			)
	order by vc.code`

	err = db.Get().Select(&coverageDetails, query, currentContractFormID.Int64, code)
	if err != nil {
		err = errors.Wrap(err, "error getting coverage details")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage("Error getting coverage details", nil)
	}

	return http.StatusOK, map[string]interface{}{"coverageDetails": coverageDetails, "contractFormID": currentContractFormID.Int64}
}

// ContractCoverageAsPdf generates the pdf for covered components
func ContractCoverageAsPdf(w http.ResponseWriter, req *http.Request, user db.CurrentUser) {
	CoverageList(w, req)
}

// ContractCancellationQuotesList returns cancellation estimates for all contracts of a VIN
func ContractCancellationQuotesList(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	return CancellationEstimateList(w, req, user, false, false, false)
}

// ContractCancellationEstimateListAsPdf returns cancellation estimates for given contract list in PDF format
func ContractCancellationEstimateListAsPdf(w http.ResponseWriter, req *http.Request, user db.CurrentUser) {
	CancellationEstimateListAsPdf(w, req, user, false, false, false)
}

// ContractCancellationUpdateRequest updates contract cancellations for all given contracts
func ContractCancellationUpdateRequest(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	return ContractCancel(w, req, user, true, false, false, false, nil)
}

// ContractCancellationQuoteListAsPdf returns cancellation estimate quotes for given contract list in PDF format
func ContractCancellationQuoteListAsPdf(w http.ResponseWriter, req *http.Request, user db.CurrentUser) {
	CancellationQuoteListAsPdf(w, req, user, false, false, false)
}

// ContractCancellationRequestForm returns contract cancellation form given contract list in PDF format
func ContractCancellationRequestForm(w http.ResponseWriter, req *http.Request, user db.CurrentUser) {
	contractID, err := strconv.Atoi(chi.URLParam(req, "id"))
	if err != nil {
		ReportError(req, errors.Wrapf(err, "failed to parse contract id %s to int", chi.URLParam(req, "id")))
		w.WriteHeader(http.StatusBadRequest)
		return
	}

	allowed, err := user.CanAccessContract(contractID)
	if err != nil {
		ReportError(req, errors.Wrap(err, "failed to check user authorization"))
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
	if !allowed {
		w.WriteHeader(http.StatusUnauthorized)
		fmt.Fprint(w, "Unauthorized")
	}

	CancellationRequestForm(w, req, user)
}

// ContractActivate activate contract and returns error if any
func ContractActivate(contractID int, userID int) error {
	tx, err := db.Get().Beginx()
	if err != nil {
		return errors.Wrapf(err, "Error starting transaction for contract activate")
	}

	err = contractActivate(tx, contractID, userID, true)
	if err != nil {
		tx.Rollback()
		return err
	}

	err = tx.Commit()
	if err != nil {
		return errors.Wrapf(err, "Error committing transaction for contract activate")
	}

	return nil
}

func contractActivate(tx *sqlx.Tx, contractID int, userID int, updateActivateDate bool) error {
	err := db.CreateContractLog(tx, contractID, userID)
	if err != nil {
		return errors.Wrap(err, "error contract_logs insert")
	}

	var contract db.Contract
	contract.ID = contractID
	contract.UpdatedByUserID = userID
	contract.Status = db.ContractStatusActive
	contract.Event = db.ContractEventActivate
	contract.EventNotes = ""

	var updateActivatedOn string
	if updateActivateDate {
		updateActivatedOn = `, activated_on = now() at time zone 'utc'`
	}

	query := `update contracts
              set version = version + 1,
              updated_at = now() at time zone 'utc', updated_by_user_id = :updated_by_user_id,
              status = :status, event = :event, event_notes = :event_notes` + updateActivatedOn + `
             where id = :id
             returning updated_at`

	contractStmt, err := tx.PrepareNamed(query)
	if err != nil {
		return errors.Wrap(err, "error preparing contracts update")
	}

	err = contractStmt.Get(&contract, contract)
	if err != nil {
		return errors.Wrap(err, "error executing contracts update")
	}

	userName := ""

	query = `select (cu.first_name || ' ' || cu.last_name) 
            from current_users cu 
            where cu.id = $1`

	err = db.Get().Get(&userName, query, userID)
	if err != nil {
		return errors.Wrap(err, "error getting user name")
	}

	query = `insert into contract_events(created_at, created_by_user_id, created_by_name, contract_id, description)values(
	now() at time zone 'utc', $1, $2, $3, $4)`
	_, err = db.Get().Exec(query, userID, userName, contract.ID, db.ContractEventActivate)
	if err != nil {
		return errors.Wrap(err, "error inserting contract event")
	}

	//TODO: when introduce new type flag for SPP change query to update flag
	query = `update contract_flags set cleared_at = now() at time zone 'utc', cleared_by_user_id = $1
            where contract_id = $2 and flag_reason = $3 and cleared_at is NULL and deleted_at is NULL`
	_, err = tx.Exec(query, userID, contractID, db.ContractFlagNewSPPContract)
	if err != nil {
		return errors.Wrap(err, "error clearing contract log")
	}

	return nil
}

// ClaimRO stores the contract number and claim date for a claim
type ClaimRO struct {
	ContractNumber string             `json:"contract_number" db:"contract_number"`
	ClaimDate      types.JSPQNullDate `json:"claim_date" db:"claim_date"`
	ProductCode    string             `json:"product_code" db:"product_code"`
	StoreID        null.Int           `json:"store_id" db:"store_id"`
}

// claimsByRO search in phizz, on ro number
func claimsByRO(ctx context.Context, ro string) ([]ClaimRO, error) {
	txn := newrelic.FromContext(ctx)

	url := conf.Get().Phizz.BaseURL + "/ext/auto-claims/" + ro

	if conf.Get().Phizz.Log {
		util.LogMessagef(ctx, "[Phizz-auto-claims-ro] request URL: %s", url)
	}

	phizzReq, err := http.NewRequest("GET", url, nil)
	if err != nil {
		err = errors.Wrap(err, "could not create Phizz-auto-claims-ro request")
		return nil, errors.Wrap(err, "could not create Phizz-auto-claims-ro request")
	}

	phizzReq.Header.Set("Content-Type", "application/json")
	phizzReq.Header.Set("Accept", "application/json")
	salt := []byte(conf.Get().Phizz.AuthSalt)
	checksum := sha512.Sum512(salt)
	phizzReq.Header.Set("Phizz-Checksum", hex.EncodeToString(checksum[:]))

	client := http.Client{Timeout: time.Second * 30}
	resp, err := nr.External(txn, client, phizzReq)
	if err != nil {
		err = errors.Wrap(err, "Invalid response for Phizz-auto-claims-ro")
		return nil, errors.Wrap(err, "Invalid response for Phizz-auto-claims-ro")
	}
	defer resp.Body.Close()

	bodyBytes, _, err := util.ReadResponse(resp.Body)
	if err != nil && err != io.EOF && err != io.ErrUnexpectedEOF {
		err = errors.Wrap(err, "Invalid response for Phizz-auto-claims-ro")
		return nil, errors.Wrap(err, "Invalid response for Phizz-auto-claims-ro")
	}

	if conf.Get().Phizz.Log {
		util.LogMessagef(ctx, "[Phizz-Auth] response status: %s body: %s", resp.Status, string(bodyBytes))
	}

	if resp.StatusCode != http.StatusOK {
		err = errors.Wrap(err, "Invalid response for Phizz-auto-claims-ro")
		return nil, errors.Wrap(err, "Invalid response for Phizz-auto-claims-ro")
	}

	claims := struct {
		List  []ClaimRO `json:"automotive_claims"`
		Count int       `json:"count"`
	}{}

	err = json.Unmarshal(bodyBytes, &claims)
	if err != nil {
		err = errors.Wrap(err, "Invalid response for Phizz-auto-claims-ro")
		return nil, errors.Wrap(err, "Invalid response for Phizz-auto-claims-ro")
	}
	return claims.List, nil
}

// GetContractCost The caller is responsible for rolling back or committing the transaction
func GetContractCost(tx *sqlx.Tx, contractID int) (decimal.Decimal, error) {
	var err error
	query := `select c.id, c.plan_cost,
		(select coalesce(sum(case when ca.is_invoiceable and ca.affects_contract_cost then ca.cost else 0 end), 0) from contract_adjustments ca where ca.contract_id = $1) adjustments_cost,
		(select coalesce(sum(co.cost), 0) from contract_options co where co.contract_id = $1) options_cost,
		(select coalesce(sum(cs.cost), 0) from contract_surcharges cs where cs.contract_id = $1) surcharges_cost,
		(select coalesce(sum(cpsa.amount), 0) from contract_post_sale_adjustments cpsa where cpsa.contract_id = $1) post_sale_adjustments_cost
		from contracts c
		where c.id = $1`

	contractCosts := struct {
		ID                      int             `db:"id"`
		PlanCost                decimal.Decimal `db:"plan_cost"`
		AdjustmentsCost         decimal.Decimal `db:"adjustments_cost"`
		OptionsCost             decimal.Decimal `db:"options_cost"`
		SurchargesCost          decimal.Decimal `db:"surcharges_cost"`
		PostSaleAdjustmentsCost decimal.Decimal `db:"post_sale_adjustments_cost"`
	}{}

	if tx != nil {
		err = tx.Get(&contractCosts, query, contractID)
	} else {
		err = db.Get().Get(&contractCosts, query, contractID)
	}
	if err != nil {
		return decimal.Zero, errors.Wrap(err, "error while getting contract cost")
	}

	return contractCosts.PlanCost.
		Add(contractCosts.AdjustmentsCost).
		Add(contractCosts.OptionsCost).
		Add(contractCosts.SurchargesCost).
		Add(contractCosts.PostSaleAdjustmentsCost), nil
}

type contract struct {
	ID             int                `db:"id"`
	RateSheetID    int                `db:"rate_sheet_id"`
	Name           null.String        `db:"name"`
	Code           null.String        `db:"code"`
	Cost           decimal.Decimal    `db:"cost"`
	Tags           pq.StringArray     `db:"tags"`
	InspectionDate types.JSPQNullDate `db:"inspection_date"`
	CreatedAt      types.JSPQDate     `db:"created_at"`
}

type rateDetails struct {
	RateSheetID int      `db:"rate_sheet_id"`
	SurchargeID null.Int `db:"id"`
}

type tags struct {
	Tags pq.StringArray `db:"tags"`
}

func getTags() ([]tags, error) {
	query := `select akeys(tags)  tags from surcharges WHERE tags ?| ARRAY[$1, $2]`

	var listOfTags []tags
	err := db.Get().Select(&listOfTags, query, RateSheetInspection, RateSheetInspectionRefund)
	if err != nil {
		return []tags{}, errors.Wrap(err, "error getting tags")
	}
	return listOfTags, nil

}

// AllSurchargeContracts will add inspection surcharge for all the contracts
func AllSurchargeContracts(ctx context.Context, user db.CurrentUser) error {
	var total int
	allTags, err := getTags()
	if err != nil {
		return errors.Wrap(err, "error fetching tags")
	}

	minDays, maxDays := 365, 0
	var inspDays int

	for _, val := range allTags {
		var inspType string
		for _, value := range val.Tags {
			if value != RateSheetInspection && value != RateSheetInspectionRefund {

				inspDays, err = strconv.Atoi(value[:len(value)-1])
				if err != nil {
					return errors.Wrap(err, "error converting days")
				}

			} else {
				inspType = value
			}
		}
		if inspDays < minDays && inspType == RateSheetInspection {
			minDays = inspDays
		}
		if inspDays > maxDays && inspType == RateSheetInspectionRefund {
			maxDays = inspDays
		}
	}

	// Allow a larger window so we can add inspections on contracts that are a little older
	maxDays *= 2

	contracts, err := getContractsWithoutInspection(ctx, minDays, maxDays)
	if err != nil {
		return errors.Wrap(err, "error fetching surcharge contracts")
	}
	if contracts == nil {
		return nil
	}

	tx, err := db.Get().Beginx()
	if err != nil {
		return errors.Wrap(err, "could not begin transaction")
	}

	for _, contract := range contracts {
		var customer db.Customer
		err = tx.Unsafe().Get(&customer, `select * from customers where id = $1`, contract.CustomerID)
		if err != nil {
			tx.Rollback()
			return errors.Wrapf(err, "error fetching customer details for contract %s", contract.Code)
		}

		var sale db.Sale
		err = tx.Unsafe().Get(&sale, `select * from sales where id = $1`, contract.SaleID)
		if err != nil {
			tx.Rollback()
			return errors.Wrapf(err, "error fetching sale details for contract %s", contract.Code)
		}

		var classification = &db.Classification{}
		err = tx.Unsafe().Get(classification, `select * from classifications where id = $1`, contract.ClassificationID)
		if err != nil {
			if err != sql.ErrNoRows {
				tx.Rollback()
				return errors.Wrapf(err, "error fetching classification details for contract %s", contract.Code)
			}
			classification = nil
		}

		rateQuery, err := db.RateQueryData(contract.CreatedByUserID, contract, sale, customer)
		if err != nil {
			tx.Rollback()
			return errors.Wrapf(err, "error fetching rate query for contract %s", contract.Code)
		}

		var rateSheetID int
		err = tx.Get(&rateSheetID, `select p.rate_sheet_id from plans p
			join contracts c on c.plan_id = p.id
			where c.id = $1
		`, contract.ID)
		if err != nil {
			tx.Rollback()
			return errors.Wrapf(err, "error getting ratesheet ID for contract %s", contract.Code)
		}

		originalContractCost, err := GetContractCost(tx, contract.ID)
		if err != nil {
			tx.Rollback()
			return errors.Wrapf(err, "could not get contract cost for contract %s", contract.Code)
		}
		surcharges, err := db.FindSurcharges(rateSheetID, rateQuery, classification)
		if err != nil {
			tx.Rollback()
			return errors.Wrapf(err, "could not find surcharges for contract %s", contract.Code)
		}

		for _, surcharge := range surcharges {
			if surcharge.Rateable.Tags.Map[RateSheetInspection].Valid {
				inspDays := 0
				trim := strings.TrimSpace
				for key := range surcharge.Rateable.Tags.Map {
					newKey := trim(key)
					if newKey != "" && newKey != RateSheetInspection && newKey != RateSheetInspectionRefund {
						inspDays, err = strconv.Atoi(newKey[:len(newKey)-1])
						if err != nil {
							tx.Rollback()
							return errors.Wrap(err, "error converting days")
						}
					}
				}
				now, err := types.InMountainTime(time.Now())
				if err != nil {
					tx.Rollback()
					return errors.Wrap(err, "could not get current time")
				}
				effectiveDate, err := types.InMountainTime(contract.EffectiveDate)
				if err != nil {
					tx.Rollback()
					return errors.Wrap(err, "could not get effective date")
				}
				timeSince := now.Sub(effectiveDate)
				if timeSince > time.Hour*time.Duration(24*(inspDays+1)) {
					id := 0
					tx.Get(&id, `select id from contract_surcharges where contract_id = $1 and name = $2`, contract.ID, surcharge.Name)
					if id == 0 {
						total++
						// Save the current state of the contract before making changes
						err = db.CreateContractLog(tx, contract.ID, user.ID)
						if err != nil {
							// Caller is responsible for rolling back the transaction
							return errors.Wrap(err, "error creating contract log")
						}

						// Add a record in contract_surcharges table
						addQ := `insert into contract_surcharges (created_at, contract_id, name, code, cost, surcharge_id, rate_bucket_id) 
								values(now() at time zone 'utc', $1, $2, $3, $4, $5, $6)`
						addQ, args, err := sqlx.In(addQ, contract.ID, surcharge.Name, surcharge.Code, surcharge.Rateable.Cost, surcharge.ID, surcharge.RateBucketID.Int64)
						if err != nil {
							tx.Rollback()
							return errors.Wrap(err, "could not add surcharges")
						}

						addQ = tx.Rebind(addQ)
						_, err = tx.Exec(addQ, args...)
						if err != nil {
							tx.Rollback()
							return errors.Wrap(err, "could not add surcharges")
						}

						// Add a record in contract_flags table
						query := `insert into contract_flags (flag_reason, contract_id, flagged_at, created_by_user_id) 
                				values ($1, $2, now() at time zone 'utc', $3)`

						_, err = tx.Exec(query, db.ContractFlagInspectionSurchargeAdded, contract.ID, user.ID)
						if err != nil {
							tx.Rollback()
							return errors.Wrap(err, "could not add contract flags")
						}

						err = AddSurchargeTransactionRecord(ctx, tx, contract, sale, originalContractCost, surcharge.Name.String, user)
						if err != nil {
							tx.Rollback()
							return errors.Wrap(err, "error adding transaction record")
						}
					}
				}
			}
		}
	}

	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		return errors.Wrap(err, "could not commit contract surcharge transaction")
	}
	util.LogMessagef(ctx, "Added %d inspection surcharges", total)

	return nil
}

// AddSurchargeTransactionRecord adds surcharge transaction record
func AddSurchargeTransactionRecord(ctx context.Context,
	tx *sqlx.Tx,
	contract db.Contract,
	sale db.Sale,
	oldCost decimal.Decimal,
	surchargeName string,
	user db.CurrentUser,
) error {
	var vinRecord db.VINRecord
	err := tx.Unsafe().Get(&vinRecord, `select * from vin_records where id = $1`, sale.VINRecordID)
	if err != nil {
		// Caller is responsible for rolling back the transaction
		return errors.Wrap(err, "error finding vin record")
	}

	tran := db.Transaction{}
	tran.ContractID = contract.ID
	tran.CreatedByUserID = user.ID
	tran.StoreID = contract.StoreID
	tran.VehicleYear = vinRecord.Year
	tran.VehicleMake = vinRecord.Make
	tran.VehicleModel = vinRecord.Model
	tran.TransactionType = db.TranTypeContractChange
	tran.TransactionSubtype = null.StringFrom(db.TranSubtypeSurchargeAdded)
	tran.ContractPrice = contract.Price

	newCost, err := GetContractCost(tx, contract.ID)
	if err != nil {
		// Caller is responsible for rolling back the transaction
		return errors.Wrap(err, "could not get new cost")
	}
	tran.Amount = newCost.Sub(oldCost)

	// Add a record in transaction table
	err = db.CreateTransaction(ctx, tx, &tran)
	if err != nil {
		// Caller is responsible for rolling back the transaction
		return errors.Wrap(err, "could not create transaction for contract")
	}

	// Create transaction breakouts
	previousContract, err := db.GetContractWithDataByID(ctx, nil, contract.ID)
	if err != nil {
		return errors.Wrap(err, "failed to get previous contract data")
	}
	currentContract, err := db.GetContractWithDataByID(ctx, tx, contract.ID)
	if err != nil {
		return errors.Wrap(err, "failed to get previous contract data")
	}
	previousBreakouts := db.GetContractCostBreakouts(previousContract, user.ID, tran.ID)
	currentBreakouts := db.GetContractCostBreakouts(currentContract, user.ID, tran.ID)
	breakoutDiffs := db.SubTransactionBreakouts(previousBreakouts, currentBreakouts)
	for _, bo := range breakoutDiffs {
		err = db.InsertTransactionBreakout(ctx, tx, bo)
		if err != nil {
			msg := fmt.Sprintf("failed to save transaction breakout for contract %s", contract.Code)
			return errors.WithMessage(err, msg)
		}
	}

	event := fmt.Sprintf("%s: %s ($%s)",
		db.ContractEventAddSurcharge,
		surchargeName,
		tran.Amount.StringFixed(2),
	)

	contract.Event = event
	contract.EventNotes = ""

	// Add a record in contract_events table
	query := `insert into contract_events(created_at, created_by_user_id, created_by_name, contract_id, description)
			values(now() at time zone 'utc', $1, $2, $3, $4)`
	_, err = tx.Exec(query, user.ID, user.FullName(), contract.ID, contract.Event)
	if err != nil {
		// Caller is responsible for rolling back the transaction
		return errors.Wrap(err, "error inserting contract event")
	}

	// Mark contract as updated, increment contract version
	query = `update contracts set
			version = version + 1,
			updated_at = now() at time zone 'utc',
			updated_by_user_id = $1,
			event = $2,
			event_notes = ''
		where id = $3
	`
	_, err = tx.Exec(query, user.ID, contract.Event, contract.ID)
	if err != nil {
		// Caller is responsible for rolling back the transaction
		return errors.Wrap(err, "error updating contract")
	}

	return nil
}

func getContractsWithoutInspection(ctx context.Context, minDays, maxDays int) ([]db.Contract, error) {
	// Find contracts that do not have an active inspection that
	// satisfies the minimum inspection level of the product
	// and do not already have a 'No Inspection' surcharge
	query := `select c.*
			from sales s
				join contracts c on c.sale_id = s.id
				join vin_records vr on vr.id = c.vin_record_id
				join stores st on st.id = s.store_id
				join companies co on co.id = st.company_id
					-- The surcharge should not be added to contracts for Arrowhead
					and co.code != 'AR1'
				join product_variants pv on pv.id = c.product_variant_id
				join products p on p.id = pv.product_id
				left join inspection_eligibility_levels p_iel on p_iel.name = p.minimum_inspection_level
				left join inspections i on i.vin = vr.vin
					and i.expired_by_user_id is null
					and i.expires_on > c.created_at
				left join inspection_eligibility_levels i_iel on i_iel.name = i.eligibility_level
				left join transactions t on t.contract_id = c.id
					and t.transaction_type = 'New Contract'
				left join invoice_items ii on ii.transaction_id = t.id
				left join invoices inv on inv.id = ii.invoice_id
			where s.inspection_liability = 'Accepted'
				and c.product_type_code = 'VSC'
				and c.status in ($1, $2)
				-- doesn't have an inspection with a high enough eligibility level
				and coalesce(p_iel.rank,0) > coalesce(i_iel.rank, 0)
				and c.effective_date between (now() - interval '1 day' * $3)::date and (now() - interval '1 day' * $4)::date
				and not exists (
					select cs.id from contract_surcharges cs
					join surcharges sc on sc.id = cs.surcharge_id
						and sc.tags ? 'insp'
					where cs.contract_id = c.id
				)
				-- Do not add surcharge on contracts that were invoiced in SB.
				and inv.invoice_date > $5`
	lastInvoiceBeforeRelease := time.Date(2019, 12, 2, 0, 0, 0, 0, time.Local)
	var contracts []db.Contract
	err := db.Get().Select(&contracts, query, db.ContractStatusActive, db.ContractStatusPending, maxDays, minDays, lastInvoiceBeforeRelease)
	if err != nil {
		return []db.Contract{}, errors.Wrapf(err, "error getting contract surcharges")
	}
	util.LogMessagef(ctx, "Examining %d contracts", len(contracts))
	return contracts, nil
}

// LoadContractDetails loads contract details
func LoadContractDetails(code string) (ContractEditDetails, error) {
	ctx := context.TODO()

	var contract ContractEditDetails
	err := db.Get().Unsafe().Get(&contract, "select * from contracts where code = $1 or original_code = $1", code)
	if err != nil {
		if err == sql.ErrNoRows {
			return ContractEditDetails{}, err
		}
		return ContractEditDetails{}, errors.Wrapf(err, "error getting contract %s", code)
	}

	var tx *sqlx.Tx
	contract.Adjustments, err = db.GetContractAdjustments(ctx, tx, contract.ID)
	if err != nil {
		return ContractEditDetails{}, errors.Wrapf(err, "error getting contract adjustments")
	}

	contract.Options, err = db.GetContractOptions(ctx, tx, contract.ID)
	if err != nil {
		return ContractEditDetails{}, errors.Wrapf(err, "error getting contract options")
	}

	contract.Surcharges, err = db.GetContractSurcharges(ctx, tx, contract.ID)
	if err != nil {
		return ContractEditDetails{}, errors.Wrapf(err, "error getting contract surcharges")
	}

	err = db.Get().Unsafe().Select(&contract.PostSaleAdjustments, "select * from contract_post_sale_adjustments where contract_id = $1", contract.ID)
	if err != nil {
		return ContractEditDetails{}, errors.Wrapf(err, "error getting contract post_sale_adjustments")
	}

	err = db.Get().Unsafe().Get(&contract.Sale, "select * from sales where id = $1", contract.SaleID)
	if err != nil {
		return ContractEditDetails{}, errors.Wrapf(err, "error getting sale")
	}

	err = db.Get().Unsafe().Get(&contract.Customer, "select * from customers where id = $1", contract.CustomerID)
	if err != nil {
		return ContractEditDetails{}, errors.Wrapf(err, "error getting customer")
	}

	err = db.Get().Unsafe().Get(&contract.VINRecord, "select * from vin_records where id = $1", contract.VINRecordID)
	if err != nil {
		return ContractEditDetails{}, errors.Wrapf(err, "error getting vin record")
	}

	err = db.Get().Unsafe().Get(&contract.Plan, "select * from plans where id = $1", contract.PlanID)
	if err != nil {
		return ContractEditDetails{}, errors.Wrapf(err, "error getting plan")
	}

	err = db.Get().Unsafe().Get(&contract.Store, "select * from stores where id = $1", contract.StoreID)
	if err != nil {
		return ContractEditDetails{}, errors.Wrapf(err, "error getting store")
	}

	err = db.Get().Unsafe().Get(&contract.ProductType, "select * from product_types where id = $1", contract.ProductTypeID)
	if err != nil {
		return ContractEditDetails{}, errors.Wrapf(err, "error getting product type")
	}

	if contract.ClassificationID.Valid {
		rows, err := db.Get().Unsafe().Queryx("select * from classifications where id = $1", contract.ClassificationID.Int64)
		if err != nil {
			return ContractEditDetails{}, errors.Wrapf(err, "error getting classification")
		}
		defer func() { _ = rows.Close() }()
		if rows.Next() {
			contract.Classification = &db.Classification{}
			err = rows.StructScan(contract.Classification)
			if err != nil {
				return ContractEditDetails{}, errors.Wrapf(err, "error getting classification")
			}
		}
	}

	pvQuery := `select pv.*
	from product_variants pv
		join rate_sheets rs on rs.product_variant_id = pv.id
		where rs.id = $1`
	err = db.Get().Unsafe().Get(&contract.ProductVariant, pvQuery, contract.Plan.RateSheetID)
	if err != nil {
		return ContractEditDetails{}, errors.Wrapf(err, "error getting product variant")
	}

	if contract.ProductType.Code == "MNT" && contract.Store.AllowMaintenanceEffectiveDateChange {
		month, day := dateDiff(contract.EffectiveDate, contract.Sale.ContractDate)
		if day == 0 && (month == 6 || month == 12 || month == 18 || month == 24 || month == 36) {
			contract.EffectiveDateChange = month
		}
	}

	contract.CurrentCost = contract.PlanCost
	for _, a := range contract.Adjustments {
		if a.IsInvoiceable && a.AffectsContractCost {
			contract.CurrentCost = contract.CurrentCost.Add(a.Cost)
		}
	}
	for _, o := range contract.Options {
		contract.CurrentCost = contract.CurrentCost.Add(o.Cost)
	}
	for _, s := range contract.Surcharges {
		contract.CurrentCost = contract.CurrentCost.Add(s.Cost)
	}
	for _, psa := range contract.PostSaleAdjustments {
		contract.CurrentCost = contract.CurrentCost.Add(psa.Amount)
	}

	return contract, nil
}

// NewQuotePlanWithAdjustments returns new quote plan with adjustment values
func NewQuotePlanWithAdjustments(
	contract ContractEditDetails,
	plan db.Plan,
	pvID int,
	rbMap map[int]db.RateBucket,
) (*db.QuotePlanWithAdjustments, error) {

	rsQuery := `select rs.id from product_variants pv
		join rate_sheets rs on rs.product_variant_id = pv.id
		where
			pv.id = $1
			and rs.id in (
				-- Find the active rate sheet for the product variant
				-- This is more complicated than it should be because deactivated is not set
				-- for a lot of rate sheets that have had newer versions activated
				select acts.rate_sheet_id
				from rate_sheets rs
				join active_rate_sheets acts on acts.rate_sheet_id = rs.id
				where rs.product_variant_id = pv.id
				and acts.activated_on < now() at time zone 'utc'
				and deactivated_at is null
				order by acts.activated_on desc, acts.created_at desc
				limit 1
			)`
	var rsID int
	err := db.Get().Get(&rsID, rsQuery, pvID)
	if err != nil {
		return nil, errors.Wrapf(err, "error getting ratesheet for product variant id %d", pvID)
	}
	rateQuery, err := db.RateQueryData(contract.CreatedByUserID, contract.Contract, contract.Sale, contract.Customer)
	if err != nil {
		return nil, errors.Wrap(err, "error creating a rate query")
	}

	adjustments, err := db.FindAdjustments(rsID, rateQuery)
	if err != nil {
		return nil, errors.Wrap(err, "error during FindAdjustments")
	}

	qpa, err := db.NewQuotePlanWithAdjustments(
		plan,
		adjustments,
		contract.EffectiveMileage,
		contract.ProductTypeCode,
		rbMap,
	)
	if err != nil {
		return nil, errors.Wrap(err, "error during NewQuotePlanWithAdjustments")
	}
	return &qpa, nil
}

func signContract(ctx context.Context, contractID int, user db.CurrentUser, signDate time.Time) error {

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		err = errors.Wrap(err, "error beginning transaction for contract sign")
		return err
	}

	err = db.CreateContractLog(tx, contractID, user.ID)
	if err != nil {
		return errors.Wrap(err, "error contract_logs insert")
	}

	var contract db.Contract
	contract.ID = contractID
	contract.UpdatedByUserID = user.ID
	contract.ContractSignedDate.SetValid(signDate)
	contract.EventNotes = "Contract signed"

	query := `update contracts
              set version = version + 1,
              updated_at = now() at time zone 'utc', updated_by_user_id = :updated_by_user_id,
              event = :event, event_notes = :event_notes, contract_signed_date = :contract_signed_date,
			  e_sale_complete_date = now() at time zone 'utc'
             where id = :id`

	contractStmt, err := tx.PrepareNamedContext(ctx, query)
	if err != nil {
		return errors.Wrap(err, "error preparing contracts update")
	}

	_, err = contractStmt.ExecContext(ctx, contract)
	if err != nil {
		return errors.Wrap(err, "error executing contracts update")
	}

	userName := ""

	query = `select (cu.first_name || ' ' || cu.last_name) 
            from current_users cu 
            where cu.id = $1`

	err = tx.GetContext(ctx, &userName, query, user.ID)
	if err != nil {
		return errors.Wrap(err, "error getting user name")
	}

	query = `insert into contract_events(created_at, created_by_user_id, created_by_name, contract_id, description)values(
		$1, $2, $3, $4, $5)`
	_, err = tx.ExecContext(ctx, query, signDate, user.ID, userName, contract.ID, db.ContractEventSigned)
	if err != nil {
		return errors.Wrap(err, "error inserting contract event")
	}

	query = `insert into contract_events(created_at, created_by_user_id, created_by_name, contract_id, description)values(
	now() at time zone 'utc', $1, $2, $3, $4)`
	_, err = tx.ExecContext(ctx, query, user.ID, userName, contract.ID, db.ContractEventESaleComplete)
	if err != nil {
		return errors.Wrap(err, "error inserting contract event")
	}

	err = tx.Commit()

	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "error in committing contract sign")
	}
	return nil
}

// GetESaleContracts fetches contracts by product type id for an e-sale
func GetESaleContracts(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()
	type eContract struct {
		db.Contract
		URL string `json:"url"`
	}
	var contracts []eContract

	eSaleID := chi.URLParam(req, "e_sale_id")
	query := `select c.* from contracts c
    	inner join sales s on c.sale_id = s.id
    	inner join e_sales es on s.id = es.sale_id
        inner join stores st on s.store_id = st.id
        inner join stores_user_versions suv on st.id = suv.store_id
		where es.id = $1 and suv.user_version_id = $2`
	if err := db.Get().SelectContext(ctx, &contracts, query, eSaleID, user.UserVersionID); err != nil {
		util.ReportError(ctx, errors.Wrap(err, "failed to fetch contracts"))
		return http.StatusInternalServerError, ErrorMessage("Error in getting contracts", nil)
	}

	for index := range contracts {
		pdf, err := s3util.PresignedURL(contracts[index].ContractFormS3Region.String, contracts[index].ContractFormS3Bucket.String, contracts[index].ContractFormFileName.String, "", "application/pdf")
		if err != nil {
			util.ReportError(ctx, errors.Wrap(err, "failed to fetch contract url"))
			return http.StatusInternalServerError, ErrorMessage("Error in getting contract url", nil)
		}
		contracts[index].URL = pdf
	}

	return http.StatusOK, map[string]interface{}{
		"contracts": contracts,
	}
}
