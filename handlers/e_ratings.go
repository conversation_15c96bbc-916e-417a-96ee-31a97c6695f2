package handlers

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"
	"whiz/conf"
	"whiz/constants"
	"whiz/db"
	"whiz/dms"
	"whiz/slice"
	"whiz/types"
	"whiz/util"

	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"gopkg.in/guregu/null.v3"
)

const (
	htmlDateFormat           = `2006-01-02`
	tekionDealerPlatformName = "tekion"
)

func getProductRates(
	ctx context.Context,
	rq *db.RateQuery,
	dealerPlatformID int,
	roundup bool,
) ([]db.QuoteProductTypeWithData, error) {
	var quoteProductTypes []db.QuoteProductTypeWithData
	var query string

	classificationsMemo := map[int]*db.Classification{}

	rateSheetIDs, err := db.ActiveRateSheetsForStore(rq.Store.ID, rq.ContractDate)
	if err != nil {
		err = errors.Wrap(err, "error in getting rate sheet list")
		return quoteProductTypes, err
	}

	rbMap, err := db.GetRateBucketMapByID(ctx)
	if err != nil {
		err = errors.WithMessage(err, "error getting rate bucket map")
		// Report the error but continue.  It is non-fatal.
		util.ReportError(ctx, err)
	}

	quoteProductTypesMap := map[int]int{} // key = product_type_id, value = quoteProductTypes index
	quoteProductTypes = []db.QuoteProductTypeWithData{}
	for _, rateSheetID := range rateSheetIDs {
		var productTypeID int
		query = `select p.product_type_id 
			from rate_sheets rs
			join product_variants pv on rs.product_variant_id = pv.id
			join products p on pv.product_id = p.id
			join product_types pt on p.product_type_id = pt.id
			where rs.id = $1`
		err = db.Get().GetContext(ctx, &productTypeID, query, rateSheetID)
		if err != nil {
			err = errors.Wrapf(err, "error getting product type id for rate sheet id %v", rateSheetID)
			return quoteProductTypes, err
		}
		var qptIdx int
		var ok bool
		if qptIdx, ok = quoteProductTypesMap[productTypeID]; !ok {

			query = `select id product_type_id, name, code, position
				from product_types where id = $1`
			var quoteProductType db.QuoteProductTypeWithData
			err = db.Get().GetContext(ctx, &quoteProductType, query, productTypeID)
			if err != nil {
				err = errors.Wrapf(err, "error getting product type (%v) for rate sheet id %v", productTypeID, rateSheetID)
				return quoteProductTypes, err
			}

			quoteProductTypes = append(quoteProductTypes, quoteProductType)
			qptIdx = len(quoteProductTypes) - 1 // NOTE: Not thread-safe
			quoteProductTypesMap[productTypeID] = qptIdx
		}

		quoteProductVariant := db.QuoteProductVariantWithData{}
		query = `select rs.product_variant_id, pv.core_rate_bucket_id, pv.name,
		coalesce(dppv.display_name, pv.display_name) display_name,
			p.additive_mileage, p.position,
			p.transmit_type, p.unidata_code, p.spp_company,
			p.e_rating_product_description, p.e_rating_product_url
			from rate_sheets rs
			join product_variants pv
				on rs.product_variant_id = pv.id
			join products p
				on pv.product_id = p.id
			left join dealer_platform_product_variants dppv
				on (dppv.product_variant_id = pv.id and dppv.dealer_platform_id = $1)
			where rs.id = $2`
		err = db.Get().GetContext(ctx, &quoteProductVariant, query, dealerPlatformID, rateSheetID)
		if err != nil {
			err = errors.Wrapf(err, "error getting product variant for rate sheet id %v", rateSheetID)
			return quoteProductTypes, err
		}

		//Check if PV is enabled for eSales
		isEnabled, err := GetProductVariantEnabledForESales(ctx, quoteProductVariant.ProductVariantID, rq.Store)
		if err != nil {
			err = errors.Wrapf(err, "error getting product variant enabled for id %v", quoteProductVariant.ID)
			return quoteProductTypes, err
		}
		if !isEnabled {
			continue
		}

		classificationList, err := db.FindClassificationList(rateSheetID, rq.ContractDate)
		if err != nil {
			err = errors.Wrapf(err, "error getting classification list for rate sheet id %v with contract date %v", rateSheetID, rq.ContractDate)
			return quoteProductTypes, err
		}
		var classification *db.Classification
		if classificationList != nil {
			var ok bool
			if classification, ok = classificationsMemo[classificationList.ID]; !ok {
				classification, err = db.FindClassification(classificationList.ID, rq.VINRecord, rq.IsNew)
				if err != nil {
					err = errors.Wrapf(err, "error getting classification for rate sheet id %v with contract date %v", rateSheetID, rq.ContractDate)
					return quoteProductTypes, err
				}
				classificationsMemo[classificationList.ID] = classification
			}
			if classification != nil {
				quoteProductVariant.ClassificationID.Valid = true
				quoteProductVariant.ClassificationID.Int64 = int64(classification.ID)
			}
		}

		plans, err := db.FindPlans(rateSheetID, *rq, classification)
		if err != nil {
			err = errors.Wrap(err, "error during FindPlans")
			return quoteProductTypes, err
		}
		if len(plans) < 1 {
			continue
		}
		options, err := db.FindOptions(rateSheetID, *rq, classification)
		if err != nil {
			err = errors.Wrap(err, "error during FindOptions")
			return quoteProductTypes, err
		}
		surcharges, err := db.FindSurcharges(rateSheetID, *rq, classification)
		if err != nil {
			err = errors.Wrap(err, "error during FindSurcharges")
			return quoteProductTypes, err
		}
		adjustments, err := db.FindAdjustments(rateSheetID, *rq)
		if err != nil {
			err = errors.Wrap(err, "error during FindAdjustments")
			return quoteProductTypes, err
		}
		caps, err := db.FindCaps(rateSheetID, *rq)
		if err != nil {
			err = errors.Wrap(err, "error during FindCaps")
			return quoteProductTypes, err
		}
		rules, err := db.FindProductRules(rateSheetID, rq.ContractDate)
		if err != nil {
			err = errors.Wrap(err, "error during FindProductRules")
			return quoteProductTypes, err
		}
		coupons, err := db.FindCoupons(rateSheetID, *rq)
		if err != nil {
			err = errors.Wrap(err, "error during FindCoupons")
			return quoteProductTypes, err
		}
		quoteProductVariant.QuoteCoupons = db.NewQuoteCoupons(coupons)

		// We need to process the rule with current product variant.
		rq.ProductVariantID = strconv.Itoa(quoteProductVariant.ProductVariantID)
		rqParams := rq.GetProductRulesParams()

		quoteProductVariant.QuoteAdjustments = db.NewQuoteAdjustments(adjustments)

		quoteProductVariant.QuotePlans = make([]db.QuotePlanWithAdjustments, len(plans))

		productTypeCode := getProductTypeCode(quoteProductTypes, productTypeID)

		for i, plan := range plans {
			quoteProductVariant.QuotePlans[i], err = db.NewQuotePlanWithAdjustments(
				plan,
				adjustments,
				rq.Odometer,
				productTypeCode,
				rbMap,
			)
			if err != nil {
				err = errors.Wrapf(err, "error during NewQuotePlanWithAdjustments on Plan (%d)", plan.ID)
				return quoteProductTypes, err
			}
			_, err = quoteProductVariant.QuotePlans[i].ProcessRules(rules, rqParams)
			if err != nil {
				err = errors.Wrapf(err, "one or more errors processing rules on Plan (%d)", plan.ID)
				return quoteProductTypes, err
			}
		}

		quoteProductVariant.QuoteOptions = make([]db.QuoteOption, len(options))
		for i, option := range options {
			quoteProductVariant.QuoteOptions[i] = db.NewQuoteOption(option)
			_, err = quoteProductVariant.QuoteOptions[i].ProcessRules(rules, rqParams)
			if err != nil {
				err = errors.Wrapf(err, "one or more errors processing rules on Option (%d)", option.ID)
				return quoteProductTypes, err
			}
		}

		quoteProductVariant.QuoteSurcharges = make([]db.QuoteSurcharge, len(surcharges))
		for i, surcharge := range surcharges {
			quoteProductVariant.QuoteSurcharges[i] = db.NewQuoteSurcharge(surcharge)
			_, err = quoteProductVariant.QuoteSurcharges[i].ProcessRules(rules, rqParams)
			if err != nil {
				err = errors.Wrapf(err, "one or more errors processing rules on Surcharge (%d)", surcharge.ID)
				return quoteProductTypes, err
			}
		}

		quoteProductVariant.QuoteCaps = db.NewQuoteCaps(caps)

		// Get BestCapPrice and BestCapID for each plan
		getter := db.NewPricingFormulaParameterGetter(
			quoteProductVariant.ProductVariantID,
			rq.Store.ID,
			rq.ContractDate,
		)
		for i := range quoteProductVariant.QuotePlans {
			qp := quoteProductVariant.QuotePlans[i]
			dealerCost := qp.GetDealerTotalCost(quoteProductVariant)
			tcaCost := qp.GetTcaTotalCost(quoteProductVariant)

			cap, value, err := db.GetBestQuoteCap(
				ctx,
				quoteProductVariant.QuoteCaps,
				tcaCost,
				dealerCost,
				decimal.NullDecimal{Valid: true, Decimal: rq.FinanceDetails.Amount},
				getter,
				true,
				roundup,
			)
			if errors.Is(err, db.ErrNoPricingFormula) {
				continue
			}
			if err != nil {
				err = errors.WithMessage(err, "error during GetBestQuoteCap")
				return quoteProductTypes, err
			}

			quoteProductVariant.QuotePlans[i].BestCapPrice = decimal.NullDecimal{
				Valid:   true,
				Decimal: value,
			}
			if cap != nil {
				quoteProductVariant.QuotePlans[i].BestCapID = null.IntFrom(int64(cap.CapID))
				quoteProductVariant.QuotePlans[i].PriceToCostRatio = cap.PriceToCostRatio
			}
		}

		quoteProductTypes[qptIdx].QuoteProductVariants = append(quoteProductTypes[qptIdx].QuoteProductVariants, quoteProductVariant)
	}
	return quoteProductTypes, nil
}

func createAPIErr(status int, err error, errCode string, message string) *APIError {
	return &APIError{
		StatusCode:     status,
		Err:            err,
		ErrCode:        errCode,
		DisplayMessage: message,
	}
}

func rateQuery(req *http.Request, dealerPlatform db.DealerPlatform) (db.RateQuery, *APIError) {
	var q db.RateQuery

	dealerID := req.FormValue("dealer_id")
	if dealerID == "" {
		return q, createAPIErr(http.StatusBadRequest, nil, "", "Bad Request: dealer_id is missing")
	}

	ctx := req.Context()
	err := db.Get().Unsafe().GetContext(ctx, &q.Store, `select * from stores where e_rating_dealer_id = $1`, dealerID)
	if err != nil {
		err = errors.Wrapf(err, "error getting store for rate query for dealer id %s", dealerID)
		if err == sql.ErrNoRows {
			return q, createAPIErr(http.StatusNotFound, err, db.ErrCodeDealerNotFound, "Dealer ID not found")
		}
		return q, createAPIErr(http.StatusInternalServerError, err, db.ErrCodeUnexpectedErr, "Database error")
	}

	q.DealType = dealerPlatform.DefaultSaleType

	q.DealerSystem = dealerPlatform.Name

	vin := req.FormValue("vin")
	if vin == "" {
		return q, createAPIErr(http.StatusBadRequest, nil, "", "Bad Request: vin is missing")
	}
	if len(vin) != 17 {
		return q, createAPIErr(http.StatusBadRequest, nil, "", "Bad Request: vin is invalid")
	}

	q.VINRecord, err = VINRecordLookup(ctx, vin)
	if err != nil {
		if serr, ok := err.(*ErrorVIN); ok {
			return q, createAPIErr(serr.Status, serr.Err, serr.ErrCode, serr.ErrorMessage)
		}
		return q, createAPIErr(http.StatusBadRequest, nil, "", "Bad Request: Error finding VIN")
	}

	odometerStr := req.FormValue("odometer")
	if odometerStr == "" {
		return q, createAPIErr(http.StatusBadRequest, nil, "", "Bad Request: odometer is missing")
	}
	q.Odometer, err = strconv.Atoi(odometerStr)
	if err != nil {
		return q, createAPIErr(http.StatusBadRequest, nil, "", "Bad Request: invalid odometer")
	}

	isNew := req.FormValue("is_new")
	if isNew == "" {
		return q, createAPIErr(http.StatusBadRequest, nil, "", "Bad Request: is_new missing")
	}
	q.IsNew = (isNew == "true")

	paymentType := req.FormValue("payment_type")
	if paymentType == "" {
		return q, createAPIErr(http.StatusBadRequest, nil, "", "Bad Request: payment_type missing")
	}
	paymentType = strings.ToLower(paymentType)
	if paymentType == "lease" {
		q.PaymentType = dms.PaymentTypeLease
	} else if paymentType == "cash" {
		q.PaymentType = dms.PaymentTypeCash
	} else if paymentType == "loan" {
		q.PaymentType = dms.PaymentTypeLoan
	} else {
		return q, createAPIErr(http.StatusBadRequest, nil, "", "Bad Request: invalid payment_type")
	}

	isCPO := req.FormValue("is_cpo")
	if isCPO == "" {
		return q, createAPIErr(http.StatusBadRequest, nil, "", "Bad Request: is_cpo is missing")
	}
	q.IsCPO = (isCPO == "true")

	// if isCPO is true, check if the make is certifiable at this store
	if q.IsCPO {
		makeFound := false
		for m, k := range q.Store.CertifiableMakes.Map {
			if q.VINRecord.Make == m && k.Valid {
				makeFound = true
			}
		}
		if !makeFound {
			return q, createAPIErr(http.StatusBadRequest, nil, "", fmt.Sprintf("Bad request: A %s cannot be sold as certified pre-owned at this store", q.VINRecord.Make))
		}
	}

	var dp db.DealerPlatform
	err = db.Get().Unsafe().GetContext(ctx, &dp, `select * from dealer_platforms where id = $1`, dealerPlatform.ID)
	if err != nil {
		err = errors.Wrapf(err, "error getting dealer platform data for dealerPlatform %s", dealerPlatform.Name)
		return q, createAPIErr(http.StatusInternalServerError, err, db.ErrCodeUnexpectedErr, "Database error")
	}

	// optional deal_type
	dealTypeStr := req.FormValue("deal_type")
	if dealTypeStr != "" {
		dealTypeStr = strings.ToLower(dealTypeStr)

		if dealTypeStr == "finance_deal" {
			q.DealType = db.SaleTypeFinanceDeal
		} else if dealTypeStr == "service_ro" {
			q.DealType = db.SaleTypeServiceRO
		} else {
			return q, createAPIErr(http.StatusBadRequest, nil, "", "Bad Request: deal_type is invalid")
		}

		// Check if incoming deal type is allowed by
		if dealTypeAllowed := slice.ContainsString(dp.AllowedSaleTypes, q.DealType); !dealTypeAllowed {
			return q, createAPIErr(http.StatusBadRequest, nil, "", "Bad Request: deal_type is not allowed")
		}
	} else {
		q.DealType = dp.DefaultSaleType
	}

	// optional msrp
	msrpStr := req.FormValue("msrp")
	if msrpStr != "" {
		q.MSRP, err = decimal.NewFromString(msrpStr)
		if err != nil {
			return q, createAPIErr(http.StatusBadRequest, nil, "", "Bad Request: msrp invalid")
		}
	}

	// optional finance_term
	finTermStr := req.FormValue("finance_term")
	if finTermStr != "" {
		q.FinanceDetails.Term, err = strconv.Atoi(finTermStr)
		if err != nil {
			return q, createAPIErr(http.StatusBadRequest, nil, "", "Bad Request: finance_term invalid")
		}
	}

	// optional finance_amount
	finAmtStr := req.FormValue("finance_amount")
	if finAmtStr != "" {
		q.FinanceDetails.Amount, err = decimal.NewFromString(finAmtStr)
		if err != nil {
			return q, createAPIErr(http.StatusBadRequest, nil, "", "Bad Request: finance_amount invalid")
		}
	}

	lenderName := req.FormValue("lender_name")
	var isValidLender bool
	var validatedLenderName string
	if lenderName != "" {
		isValidLender, validatedLenderName, err = isValidESaleLender(ctx, lenderName, q.Store.ID)
		if err != nil {
			return q, createAPIErr(http.StatusInternalServerError, err, db.ErrCodeUnexpectedErr, "Error in validating the lender")
		}
		q.FinanceDetails.Lender = validatedLenderName

		if !isValidLender {
			return q, createAPIErr(http.StatusBadRequest, err, db.ErrCodeInvalidESaleLender, "Bad Request: lender_name invalid")
		}
	}

	// optional key_remotes
	keyRemoteStr := req.FormValue("key_remotes")
	if keyRemoteStr != "" {
		q.KeysRemotes, err = strconv.Atoi(keyRemoteStr)
		if err != nil {
			return q, createAPIErr(http.StatusBadRequest, nil, "", "Bad Request: key_remotes invalid")
		}
	} else { // if key_remotes is not passed default should be 2
		q.KeysRemotes = 2
	}

	if req.FormValue("transaction_date") != "" {
		td, err := time.Parse(htmlDateFormat, req.FormValue("transaction_date"))
		if err != nil {
			return q, createAPIErr(http.StatusBadRequest, err, "", "Bad request: transaction_date invalid")
		}
		q.ContractDate = td
	} else {
		q.ContractDate, err = types.InMountainTime(time.Now())
		if err != nil {
			err = errors.Wrapf(err, "error getting local time in ratequery for dealerPlatform %s", dealerPlatform.Name)
			return q, createAPIErr(http.StatusInternalServerError, err, db.ErrCodeUnexpectedErr, "Error getting local time")
		}
	}

	isCommercialUse := req.FormValue("is_commercial_use")
	q.IsCommercialUse = (isCommercialUse == "true")

	q.HasGAPLicense = true
	q.HasDeal = true
	q.IsOnlineSale = true

	var storeCompany struct {
		Code           string   `db:"code"`
		ESaleGapUserID null.Int `db:"e_sale_gap_user_id"`
	}
	companyQuery := `select c.code as code, s.e_sale_gap_user_id as e_sale_gap_user_id 
		from companies c 
	    join stores s on s.company_id = c.id 
		where s.id = $1`
	err = db.Get().GetContext(ctx, &storeCompany, companyQuery, q.Store.ID)
	if err != nil {
		if err == sql.ErrNoRows {
			return q, createAPIErr(http.StatusBadRequest, nil, db.ErrCodeSaleNotFound, "Sale not found")
		}
		err = errors.Wrapf(err, "error getting company in ratequery for store id %d", q.Store.ID)
		return q, createAPIErr(http.StatusInternalServerError, err, db.ErrCodeUnexpectedErr, "Error getting company")
	}

	q.Company = storeCompany.Code
	q.HasGAPLicense = storeCompany.ESaleGapUserID.Valid

	storeCompanyGroup, err := db.GetCompanyGroupByStoreID(ctx, q.Store.ID)
	if err != nil {
		err = errors.WithMessagef(err, "error getting company group in ratequery for store id %d", q.Store.ID)
		return q, createAPIErr(http.StatusInternalServerError, err, db.ErrCodeUnexpectedErr, "Error getting company group")
	}
	if storeCompanyGroup != nil {
		q.CompanyGroup = strconv.Itoa(storeCompanyGroup.ID)
	}

	return q, nil
}

type surcharge struct {
	SurchargeID  int
	Name         string
	Price        decimal.Decimal
	Selectable   bool
	PlanDuration null.Int
}
type option struct {
	OptionID     int             `json:"option_id"`
	Name         string          `json:"name"`
	OptionGroup  null.String     `json:"option_group"`
	Price        decimal.Decimal `json:"price"`
	Cost         decimal.Decimal `json:"cost"`
	Selectable   bool            `json:"selectable"`
	Preselected  bool            `json:"preselected"`
	PlanDuration null.Int        `json:"plan_duration"`
}
type plan struct {
	PlanID      int             `json:"plan_id"`
	Name        string          `json:"name"`
	Cost        decimal.Decimal `json:"cost"`
	Price       decimal.Decimal `json:"price"`
	TermMonths  int             `json:"term_months"`
	TermMiles   null.Int        `json:"term_miles"`
	Description null.String     `json:"description"`
}
type priceCap struct {
	CapID int             `json:"cap_id"`
	Name  string          `json:"name"`
	Price decimal.Decimal `json:"price"`
}

// will map caps to price except for maintenance, for maintenance cost will be mapped to price
type productVariant struct {
	ProductVariantID int         `json:"product_variant_id"`
	Name             string      `json:"name"`
	Caps             []priceCap  `json:"-"`
	Plans            []plan      `json:"plans"`
	Options          []option    `json:"options"`
	Surcharges       []surcharge `json:"-"`
}

// ProductRates is the response type for the /xtk/pen/rates endpoint
type ProductRates struct {
	ProductTypeID   int              `json:"product_type_id"`
	ProductTypeName string           `json:"product_type_name"`
	ProductTypeCode string           `json:"product_type_code"`
	ProductURL      string           `json:"product_url"`
	ProductVariants []productVariant `json:"product_variants"`
	Position        int              `json:"-"`
}

func quoteProductsToERates(
	financeAmount decimal.Decimal,
	quoteProudcts []db.QuoteProductTypeWithData,
	dealerPlatform *db.DealerPlatform,
	storeID int,
	contractDate time.Time,
	roundup bool,
) ([]ProductRates, error) {
	rates := []ProductRates{}
	rateCount := 0
	for _, quote := range quoteProudcts {
		if len(quote.QuoteProductVariants) == 0 {
			continue // if no product variants, skip
		}
		var r ProductRates
		r.ProductTypeID = quote.ProductTypeID
		r.Position = quote.Position
		r.ProductTypeCode = quote.Code
		r.ProductTypeName = quote.Name
		if quote.QuoteProductVariants[0].ERatingProductURL.Valid {
			r.ProductURL = quote.QuoteProductVariants[0].ERatingProductURL.String
		}
		rates = append(rates, r)
		pvCount := 0
		for _, qpv := range quote.QuoteProductVariants {
			nonFilteredPlanCount := 0
			// check and count if we have non filtered plans for PV
			for _, qp := range qpv.QuotePlans {
				if qp.FilteredByProductRuleID.Valid {
					continue
				} else {
					nonFilteredPlanCount++
				}
			}
			// if there are no plans, skip this product variant
			if nonFilteredPlanCount == 0 {
				continue
			}

			var pv productVariant
			pv.ProductVariantID = qpv.ProductVariantID
			pv.Name = qpv.DisplayName
			rates[rateCount].ProductVariants = append(rates[rateCount].ProductVariants, pv)

			for _, qc := range qpv.QuoteCaps {
				var cp priceCap
				cp.CapID = qc.CapID
				cp.Name = qc.Name
				cp.Price = qc.Amount.Ceil()
				rates[rateCount].ProductVariants[pvCount].Caps = append(rates[rateCount].ProductVariants[pvCount].Caps, cp)
			}

			priceToCostRatio := db.DefaultPriceToCostRatio.Decimal

			// Price is equal to cap for all products other than mnt
			for _, qp := range qpv.QuotePlans {
				// check and skip if plan is filtered by product rule
				if qp.FilteredByProductRuleID.Valid {
					continue
				}
				var pl plan
				pl.PlanID = qp.PlanID
				pl.Name = qp.Name
				pl.TermMonths = qp.Duration
				if qp.Mileage.Valid {
					pl.TermMiles = null.IntFrom(qp.Mileage.Int64)
				}

				tcaCost := qp.GetTcaTotalCost(qpv)
				dealerCost := qp.GetDealerTotalCost(qpv)
				pl.Cost = dealerCost.RoundBank(2)

				// TEMPORARY: for now, we only need to set the Price to the Cost when
				// the dealer platform is Tekion and the Product Type is Service (VSC)
				// This can be removed once Tekion is able to make use of the Cost field
				// for the Service (VSC) products.
				if strings.Contains(strings.ToLower(dealerPlatform.Name), tekionDealerPlatformName) && quote.QuoteProductType.Name == db.ProductTypeService {
					pl.Price = pl.Cost
					if roundup {
						pl.Price = pl.Price.Ceil()
					}
				} else {
					if len(qpv.QuoteCaps) > 0 {
						capResult, cap, err := getBestPriceCap(
							qpv.QuoteCaps,
							tcaCost,
							dealerCost,
							financeAmount,
							pv.ProductVariantID,
							storeID,
							contractDate,
							roundup,
						)
						if err != nil {
							return nil, err
						}
						if !roundup {
							pl.Price = capResult.RoundBank(2)
						} else {
							pl.Price = capResult.Ceil()
						}
						if cap != nil &&
							cap.PriceToCostRatio.Valid &&
							cap.PriceToCostRatio.Decimal.GreaterThan(priceToCostRatio) {

							priceToCostRatio = cap.PriceToCostRatio.Decimal
						}
					} else {
						if !roundup {
							pl.Price = dealerCost.RoundBank(2)
						} else {
							pl.Price = dealerCost.Ceil()
						}
					}
					if pl.Price.Abs().GreaterThanOrEqual(decimal.NewFromFloat(float64(db.CapMaxAmount))) {
						pl.Price = decimal.Zero
					}
				}
				pl.Description = qpv.ERatingProductDescription
				rates[rateCount].ProductVariants[pvCount].Plans = append(rates[rateCount].ProductVariants[pvCount].Plans, pl)
			}

			// The price of pre-selected options is not included in
			// the total contract price for plans that are configured as NoCap
			var isNoCap bool
			for _, cap := range qpv.QuoteCaps {
				if cap.IsNoCap {
					isNoCap = true
					break
				}
			}

			for _, qo := range qpv.QuoteOptions {
				// check and skip option if filtered by Product rule
				if qo.FilteredByProductRuleID.Valid {
					continue
				}
				var opt option
				opt.OptionID = qo.OptionID
				opt.Name = qo.Name.String
				if len(qo.OptionGroup.String) > 0 {
					opt.OptionGroup = null.StringFrom(qo.OptionGroup.String)
				}
				opt.Selectable = qo.Selectable
				opt.Preselected = qo.Preselected
				if !isNoCap || !opt.Preselected {
					opt.Price = qo.Cost.Mul(priceToCostRatio)

					if !roundup {
						opt.Price = opt.Price.RoundBank(2)
					} else {
						opt.Price = opt.Price.Ceil()
					}
				}
				opt.Cost = qo.Cost.RoundBank(2)
				opt.PlanDuration = qo.PlanDuration
				rates[rateCount].ProductVariants[pvCount].Options = append(rates[rateCount].ProductVariants[pvCount].Options, opt)
			}

			for _, qs := range qpv.QuoteSurcharges {
				// check and skip surcharge if filtered by Product rule
				// currently surcharges are not returned in response
				if qs.FilteredByProductRuleID.Valid {
					continue
				}
				var su surcharge
				su.SurchargeID = qs.SurchargeID
				su.Name = qs.Name.String
				su.Price = qs.Cost.Mul(priceToCostRatio)
				if !roundup {
					su.Price = su.Price.RoundBank(2)
				} else {
					su.Price = su.Price.Ceil()
				}
				su.Selectable = qs.Selectable
				su.PlanDuration = qs.PlanDuration

				rates[rateCount].ProductVariants[pvCount].Surcharges = append(rates[rateCount].ProductVariants[pvCount].Surcharges, su)
			}
			pvCount++
		}
		// delete the productType if there are no product variants
		if len(rates[rateCount].ProductVariants) == 0 {
			if rateCount > 0 { // remove the top (current) which has pv null
				rates = rates[:rateCount]
			} else { // this is the only element, then initialize the slice to empty
				rates = []ProductRates{}
			}
		} else {
			rateCount++
		}
	}
	return rates, nil
}

func getPenRatesRequestBody(req *http.Request) interface{} {
	return struct {
		DealerID        string
		VehiclePrice    string
		Vin             string
		Odometer        string
		IsNew           string
		PaymentType     string
		IsCPO           string
		DealType        string
		Msrp            string
		FinanceTerm     string
		FinanceAmount   string
		KeyRemotes      string
		TransactionDate string
		IsCommercialUse string
	}{
		DealerID:        req.FormValue("dealer_id"),
		VehiclePrice:    req.FormValue("vehicle_price"),
		Vin:             req.FormValue("vin"),
		Odometer:        req.FormValue("odometer"),
		IsNew:           req.FormValue("is_new"),
		PaymentType:     req.FormValue("payment_type"),
		IsCPO:           req.FormValue("is_cpo"),
		DealType:        req.FormValue("deal_type"),
		Msrp:            req.FormValue("msrp"),
		FinanceTerm:     req.FormValue("finance_term"),
		FinanceAmount:   req.FormValue("finance_amount"),
		KeyRemotes:      req.FormValue("key_remotes"),
		TransactionDate: req.FormValue("transaction_date"),
		IsCommercialUse: req.FormValue("is_commercial_use"),
	}
}

// PENRates returns all contracts for the vin
func PENRates(_ http.ResponseWriter, req *http.Request, xtkUserCode string) (int, map[string]interface{}) {
	ctx := req.Context()
	const errMsg = "Error in getting rates"

	logPENRequest(ctx, getPenRatesRequestBody(req), xtkUserCode, req.URL.Path, "")
	// Get the dealer platform record associated with the provided xtkUserCode
	var dp db.DealerPlatform
	dp, err := getDealerPlatformForXtkCode(ctx, xtkUserCode)
	if err != nil {
		err = errors.WithMessage(err, "error in getting dealer platform")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(errMsg, nil)
	}

	dealerID := req.FormValue("dealer_id")
	if dealerID == "" {
		return http.StatusBadRequest, ErrorMessage("Bad Request: dealer_id is missing", nil)
	}

	var storeID int
	err = db.Get().GetContext(ctx, &storeID, `select id from stores where e_rating_dealer_id = $1`, dealerID)
	if err != nil {
		err = errors.Wrapf(err, "error getting store for for dealer id %s", dealerID)
		if err == sql.ErrNoRows {
			ReportError(req, err)
			return http.StatusNotFound, ErrorMessage(errMsg, nil)
		}
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(errMsg, nil)
	}

	dealerIDEnabled, err := isDPAccessibleToStore(ctx, storeID, dp.ID)
	if err != nil {
		err = errors.WithMessagef(err, "error in validating store dealer platform for store (%d) dealer(%d)", storeID, dp.ID)
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(errMsg, nil)
	}
	if !dealerIDEnabled {
		return http.StatusBadRequest, ErrorMessage("Dealer platform ID not enabled for this store", nil)
	}

	rq, apiErr := rateQuery(req, dp)
	if apiErr != nil {
		var data map[string]interface{}
		if apiErr.StatusCode != http.StatusBadRequest {
			ReportError(req, apiErr.Err)
		}
		if apiErr.ErrCode != "" {
			data = make(map[string]interface{})
			data["error_code"] = apiErr.ErrCode
		}
		return apiErr.StatusCode, ErrorMessage(apiErr.DisplayMessage, data)
	}

	query := `select id from current_users where first_name='SYSTEM'`
	systemUserID := 0
	err = db.Get().GetContext(ctx, &systemUserID, query)
	if err != nil {
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(errMsg, nil)
	}

	var p newSalePayload
	p.StoreID = rq.Store.ID
	p.ContractDate.Time = rq.ContractDate
	if rq.IsNew {
		p.VehicleCondition = dms.VehicleConditionNew
	} else {
		p.VehicleCondition = dms.VehicleConditionUsed
	}
	p.Odometer = rq.Odometer
	p.SaleType = rq.DealType
	p.Company = rq.Company
	p.CompanyGroup = rq.CompanyGroup

	// Check if should be rounding up based on most recent eSale, if there is one
	roundup, err := shouldeRatingRoundUp(ctx, rq.VINRecord.ID, rq.Store.ID)
	if err != nil {
		ReportError(req, errors.Wrap(err, "error in checking if should round up"))
		return http.StatusInternalServerError, ErrorMessage(errMsg, nil)
	}

	quoteProducts, err := getProductRates(ctx, &rq, dp.ID, roundup)
	if err != nil {
		ReportError(req, errors.Wrap(err, "error in getting rates"))
		return http.StatusInternalServerError, ErrorMessage(errMsg, nil)
	}

	vehiclePriceStr := req.FormValue("vehicle_price")
	var vehiclePrice decimal.Decimal
	if vehiclePriceStr != "" {
		vehiclePrice, err = decimal.NewFromString(vehiclePriceStr)
		if err != nil {
			ReportError(req, errors.Wrap(err, "error in converting vehicle price"))
			return http.StatusInternalServerError, ErrorMessage("Error in getting rates", nil)
		}
	}

	isAdminTest := false
	user, ok := req.Context().Value(constants.ContextKeyCurrentUser).(*db.CurrentUser)
	if ok && user != nil {
		isAdminTest = true
	}

	eSale := createEsalesRecord(&rq, dp.ID, vehiclePrice, isAdminTest, roundup)

	eSaleID, err := insertEsalesRecord(ctx, &eSale)
	if err != nil {
		ReportError(req, errors.Wrap(err, "error in creating e_sales record"))
		return http.StatusInternalServerError, ErrorMessage(errMsg, nil)
	}
	rates, err := quoteProductsToERates(rq.FinanceDetails.Amount, quoteProducts, &dp, p.StoreID, p.ContractDate.Time, roundup)
	if err != nil {
		ReportError(req, errors.Wrap(err, "error in converting quote products to e_rates"))
		return http.StatusInternalServerError, ErrorMessage(errMsg, nil)
	}

	sort.Slice(rates, func(i, j int) bool {
		return rates[i].Position < rates[j].Position
	})

	responseData := map[string]interface{}{"e_sale_id": eSaleID, "rates": rates, "count": len(rates)}

	// log response body
	logPENResponse(ctx, responseData, xtkUserCode, req.URL.Path, "")

	return http.StatusOK, responseData
}

func logPENRequest(ctx context.Context, requestResponse interface{}, userCode string, url string, contractCode string) {
	penConfLogLevel := conf.Get().Pen.LogLevel
	if penConfLogLevel != conf.LogLevelNone {
		d, _ := json.MarshalIndent(requestResponse, "", "  ")

		if logString := getPENRequestLogString(d, userCode, url, contractCode); logString != "" {
			util.LogMessagef(ctx, logString)
		}
	}
}

func getPENRequestLogString(body []byte, userCode string, url string, contractCode string) string {
	return fmt.Sprintf("[PEN API Request]URL: %s UserCode: %s, Contract Code: %s, Body: %s", url, userCode, contractCode, string(body))
}

func logPENResponse(ctx context.Context, requestResponse interface{}, userCode string, url string, contractCode string) {
	penConfLogLevel := conf.Get().Pen.LogLevel
	if penConfLogLevel == conf.LogLevelFull {
		d, _ := json.MarshalIndent(requestResponse, "", "  ")

		if logString := getPENResponseLogString(d, userCode, url, contractCode); logString != "" {
			util.LogMessagef(ctx, logString)
		}
	}
}

func getPENResponseLogString(body []byte, userCode string, url string, contractCode string) string {
	return fmt.Sprintf("[PEN API Response]URL: %s UserCode: %s, Contract Code: %s, Body: %s", url, userCode, contractCode, string(body))
}

// getBestPriceCap based on capCalculator.getBest, also returns if it's a fix price
func getBestPriceCap(
	priceCaps []db.QuoteCap,
	tcaCost decimal.Decimal,
	dealerCost decimal.Decimal,
	financeAmount decimal.Decimal,
	productVariantID int,
	storeID int,
	contractDate time.Time,
	roundup bool,
) (
	decimal.Decimal,
	*db.QuoteCap,
	error,
) {
	if len(priceCaps) == 0 {
		return decimal.NewFromFloat(float64(db.CapMaxAmount)), nil, nil
	}

	ctx := context.Background()
	getter := db.NewPricingFormulaParameterGetter(
		productVariantID,
		storeID,
		contractDate,
	)
	cap, value, err := db.GetBestQuoteCap(
		ctx,
		priceCaps,
		tcaCost,
		dealerCost,
		decimal.NullDecimal{Valid: true, Decimal: financeAmount},
		getter,
		false,
		roundup,
	)
	if err != nil && err != db.ErrNoPricingFormula {
		return decimal.Zero, nil, err
	}

	if cap == nil {
		return decimal.NewFromFloat(float64(db.CapMaxAmount)), nil, nil
	}

	return value, cap, nil
}

func createEsalesRecord(rq *db.RateQuery, dealerPlatformID int, vehiclePrice decimal.Decimal, isAdminTest bool, roundUp bool) db.ESale {
	var eSale db.ESale
	eSale.VINRecordID = rq.VINRecord.ID
	eSale.StoreID = rq.Store.ID
	eSale.DealerPlatformID = dealerPlatformID
	eSale.SaleType = rq.DealType
	eSale.PaymentType = rq.PaymentType
	eSale.FinanceTerm.Int64 = int64(rq.FinanceDetails.Term)
	eSale.FinanceTerm.Valid = true
	eSale.FinanceAmount.Decimal = rq.FinanceDetails.Amount
	eSale.FinanceAmount.Valid = true
	eSale.IsCPO = rq.IsCPO
	eSale.KeysRemotes = rq.KeysRemotes
	eSale.IsNew = rq.IsNew
	eSale.Odometer = rq.Odometer
	eSale.ContractDate = rq.ContractDate
	eSale.PriceRoundUp = roundUp
	if vehiclePrice.GreaterThan(decimal.Zero) {
		eSale.VehiclePrice = decimal.NullDecimal{Decimal: vehiclePrice, Valid: true}
	}
	eSale.MSRP = rq.MSRP
	eSale.IsAdminTest = isAdminTest
	if rq.FinanceDetails.Lender != "" {
		eSale.LenderName.SetValid(rq.FinanceDetails.Lender)
	}
	eSale.IsCommercialUse = rq.IsCommercialUse
	return eSale
}

func insertEsalesRecord(ctx context.Context, es *db.ESale) (int, error) {
	eSaleID := 0
	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		return eSaleID, errors.Wrap(err, "could not begin transaction")
	}

	salesStmt, err := tx.PrepareNamedContext(ctx,
		`insert into e_sales (
			created_at, 
			vin_record_id, 
			store_id, 
			dealer_platform_id,
			sale_type, 
			payment_type, 
			finance_term, 
			finance_amount,
			is_cpo, 
			keys_remotes, 
			is_new, 
			odometer, 
			vehicle_price, 
			msrp, 
			contract_date, 
			is_admin_test, 
			lender_name,
			is_commercial_use,
			price_round_up
		) values (
			now() at time zone 'utc', 
			:vin_record_id, 
			:store_id, 
			:dealer_platform_id,
			:sale_type, 
			:payment_type, 
			:finance_term, 
			:finance_amount, 
			:is_cpo, 
			:keys_remotes,
			:is_new, 
			:odometer, 
			:vehicle_price, 
			:msrp, 
			:contract_date, 
			:is_admin_test, 
			:lender_name, 
			:is_commercial_use,
			:price_round_up
		) 
		returning id`)
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "could not prepare insert into sales")
		return eSaleID, err
	}
	defer func() { _ = salesStmt.Close() }()
	err = salesStmt.GetContext(ctx, &eSaleID, es)
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "could not insert esale")
		return eSaleID, err
	}

	err = tx.Commit()
	if err != nil {
		_ = tx.Rollback()
		return eSaleID, errors.Wrap(err, "error committing transaction")
	}

	return eSaleID, nil
}

// shouldeRatingRoundUp checks to see if rounding should be done if there's a previous
// eSale record for the specified VIN record and store between the cutoff date and the
// rounding went affective.
// For example: Rounding Effective Date is 2025-03-21 and Cutoff Date would be
// 2024-12-24 (i.e. 90 days). If there's an eSale found between 2024-12-24 and 2025-03-21
// then we shouldn't round up, otherwise rounding up should take place.
func shouldeRatingRoundUp(ctx context.Context, vinRecordID int, storeID int) (bool, error) {
	roundup := true

	cfg := conf.Get()

	if !cfg.EnablePriceRoundUp {
		return false, nil
	}

	if cfg.EnablePriceNotRoundUpCheck {
		roundupEffectiveDate, err := time.Parse(util.DateOnly, cfg.PriceRoundUpEffectiveDate)
		if err != nil {
			return roundup, errors.Wrap(err, "error parsing effective date from config")
		}

		cutoffDate := roundupEffectiveDate.AddDate(0, 0, -1*cfg.PriceRoundUpCutoffDays)

		util.LogMessagef(ctx, "Checking for previous eSale between %s and %s", cutoffDate, roundupEffectiveDate)

		query := `
			select 
				id 
			from e_sales
			where
				store_id = $1
				and vin_record_id = $2
				and created_at < $3
				and created_at >= $4
			order by
				created_at desc
			limit 1`

		var saleID int
		err = db.Get().GetContext(ctx, &saleID, query, storeID, vinRecordID, roundupEffectiveDate, cutoffDate)
		if err != nil {
			if err != sql.ErrNoRows {
				return roundup, errors.Wrap(err, "error checking for previous eSale")
			}

			// There is no previous eSale found, so should round up
			return roundup, nil
		}

		if saleID > 0 {
			util.LogMessagef(ctx, "Previous eSale found: %d, so should not round up", saleID)

			// There's a Sale id so we should not round up
			roundup = false
		}
	}

	util.LogMessage(ctx, "Should round up")

	return roundup, nil
}
