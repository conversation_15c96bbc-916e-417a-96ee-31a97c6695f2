package handlers

import (
	"encoding/json"
	"net/http"
	"time"

	"whiz/constants"
	"whiz/db"
	"whiz/session"

	sentryhttp "github.com/getsentry/sentry-go/http"
	"github.com/gorilla/csrf"
	"github.com/pkg/errors"
)

// JSONDate wraps time.Time to give a standard date format to unmarshal a JSON string to Time.
type JSONDate struct {
	time.Time
}

// UnmarshalJSON is needed to implement the json.Unmarshaler interface
func (d *JSONDate) UnmarshalJSON(b []byte) error {
	parse, err := time.Parse(`"2006-01-02"`, string(b))
	if err != nil {
		return errors.Wrap(err, "time.Parse failed")
	}
	*d = JSONDate{parse}
	return nil
}

// ErrEmptyRequestBody is to standardize an empty request body error.
var ErrEmptyRequestBody = errors.New("empty request body")

func jsonReqData(req *http.Request, val interface{}) error {
	if req.Body == nil {
		return ErrEmptyRequestBody
	}
	defer func() { _ = req.Body.Close() }()
	err := json.NewDecoder(req.Body).Decode(val)
	if err != nil {
		return errors.Wrap(err, "json.NewDecoder failed")
	}
	return nil
}

// SentryHandler returns a sentry Handler instance
// which can be used to wrap other handlers using
// Handle or HandleFunc
func SentryHandler() *sentryhttp.Handler {
	return sentryhttp.New(sentryhttp.Options{
		// Allow our own recover function to receive the panic and log it
		Repanic:         true,
		WaitForDelivery: true,
	})
}

// APIHandlerFunc is our special func for JSON requests
type APIHandlerFunc func(http.ResponseWriter, *http.Request) (int, map[string]interface{})

// APIHandler converts the standard HTTP handler func with our custom apiHandler.
// This will render the map[string]interface{} data to JSON.
func APIHandler(handler APIHandlerFunc) func(http.ResponseWriter, *http.Request) {
	return func(w http.ResponseWriter, req *http.Request) {
		w.Header().Set("X-CSRF-Token", csrf.Token(req))
		status, data := handler(w, req)
		_ = r.JSON(w, status, data)
	}
}

// AuthenticatedAPIHandlerFunc is our special func for authenticated JSON requests
type AuthenticatedAPIHandlerFunc func(http.ResponseWriter, *http.Request, db.CurrentUser) (int, map[string]interface{})

// AuthenticatedAPIHandler converts the standard HTTP handler func with our custom AuthenticatedAPIHandler only for authenticated users.
// This will render the map[string]interface{} data to JSON.
func AuthenticatedAPIHandler(handler AuthenticatedAPIHandlerFunc, roles []string) func(http.ResponseWriter, *http.Request) {
	return func(w http.ResponseWriter, req *http.Request) {
		w.Header().Set("X-CSRF-Token", csrf.Token(req))
		user, ok := req.Context().Value(constants.ContextKeyCurrentUser).(*db.CurrentUser)
		if !ok || user == nil {
			_ = r.JSON(w, http.StatusForbidden, ErrorMessage("Session error", nil))
			return
		}
		if !Permitted(*user, roles) {
			_ = r.JSON(w, http.StatusForbidden, ErrorMessage("Not authorized", nil))
			return
		}
		status, data := handler(w, req, *user)
		_ = r.JSON(w, status, data)
	}
}

// AwsAPIHandlerFunc is our special func for JSON requests
type AwsAPIHandlerFunc func(http.ResponseWriter, *http.Request) (int, map[string]interface{})

// AwsAuthAPIHandler converts the standard HTTP handler func with our custom AwsAuthAPIHandler only for authenticated users.
// This will render the map[string]interface{} data to JSON.
func AwsAuthAPIHandler(handler AwsAPIHandlerFunc, impersonationRoles []string) func(http.ResponseWriter, *http.Request) {
	return func(w http.ResponseWriter, req *http.Request) {
		w.Header().Set("X-CSRF-Token", csrf.Token(req))
		status, data := handler(w, req)
		_ = r.JSON(w, status, data)
	}
}

// XtkAPIHandlerFunc is our special func for JSON requests
type XtkAPIHandlerFunc func(http.ResponseWriter, *http.Request, string) (int, map[string]interface{})

// XtkAuthAPIHandler converts the standard HTTP handler func with our custom XtkAuthAPIHandler only for authenticated users.
// This will render the map[string]interface{} data to JSON.
func XtkAuthAPIHandler(handler XtkAPIHandlerFunc, impersonationRoles []string) func(http.ResponseWriter, *http.Request) {
	return func(w http.ResponseWriter, req *http.Request) {
		xtkUserCode, ok := req.Context().Value(db.XtkCodeKey).(string)
		if !ok || xtkUserCode == "" {
			// If there was not an Authorization header with an xtk_code,
			// then look to see if user impersonation is being used.
			user, ok := req.Context().Value(constants.ContextKeyCurrentUser).(*db.CurrentUser)
			if ok && user != nil {
				if !Permitted(*user, impersonationRoles) {
					_ = r.JSON(w, http.StatusForbidden, ErrorMessage("Not authorized", nil))
					return
				}

				// If the user has a role that is permitted for impersonation, then get the xtk_code
				// from the query params.
				xtkUserCode = req.FormValue("xtk_code")
			}
		}

		if xtkUserCode == "" {
			_ = r.JSON(w, http.StatusForbidden, ErrorMessage("Not authorized", nil))
			return
		}

		w.Header().Set("X-CSRF-Token", csrf.Token(req))
		status, data := handler(w, req, xtkUserCode)
		_ = r.JSON(w, status, data)
	}
}

// AsburyAPIHandlerFunc is our special func for JSON requests
type AsburyAPIHandlerFunc func(http.ResponseWriter, *http.Request) (int, map[string]interface{})

// AsburyAuthAPIHandler converts the standard HTTP handler func with our custom AsburyAuthAPIHandler only for authenticated users.
// This will render the map[string]interface{} data to JSON.
func AsburyAuthAPIHandler(handler AsburyAPIHandlerFunc, impersonationRoles []string) func(http.ResponseWriter, *http.Request) {
	return func(w http.ResponseWriter, req *http.Request) {

		w.Header().Set("X-CSRF-Token", csrf.Token(req))
		status, data := handler(w, req)
		_ = r.JSON(w, status, data)
	}
}

// StructToAPIResponse converts any struct to a map[string]interface{}
// so that we can eliminate unnecessary nesting in the response
func StructToAPIResponse(input interface{}) (map[string]interface{}, error) {
	var apiResponse map[string]interface{}
	var err error
	var jsonInput []byte
	if jsonInput, err = json.Marshal(input); err != nil {
		return nil, errors.Wrap(err, "failed to marshal API response")
	}
	if err = json.Unmarshal(jsonInput, &apiResponse); err != nil {
		return nil, errors.Wrap(err, "failed convert struct to API response")
	}
	return apiResponse, nil
}

// Permitted tests if the current session user can access given roles.
// Always true when no roles are given.
// Always false when current session user cannot be loaded
func Permitted(user db.CurrentUser, roles []string) bool {
	// assume nil or empty slice means anyone is permitted
	if len(roles) == 0 {
		return true
	}
	for _, role := range roles {
		if user.HasRole(role) {
			return true
		}
	}
	return false
}

// Session will return the current user and store for the session
func Session(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	s, err := session.GetSession(req, "")
	if err != nil || s == nil {
		if err == nil {
			err = errors.New("no session loaded")
		} else {
			err = errors.Wrap(err, "load session failed")
		}
		ctx := req.Context()
		session.LogErrorMessage(&ctx, "Failed to get session")
		session.ReportError(ctx, err)
		PrepareReauthenticate(w, req)
		return http.StatusNotFound, ErrorMessage("Need to login", nil)
	}

	storeID := s.GetStoreID()

	data, err := sessionData(user.ID, storeID)
	if err != nil {
		err = errors.Wrap(err, "error getting session data")
		ctx := s.GetContext()
		session.ReportError(*ctx, err)
		return http.StatusInternalServerError, ErrorMessage("Error getting session data", nil)
	}

	return http.StatusOK, data
}

// Health just returns the current time
func Health(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	return http.StatusOK, map[string]interface{}{"Time": time.Now()}
}

// NotFoundHandler just returns not found page
func NotFoundHandler(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	return http.StatusNotFound, map[string]interface{}{"error": "API not found"}
}

// HasSSOUser just returns the count of sso user
func HasSSOUser(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	count := 0
	_ = db.Get().Get(&count, "select count(*) as count from current_users where active = true and require_sso = false")
	return http.StatusOK, map[string]interface{}{"count": count}
}

// OktaAPIHandlerFunc is our special func for JSON requests for Okta-authenticated requests
type OktaAPIHandlerFunc func(http.ResponseWriter, *http.Request) (int, APIResponse)

// OktaAPIHandler converts the standard HTTP handler func with our custom OktaAPIHandler.
func OktaAPIHandler(handler OktaAPIHandlerFunc) func(http.ResponseWriter, *http.Request) {
	return func(w http.ResponseWriter, req *http.Request) {
		status, data := handler(w, req)
		_ = r.JSON(w, status, data)
	}
}
