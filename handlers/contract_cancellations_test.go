package handlers

import (
	"testing"
	"time"

	"whiz/db"
	"whiz/types"

	"github.com/lib/pq"
	"github.com/shopspring/decimal"
	"gopkg.in/guregu/null.v3"
)

func Test_cancellationInfo(t *testing.T) {
	effDate := types.JSPQNullDate{}
	effDate.Scan(time.Date(2014, time.March, 29, 0, 0, 0, 0, time.UTC))
	expDate := types.JSPQNullDate{}
	expDate.Scan(time.Date(2020, time.March, 29, 0, 0, 0, 0, time.UTC))
	expirationMileage := null.Int{}
	expirationMileage.Scan(100000)
	cancelDate := types.JSPQDate{}
	cancelDate.Scan(time.Date(2019, time.March, 29, 0, 0, 0, 0, time.UTC))
	financeTerm := null.Int{}
	financeTerm.Scan(60)

	firstPaymentDate := types.JSPQNullDate{}
	firstPaymentDate.Scan(time.Date(2018, time.March, 29, 0, 0, 0, 0, time.UTC))

	request := EstimateRequestInfo{
		CancelContractOptions: CancelContractOptions{
			CancelDate:     cancelDate,
			CurrentDate:    time.Now(),
			CancelReasonID: 1,
			Mileage:        50000,
		},
		Contracts: []int{},
	}

	contract := ContractDetails{
		ID:                       1,
		Code:                     "PPM32",
		EffectiveDate:            effDate,
		ExpirationDate:           expDate,
		EffectiveMileage:         0,
		ExpirationMileage:        expirationMileage,
		StoreCode:                "",
		Price:                    decimal.NewFromFloat(1000),
		Cost:                     decimal.NewFromFloat(500),
		PlanCost:                 decimal.NewFromFloat(500),
		CustomerID:               1,
		Status:                   "",
		FinanceTerm:              financeTerm,
		ProductTypeCode:          "MC",
		ProductTypeName:          "Maintenance",
		ContractFormID:           null.Int{},
		Lender:                   "",
		PaymentType:              "",
		StoreStateCode:           "",
		FirstPaymentDate:         firstPaymentDate,
		PlanDuration:             60,
		CoreRateBucketID:         1,
		RateBucketRefundSettings: rbRefundSettings,
		RateBuckets:              rateBuckets,
	}

	rulePrMiles := db.CancelRule{
		FlatCancelDeductClaims: false,
		CancelCalculations:     types.CancelCalculationRules{{Name: typePrMiles, DeductClaims: true}},
	}

	rulePrDays := db.CancelRule{
		FlatCancelDeductClaims: false,
		CancelCalculations:     types.CancelCalculationRules{{Name: typePrDays, DeductClaims: true}},
	}

	rulePrClaims := db.CancelRule{
		FlatCancelDeductClaims: false,
		CancelCalculations:     types.CancelCalculationRules{{Name: typePrClaims, DeductClaims: true}},
	}

	rule78s := db.CancelRule{
		FlatCancelDeductClaims: false,
		CancelCalculations:     types.CancelCalculationRules{{Name: type78s, DeductClaims: true}},
	}

	ruleContractPrice := db.CancelRule{
		FlatCancelDeductClaims: false,
		CancelCalculations:     types.CancelCalculationRules{{Name: typeContractPrice, DeductClaims: true}},
	}

	ruleFlatCancellable := db.CancelRule{
		FlatCancelDeductClaims: true,
		CancelCalculations:     types.CancelCalculationRules{{Name: typePrMiles, DeductClaims: true}},
	}

	type args struct {
		cancellationRequest   EstimateRequestInfo
		contract              ContractDetails
		rule                  db.CancelRule
		flatCancellable       bool
		cancellationEstimates *CancelContractQuote
	}
	tests := []struct {
		name    string
		args    args
		want    CancelContractQuote
		wantErr bool
	}{
		{name: "Test type pr miles",
			args: args{
				cancellationRequest: request,
				contract:            contract,
				rule:                rulePrMiles,
				flatCancellable:     false,
				cancellationEstimates: &CancelContractQuote{
					Fee:              decimal.NewFromFloat(50),
					ClaimTotalAmount: decimal.Zero,
					ClaimCount:       0,
				},
			},
			want: CancelContractQuote{
				ID:                1,
				Code:              "PPM32",
				ProductTypeCode:   "MC",
				Status:            "",
				ProductTypeName:   "Maintenance",
				StoreCode:         "",
				EffectiveDate:     effDate,
				Cost:              decimal.NewFromFloat(500),
				Price:             decimal.NewFromFloat(1000),
				Factor:            "50% miles used",
				FactorPercent:     decimal.NewFromFloat(0.5),
				Ranking:           0,
				Cancellable:       false,
				Fee:               decimal.NewFromFloat(50),
				PercentMonthsUsed: decimal.NewFromFloat(83.3),
				PercentMilesUsed:  decimal.NewFromFloat(50),
				CustomerRefund:    decimal.NewFromFloat(450),
				StoreRefund:       decimal.NewFromFloat(200),
				ClaimCount:        0,
				ClaimTotalAmount:  decimal.Zero,
			},
			wantErr: false},
		{name: "Test type pr days",
			args: args{
				cancellationRequest: request,
				contract:            contract,
				rule:                rulePrDays,
				flatCancellable:     false,
				cancellationEstimates: &CancelContractQuote{
					Fee:              decimal.NewFromFloat(50),
					ClaimTotalAmount: decimal.Zero,
					ClaimCount:       0,
				},
			},
			want: CancelContractQuote{
				ID:                1,
				Code:              "PPM32",
				ProductTypeCode:   "MC",
				Status:            "",
				ProductTypeName:   "Maintenance",
				StoreCode:         "",
				EffectiveDate:     effDate,
				Cost:              decimal.NewFromFloat(500),
				Price:             decimal.NewFromFloat(1000),
				Factor:            "83.3% days used",
				FactorPercent:     decimal.NewFromFloat(.167),
				Ranking:           0,
				Cancellable:       false,
				Fee:               decimal.NewFromFloat(50),
				PercentMonthsUsed: decimal.NewFromFloat(83.3),
				PercentMilesUsed:  decimal.NewFromFloat(50),
				CustomerRefund:    decimal.NewFromFloat(117),
				StoreRefund:       decimal.NewFromFloat(33.5),
				ClaimCount:        0,
				ClaimTotalAmount:  decimal.Zero,
			},
			wantErr: false},
		{name: "Test type pr claims",
			args: args{
				cancellationRequest: request,
				contract: ContractDetails{
					ID:                       1,
					Code:                     "DPS88",
					EffectiveDate:            effDate,
					ExpirationDate:           expDate,
					EffectiveMileage:         0,
					ExpirationMileage:        expirationMileage,
					StoreCode:                "",
					Price:                    decimal.NewFromFloat(1000),
					Cost:                     decimal.NewFromFloat(500),
					PlanCost:                 decimal.NewFromFloat(500),
					CustomerID:               1,
					Status:                   "",
					FinanceTerm:              financeTerm,
					ProductTypeCode:          "DP",
					ProductTypeName:          "DrivePur",
					ContractFormID:           null.Int{},
					Lender:                   "",
					PaymentType:              "",
					StoreStateCode:           "",
					FirstPaymentDate:         types.JSPQNullDate{},
					PlanDuration:             60,
					ClaimsCount:              1,
					CoreRateBucketID:         1,
					RateBucketRefundSettings: rbRefundSettings,
					RateBuckets:              rateBuckets,
				},
				rule:            rulePrClaims,
				flatCancellable: false,
				cancellationEstimates: &CancelContractQuote{
					Fee:              decimal.NewFromFloat(50),
					ClaimTotalAmount: decimal.NewFromFloat(200),
					ClaimCount:       1,
				},
			},
			want: CancelContractQuote{
				ID:                1,
				Code:              "PPM32",
				ProductTypeCode:   "MC",
				Status:            "",
				ProductTypeName:   "Maintenance",
				StoreCode:         "",
				EffectiveDate:     effDate,
				Cost:              decimal.NewFromFloat(500),
				Price:             decimal.NewFromFloat(1000),
				Factor:            "20% applications used",
				FactorPercent:     decimal.NewFromFloat(0.8),
				Ranking:           0,
				Cancellable:       false,
				Fee:               decimal.NewFromFloat(50),
				PercentMonthsUsed: decimal.NewFromFloat(83.3),
				PercentMilesUsed:  decimal.NewFromFloat(50),
				CustomerRefund:    decimal.NewFromFloat(550),
				StoreRefund:       decimal.NewFromFloat(150),
				ClaimCount:        1,
				ClaimTotalAmount:  decimal.NewFromFloat(200),
			},
			wantErr: false},
		{name: "Test type contract price",
			args: args{
				cancellationRequest: request,
				contract:            contract,
				rule:                ruleContractPrice,
				flatCancellable:     false,
				cancellationEstimates: &CancelContractQuote{
					Fee:              decimal.NewFromFloat(50),
					ClaimTotalAmount: decimal.Zero,
					ClaimCount:       0,
				},
			},
			want: CancelContractQuote{
				ID:                1,
				Code:              "PPM32",
				ProductTypeCode:   "MC",
				Status:            "",
				ProductTypeName:   "Maintenance",
				StoreCode:         "",
				EffectiveDate:     effDate,
				Cost:              decimal.NewFromFloat(500),
				Price:             decimal.NewFromFloat(1000),
				Factor:            "",
				FactorPercent:     decimal.NewFromFloat(1),
				Ranking:           0,
				Cancellable:       false,
				Fee:               decimal.NewFromFloat(50),
				PercentMonthsUsed: decimal.NewFromFloat(83.3),
				PercentMilesUsed:  decimal.NewFromFloat(50),
				CustomerRefund:    decimal.NewFromFloat(950),
				StoreRefund:       decimal.NewFromFloat(450),
				ClaimCount:        0,
				ClaimTotalAmount:  decimal.Zero,
			},
			wantErr: false},
		{name: "Test type rule of 78s",
			args: args{
				cancellationRequest: EstimateRequestInfo{
					CancelContractOptions: CancelContractOptions{
						CancelDate:     cancelDate,
						CancelReasonID: 1,
						Mileage:        50000,
					},
					Contracts: []int{},
				},
				contract: ContractDetails{
					ID:                       1,
					Code:                     "PPM32",
					EffectiveDate:            firstPaymentDate,
					ExpirationDate:           expDate,
					EffectiveMileage:         0,
					ExpirationMileage:        expirationMileage,
					StoreCode:                "",
					Price:                    decimal.NewFromFloat(1000),
					Cost:                     decimal.NewFromFloat(500),
					PlanCost:                 decimal.NewFromFloat(500),
					CustomerID:               1,
					Status:                   "",
					FinanceTerm:              financeTerm,
					ProductTypeCode:          "MC",
					ProductTypeName:          "Maintenance",
					ContractFormID:           null.Int{},
					Lender:                   "",
					PaymentType:              "",
					StoreStateCode:           "",
					FirstPaymentDate:         firstPaymentDate,
					PlanDuration:             60,
					CoreRateBucketID:         1,
					RateBucketRefundSettings: rbRefundSettings,
					RateBuckets:              rateBuckets,
				},
				rule:            rule78s,
				flatCancellable: false,
				cancellationEstimates: &CancelContractQuote{
					Fee:              decimal.NewFromFloat(50),
					ClaimTotalAmount: decimal.Zero,
					ClaimCount:       0,
				},
			},
			want: CancelContractQuote{
				ID:                1,
				Code:              "PPM32",
				ProductTypeCode:   "MC",
				Status:            "",
				ProductTypeName:   "Maintenance",
				StoreCode:         "",
				EffectiveDate:     effDate,
				Cost:              decimal.NewFromFloat(500),
				Price:             decimal.NewFromFloat(1000),
				Factor:            "",
				FactorPercent:     decimal.NewFromFloat(0.6403),
				Ranking:           0,
				Cancellable:       false,
				Fee:               decimal.NewFromFloat(50),
				PercentMonthsUsed: decimal.NewFromFloat(49.93),
				PercentMilesUsed:  decimal.NewFromFloat(50),
				CustomerRefund:    decimal.NewFromFloat(590.3),
				StoreRefund:       decimal.NewFromFloat(270.15),
				ClaimCount:        0,
				ClaimTotalAmount:  decimal.Zero,
			},
			wantErr: false},
		{name: "Test type rule flat cancellable",
			args: args{
				cancellationRequest: EstimateRequestInfo{
					CancelContractOptions: CancelContractOptions{
						CancelDate:     cancelDate,
						CancelReasonID: 1,
						Mileage:        50000,
					},
					Contracts: []int{},
				},
				contract:        contract,
				rule:            ruleFlatCancellable,
				flatCancellable: true,
				cancellationEstimates: &CancelContractQuote{
					Fee:              decimal.NewFromFloat(50),
					ClaimTotalAmount: decimal.Zero,
					ClaimCount:       0,
				},
			},
			want: CancelContractQuote{
				ID:                1,
				Code:              "PPM32",
				ProductTypeCode:   "MC",
				Status:            "",
				ProductTypeName:   "Maintenance",
				StoreCode:         "",
				EffectiveDate:     effDate,
				Cost:              decimal.NewFromFloat(500),
				Price:             decimal.NewFromFloat(1000),
				Factor:            "",
				FactorPercent:     decimal.NewFromFloat(1),
				Ranking:           0,
				Cancellable:       false,
				Fee:               decimal.NewFromFloat(50),
				PercentMonthsUsed: decimal.NewFromFloat(83.3),
				PercentMilesUsed:  decimal.NewFromFloat(50),
				CustomerRefund:    decimal.NewFromFloat(950),
				StoreRefund:       decimal.NewFromFloat(450),
				ClaimCount:        0,
				ClaimTotalAmount:  decimal.Zero,
			},
			wantErr: false},
		{name: "Test type rule flat cancellable - drive pur",
			args: args{
				cancellationRequest: EstimateRequestInfo{
					CancelContractOptions: CancelContractOptions{
						CancelDate:     cancelDate,
						CancelReasonID: 1,
						Mileage:        50000,
					},
					Contracts: []int{},
				},
				contract: ContractDetails{
					ID:                       1,
					Code:                     "DPS88",
					EffectiveDate:            effDate,
					ExpirationDate:           expDate,
					EffectiveMileage:         0,
					ExpirationMileage:        expirationMileage,
					StoreCode:                "",
					Price:                    decimal.NewFromFloat(2000),
					Cost:                     decimal.NewFromFloat(1000),
					PlanCost:                 decimal.NewFromFloat(1000),
					CustomerID:               1,
					Status:                   "",
					FinanceTerm:              financeTerm,
					ProductTypeCode:          "DP",
					ProductTypeName:          "DrivePur",
					ContractFormID:           null.Int{},
					Lender:                   "",
					PaymentType:              "",
					FirstPaymentDate:         types.JSPQNullDate{},
					PlanDuration:             120,
					ClaimsCount:              2,
					CoreRateBucketID:         1,
					RateBucketRefundSettings: rbRefundSettings,
					RateBuckets:              rateBuckets,
				},
				rule:            ruleFlatCancellable,
				flatCancellable: true,
				cancellationEstimates: &CancelContractQuote{
					Fee:              decimal.Zero,
					ClaimTotalAmount: decimal.NewFromFloat(400),
					ClaimCount:       2,
				},
			},
			want: CancelContractQuote{
				ID:                1,
				Code:              "DPS88",
				ProductTypeCode:   "DP",
				Status:            "",
				ProductTypeName:   "DrivePur",
				StoreCode:         "",
				EffectiveDate:     effDate,
				Cost:              decimal.NewFromFloat(1000),
				Price:             decimal.NewFromFloat(2000),
				Factor:            "",
				FactorPercent:     decimal.NewFromFloat(0.8),
				Ranking:           0,
				Cancellable:       false,
				Fee:               decimal.Zero,
				PercentMonthsUsed: decimal.NewFromFloat(83.3),
				PercentMilesUsed:  decimal.NewFromFloat(50),
				CustomerRefund:    decimal.NewFromFloat(1600),
				StoreRefund:       decimal.NewFromFloat(800),
				ClaimCount:        2,
				ClaimTotalAmount:  decimal.NewFromFloat(400),
			},
			wantErr: false},
		{name: "Test type refund breakouts",
			args: args{
				cancellationRequest: request,
				contract: ContractDetails{
					ID:                       1,
					Code:                     "PPM32",
					EffectiveDate:            effDate,
					ExpirationDate:           expDate,
					EffectiveMileage:         0,
					ExpirationMileage:        expirationMileage,
					StoreCode:                "",
					Price:                    decimal.NewFromFloat(520),
					Cost:                     decimal.NewFromFloat(420),
					PlanCost:                 decimal.NewFromFloat(200),
					CustomerID:               1,
					Status:                   "",
					FinanceTerm:              financeTerm,
					ProductTypeCode:          "MC",
					ProductTypeName:          "Maintenance",
					ContractFormID:           null.Int{},
					Lender:                   "",
					PaymentType:              "",
					StoreStateCode:           "",
					FirstPaymentDate:         types.JSPQNullDate{},
					PlanDuration:             60,
					CoreRateBucketID:         1,
					RateBucketRefundSettings: rbRefundSettings,
					RateBuckets:              rateBuckets,
					Adjustments: []db.ContractAdjustment{
						{
							RateBucketID:        3, // PRORATA refunded to rate bucket 2
							Cost:                decimal.NewFromFloat(20.00),
							AffectsContractCost: true,
						},
						{
							RateBucketID:        5, // FULL refunded to rate bucket 5
							Cost:                decimal.NewFromFloat(100),
							AffectsContractCost: true,
						},
						{
							RateBucketID:        21, // Non-refundable
							IsInvoiceable:       true,
							Cost:                decimal.NewFromFloat(100),
							AffectsContractCost: true,
						},
					},
				},
				rule:            rulePrMiles,
				flatCancellable: false,
				cancellationEstimates: &CancelContractQuote{
					Fee:              decimal.NewFromFloat(25),
					ClaimTotalAmount: decimal.NewFromFloat(25),
					ClaimCount:       1,
				},
			},
			want: CancelContractQuote{
				ID:                1,
				Code:              "PPM32",
				ProductTypeCode:   "MC",
				Status:            "",
				ProductTypeName:   "Maintenance",
				StoreCode:         "",
				EffectiveDate:     effDate,
				Cost:              decimal.NewFromFloat(320.0),
				Price:             decimal.NewFromFloat(520.0),
				Factor:            "50% miles used",
				FactorPercent:     decimal.NewFromFloat(0.5),
				Ranking:           0,
				Cancellable:       false,
				Fee:               decimal.NewFromFloat(25),
				PercentMonthsUsed: decimal.NewFromFloat(83.3),
				PercentMilesUsed:  decimal.NewFromFloat(50),
				CustomerRefund:    decimal.NewFromFloat(210),
				StoreRefund:       decimal.NewFromFloat(110),
				ClaimCount:        1,
				ClaimTotalAmount:  decimal.NewFromFloat(25),
				Breakouts: []db.ContractCancellationBreakout{
					db.ContractCancellationBreakout{
						RateBucketID: 1,
						Amount:       decimal.NewFromFloat(200.0),
						RefundType:   null.StringFrom(db.RefundTypeProRata),
					},
				},
			},
			wantErr: false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := cancellationInfo(tt.args.cancellationRequest.CancelContractOptions, tt.args.contract, tt.args.rule, tt.args.flatCancellable, tt.args.cancellationEstimates, nil); (err != nil) != tt.wantErr {
				t.Errorf("cancellationInfo() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.args.cancellationEstimates.Factor != tt.want.Factor {
				t.Errorf("cancellationInfo() factor = %v, want %v", tt.args.cancellationEstimates.Factor, tt.want.Factor)
			}
			if !tt.args.cancellationEstimates.FactorPercent.Equal(tt.want.FactorPercent) {
				t.Errorf("cancellationInfo() factorPercent = %v, want %v", tt.args.cancellationEstimates.FactorPercent, tt.want.FactorPercent)
			}
			if !tt.args.cancellationEstimates.PercentMilesUsed.Equal(tt.want.PercentMilesUsed) {
				t.Errorf("cancellationInfo() PercentMilesUsed = %v, want %v", tt.args.cancellationEstimates.PercentMilesUsed, tt.want.PercentMilesUsed)
			}
			if !tt.args.cancellationEstimates.PercentMonthsUsed.Equal(tt.want.PercentMonthsUsed) {
				t.Errorf("cancellationInfo() PercentMonthsUsed = %v, want %v", tt.args.cancellationEstimates.PercentMonthsUsed, tt.want.PercentMonthsUsed)
			}
			if !tt.args.cancellationEstimates.Fee.Equal(tt.want.Fee) {
				t.Errorf("cancellationInfo() Fee = %v, want %v", tt.args.cancellationEstimates.Fee, tt.want.Fee)
			}
			if !tt.args.cancellationEstimates.StoreRefund.Equal(tt.want.StoreRefund) {
				t.Errorf("cancellationInfo() StoreRefund = %v, want %v", tt.args.cancellationEstimates.StoreRefund, tt.want.StoreRefund)
			}
			if !tt.args.cancellationEstimates.CustomerRefund.Equal(tt.want.CustomerRefund) {
				t.Errorf("cancellationInfo() CustomerRefund = %v, want %v", tt.args.cancellationEstimates.CustomerRefund, tt.want.CustomerRefund)
			}
			if tt.args.cancellationEstimates.ClaimCount != tt.want.ClaimCount {
				t.Errorf("cancellationInfo() ClaimCount = %d, want %d", tt.args.cancellationEstimates.ClaimCount, tt.want.ClaimCount)
			}
			if !tt.args.cancellationEstimates.ClaimTotalAmount.Equal(tt.want.ClaimTotalAmount) {
				t.Errorf("cancellationInfo() ClaimTotalAmount = %v, want %v", tt.args.cancellationEstimates.ClaimTotalAmount, tt.want.ClaimTotalAmount)
			}
			if !tt.args.cancellationEstimates.Cost.Equal(tt.want.Cost) {
				t.Errorf("cancellationInfo() Cost = %v, want %v", tt.args.cancellationEstimates.Cost, tt.want.Cost)
			}
			if !tt.args.cancellationEstimates.Price.Equal(tt.want.Price) {
				t.Errorf("cancellationInfo() Price = %v, want %v", tt.args.cancellationEstimates.Price, tt.want.Price)
			}

		})
	}
}

func Test_cancellationInfoRuleOf78s(t *testing.T) {
	cancelDate := types.JSPQDate{}
	cancelDate.Scan(time.Date(2017, time.September, 2, 0, 0, 0, 0, time.UTC))
	financeTerm := null.Int{}
	financeTerm.Scan(72)

	firstPaymentDate := types.JSPQNullDate{}
	firstPaymentDate.Scan(time.Date(2013, time.October, 5, 0, 0, 0, 0, time.UTC))

	contract := ContractDetails{
		ID:                       1,
		Code:                     "PPM32",
		EffectiveDate:            firstPaymentDate,
		EffectiveMileage:         0,
		StoreCode:                "",
		Price:                    decimal.NewFromFloat(420),
		Cost:                     decimal.NewFromFloat(273),
		PlanCost:                 decimal.NewFromFloat(273),
		CustomerID:               1,
		Status:                   "",
		FinanceTerm:              financeTerm,
		ProductTypeCode:          "MC",
		ProductTypeName:          "Maintenance",
		ContractFormID:           null.Int{},
		Lender:                   "",
		PaymentType:              "",
		StoreStateCode:           "",
		FirstPaymentDate:         firstPaymentDate,
		PlanDuration:             72,
		CoreRateBucketID:         1,
		RateBucketRefundSettings: rbRefundSettings,
		RateBuckets:              rateBuckets,
	}

	rule78s := db.CancelRule{
		FlatCancelDeductClaims: false,
		CancelCalculations:     types.CancelCalculationRules{{Name: type78s, DeductClaims: true}},
	}

	type args struct {
		cancellationRequest   EstimateRequestInfo
		contract              ContractDetails
		rule                  db.CancelRule
		flatCancellable       bool
		cancellationEstimates *CancelContractQuote
	}
	tests := []struct {
		name    string
		args    args
		want    CancelContractQuote
		wantErr bool
	}{
		{name: "Test type rule of 78s",
			args: args{
				cancellationRequest: EstimateRequestInfo{
					CancelContractOptions: CancelContractOptions{
						CancelDate:     cancelDate,
						CancelReasonID: 1,
						Mileage:        50000,
					},
					Contracts: []int{},
				},
				contract:        contract,
				rule:            rule78s,
				flatCancellable: false,
				cancellationEstimates: &CancelContractQuote{
					Fee:              decimal.NewFromFloat(25),
					ClaimTotalAmount: decimal.Zero,
					ClaimCount:       0,
				},
			},

			want: CancelContractQuote{
				Fee:            decimal.NewFromFloat(25),
				Factor:         "",
				FactorPercent:  decimal.NewFromFloat(0.1214),
				Cost:           decimal.NewFromFloat(273),
				Price:          decimal.NewFromFloat(420),
				CustomerRefund: decimal.NewFromFloat(25.99),
				StoreRefund:    decimal.NewFromFloat(8.14),
			},
			wantErr: false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := cancellationInfo(tt.args.cancellationRequest.CancelContractOptions, tt.args.contract, tt.args.rule, tt.args.flatCancellable, tt.args.cancellationEstimates, nil); (err != nil) != tt.wantErr {
				t.Errorf("cancellationInfo() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.args.cancellationEstimates.Factor != tt.want.Factor {
				t.Errorf("cancellationInfo() factor = %v, want %v", tt.args.cancellationEstimates.Factor, tt.want.Factor)
			}
			if !tt.args.cancellationEstimates.FactorPercent.Equal(tt.want.FactorPercent) {
				t.Errorf("cancellationInfo() factorPercent = %v, want %v", tt.args.cancellationEstimates.FactorPercent, tt.want.FactorPercent)
			}
			if !tt.args.cancellationEstimates.Fee.Equal(tt.want.Fee) {
				t.Errorf("cancellationInfo() Fee = %v, want %v", tt.args.cancellationEstimates.Fee, tt.want.Fee)
			}
			if !tt.args.cancellationEstimates.StoreRefund.Equal(tt.want.StoreRefund) {
				t.Errorf("cancellationInfo() StoreRefund = %v, want %v", tt.args.cancellationEstimates.StoreRefund, tt.want.StoreRefund)
			}
			if !tt.args.cancellationEstimates.CustomerRefund.Equal(tt.want.CustomerRefund) {
				t.Errorf("cancellationInfo() CustomerRefund = %v, want %v", tt.args.cancellationEstimates.CustomerRefund, tt.want.CustomerRefund)
			}
			if !tt.args.cancellationEstimates.Cost.Equal(tt.want.Cost) {
				t.Errorf("cancellationInfo() Cost = %v, want %v", tt.args.cancellationEstimates.Cost, tt.want.Cost)
			}
			if !tt.args.cancellationEstimates.Price.Equal(tt.want.Price) {
				t.Errorf("cancellationInfo() Price = %v, want %v", tt.args.cancellationEstimates.Price, tt.want.Price)
			}
		})
	}
}

var rbRefundSettings = []db.RateBucketRefundSetting{
	db.RateBucketRefundSetting{
		RateBucketID:       1,
		RefundRateBucketID: 1,
		RefundType:         db.RefundTypeProRata,
	},
	db.RateBucketRefundSetting{
		RateBucketID:       2,
		RefundRateBucketID: 2,
		RefundType:         db.RefundTypeProRata,
	},
	db.RateBucketRefundSetting{
		RateBucketID:       3,
		RefundRateBucketID: 2,
		RefundType:         db.RefundTypeProRata,
	},
	db.RateBucketRefundSetting{
		RateBucketID:       4,
		RefundRateBucketID: 2,
		RefundType:         db.RefundTypeProRata,
	},
	db.RateBucketRefundSetting{
		RateBucketID:       5,
		RefundRateBucketID: 5,
		RefundType:         db.RefundTypeFull,
	},
	db.RateBucketRefundSetting{
		RateBucketID:       6,
		RefundRateBucketID: 6,
		RefundType:         db.RefundTypeFull,
	},
	db.RateBucketRefundSetting{
		RateBucketID:       14,
		RefundRateBucketID: 14,
		RefundType:         db.RefundTypeProRata,
		StartedOn: types.JSPQNullDate{
			NullTime: pq.NullTime{
				Valid: true,
				Time:  time.Date(2019, 7, 1, 0, 0, 0, 0, time.UTC),
			},
		},
	},
	db.RateBucketRefundSetting{
		RateBucketID:       14,
		RefundRateBucketID: 14,
		RefundType:         db.RefundTypeProRata,
		EndedOn: types.JSPQNullDate{
			NullTime: pq.NullTime{
				Valid: true,
				Time:  time.Date(2019, 6, 30, 0, 0, 0, 0, time.UTC),
			},
		},
	},
}

var rateBuckets = []db.RateBucket{
	db.RateBucket{ID: 1, IsRefundable: true},
	db.RateBucket{ID: 2, IsRefundable: true},
	db.RateBucket{ID: 3, IsRefundable: true},
	db.RateBucket{ID: 4, IsRefundable: true},
	db.RateBucket{ID: 5, IsRefundable: true},
	db.RateBucket{ID: 6, IsRefundable: true},
	db.RateBucket{ID: 11, IsRefundable: false},
	db.RateBucket{ID: 14, IsRefundable: true},
	db.RateBucket{ID: 21, IsRefundable: false},
}
