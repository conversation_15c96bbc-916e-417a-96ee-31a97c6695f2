package handlers

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"image/jpeg"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"
	"whiz/conf"
	"whiz/db"
	"whiz/s3util"
	"whiz/util"

	"github.com/go-chi/chi"
	newrelic "github.com/newrelic/go-agent"
	"github.com/pkg/errors"
	"golang.org/x/image/tiff"
	"gopkg.in/guregu/null.v3"
)

const (
	// RequestSourceTCAConnect is used when the users drag and drop documents into the TCA Connect portal
	RequestSourceTCAConnect = "TCA Connect"
	// RequestSourceAsburyAPI is used when the request comes from the Asbury API
	RequestSourceAsburyAPI = "Asbury API"
)

const (
	// RequestStatusProcessing is the status of a newly created AI cancellation request currently read by the AI engine
	RequestStatusProcessing = "Processing"
	// RequestStatusProcessed is the status of a AI cancellation request which is processed by the AI engine
	RequestStatusProcessed = "Processed"
	// RequestStatusCancelled is the status of a cancelled AI cancellation request
	RequestStatusCancelled = "Cancelled"
	// RequestStatusVerified is the status of a verified AI cancellation request
	RequestStatusVerified = "Verified"
)

type payloadFiles struct {
	FileID   int    `json:"file_id"`
	FilePath string `json:"file_path"`
}
type processingLambdaPayload struct {
	RequestID       int            `json:"request_id"`
	Environment     string         `json:"environment"`
	ConnectHostname string         `json:"connect_hostname"`
	S3Bucket        string         `json:"s3_bucket"`
	UserID          int            `json:"user_id"`
	Files           []payloadFiles `json:"files"`
}

// AICancellationRequestPayload represents the payload for an AI cancellation request
type AICancellationRequestPayload struct {
	FileContent string `json:"file_content"`
	Name        string `json:"name"`
	Description string `json:"description"`
	ContentType string `json:"content_type"`
	Source      string `json:"source"`
}

// AICancellationAttachment represents a single attachment on an AI cancellation request
type AICancellationAttachment struct {
	ID               int    `db:"id" json:"id"`
	AICancellationID int    `db:"ai_cancellation_id" json:"ai_cancellation_id"`
	S3Bucket         string `db:"s3_bucket" json:"s3_bucket"`
	S3FileName       string `db:"s3_file_name" json:"s3_file_name"`
	FileName         string `db:"file_name" json:"file_name"`
	ContentType      string `db:"content_type" json:"content_type"`
	Description      string `db:"description" json:"description"`
	CreatedByUserID  int    `db:"created_by_user_id" json:"created_by_user_id"`
	URL              string `db:"-" json:"url"`
}

func sendProcessingRequest(payload *processingLambdaPayload) error {
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return errors.Wrap(err, "error marshaling payload")
	}
	resp, err := http.Post(
		conf.Get().AICancellation.CancellationServer+"/prod/cancel",
		"application/json",
		bytes.NewReader(payloadBytes),
	)
	if err != nil {
		return errors.Wrap(err, "error sending request to processing lambda")
	}
	defer func() { _ = resp.Body.Close() }()
	if resp.StatusCode != http.StatusOK {
		return errors.New("bad response from processing lambda")
	}
	return nil
}

func convertTIFFToJPEG(ctx context.Context, tiffData []byte) ([]byte, error) {
	img, err := tiff.Decode(bytes.NewReader(tiffData))
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error decoding TIFF image"))
	}

	var jpegBuffer bytes.Buffer
	err = jpeg.Encode(&jpegBuffer, img, &jpeg.Options{Quality: 90})
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error encoding JPEG image"))
	}

	return jpegBuffer.Bytes(), nil
}

// CancellationRequestCreate creates a new cancellation request
func CancellationRequestCreate(ctx context.Context, payload []*AICancellationRequestPayload, user db.CurrentUser) (int, map[string]interface{}) {

	txn := newrelic.FromContext(ctx)
	var aica db.AICancellationAttachment
	var attachments []db.AICancellationAttachment
	for index := range payload {
		aica.FileName = strings.TrimSpace(payload[index].Name)
		if aica.FileName == "" {
			return http.StatusBadRequest, ErrorMessage("Invalid file name", nil)
		}
		if len(payload[index].FileContent) == 0 {
			return http.StatusBadRequest, ErrorMessage("Invalid file content", nil)
		}

		// cancellation attachment payload
		aica.CreatedByUserID = user.ID
		aica.S3Bucket = s3util.Bucket()
		timeStamp := time.Now().Format("20060102150405")
		// Use custom escape function for S3 key to handle both '+' and '#' correctly
		encodedName := s3util.EscapeS3Key(payload[index].Name)
		aica.S3FileName = fmt.Sprintf("ai-cancellations/%s_%s_%s", strconv.Itoa(user.ID), timeStamp, encodedName)
		aica.ContentType = payload[index].ContentType
		aica.Description = payload[index].Description

		// decode base64 encoded file content
		data, err := base64.StdEncoding.DecodeString(payload[index].FileContent)
		if err != nil {
			util.ReportError(ctx, errors.Wrap(err, "error decoding file content"))
			return http.StatusInternalServerError, ErrorMessage("Error decoding file content", nil)
		}

		// upload attachment to S3 bucket
		err = s3util.Put(txn, bytes.NewReader(data), s3util.DefaultRegion, aica.S3Bucket, aica.S3FileName)
		if err != nil {
			err = errors.Wrapf(err, "error uploading cancellation attachment file to %s/%s", aica.S3Bucket, aica.S3FileName)
			util.ReportError(ctx, err)
			return http.StatusInternalServerError, ErrorMessage("Error uploading file", nil)
		}

		// convert TIFF image to JPEG as they are not supported in the browser
		if payload[index].ContentType == "image/tiff" {

			jpegData, err := convertTIFFToJPEG(ctx, data)
			if err != nil {
				util.ReportError(ctx, errors.Wrap(err, "Error converting TIFF to JPEG"))
			}

			// update content type and file name & content
			aica.OriginalFileName = null.StringFrom(aica.FileName)
			aica.OriginalContentType = null.StringFrom(aica.ContentType)
			aica.OriginalS3FileName = null.StringFrom(aica.S3FileName)
			payload[index].Name = strings.TrimSpace(payload[index].Name)
			aica.FileName = strings.Replace(payload[index].Name, ".tiff", ".jpg", -1)
			aica.ContentType = "image/jpeg"
			aica.S3FileName = fmt.Sprintf("ai-cancellations/%s_%s_%s", strconv.Itoa(user.ID), timeStamp, aica.FileName)

			// upload JPEG attachment to S3 bucket
			err = s3util.Put(txn, bytes.NewReader(jpegData), s3util.DefaultRegion, aica.S3Bucket, aica.S3FileName)
			if err != nil {
				err = errors.Wrapf(err, "error uploading cancellation attachment file to %s/%s", aica.S3Bucket, aica.S3FileName)
				util.ReportError(ctx, err)
				return http.StatusInternalServerError, ErrorMessage("Error uploading file", nil)
			}
		}

		attachments = append(attachments, aica)
	}

	tx, err := db.Get().Beginx()
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error starting transaction"))
		return http.StatusInternalServerError, ErrorMessage("Error starting database transaction", nil)
	}
	defer func() { _ = tx.Rollback() }()

	cancellationRequest := &db.AICancellation{
		Source:           payload[0].Source,
		Status:           RequestStatusProcessing,
		CreatedByUserID:  user.ID,
		AssignedToUserID: user.ID, // Assign to the current user by default
	}

	// insert cancellation request into database
	query := `insert into ai_cancellations (source, status, created_by_user_id, assigned_to_user_id)
		values (:source, :status, :created_by_user_id, :assigned_to_user_id) returning id`
	stmt, err := tx.PrepareNamedContext(ctx, query)
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error preparing insert statement"))
		return http.StatusInternalServerError, ErrorMessage("Error preparing insert statement", nil)
	}
	defer func() { _ = stmt.Close() }()
	var id int
	err = stmt.GetContext(ctx, &id, cancellationRequest)
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error inserting cancellation request"))
		return http.StatusInternalServerError, ErrorMessage("Error inserting request", nil)
	}

	// create processing lambda payload
	var fileList []payloadFiles
	lambdaPayload := processingLambdaPayload{
		RequestID:       id,
		Environment:     conf.Get().AppEnv,
		ConnectHostname: conf.Get().Server,
		S3Bucket:        s3util.Bucket(),
		UserID:          user.ID,
		Files:           fileList,
	}

	// insert attachment into database
	for index := range attachments {
		attachment := &db.AICancellationAttachment{
			AICancellationID:    id,
			S3Bucket:            attachments[index].S3Bucket,
			S3FileName:          attachments[index].S3FileName,
			FileName:            attachments[index].FileName,
			ContentType:         attachments[index].ContentType,
			Description:         attachments[index].Description,
			CreatedByUserID:     user.ID,
			OriginalS3FileName:  attachments[index].OriginalS3FileName,
			OriginalFileName:    attachments[index].OriginalFileName,
			OriginalContentType: attachments[index].OriginalContentType,
		}
		attachmentQuery := `insert into ai_cancellation_attachments (ai_cancellation_id, s3_bucket, s3_file_name, file_name,
			content_type, description, created_by_user_id, original_s3_file_name, original_file_name, original_content_type)
		values (:ai_cancellation_id, :s3_bucket, :s3_file_name, :file_name, :content_type, :description,
			:created_by_user_id, :original_s3_file_name, :original_file_name, :original_content_type) returning id`
		stmt, err = tx.PrepareNamedContext(ctx, attachmentQuery)
		if err != nil {
			util.ReportError(ctx, errors.Wrap(err, "error preparing insert statement"))
			return http.StatusInternalServerError, ErrorMessage("error preparing insert statement", nil)
		}
		var attachmentID int
		err = stmt.GetContext(ctx, &attachmentID, attachment)
		if err != nil {
			util.ReportError(ctx, errors.Wrap(err, "error inserting cancellation request"))
			return http.StatusInternalServerError, ErrorMessage("error inserting request", nil)
		}
		lambdaPayload.Files = append(lambdaPayload.Files, payloadFiles{
			FileID:   attachmentID,
			FilePath: url.QueryEscape(attachment.S3FileName),
		})
	}
	defer func() { _ = stmt.Close() }()

	err = tx.Commit()
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error committing transaction"))
		return http.StatusInternalServerError, ErrorMessage("error committing transaction", nil)
	}

	err = sendProcessingRequest(&lambdaPayload)
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error sending processing request"))
		return http.StatusInternalServerError, ErrorMessage("Error connecting to the processing server", nil)
	}

	return http.StatusOK, map[string]interface{}{
		"id": id,
	}
}

// CancellationRequestResubmit resubmits a cancellation request for processing
func CancellationRequestResubmit(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()
	idStr := chi.URLParam(req, "id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return http.StatusBadRequest, ErrorMessage("Invalid request id", nil)
	}

	// Get the cancellation request and its attachments
	var cancellationRequest db.AICancellation
	query := `select id, source, status, created_at
		from ai_cancellations
		where id = $1 and created_by_user_id = $2 and deleted_at is null`

	err = db.Get().GetContext(ctx, &cancellationRequest, query, id, user.ID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return http.StatusBadRequest, ErrorMessage("Incorrect request id", nil)
		}
		util.ReportError(ctx, errors.Wrapf(err, "error getting request with ID %d", id))
		return http.StatusInternalServerError, ErrorMessage("Error getting request", nil)
	}

	// Get attachments for the request
	var attachments []AICancellationAttachment
	query = `select id, s3_bucket, s3_file_name, file_name, content_type
		from ai_cancellation_attachments
		where ai_cancellation_id = $1`

	err = db.Get().SelectContext(ctx, &attachments, query, id)
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error getting attachments"))
		return http.StatusInternalServerError, ErrorMessage("Error getting attachments", nil)
	}

	if len(attachments) == 0 {
		return http.StatusBadRequest, ErrorMessage("No attachments found for the request", nil)
	}

	// Create processing lambda payload
	var fileList []payloadFiles
	lambdaPayload := processingLambdaPayload{
		RequestID:       id,
		Environment:     conf.Get().AppEnv,
		ConnectHostname: conf.Get().Server,
		S3Bucket:        s3util.Bucket(),
		UserID:          user.ID,
		Files:           fileList,
	}

	// Add files to the lambda payload
	for _, attachment := range attachments {
		lambdaPayload.Files = append(lambdaPayload.Files, payloadFiles{
			FileID:   attachment.ID,
			FilePath: url.PathEscape(attachment.S3FileName),
		})
	}

	// Update request status to Processing
	tx, err := db.Get().Beginx()
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error starting transaction"))
		return http.StatusInternalServerError, ErrorMessage("Error starting database transaction", nil)
	}
	defer func() { _ = tx.Rollback() }()

	query = `update ai_cancellations 
		set status = $1, 
			processing_result = null,
			processing_error = null,
			updated_at = now() at time zone 'utc'
		where id = $2`

	_, err = tx.ExecContext(ctx, query, RequestStatusProcessing, id)
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error updating request status"))
		return http.StatusInternalServerError, ErrorMessage("Error updating request status", nil)
	}

	err = tx.Commit()
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error committing transaction"))
		return http.StatusInternalServerError, ErrorMessage("Error committing transaction", nil)
	}

	// Send processing request
	err = sendProcessingRequest(&lambdaPayload)
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error sending processing request"))
		return http.StatusInternalServerError, ErrorMessage("Error connecting to the processing server", nil)
	}

	return http.StatusOK, map[string]interface{}{
		"id":     id,
		"status": "Resubmitted for processing",
	}
}
