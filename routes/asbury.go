package routes

import (
	"whiz/handlers"
	"whiz/middleware"

	middlewareRateLimits "whiz/middleware/ratelimit"

	"github.com/go-chi/chi"
	"github.com/go-chi/cors"
)

// AsburyHandlers sets up a new router with /xtk.
func AsburyHandlers(r chi.Router) {
	s := chi.NewRouter()
	s.Use(middleware.AsburyAuthenticate)

	cors := cors.New(cors.Options{
		AllowedOrigins: []string{"*"},
		AllowedMethods: []string{"POST"},
		AllowedHeaders: []string{"Accept", "Content-Type", "Origin", "X-CSRF-Token"},
		MaxAge:         300,
	})
	s.Use(cors.Handler)
	addRateLimitedAsburyAPIRoute(s, "POST", "/cancellations", middlewareRateLimits.SlidingWindowRateLimitCheck, handlers.AsburyCancelContracts, nil)
	r.<PERSON>("/asbury", s)
}
