package routes

import (
	"whiz/cancelquotehandlers"
	"whiz/conf"
	"whiz/middleware"

	"github.com/go-chi/chi"
	"github.com/go-chi/cors"
)

// LenderHandlers mounts the lender cancelation quotes API routes to the router
func LenderHandlers(r chi.Router) {
	s := chi.NewRouter()

	config := conf.Get()

	// CORS middleware setup
	corsMiddleware := cors.New(cors.Options{
		AllowedOrigins:   []string{config.LenderQuotes.ServerURL},
		AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders:   []string{"Accept", "Authorization", "Content-Type"},
		AllowCredentials: true,
		MaxAge:           300,
	})
	s.Use(corsMiddleware.Handler)

	s.Use(middleware.ValidateLenderToken)

	addOktaAPIRoute(s, "GET", "/contracts/cancellation/reasons", cancelquotehandlers.ContractCancelReasonList)
	addOktaAPIRoute(s, "GET", "/contracts/search", cancelquotehandlers.ContractSearch)
	addOktaAPIRoute(s, "GET", "/contracts/cancellation/quotes", cancelquotehandlers.ContractCancelQuotes)
	addOktaAPIRoute(s, "GET", "/contracts/vin/name/autocomplete", cancelquotehandlers.GetAutocompleteNamesForVINHandler)

	r.Mount("/lender/api", s)
}
