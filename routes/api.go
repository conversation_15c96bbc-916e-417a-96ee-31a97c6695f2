package routes

import (
	"fmt"
	"net/http"
	"strings"
	"whiz/adminhandlers"
	"whiz/conf"
	"whiz/db"
	"whiz/handlers"
	"whiz/middleware"

	"github.com/go-chi/chi"
	"github.com/gorilla/csrf"
)

// cSpell: Ignore ldcs mstr unvoid drivepur fiscdk unexpire bdcro dmsro

// APIHandlers sets up a new router with Authenticate middleware.
// Adds the API routes to that router.
func APIHandlers(r chi.Router) {
	s := chi.NewRouter()
	s.Use(middleware.Authenticate)
	s.Use(middleware.Active)
	config := conf.Get()
	s.Use(csrf.Protect(
		[]byte(config.CSRF.Key),
		csrf.Secure(config.CSRF.Secure),
		csrf.MaxAge(config.CSRF.MaxAge),
		csrf.HttpOnly(true),
		csrf.ErrorHandler(http.HandlerFunc(handlers.APIHandler(handlers.CSRFError))),
	))

	addAuthAPIRoute(s, "GET", "/estimates/makes", handlers.EstimateMakes, []string{})
	addAuthAPIRoute(s, "GET", "/estimates/models", handlers.EstimateModels, []string{})
	addAuthAPIRoute(s, "GET", "/estimates/trims", handlers.EstimateTrims, []string{})
	addAuthAPIRoute(s, "GET", "/estimates/vehicles", handlers.EstimateVehicles, []string{})
	addAuthAPIRoute(s, "POST", "/estimates", handlers.EstimateCreate, []string{db.RoleFinanceDeal})

	addAuthRoute(s, "GET", "/ldcs-redirect", handlers.LDCSRedirect, []string{db.RoleLDCSAccounting, db.RoleLDCSFinance, db.RoleLDCSService})
	addAuthRoute(s, "GET", "/phizz-redirect", handlers.PhizzRedirect, []string{
		db.RoleGAPClaims,
		db.RoleAccounting,
		db.RoleGAPClaimsManager,
		db.RoleAutoClaims,
		db.RoleAutoClaimsManager,
		db.RoleViewOnlyClaims,
	})

	addAPIRoute(s, "GET", "/health", handlers.Health)
	addAPIRoute(s, "GET", "/health/has-sso-user", handlers.HasSSOUser)
	addAuthAPIRoute(s, "GET", "/session", handlers.Session, nil)
	addAuthAPIRoute(s, "PUT", "/session/store", handlers.UpdateStore, nil)

	addAuthAPIRoute(s, "GET", "/dashboard-sales", handlers.DashSales, []string{db.RoleServiceRO, db.RoleBDCRO, db.RoleFinanceDeal, db.RoleDealerViewOnly})
	addAuthAPIRoute(s, "GET", "/dashboard-remit-sales", handlers.DashRemitSales, []string{db.RoleRemit})

	addAuthAPIRoute(s, "GET", "/store-uploads", adminhandlers.StoreUploadsIndex, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/store-uploads", adminhandlers.StoreUploadCreate, []string{db.RoleStoreUploads})
	addAuthAPIRoute(s, "GET", "/store-uploads/{id:[0-9]+}", adminhandlers.StoreUploadShow, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "PUT", "/store-uploads/{id:[0-9]+}", adminhandlers.StoreUploadUpdate, []string{db.RoleStoreUploads})
	addAuthAPIRoute(s, "GET", "/store-uploads/supporting-data", adminhandlers.StoreUploadSupportingData, []string{db.RoleAdminView})
	addAuthRoute(s, "GET", "/store-uploads/{id:[0-9]+}/download", adminhandlers.StoreUploadDownload, []string{db.RoleAdminView})
	addAuthRoute(s, "GET", "/store-uploads/{id:[0-9]+}/view", adminhandlers.StoreUploadView, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "DELETE", "/store-uploads/{id:[0-9]+}/delete", adminhandlers.StoreUploadDelete, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/store-uploads/{store_id:[0-9]+}/get-id-by-name", handlers.GetStoreUploadIDByName, nil)

	addAuthAPIRoute(s, "GET", "/store-resources/{store_id:[0-9]+}", handlers.StoreUploads, nil)

	addAuthAPIRoute(s, "GET", "/reports/remit/{month:[0-9]{4}-[0-9]{2}}", handlers.RemitReport, nil)
	addAuthAPIRoute(s, "GET", "/reports/service-sales/{month:[0-9]{4}-[0-9]{2}}", handlers.ServiceSalesReport, nil)

	addAuthAPIRoute(s, "GET", "/classification-lists", adminhandlers.ClassificationListIndex, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/classification-lists", adminhandlers.ClassificationListCreate, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "GET", "/classification-lists/{id:[0-9]+}", adminhandlers.ClassificationListShow, []string{db.RoleAdminView})
	addAuthRoute(s, "GET", "/classification-lists/{id:[0-9]+}/csv", adminhandlers.ClassificationListAsCSV, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "PUT", "/classification-lists/{id:[0-9]+}", adminhandlers.ClassificationListUpdate, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "POST", "/classification-lists/{id:[0-9]+}/clone", adminhandlers.ClassificationListClone, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "POST", "/classification-lists/{id:[0-9]+}/test-vehicle", adminhandlers.ClassificationListTestVehicle, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/classification-lists/{classification_list_id}/classifications", adminhandlers.ClassificationIndex, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/classification-lists/{classification_list_id}/classifications", adminhandlers.ClassificationCreate, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "POST", "/classification-lists/{classification_list_id}/classification-codes", adminhandlers.ClassificationCodeCreate, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "GET", "/classification-lists/{classification_list_id}/classifications/all", adminhandlers.ClassificationIndexAll, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/classification-lists/{classification_list_id}/classifications/supporting-data", adminhandlers.ClassificationSupportingData, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/classification-lists/{classification_list_id}/classifications/reorder/{make}", adminhandlers.ClassificationReorder, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "GET", "/classification-lists/{classification_list_id}/classifications/{id}", adminhandlers.ClassificationShow, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "PUT", "/classification-lists/{classification_list_id}/classifications/{id}", adminhandlers.ClassificationUpdate, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "DELETE", "/classification-lists/{classification_list_id}/classifications/{id}", adminhandlers.ClassificationDelete, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "GET", "/classification-lists/trims-for-make-and-models", adminhandlers.TrimsForMakesAndModels, []string{db.RoleAdminView})

	addAuthAPIRoute(s, "GET", "/stores", adminhandlers.StoreIndex, []string{db.RoleAdminView, db.RoleDealershipManagement})
	addAuthAPIRoute(s, "POST", "/stores", adminhandlers.StoreCreate, []string{db.RoleProductManager, db.RoleDealershipManagement})
	addAuthAPIRoute(s, "GET", "/stores/{id:[0-9]+}", adminhandlers.StoreShow, []string{db.RoleAdminView, db.RoleDealershipManagement})
	addAuthAPIRoute(s, "PUT", "/stores/{id:[0-9]+}", adminhandlers.StoreUpdate, []string{db.RoleProductManager, db.RoleDealershipManagement})
	addAuthAPIRoute(s, "GET", "/stores/{id:[0-9]+}/supporting-data", adminhandlers.StoreSupportingData, []string{db.RoleDealershipManagement})
	addAuthAPIRoute(s, "GET", "/stores/{id:[0-9]+}/product-variants", adminhandlers.StoreProductVariants, []string{db.RoleAdminView, db.RoleDealershipManagement})
	addAuthAPIRoute(s, "POST", "/stores/{id:[0-9]+}/product-variants", adminhandlers.StoreProductVariantsCreate, []string{db.RoleProductManager, db.RoleDealershipManagement})
	addAuthAPIRoute(s, "GET", "/stores/{id:[0-9]+}/adjustments", adminhandlers.StoreAdjustments, []string{db.RoleAdminView, db.RoleProductManager})
	addAuthAPIRoute(s, "GET", "/stores/{id:[0-9]+}/caps", adminhandlers.StoreCaps, []string{db.RoleAdminView, db.RoleProductManager})
	addAuthAPIRoute(s, "GET", "/stores/{id:[0-9]+}/users", adminhandlers.StoreUsers, []string{db.RoleAdminView, db.RoleProductManager})
	addAuthAPIRoute(s, "GET", "/stores/{id:[0-9]+}/product-taxes", adminhandlers.StoreProductTaxes, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "POST", "/stores/{id:[0-9]+}/end-product-variants", adminhandlers.StoreEndUnEndedProductVariants, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "GET", "/stores/{id:[0-9]+}/e-sales-info", adminhandlers.StoreESalesInfo, []string{db.RoleLDCSAccounting, db.RoleLDCSFinance})
	addAuthAPIRoute(s, "GET", "/admin/stores/{id:[0-9]+}/clp-policy", adminhandlers.CLPPolicyFormIndex, []string{db.RoleDealershipManagement})
	addAuthAPIRoute(s, "GET", "/admin/stores/{id:[0-9]+}/clp-policy/supporting-data", adminhandlers.CLPPolicyFormSupportingData, []string{db.RoleDealershipManagement})
	addAuthAPIRoute(s, "POST", "/admin/stores/{id:[0-9]+}/clp-policy", adminhandlers.CLPPolicyFormCreate, []string{db.RoleProductManager, db.RoleDealershipManagement})
	addAuthRoute(s, "GET", "/admin/stores/{id:[0-9]+}/policy/{policy_id:[0-9]+}/download", adminhandlers.PolicyFileDownload, []string{db.RoleProductManager, db.RoleDealershipManagement})

	addAuthAPIRoute(s, "GET", "/product-types", adminhandlers.ProductTypeIndex, nil)
	addAuthAPIRoute(s, "GET", "/product-types/{id:[0-9]+}", adminhandlers.ProductTypeShow, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/product-types/{id:[0-9]+}/products", adminhandlers.ProductTypeProducts, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/product-types/{id:[0-9]+}/reorder", adminhandlers.ProductTypeReorder, []string{db.RoleProductManager})

	addAuthAPIRoute(s, "GET", "/products", adminhandlers.ProductIndex, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/products/supporting-data", adminhandlers.ProductSupportingData, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/products/{id:[0-9]+}", adminhandlers.ProductShow, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/products/{id:[0-9]+}/product-variants", adminhandlers.ProductsProductVariants, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/products/{id:[0-9]+}/adjustments", adminhandlers.ProductShowAdjustments, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/products", adminhandlers.ProductCreate, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "PUT", "/products/{id:[0-9]+}", adminhandlers.ProductUpdate, []string{db.RoleProductManager})

	addAuthAPIRoute(s, "GET", "/products/{id:[0-9]+}/vehicle-component-lists", adminhandlers.ProductVehicleComponentListIndex, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/product-vehicle-component-lists/{product_id:[0-9]+}/supporting-data", adminhandlers.ProductVehicleComponentListSupportingData, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/products/{id:[0-9]+}/vehicle-component-lists/test", adminhandlers.ProductVehicleComponentListTest, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/products/{id:[0-9]+}/vehicle-component-lists/reorder", adminhandlers.ProductVehicleComponentListsReorder, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "POST", "/product-vehicle-component-lists", adminhandlers.ProductVehicleComponentListCreate, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "GET", "/product-vehicle-component-lists/{id:[0-9]+}", adminhandlers.ProductVehicleComponentListShow, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "PUT", "/product-vehicle-component-lists/{id:[0-9]+}", adminhandlers.ProductVehicleComponentListUpdate, []string{db.RoleProductManager})

	addAuthAPIRoute(s, "GET", "/product-variants", adminhandlers.ProductVariantIndex, []string{db.RoleAdminView})
	addAuthRoute(s, "GET", "/product-variants/csv", adminhandlers.ProductVariantCsv, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/product-variants/supporting-data", adminhandlers.ProductVariantSupportingData, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/product-variants/stores/supporting-data", adminhandlers.ProductVariantStoresSupportingData, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/product-variants/{id:[0-9]+}", adminhandlers.ProductVariantShow, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/product-variants/{id:[0-9]+}/stores", adminhandlers.ProductVariantStores, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/product-variants", adminhandlers.ProductVariantCreate, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "PUT", "/product-variants/{id:[0-9]+}", adminhandlers.ProductVariantUpdate, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "GET", "/product-variants/{id:[0-9]+}/rate-sheets", adminhandlers.ProductVariantRateSheets, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/product-variants/{id:[0-9]+}/rate-sheets", adminhandlers.RateSheetCreate, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "POST", "/product-variants/{id:[0-9]+}/stores", adminhandlers.ProductVariantAddStores, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "POST", "/product-variants/stores", adminhandlers.ProductVariantStoresSave, []string{db.RoleProductManager})

	addAuthAPIRoute(s, "DELETE", "/product-variants-stores/{id:[0-9]+}", adminhandlers.ProductVariantStoreDelete, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "PUT", "/product-variants-stores/{id:[0-9]+}", adminhandlers.ProductVariantStoreUpdate, []string{db.RoleProductManager})

	addAuthAPIRoute(s, "PUT", "/rate-sheets/{id:[0-9]+}", adminhandlers.RateSheetUpdate, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "GET", "/rate-sheets/{id:[0-9]+}", adminhandlers.RateSheetShow, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "POST", "/rate-sheets/{id:[0-9]+}/activate", adminhandlers.ActivateRateSheet, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "POST", "/rate-sheets/{id:[0-9]+}/test", adminhandlers.RateSheetTest, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/rate-sheets/test-supporting-data", adminhandlers.RateSheetTestSupportingData, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/active-rate-sheets/{id:[0-9]+}/deactivate", adminhandlers.ActiveRateSheetDeactivate, []string{db.RoleProductManager})

	addAuthAPIRoute(s, "GET", "/users", adminhandlers.UserIndex, []string{db.RoleUserProvisioning, db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/users", adminhandlers.UserCreate, []string{db.RoleUserProvisioning})
	addAuthAPIRoute(s, "GET", "/users/supporting-data", adminhandlers.UserSupportingData, []string{db.RoleAdminView, db.RoleUserProvisioning})
	addAuthAPIRoute(s, "GET", "/users/{id:[0-9]+}", adminhandlers.UserShow, []string{db.RoleAdminView, db.RoleUserProvisioning})
	addAuthAPIRoute(s, "PUT", "/users/{id:[0-9]+}", adminhandlers.UserUpdate, []string{db.RoleUserProvisioning})
	addAuthAPIRoute(s, "POST", "/users/{id:[0-9]+}/resend-confirm", adminhandlers.UserResendConfirm, []string{db.RoleUserProvisioning})
	addAuthAPIRoute(s, "GET", "/users/{id:[0-9]+}/versions", adminhandlers.UserVersions, []string{db.RoleAdminView, db.RoleUserProvisioning})
	addAuthAPIRoute(s, "POST", "/users/{id:[0-9]+}/send-reset-password", adminhandlers.UserSendResetPassword, []string{db.RoleUserProvisioning})
	addAuthAPIRoute(s, "POST", "/users/{id:[0-9]+}/mstr", adminhandlers.UserCreateMSTRUser, []string{db.RoleUserProvisioning})
	addAuthAPIRoute(s, "DELETE", "/users/{id:[0-9]+}/mstr", adminhandlers.UserDeleteMSTRUser, []string{db.RoleUserProvisioning})
	addAuthAPIRoute(s, "POST", "/users/{id:[0-9]+}/activate", adminhandlers.UserActivate, []string{db.RoleUserProvisioning})
	addAuthAPIRoute(s, "POST", "/users/{id:[0-9]+}/deactivate", adminhandlers.UserDeactivate, []string{db.RoleUserProvisioning})
	addAuthAPIRoute(s, "POST", "/users/{id:[0-9]+}/terminate", adminhandlers.UserTerminate, []string{db.RoleUserProvisioning})
	addAuthAPIRoute(s, "POST", "/users/{id:[0-9]+}/notes", adminhandlers.UserCreateNote, []string{db.RoleUserProvisioning})
	addAuthAPIRoute(s, "GET", "/users/{id:[0-9]+}/notes", adminhandlers.UserNotes, []string{db.RoleAdminView, db.RoleUserProvisioning})
	addAuthAPIRoute(s, "POST", "/users/{id:[0-9]+}/unlock", adminhandlers.UserUnlock, []string{db.RoleUserProvisioning})

	addAuthRoute(s, "GET", "/user-reports", adminhandlers.UserReports, []string{db.RoleAdminView})

	addAuthAPIRoute(s, "POST", "/user-job-title", adminhandlers.JobTitleCreate, []string{db.RoleJobTitleManagement})
	addAuthAPIRoute(s, "PUT", "/job-title/{id:[0-9]+}", adminhandlers.JobTitleUpdate, []string{db.RoleJobTitleManagement})
	addAuthAPIRoute(s, "GET", "/job-titles", adminhandlers.JobTitleIndex, nil)
	addAuthAPIRoute(s, "GET", "/job-title/{id:[0-9]+}", adminhandlers.JobTitle, nil)
	addAuthAPIRoute(s, "PUT", "/job-title/{id:[0-9]+}/toggle-activation", adminhandlers.ToggleJobTitleActivation, []string{db.RoleJobTitleManagement})

	addAuthAPIRoute(s, "GET", "/companies", adminhandlers.CompanyIndex, []string{db.RoleDealershipManagement})
	addAuthAPIRoute(s, "GET", "/companies/supporting-data", adminhandlers.CompanySupportingData, []string{db.RoleDealershipManagement})
	addAuthAPIRoute(s, "GET", "/companies/product-variants/supporting-data", adminhandlers.CompanyProductVariantsSupportingData, []string{db.RoleDealershipManagement})
	addAuthAPIRoute(s, "POST", "/companies/{id:[0-9]+}/product-variants", adminhandlers.CompanyProductVariantsSave, []string{db.RoleDealershipManagement})
	addAuthAPIRoute(s, "DELETE", "/companies/{id:[0-9]+}/product-variants/{pv_id:[0-9]+}", adminhandlers.CompanyProductVariantsDelete, []string{db.RoleDealershipManagement})
	addAuthAPIRoute(s, "GET", "/companies/{id:[0-9]+}", adminhandlers.CompanyShow, []string{db.RoleDealershipManagement})
	addAuthAPIRoute(s, "POST", "/companies", adminhandlers.CompanyCreate, []string{db.RoleProductManager, db.RoleDealershipManagement})
	addAuthAPIRoute(s, "PUT", "/companies/{id:[0-9]+}", adminhandlers.CompanyUpdate, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "GET", "/companies/{id:[0-9]+}/readable-companies-supporting-data", adminhandlers.CompanyReadableCompaniesSupportingData, []string{db.RoleProductManager, db.RoleDealershipManagement})
	addAuthAPIRoute(s, "PUT", "/companies/{id:[0-9]+}/readable-companies", adminhandlers.CompanyUpdateReadableCompanies, []string{db.RoleProductManager, db.RoleDealershipManagement})
	addAuthAPIRoute(s, "GET", "/active-companies", adminhandlers.UserActiveCompanies, []string{db.RoleLDCSAccounting, db.RoleDealerViewOnly, db.RoleDealershipManagement})

	addAuthAPIRoute(s, "GET", "/company-groups", adminhandlers.CompanyGroupIndex, []string{db.RoleDealershipManagement})
	addAuthAPIRoute(s, "GET", "/company-groups/supporting-data", adminhandlers.CompanyGroupSupportingData, []string{db.RoleDealershipManagement})
	addAuthAPIRoute(s, "POST", "/company-groups", adminhandlers.CompanyGroupCreate, []string{db.RoleProductManager, db.RoleDealershipManagement})
	addAuthAPIRoute(s, "GET", "/company-groups/{id:[0-9]+}", adminhandlers.CompanyGroupShow, []string{db.RoleDealershipManagement})
	addAuthAPIRoute(s, "PUT", "/company-groups/{id:[0-9]+}", adminhandlers.CompanyGroupUpdate, []string{db.RoleDealershipManagement})

	addAuthAPIRoute(s, "GET", "/dms/supporting-data", adminhandlers.DMSSupportingData, []string{db.RoleAdminView})

	addAuthAPIRoute(s, "GET", "/dms/deal/{storeCode}/{dealNumber}", handlers.DMSDeal, nil)
	addAuthAPIRoute(s, "GET", "/dms/ro/{storeCode}/{roNumber}", handlers.DMSRO, nil)

	addAuthAPIRoute(s, "GET", "/vin/{vin}", handlers.VINLookup, nil)
	addAuthAPIRoute(s, "GET", "/vin/{vin}/active-contracts", handlers.VINActiveContracts, nil)
	addAuthAPIRoute(s, "GET", "/vehicle-makes/{year:[0-9]{4}}", handlers.MakesForYear, nil)
	addAuthAPIRoute(s, "GET", "/vehicle-models/{year:[0-9]{4}}/{make}", handlers.ModelsForYearAndMake, nil)
	addAuthAPIRoute(s, "GET", "/vehicle-trims/{year:[0-9]{4}}/{make}/{model}", handlers.TrimsForYearMakeAndModel, nil)

	addAuthAPIRoute(s, "GET", "/vin-overrides", adminhandlers.VINOverridesIndex, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "GET", "/vin-overrides/supporting-data", adminhandlers.VINOverrideSupportingData, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "POST", "/vin-overrides", adminhandlers.VINOverridesCreate, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "GET", "/vin-overrides/{id:[0-9]+}", adminhandlers.VINOverridesShow, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "PUT", "/vin-overrides/{id:[0-9]+}", adminhandlers.VINOverridesUpdate, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "DELETE", "/vin-overrides/{id:[0-9]+}", adminhandlers.VINOverridesDelete, []string{db.RoleProductManager})

	addAuthAPIRoute(s, "GET", "/rate-buckets", adminhandlers.RateBucketIndex, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/rate-buckets/supporting-data", adminhandlers.RateBucketSupportingData, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/rate-buckets/{id:[0-9]+}", adminhandlers.RateBucketShow, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/rate-buckets", adminhandlers.RateBucketCreate, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "PUT", "/rate-buckets/{id:[0-9]+}", adminhandlers.RateBucketUpdate, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "GET", "/rate-buckets/{id:[0-9]+}/refund-settings", adminhandlers.RateBucketRefundSettings, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/rate-buckets/refund-settings/supporting-data", adminhandlers.RateBucketRefundSettingSupportingData, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/rate-buckets/refund-settings", adminhandlers.RateBucketRefundSettingsCreate, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "PUT", "/rate-buckets/refund-settings/{id:[0-9]+}", adminhandlers.RateBucketRefundSettingsUpdate, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "DELETE", "/rate-buckets/refund-settings/{id:[0-9]+}", adminhandlers.RateBucketRefundSettingsDelete, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/rate-buckets/{id:[0-9]+}/reinstate-settings", adminhandlers.RateBucketReinstateSettings, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/rate-buckets/reinstate-settings/supporting-data", adminhandlers.RateBucketReinstateSettingSupportingData, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/rate-buckets/reinstate-settings", adminhandlers.RateBucketReinstateSettingsCreate, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "PUT", "/rate-buckets/reinstate-settings/{id:[0-9]+}", adminhandlers.RateBucketReinstateSettingsUpdate, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "DELETE", "/rate-buckets/reinstate-settings/{id:[0-9]+}", adminhandlers.RateBucketReinstateSettingsDelete, []string{db.RoleAdminView})

	addAuthAPIRoute(s, "GET", "/adjustments", adminhandlers.AdjustmentsIndex, []string{db.RoleAdminView})
	addAuthRoute(s, "GET", "/adjustments/csv", adminhandlers.AdjustmentsCsv, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/adjustments", adminhandlers.AdjustmentCreate, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "GET", "/adjustments/supporting-data", adminhandlers.AdjustmentSupportingData, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/adjustments/index-supporting-data", adminhandlers.AdjustmentsIndexSupportingData, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/adjustments/index-product-supporting-data", adminhandlers.AdjustmentsIndexProductSupportingData, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/adjustments/{id:[0-9]+}", adminhandlers.AdjustmentShow, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "PUT", "/adjustments/{id:[0-9]+}", adminhandlers.AdjustmentUpdate, []string{db.RoleAdminView})
	addAuthRoute(s, "GET", "/adjustments/{id:[0-9]+}/download", adminhandlers.AdjustmentCSVDownload, []string{db.RoleProductManager})

	addAuthAPIRoute(s, "GET", "/fees", adminhandlers.FeeIndex, []string{db.RoleAdminView})
	addAuthRoute(s, "GET", "/fees/csv", adminhandlers.FeesCsv, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/fees", adminhandlers.FeeCreate, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "GET", "/fees/supporting-data", adminhandlers.FeeSupportingData, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/fees/index-supporting-data", adminhandlers.FeesIndexSupportingData, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/fees/{id:[0-9]+}", adminhandlers.FeeShow, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "PUT", "/fees/{id:[0-9]+}", adminhandlers.FeeUpdate, []string{db.RoleProductManager})

	addAuthAPIRoute(s, "GET", "/cancel-stop-rules/supporting-data", adminhandlers.CancelStopRulesSupportingData, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/cancel-stop-rules/{id:[0-9]+}", adminhandlers.CancelStopRuleCreate, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "PUT", "/cancel-stop-rules/{id:[0-9]+}", adminhandlers.CancelStopRuleUpdate, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "GET", "/cancel-stop-rules/{id:[0-9]+}", adminhandlers.CancelStopRuleShow, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "PUT", "/cancel-stop-rules/{id:[0-9]+}/reorder", adminhandlers.CancelStopRuleReorder, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "GET", "/cancel-stop-rules", adminhandlers.CancelStopRulesIndex, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "DELETE", "/cancel-stop-rules/{id:[0-9]+}", adminhandlers.CancelStopRuleDelete, []string{db.RoleProductManager})

	addAuthAPIRoute(s, "GET", "/caps", adminhandlers.CapIndex, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/caps/{id:[0-9]+}", adminhandlers.CapShow, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/caps/supporting-data", adminhandlers.CapSupportingData, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/caps", adminhandlers.CapCreate, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "PUT", "/caps/{id:[0-9]+}", adminhandlers.CapUpdate, []string{db.RoleProductManager})

	addAuthAPIRoute(s, "GET", "/cancel-rules/supporting-data", adminhandlers.CancelRulesSupportingData, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/cancel-rules/test/supporting-data", adminhandlers.CancelRulesTestSupportingData, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/cancel-rules/test/{code}/contract-data", adminhandlers.CancelRulesTestContractData, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/cancel-rules/test", adminhandlers.CancelRulesTest, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/cancel-rules", adminhandlers.CancelRulesIndex, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/cancel-rules/{id:[0-9]+}", adminhandlers.CancelRuleCreate, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "PUT", "/cancel-rules/{id:[0-9]+}", adminhandlers.CancelRuleUpdate, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "DELETE", "/cancel-rules/{id:[0-9]+}", adminhandlers.CancelRuleDelete, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "GET", "/cancel-rules/{id:[0-9]+}", adminhandlers.CancelRuleShow, []string{db.RoleAdminView})

	addAuthAPIRoute(s, "GET", "/product-rules/{id:[0-9]+}/supporting-data", adminhandlers.ProductRulesSupportingData, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/product-rules/clone-supporting-data", adminhandlers.ProductRulesCloneSupportingData, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/product-rules", adminhandlers.ProductRulesIndex, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/product-rules/{id:[0-9]+}", adminhandlers.ProductRulesCreate, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "GET", "/product-rules/{id:[0-9]+}", adminhandlers.ProductRuleShow, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "PUT", "/product-rules/{id:[0-9]+}", adminhandlers.ProductRuleUpdate, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "DELETE", "/product-rules/{id:[0-9]+}", adminhandlers.ProductRuleDelete, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "POST", "/product-rules/{id:[0-9]+}/clone", adminhandlers.ProductRulesClone, []string{db.RoleProductManager})
	addAuthRoute(s, "GET", "/product-rules/csv", adminhandlers.ProductRulesAsCsv, []string{db.RoleAdminView})

	addAuthAPIRoute(s, "GET", "/contract-forms/supporting-data", adminhandlers.ContractFormsSupportingData, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/contract-forms", adminhandlers.ContractFormsCreate, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "PUT", "/contract-forms/{id:[0-9]+}", adminhandlers.ContractFormsUpdate, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "GET", "/contract-forms/{id:[0-9]+}", adminhandlers.ContractFormsShow, []string{db.RoleAdminView})
	addAuthRoute(s, "GET", "/contract-forms/{id:[0-9]+}/download", adminhandlers.ContractFormsDownload, []string{db.RoleProductManager})
	addAuthRoute(s, "GET", "/contract-forms/{id:[0-9]+}/stamp-test", adminhandlers.ContractFormsStampTest, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/contract-forms/test", adminhandlers.ContractFormsTest, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/contract-forms/product-types", adminhandlers.ContractFormsProductTypesIndex, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/contract-forms/product-types/{product_type_id:[0-9]+}", adminhandlers.ContractFormsProductTypesShow, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/contract-forms/product-types/{product_type_id:[0-9]+}/reorder", adminhandlers.ContractFormsProductTypesReorder, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "GET", "/contract-forms/product-types/{product_type_id:[0-9]+}/supporting-data", adminhandlers.ContractFormsProductTypesTestSupportingData, []string{db.RoleAdminView})
	addAuthRoute(s, "GET", "/contract-forms/product-types/{product_type_id:[0-9]+}/csv", adminhandlers.ContractFormsProductTypesCsv, []string{db.RoleAdminView})

	addAuthAPIRoute(s, "GET", "/admin/forms/supporting-data", adminhandlers.FormSupportingData, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "GET", "/admin/forms", adminhandlers.FormIndexByType, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "GET", "/admin/forms/{id:[0-9]+}", adminhandlers.FormByID, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "POST", "/admin/forms", adminhandlers.FormSave, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "PUT", "/admin/forms", adminhandlers.FormSave, []string{db.RoleProductManager})
	addAuthRoute(s, "GET", "/admin/forms/{id:[0-9]+}/download", adminhandlers.FormFileDownload, []string{db.RoleProductManager})

	addAuthAPIRoute(s, "GET", "/internal-resources", adminhandlers.InternalResourcesIndex, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/internal-resources", adminhandlers.InternalResourceCreate, []string{db.RoleInternalResources})
	addAuthAPIRoute(s, "GET", "/internal-resources/{id:[0-9]+}", adminhandlers.InternalResourceShow, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "PUT", "/internal-resources/{id:[0-9]+}", adminhandlers.InternalResourceUpdate, []string{db.RoleInternalResources})
	addAuthAPIRoute(s, "GET", "/internal-resources/supporting-data", adminhandlers.InternalResourceSupportingData, []string{db.RoleAdminView})
	addAuthRoute(s, "GET", "/internal-resources/{id:[0-9]+}/download", adminhandlers.InternalResourceDownload, []string{db.RoleAdminView})
	addAuthRoute(s, "GET", "/internal-resources/{id:[0-9]+}/view", adminhandlers.InternalResourceView, []string{db.RoleAdminView})

	addAuthAPIRoute(s, "GET", "/admin/issued-clp-policy", adminhandlers.IssuedClipPolicyIndex, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/admin/issued-clp-policy/supporting-data", adminhandlers.IssuedClipPolicySupportingData, []string{db.RoleAdminView})

	addAuthAPIRoute(s, "GET", "/internal-resources-list", adminhandlers.InternalResources, nil)
	addAuthAPIRoute(s, "GET", "/alpha-cancels-list", adminhandlers.AlphaCancels, []string{db.RoleAdminView})

	addAuthAPIRoute(s, "GET", "/new-sale/supporting-data", handlers.NewSaleSupportingData, nil)
	addAuthAPIRoute(s, "POST", "/new-sale", handlers.CreateNewSale, []string{db.RoleServiceRO, db.RoleBDCRO, db.RoleFinanceDeal})

	addAuthAPIRoute(s, "GET", "/sales/{id:[0-9]+}", handlers.SaleShow, []string{db.RoleServiceRO, db.RoleBDCRO, db.RoleFinanceDeal, db.RoleDealerViewOnly})
	addAuthAPIRoute(s, "GET", "/sales/{id:[0-9]+}/summary", handlers.SaleSummary, []string{db.RoleServiceRO, db.RoleBDCRO, db.RoleFinanceDeal, db.RoleRemit, db.RoleDealerViewOnly})
	addAuthAPIRoute(s, "GET", "/sales/{sale_id:[0-9]+}/{product_type_id:[0-9]+}/contract", handlers.ContractFind, []string{db.RoleServiceRO, db.RoleBDCRO, db.RoleFinanceDeal, db.RoleRemit, db.RoleDealerViewOnly})
	addAuthAPIRoute(s, "GET", "/e-sales/{e_sale_id:[0-9]+}/contracts", handlers.GetESaleContracts, []string{db.RoleServiceRO, db.RoleBDCRO, db.RoleFinanceDeal, db.RoleRemit, db.RoleDealerViewOnly})
	addAuthAPIRoute(s, "GET", "/sales/{sale_id:[0-9]+}/{product_type_id:[0-9]+}/voided-contracts", handlers.ContractFindVoided, []string{db.RoleServiceRO, db.RoleBDCRO, db.RoleFinanceDeal, db.RoleDealerViewOnly})
	addAuthRoute(s, "GET", "/sales/{id:[0-9]+}/spp-contract", handlers.RedirectSPPContract, []string{db.RoleServiceRO, db.RoleBDCRO, db.RoleFinanceDeal, db.RoleDealerViewOnly})
	addAuthAPIRoute(s, "GET", "/sales/{id:[0-9]+}/linked-sales", handlers.SaleLinkedSales, []string{db.RoleServiceRO, db.RoleBDCRO, db.RoleFinanceDeal, db.RoleDealerViewOnly})

	addAuthAPIRoute(s, "POST", "/contracts", handlers.ContractCreate, []string{db.RoleServiceRO, db.RoleFinanceDeal})
	addAuthAPIRoute(s, "GET", "/contracts/remit", handlers.ContractRemitList, []string{db.RoleRemit})
	addAuthAPIRoute(s, "GET", "/contracts/remit-supporting-data", handlers.ContractRemitSupportingData, []string{db.RoleRemit})
	addAuthAPIRoute(s, "PUT", "/contracts/{id:[0-9]+}/lender", handlers.ContractUpdateLender, []string{db.RoleRemit})
	addAuthRoute(s, "GET", "/contracts/{id:[0-9]+}/download", handlers.ContractFormDownload, nil)
	addAuthAPIRoute(s, "POST", "/contracts/{id:[0-9]+}/void", handlers.ContractVoid, []string{db.RoleServiceRO, db.RoleFinanceDeal, db.RoleRemit})
	addAuthAPIRoute(s, "POST", "/contracts/{id:[0-9]+}/unvoid", handlers.ContractUnvoid, []string{db.RoleServiceRO, db.RoleFinanceDeal, db.RoleRemit})
	addAuthAPIRoute(s, "POST", "/contracts/{id:[0-9]+}/remit", handlers.ContractRemit, []string{db.RoleServiceRO, db.RoleRemit})
	addAuthAPIRoute(s, "POST", "/contracts/remit-all", handlers.ContractRemitAll, []string{db.RoleServiceRO, db.RoleRemit})
	addAuthAPIRoute(s, "POST", "/contracts/{id:[0-9]+}/cdk-ddj-send", handlers.ContractCDKDDJSend, []string{db.RoleFinanceDeal})
	addAuthAPIRoute(s, "GET", "/contracts/coverage/{code}", handlers.ContractCoverageDetails, []string{db.RoleLDCSAccounting, db.RoleLDCSService, db.RoleLDCSFinance, db.RoleServiceClosedStore, db.RoleDealerViewOnly, db.RoleAdminView})
	addAuthRoute(s, "POST", "/contracts/coverage/pdf", handlers.ContractCoverageAsPdf, []string{db.RoleLDCSAccounting, db.RoleLDCSService, db.RoleLDCSFinance, db.RoleServiceClosedStore, db.RoleDealerViewOnly, db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/admin/contracts/{code}", adminhandlers.ContractShow, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/admin/contracts/{id:[0-9]+}", adminhandlers.ContractShowByID, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "PUT", "/admin/contracts/{id:[0-9]+}", adminhandlers.ContractUpdate, []string{db.RoleAccountRep, db.RoleAccountRepManager})
	addAuthAPIRoute(s, "GET", "/admin/contracts/update-supporting-data", adminhandlers.ContractUpdateSupportingData, []string{db.RoleAccountRep, db.RoleAccountRepManager})
	addAuthAPIRoute(s, "GET", "/admin/contracts", adminhandlers.ContractIndex, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/admin/contracts/{code}/edit", adminhandlers.ContractEditView, []string{db.RoleAccountRep})
	addAuthAPIRoute(s, "PUT", "/admin/contracts/{code}/edit", adminhandlers.ContractEditSave, []string{db.RoleAccountRep})
	addAuthAPIRoute(s, "GET", "/admin/contracts/{code}/edit/endorsement-form", adminhandlers.ContractEditEndorsement, []string{db.RoleAccountRep})
	addAuthAPIRoute(s, "POST", "/admin/contracts/{contractId:[0-9]+}/attachments", adminhandlers.ContractAttachmentUpload, []string{db.RoleAccountRep, db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/admin/contracts/{contractId:[0-9]+}/attachments", adminhandlers.ContractAttachmentsList,
		[]string{db.RoleAdminView,
			db.RoleLDCSAccounting,
			db.RoleLDCSFinance,
			db.RoleLDCSService,
			db.RoleServiceClosedStore,
			db.RoleDealerViewOnly})
	addAuthRoute(s, "GET", "/admin/contracts/{contractId:[0-9]+}/attachments/{attachmentId:[0-9]+}/download", adminhandlers.ContractAttachmentDownload,
		[]string{db.RoleAdminView,
			db.RoleAccountRep,
			db.RoleServiceClosedStore,
			db.RoleDealerViewOnly})
	addAuthAPIRoute(s, "DELETE", "/admin/contracts/{contractId:[0-9]+}/attachments/{attachmentId:[0-9]+}/delete", adminhandlers.ContractAttachmentDelete, []string{db.RoleAdminView, db.RoleAccountRep})
	addAuthAPIRoute(s, "GET", "/admin/contracts/{contractId:[0-9]+}/notes", adminhandlers.ContractNoteIndex, nil)
	addAuthAPIRoute(s, "POST", "/admin/contracts/{contractId:[0-9]+}/notes", adminhandlers.ContractNoteCreate, []string{db.RoleAccountRep, db.RoleViewOnlyClaims})
	addAuthAPIRoute(s, "PUT", "/admin/contracts/{id:[0-9]+}/expire", adminhandlers.ContractExpire, []string{db.RoleAccountRepManager, db.RoleAccountRepII})
	addAuthAPIRoute(s, "PUT", "/admin/contracts/{id:[0-9]+}/unexpire", adminhandlers.ContractUnExpire, []string{db.RoleAccountRepManager})
	addAuthAPIRoute(s, "GET", "/admin/contracts/{contract_id:[0-9]+}/flags", adminhandlers.ContractFlagIndex, nil)
	addAuthAPIRoute(s, "DELETE", "/admin/contracts/{contract_id:[0-9]+}/flags", adminhandlers.ContractFlagDelete, nil)
	addAuthAPIRoute(s, "GET", "/admin/contracts/{contract_id:[0-9]+}/surcharges-and-adjustments", adminhandlers.ContractSurchargeAndAdjustmentIndex, nil)
	addAuthAPIRoute(s, "POST", "/admin/contracts/{contract_id:[0-9]+}/surcharges-and-adjustments", adminhandlers.ContractSurchargeAndAdjustmentCreate, nil)
	addAuthAPIRoute(s, "GET", "/admin/contracts/coverage/{code}", adminhandlers.ContractCoverageDetails, []string{db.RoleAdminView, db.RoleAutoClaims, db.RoleAutoClaimsManager})
	addAuthRoute(s, "POST", "/admin/contracts/coverage/pdf", handlers.ContractCoverageAsPdf, []string{db.RoleAdminView, db.RoleAutoClaims, db.RoleAutoClaimsManager})
	addAuthAPIRoute(s, "PUT", "/admin/cancellation/reasons/{id:[0-9]+}", adminhandlers.ContractCancelReasonUpdate, []string{db.RoleAccountRepManager})

	addAuthAPIRoute(s, "GET", "/contracts", handlers.ContractIndex, nil)
	addAuthAPIRoute(s, "GET", "/contracts/{id:[0-9]+}", handlers.ContractShowByID, nil)
	addAuthRoute(s, "GET", "/contracts/cancellation/estimate/pdf", handlers.ContractCancellationEstimateListAsPdf, []string{
		db.RoleLDCSFlatCancels,
		db.RoleLDCSAllCancels,
		db.RoleLDCSFinance,
		db.RoleLDCSAccounting,
	})
	addAuthAPIRoute(s, "PUT", "/contracts/cancel", handlers.ContractCancellationUpdateRequest, []string{
		db.RoleLDCSFlatCancels,
		db.RoleLDCSAllCancels,
	})
	addAuthAPIRoute(s, "GET", "/contracts/{id}/cancellation/estimate/quote", handlers.ContractCancellationQuotesList, []string{
		db.RoleLDCSFlatCancels,
		db.RoleLDCSAllCancels,
		db.RoleLDCSFinance,
		db.RoleLDCSService,
		db.RoleLDCSAccounting})
	addAuthRoute(s, "GET", "/contracts/cancellation/estimate/quote/pdf", handlers.ContractCancellationQuoteListAsPdf, []string{
		db.RoleLDCSFlatCancels,
		db.RoleLDCSAllCancels,
		db.RoleLDCSFinance,
		db.RoleLDCSService,
		db.RoleLDCSAccounting})
	addAuthRoute(s, "GET", "/contracts/{id}/cancellation/request-form", handlers.ContractCancellationRequestForm, []string{
		db.RoleLDCSFlatCancels,
		db.RoleLDCSAllCancels,
		db.RoleLDCSFinance,
		db.RoleLDCSService,
		db.RoleLDCSAccounting})
	addAuthAPIRoute(s, "GET", "/contracts/cancellation/reasons", handlers.ContractCancelReasonList, nil)
	addAuthAPIRoute(s, "GET", "/contracts/cancellation/quote-supporting-data", adminhandlers.ContractCancelSupportingData, []string{
		db.RoleLDCSFlatCancels,
		db.RoleLDCSAllCancels,
		db.RoleLDCSFinance,
		db.RoleLDCSService,
		db.RoleAdminView,
		db.RoleLDCSAccounting})
	addAuthAPIRoute(s, "POST", "/contracts/{contractId:[0-9]+}/attachments", handlers.ContractAttachmentUpload, []string{db.RoleRemit})
	addAuthAPIRoute(s, "GET", "/contracts/{contractId:[0-9]+}/attachments", handlers.ContractAttachmentsList,
		[]string{db.RoleRemit,
			db.RoleLDCSAccounting,
			db.RoleAdminView,
			db.RoleAccountRep,
			db.RoleServiceClosedStore,
			db.RoleDealerViewOnly,
			db.RoleLDCSFinance,
			db.RoleLDCSService,
			db.RoleLDCSAccounting})
	addAuthRoute(s, "GET", "/contracts/{contractId:[0-9]+}/attachments/{attachmentId:[0-9]+}/download", handlers.ContractAttachmentDownload,
		[]string{db.RoleRemit,
			db.RoleLDCSAccounting,
			db.RoleAdminView,
			db.RoleAccountRep,
			db.RoleServiceClosedStore,
			db.RoleDealerViewOnly,
			db.RoleLDCSFinance,
			db.RoleLDCSService,
			db.RoleLDCSAccounting})
	addAuthAPIRoute(s, "GET", "/coupons/supporting-data", adminhandlers.CouponSupportingData, nil)
	addAuthAPIRoute(s, "GET", "/coupons", adminhandlers.CouponsIndex, nil)
	addAuthAPIRoute(s, "GET", "/coupons/{id:[0-9]+}", adminhandlers.CouponShow, nil)
	addAuthAPIRoute(s, "POST", "/coupons", adminhandlers.CouponCreate, []string{db.RoleCoupon})
	addAuthAPIRoute(s, "PUT", "/coupons/{id:[0-9]+}", adminhandlers.CouponUpdate, []string{db.RoleCoupon})

	addAuthAPIRoute(s, "GET", "/contract-code-banks", adminhandlers.ContractCodeBanksIndex, nil)
	addAuthAPIRoute(s, "POST", "/contract-code-banks", adminhandlers.ContractCodeBanksNew, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "GET", "/contract-code-banks/{id:[0-9]+}", adminhandlers.ContractCodeBanksShow, nil)
	addAuthAPIRoute(s, "POST", "/contract-code-banks/{id:[0-9]+}/add-codes", adminhandlers.ContractCodeBanksAddCodes, []string{db.RoleProductManager})

	addAuthAPIRoute(s, "GET", "/lenders/supporting-data", adminhandlers.LenderSupportingData, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/lenders", adminhandlers.LenderIndex, []string{db.RoleAdminView})
	addAuthRoute(s, "GET", "/lenders/csv", adminhandlers.LenderCsv, []string{db.RoleAdminView})
	addAuthRoute(s, "GET", "/lenders/digital-reserve-rules/csv", adminhandlers.LenderDigitalReserveRulesCsv, []string{db.RoleAdminView, db.RoleDigitalReservesManagement})
	addAuthAPIRoute(s, "POST", "/lenders", adminhandlers.LenderCreate, []string{db.RoleLenderManagement})
	addAuthAPIRoute(s, "GET", "/lenders/{id:[0-9]+}", adminhandlers.LenderShow, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "PUT", "/lenders/{id:[0-9]+}", adminhandlers.LenderUpdate, []string{db.RoleLenderManagement, db.RoleDigitalReservesManagement})

	addAuthAPIRoute(s, "GET", "/cancellation-lenders", adminhandlers.CancellationLenderIndex, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/cancellation-lenders", adminhandlers.CancellationLenderCreate, []string{db.RoleLenderManagement})
	addAuthAPIRoute(s, "GET", "/cancellation-lenders/{id:[0-9]+}", adminhandlers.CancellationLenderShow, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "PUT", "/cancellation-lenders/{id:[0-9]+}", adminhandlers.CancellationLenderUpdate, []string{db.RoleLenderManagement})

	addAuthAPIRoute(s, "GET", "/pending-lenders/supporting-data", adminhandlers.PendingLendersSupportingData, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/pending-lenders", adminhandlers.PendingLenderIndex, []string{db.RoleAdminView})
	addAuthRoute(s, "GET", "/pending-lenders/csv", adminhandlers.PendingLendersAsCsv, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/pending-lenders/confirm", adminhandlers.PendingLenderConfirm, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "POST", "/pending-lenders/clear", adminhandlers.PendingLenderClear, []string{db.RoleAdminView})

	addAuthAPIRoute(s, "POST", "/user-gap-licenses", adminhandlers.UserGAPLicenseCreate, []string{db.RoleGAPLicenseManager})
	addAuthAPIRoute(s, "GET", "/user-gap-licenses/{id:[0-9]+}", adminhandlers.UserGAPLicenseShow, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "PUT", "/user-gap-licenses/{id:[0-9]+}", adminhandlers.UserGAPLicenseUpdate, []string{db.RoleGAPLicenseManager})

	addAuthAPIRoute(s, "GET", "/admin/sales", adminhandlers.SaleIndex, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/admin/sales/{id:[0-9]+}", adminhandlers.SaleShow, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/admin/sales/{id:[0-9]+}/quote", adminhandlers.SaleQuoteShow, []string{db.RoleAdminView})

	addAuthAPIRoute(s, "GET", "/inspections", handlers.InspectionsList, nil)
	addAuthAPIRoute(s, "GET", "/inspections/{id:[0-9]+}", handlers.InspectionsShow, []string{})
	addAuthAPIRoute(s, "PUT", "/inspections/{id:[0-9]+}", handlers.InspectionsUpdate, []string{db.RoleInspections})
	addAuthAPIRoute(s, "PUT", "/inspections/{id:[0-9]+}/repair", handlers.InspectionsRepair, []string{db.RoleInspections})
	addAuthAPIRoute(s, "POST", "/inspections/{id:[0-9]+}/deactivate", handlers.InspectionsDeactivate, []string{db.RoleInspections})
	addAuthAPIRoute(s, "GET", "/inspections/{vin}/active", handlers.InspectionsActiveByVIN, []string{})
	addAuthAPIRoute(s, "GET", "/inspections/supporting-data", handlers.InspectionsSupportingData, []string{db.RoleInspections})
	addAuthAPIRoute(s, "POST", "/inspections", handlers.InspectionsCreate, []string{db.RoleInspections})
	addAuthAPIRoute(s, "POST", "/inspections-eligibilities", handlers.InspectionEligibilitiesCreate, []string{})

	addAuthAPIRoute(s, "GET", "/admin-inspections", adminhandlers.InspectionsList, []string{db.RoleAccountRepII, db.RoleAccountRepManager})
	addAuthAPIRoute(s, "GET", "/admin-inspections/{id:[0-9]+}", adminhandlers.InspectionsShow, []string{db.RoleAccountRepII, db.RoleAccountRepManager})
	addAuthAPIRoute(s, "PUT", "/admin-inspections/{id:[0-9]+}", adminhandlers.InspectionsUpdate, []string{db.RoleAccountRepII, db.RoleAccountRepManager})
	addAuthAPIRoute(s, "PUT", "/admin-inspections/{id:[0-9]+}/expire", adminhandlers.InspectionsExpire, []string{db.RoleAccountRepII, db.RoleAccountRepManager})
	addAuthAPIRoute(s, "PUT", "/admin-inspections/{id:[0-9]+}/deactivate", adminhandlers.InspectionsDeactivate, []string{db.RoleAccountRepII, db.RoleAccountRepManager})

	addAuthAPIRoute(s, "GET", "/admin/vehicle-components", adminhandlers.VehicleComponentsList, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/admin/vehicle-components/supporting-data", adminhandlers.VehicleComponentsSupportingData, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/admin/vehicle-components", adminhandlers.VehicleComponentsCreate, []string{db.RoleVehicleComponentManager})
	addAuthAPIRoute(s, "GET", "/admin/vehicle-components/{code:[0-9]+}", adminhandlers.VehicleComponentsShow, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "PUT", "/admin/vehicle-components/{code:[0-9]+}", adminhandlers.VehicleComponentsUpdate, []string{db.RoleVehicleComponentManager})

	addAuthAPIRoute(s, "GET", "/admin/stamped-contracts", adminhandlers.StampedContractsFind, []string{db.RoleAdminView})

	addAuthAPIRoute(s, "GET", "/lca/invoices", handlers.InvoicesIndex, []string{db.RoleLDCSAccounting, db.RoleDealerViewOnly})
	addAuthRoute(s, "GET", "/lca/invoices/csv", handlers.InvoicesAsCsv, []string{db.RoleLDCSAccounting, db.RoleDealerViewOnly})
	addAuthRoute(s, "GET", "/lca/invoices/detail-csv", handlers.DetailedInvoicesAsCsv, []string{db.RoleCorporateAccounting})
	addAuthAPIRoute(s, "GET", "/lca/invoices/historical", handlers.HistoricalInvoices, []string{db.RoleLDCSAccounting, db.RoleDealerViewOnly})
	addAuthAPIRoute(s, "GET", "/lca/invoices/{number}", handlers.InvoiceShow, []string{db.RoleLDCSAccounting, db.RoleDealerViewOnly})
	addAuthAPIRoute(s, "GET", "/lca/invoices/historical/{number}", handlers.HistoricalInvoiceShow, []string{db.RoleLDCSAccounting, db.RoleDealerViewOnly})
	addAuthRoute(s, "GET", "/lca/invoices/{number}/csv", handlers.InvoiceAsCsv, []string{db.RoleLDCSAccounting, db.RoleDealerViewOnly})
	addAuthRoute(s, "GET", "/lca/invoices/historical/{number}/csv", handlers.HistoricalInvoiceAsCsv, []string{db.RoleLDCSAccounting, db.RoleDealerViewOnly})
	addAuthRoute(s, "GET", "/lca/invoices/{number}/pdf", handlers.InvoiceAsPdf, []string{db.RoleLDCSAccounting, db.RoleDealerViewOnly})
	addAuthRoute(s, "GET", "/lca/invoices/historical/{number}/pdf", handlers.HistoricalInvoiceAsPdf, []string{db.RoleLDCSAccounting, db.RoleDealerViewOnly})
	addAuthAPIRoute(s, "GET", "/lca/invoice/products", handlers.InvoiceProducts, []string{db.RoleLDCSAccounting, db.RoleDealerViewOnly})

	addAuthAPIRoute(s, "GET", "/lca/check-detail/{check_number}", handlers.CheckDetailList, []string{db.RoleLDCSAccounting, db.RoleLDCSService, db.RoleServiceClosedStore, db.RoleDealerViewOnly})
	addAuthAPIRoute(s, "GET", "/lca/checks", handlers.CheckList, []string{db.RoleLDCSAccounting, db.RoleLDCSService, db.RoleServiceClosedStore, db.RoleDealerViewOnly})
	addAuthRoute(s, "GET", "/lca/check-detail/{check_number}/csv", handlers.CheckDetailsCsv, []string{db.RoleLDCSAccounting, db.RoleLDCSService, db.RoleServiceClosedStore, db.RoleDealerViewOnly})
	addAuthRoute(s, "GET", "/lca/check-detail/{check_number}/pdf", handlers.CheckDetailsPdf, []string{db.RoleLDCSAccounting, db.RoleLDCSService, db.RoleServiceClosedStore, db.RoleDealerViewOnly})

	addAuthAPIRoute(s, "GET", "/lca/denied-claims", handlers.DeniedClaimList, []string{db.RoleLDCSAccounting, db.RoleLDCSService, db.RoleDealerViewOnly})
	addAuthRoute(s, "GET", "/lca/denied-claims/csv", handlers.DeniedClaimsCsv, []string{db.RoleLDCSAccounting, db.RoleLDCSService, db.RoleDealerViewOnly})
	addAuthRoute(s, "GET", "/lca/denied-claims/pdf", handlers.DeniedClaimsPdf, []string{db.RoleLDCSAccounting, db.RoleLDCSService, db.RoleDealerViewOnly})
	addAuthAPIRoute(s, "GET", "/lca/denied-claims/products", handlers.DeniedClaimProducts, []string{db.RoleLDCSAccounting, db.RoleLDCSService, db.RoleDealerViewOnly})

	addAuthAPIRoute(s, "GET", "/lca/sales", handlers.LCASalesList, []string{db.RoleLDCSService, db.RoleDealerViewOnly})
	addAuthRoute(s, "GET", "/lca/sales/csv", handlers.LCASalesAsCsv, []string{db.RoleLDCSService, db.RoleDealerViewOnly})
	addAuthRoute(s, "GET", "/lca/sales/pdf", handlers.LCASalesAsPdf, []string{db.RoleLDCSService, db.RoleDealerViewOnly})

	addAuthAPIRoute(s, "POST", "/lca/claims/maintenance", handlers.LCAMaintenanceCreate, []string{db.RoleLDCSService, db.RoleServiceClosedStore})
	addAuthAPIRoute(s, "POST", "/lca/claims/drivepur", handlers.LCADrivePurCreate, []string{db.RoleLDCSService, db.RoleServiceClosedStore})

	addAuthAPIRoute(s, "GET", "/lca/claims", handlers.WIPClaimIndex, []string{db.RoleLDCSService, db.RoleLDCSAccounting, db.RoleLDCSFinance, db.RoleServiceClosedStore, db.RoleDealerViewOnly})
	addAuthRoute(s, "GET", "/lca/claims/csv", handlers.WIPClaimCsv, []string{db.RoleLDCSService, db.RoleLDCSAccounting, db.RoleLDCSFinance, db.RoleServiceClosedStore, db.RoleDealerViewOnly})
	addAuthAPIRoute(s, "POST", "/lca/claims/documents", handlers.WIPClaimSaveDocument, []string{db.RoleLDCSService, db.RoleLDCSAccounting, db.RoleServiceClosedStore})
	addAuthAPIRoute(s, "GET", "/lca/claims/{id:[0-9]+}/documents", handlers.WIPClaimDocumentIndex, []string{db.RoleLDCSService, db.RoleLDCSAccounting, db.RoleLDCSFinance, db.RoleServiceClosedStore, db.RoleDealerViewOnly})
	addAuthRoute(s, "GET", "/lca/claims/documents/{id:[0-9]+}", handlers.WIPClaimDocumentDownload, []string{db.RoleLDCSService, db.RoleLDCSAccounting, db.RoleLDCSFinance, db.RoleServiceClosedStore, db.RoleDealerViewOnly})
	addAuthAPIRoute(s, "POST", "/lca/claims", handlers.WIPClaimCreate, []string{db.RoleLDCSService, db.RoleLDCSAccounting})
	addAuthAPIRoute(s, "GET", "/lca/claims/{id:[0-9]+}", handlers.WIPClaimShow, []string{db.RoleLDCSService, db.RoleLDCSAccounting, db.RoleServiceClosedStore, db.RoleDealerViewOnly})
	addAuthRoute(s, "GET", "/lca/claims/{id:[0-9]+}/pdf", handlers.WIPClaimPDF, []string{db.RoleLDCSService, db.RoleLDCSAccounting, db.RoleServiceClosedStore, db.RoleDealerViewOnly})

	addAuthAPIRoute(s, "GET", "/admin/contracts/{id}/cancellation/estimate/quote", adminhandlers.ContractCancellationQuotesList, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "PUT", "/admin/contracts/cancel", adminhandlers.ContractCancellationUpdateRequest, []string{db.RoleAdminView})
	addAuthRoute(s, "GET", "/admin/contracts/cancellation/estimate/pdf", adminhandlers.ContractCancellationEstimateListAsPdf, []string{db.RoleAdminView})
	addAuthRoute(s, "GET", "/admin/contracts/cancellation/estimate/quote/pdf", adminhandlers.ContractCancellationQuoteListAsPdf, []string{db.RoleAdminView})
	addAuthRoute(s, "GET", "/admin/contracts/{id}/cancellation/request-form", adminhandlers.ContractCancellationRequestForm, []string{db.RoleAdminView})
	addAuthRoute(s, "GET", "/admin/contracts/cancellation/check-request/pdf", adminhandlers.ContractCancellationCheckRequestPdf, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "PUT", "/admin/contracts/{id}/cancel/undo", adminhandlers.ContractCancellationUndoRequest, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "PUT", "/admin/contracts/reinstate", adminhandlers.ContractReinstateRequest, []string{db.RoleAccountRepManager, db.RoleAccountRepII})
	addAuthAPIRoute(s, "GET", "/admin/contracts/{id}/reinstate/info", adminhandlers.ContractReinstateInfo, []string{db.RoleAccountRepManager, db.RoleAccountRepII})

	addAuthAPIRoute(s, "GET", "/admin/contracts/{id}/review-cancels", adminhandlers.ContractCancellationReviewIndex, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "PUT", "/admin/contracts/review-cancels", adminhandlers.ReviewContractCancellation, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "PUT", "/admin/contracts/{id:[0-9]+}/activate", adminhandlers.ContractActivate, []string{db.RoleAccountRep})

	addAuthAPIRoute(s, "GET", "/admin/contracts/{code}/transfer/supporting-data", adminhandlers.GetTransferableContracts, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/admin/contracts/transfer-form", adminhandlers.ContractTransferForm, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/admin/contracts/transfer/endorsement-form", adminhandlers.ContractTransferEndorsementForm, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "PUT", "/admin/contracts/transfer", adminhandlers.ContractTransfer, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/admin/contracts/{vin}/transfer", adminhandlers.GetTransferableContractsByVin, []string{db.RoleAccountRepManager, db.RoleAdminView})
	addAuthAPIRoute(s, "PUT", "/admin/contracts/undo-transfer", adminhandlers.UndoContractTransfer, []string{db.RoleAccountRepManager, db.RoleAdminView})

	addAuthAPIRoute(s, "PUT", "/admin/contracts/edit-vin", adminhandlers.EditContractsVINs, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "PUT", "/admin/contracts/{id:[0-9]+}/edit-effective-mileage", adminhandlers.EditEffectiveMileage, []string{db.RoleAdminView})

	addAuthAPIRoute(s, "GET", "/admin/contracts/cit", adminhandlers.ContractTransitIndex, []string{db.RoleAdminView})

	addAuthAPIRoute(s, "GET", "/admin/accounting/fee-rules", adminhandlers.AccountingFeeRuleIndex, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/admin/accounting/fee-rules/{id:[0-9]+}", adminhandlers.AccountingFeeRuleGet, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/admin/accounting/fee-rules", adminhandlers.AccountingFeeRuleCreate, []string{db.RoleController})
	addAuthAPIRoute(s, "PUT", "/admin/accounting/fee-rules/{id:[0-9]+}", adminhandlers.AccountingFeeRuleUpdate, []string{db.RoleController})
	addAuthAPIRoute(s, "GET", "/admin/accounting/fee-rules/supporting-data", adminhandlers.AccountFeeRuleSupportingData, []string{db.RoleAdminView})

	addAuthAPIRoute(s, "GET", "/admin/accounting/rules", adminhandlers.AccountingRulesIndex, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/admin/accounting/rules/{id:[0-9]+}", adminhandlers.AccountingRuleGet, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/admin/accounting/rules", adminhandlers.AccountingRuleCreate, []string{db.RoleController})
	addAuthAPIRoute(s, "POST", "/admin/accounting/rules/{id:[0-9]+}/clone", adminhandlers.AccountingRuleClone, []string{db.RoleControllerAdmin})
	addAuthAPIRoute(s, "GET", "/admin/accounting/rules/{id:[0-9]+}/transaction-descriptions/{transaction_type:[0-9]+}", adminhandlers.GetRuleTransactionDescriptions, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "PUT", "/admin/accounting/rules/{id:[0-9]+}", adminhandlers.AccountingRuleUpdate, []string{db.RoleController})
	addAuthAPIRoute(s, "DELETE", "/admin/accounting/rules/{id:[0-9]+}", adminhandlers.AccountingRuleDelete, []string{db.RoleController})
	addAuthAPIRoute(s, "GET", "/admin/accounting/rules/supporting-data", adminhandlers.AccountingRulesSupportingData, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/admin/accounting/rules/validate/{intacctProductInvoiceId}", adminhandlers.ValidateAccountingRuleTest, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/admin/invoicing", adminhandlers.ProcessInvoicing, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/admin/accounting/invoicing/supporting-data", adminhandlers.InvoicingSupportingData, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/admin/accounting/manage/intacct-products", adminhandlers.AccountingIntacctProductsCreate, []string{db.RoleControllerAdmin})
	addAuthAPIRoute(s, "POST", "/admin/accounting/manage/intacct-accounts", adminhandlers.AccountingIntacctAccountsCreate, []string{db.RoleControllerAdmin})
	addAuthAPIRoute(s, "POST", "/admin/accounting/manage/intacct-vendors", adminhandlers.AccountingIntacctVendorsCreate, []string{db.RoleControllerAdmin})
	addAuthAPIRoute(s, "GET", "/admin/accounting/canceled-contracts", adminhandlers.CancelledContractSubmitIndex, []string{db.RoleAdminView, db.RoleAccountingCancellationHandler})
	addAuthRoute(s, "GET", "/admin/accounting/canceled-contracts/contacts/csv", adminhandlers.SubmitCancelContactDownload, []string{db.RoleAdminView, db.RoleAccountingCancellationHandler})
	addAuthRoute(s, "GET", "/admin/accounting/canceled-contracts/bills/csv", adminhandlers.SubmitCancelBillsDownload, []string{db.RoleAdminView, db.RoleAccountingCancellationHandler})
	addAuthRoute(s, "GET", "/admin/accounting/canceled-contracts/all/csv", adminhandlers.SubmitCancelAllDownload, []string{db.RoleAdminView, db.RoleAccountingCancellationHandler})
	addAuthAPIRoute(s, "POST", "/admin/accounting/canceled-contracts/contacts", adminhandlers.CreateIntacctContacts, []string{db.RoleAdminView, db.RoleAccountingCancellationHandler})
	addAuthAPIRoute(s, "POST", "/admin/accounting/canceled-contracts/submit", adminhandlers.SubmitCancelContract, []string{db.RoleAdminView, db.RoleAccountingCancellationHandler})
	addAuthAPIRoute(s, "POST", "/admin/accounting/canceled-contracts/clear", adminhandlers.ClearCancelContract, []string{db.RoleAdminView, db.RoleAccountingCancellationHandler})
	addAuthAPIRoute(s, "PUT", "/admin/accounting/canceled-contracts/manual-update", adminhandlers.ManualUpdateCancelContract, []string{db.RoleAdminView, db.RoleCancellationPaymentHandler})
	addAuthAPIRoute(s, "GET", "/admin/accounting/canceled-contracts/supporting-data", adminhandlers.CancelledContractSupportingData, []string{db.RoleAdminView, db.RoleAccountingCancellationHandler})
	addAuthAPIRoute(s, "GET", "/admin/accounting/canceled-bills", adminhandlers.CancelledBillsIndex, []string{db.RoleAdminView, db.RoleAccountingCancellationHandler})
	addAuthRoute(s, "GET", "/admin/accounting/canceled-contracts/canceled-bills/csv", adminhandlers.CancelledBillsDownload, []string{db.RoleAdminView, db.RoleAccountingCancellationHandler})

	addAuthAPIRoute(s, "GET", "/admin/invoicing/products", adminhandlers.IntacctProductInvoicesIndex, nil)
	addAuthAPIRoute(s, "POST", "/admin/banner", adminhandlers.StoreBanners, []string{db.RoleProductManager, db.RoleAccountRep})
	addAuthAPIRoute(s, "GET", "/admin/banner", adminhandlers.StoreBannersGet, []string{db.RoleAdminView, db.RoleProductManager, db.RoleAccountRep})
	addAuthAPIRoute(s, "GET", "/admin/banner/supporting-data", adminhandlers.StoreBannerSupportingDataGet, []string{db.RoleAdminView, db.RoleProductManager, db.RoleAccountRep})

	addAuthAPIRoute(s, "POST", "/quote-request", handlers.CreateQuoteRequest, []string{db.RoleServiceRO, db.RoleBDCRO, db.RoleFinanceDeal})

	// FIS
	addAuthAPIRoute(s, "POST", "/fis/data/defaults/{user_id:[0-9]+}", handlers.FISDefaultsCreateUpdate, []string{db.RoleServiceRO, db.RoleBDCRO, db.RoleFinanceDeal})
	addAuthAPIRoute(s, "POST", "/fis/data/{fis_id:[0-9]+}", handlers.FISCreateUpdate, []string{db.RoleServiceRO, db.RoleBDCRO, db.RoleFinanceDeal})
	addAuthAPIRoute(s, "POST", "/fis/sale/{sale_id:[0-9]+}", handlers.FISCreateForDeal, []string{db.RoleServiceRO, db.RoleBDCRO, db.RoleFinanceDeal})
	addAuthAPIRoute(s, "GET", "/fis/data/{fis_id:[0-9]+}", handlers.FISShow, []string{db.RoleServiceRO, db.RoleBDCRO, db.RoleFinanceDeal, db.RoleDealerViewOnly})
	addAuthAPIRoute(s, "GET", "/fis/data/{fis_id:[0-9]+}/documents", handlers.FISFetchDocuments, []string{db.RoleServiceRO, db.RoleBDCRO, db.RoleFinanceDeal, db.RoleDealerViewOnly})
	addAuthAPIRoute(s, "PUT", "/fis/data/{fis_id:[0-9]+}/cdk", handlers.FISCDKWrite, nil)
	addAuthAPIRoute(s, "GET", "/fis/data/{fis_id:[0-9]+}/cdk", handlers.FISCDKGet, nil)

	addAuthRoute(s, "GET", "/fis/data/{fis_id:[0-9]+}/pdf", handlers.FISPrintAsPdf, []string{db.RoleServiceRO, db.RoleBDCRO, db.RoleFinanceDeal, db.RoleDealerViewOnly})
	addAuthRoute(s, "GET", "/fis/{fis_id}/document/{document_id}", handlers.FISRetrieveDocument, []string{db.RoleServiceRO, db.RoleBDCRO, db.RoleFinanceDeal, db.RoleDealerViewOnly})
	addAuthAPIRoute(s, "POST", "/fis/{fis_id}/document/{document_id}/cdk-ddj-send", handlers.FISDocumentSendCDKDDJ, []string{db.RoleServiceRO, db.RoleFinanceDeal})

	addAuthAPIRoute(s, "GET", "/admin/integrations/dealer-platforms/{id:[0-9]+}", adminhandlers.DealerPlatformGetByID, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/admin/integrations/dealer-platforms", adminhandlers.DealerPlatformIndex, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/admin/integrations/dealer-platforms/stores", adminhandlers.StoreDealerPlatforms, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/admin/integrations/dealer-platforms/supporting-data", adminhandlers.DealerPlatformIndexSupportingData, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/admin/integrations/dealer-platforms", adminhandlers.DealerPlatformSave, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "GET", "/admin/integrations/dealer-platforms/{id:[0-9]+}/supporting-data", adminhandlers.DealerPlatformSupportingData, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/admin/integrations/dealer-platforms/{id:[0-9]+}/product-variants", adminhandlers.DealerPlatformProductVariantIndex, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "POST", "/admin/integrations/dealer-platforms/{id:[0-9]+}/product-variants", adminhandlers.DealerPlatformProductVariantCreate, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "PUT", "/admin/integrations/dealer-platforms/{id:[0-9]+}/product-variants/{pvid:[0-9]+}", adminhandlers.DealerPlatformProductVariantUpdate, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "DELETE", "/admin/integrations/dealer-platforms/{id:[0-9]+}/product-variants/{pvid:[0-9]+}", adminhandlers.DealerPlatformProductVariantDelete, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "PUT", "/admin/integrations/dealer-platforms/{id:[0-9]+}", adminhandlers.DealerPlatformSave, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "POST", "/admin/integrations/api_key", adminhandlers.APIKeySave, []string{db.RoleProductManager})
	addAuthAPIRoute(s, "PUT", "/admin/integrations/api_key/{id:[0-9]+}", adminhandlers.APIKeySave, []string{db.RoleProductManager})

	addAuthAPIRoute(s, "GET", "/admin/news-updates/articles", adminhandlers.ArticleIndex, []string{db.RoleNewsUpdatesManager})
	addAuthAPIRoute(s, "GET", "/admin/news-updates/articles/{id:[0-9]+}", adminhandlers.ArticleByID, []string{db.RoleNewsUpdatesManager})
	addAuthAPIRoute(s, "PUT", "/admin/news-updates/articles/{id:[0-9]+}", adminhandlers.ArticleSave, []string{db.RoleNewsUpdatesManager})
	addAuthAPIRoute(s, "POST", "/admin/news-updates/articles", adminhandlers.ArticleSave, []string{db.RoleNewsUpdatesManager})
	addAuthAPIRoute(s, "DELETE", "/admin/news-updates/articles/{id:[0-9]+}", adminhandlers.ArticleDelete, []string{db.RoleNewsUpdatesManager})
	addAuthAPIRoute(s, "PUT", "/admin/news-updates/articles/{id:[0-9]+}/set-publish", adminhandlers.ArticleUpdatePublish, []string{db.RoleNewsUpdatesManager})
	addAuthAPIRoute(s, "GET", "/admin/news-updates/articles/supporting-data", adminhandlers.ArticleSupportingData, []string{db.RoleNewsUpdatesManager})

	addAuthAPIRoute(s, "GET", "/admin/news-updates/categories", adminhandlers.CategoryIndex, []string{db.RoleNewsUpdatesManager})
	addAuthAPIRoute(s, "GET", "/admin/news-updates/categories/{id:[0-9]+}", adminhandlers.CategoryByID, []string{db.RoleNewsUpdatesManager})
	addAuthAPIRoute(s, "PUT", "/admin/news-updates/categories/{id:[0-9]+}", adminhandlers.CategorySave, []string{db.RoleNewsUpdatesManager})
	addAuthAPIRoute(s, "POST", "/admin/news-updates/categories", adminhandlers.CategorySave, []string{db.RoleNewsUpdatesManager})
	addAuthAPIRoute(s, "DELETE", "/admin/news-updates/categories/{id:[0-9]+}", adminhandlers.CategoryDelete, []string{db.RoleNewsUpdatesManager})

	addAuthRoute(s, "GET", "/admin/news-updates/articles/{id:[0-9]+}/attachments/{attachmentId:[0-9]+}/download", adminhandlers.ArticleAttachmentsDownload, []string{db.RoleNewsUpdatesManager, db.RoleNewsAndUpdatesView})
	addAuthAPIRoute(s, "POST", "/admin/news-updates/articles/{id:[0-9]+}/attachments", adminhandlers.UploadArticleAttachments, []string{db.RoleNewsUpdatesManager})

	addAuthAPIRoute(s, "GET", "/admin/roles", adminhandlers.RoleIndex, []string{db.RoleAdminView, db.RoleJobTitleManagement})
	addAuthAPIRoute(s, "GET", "/admin/roles/all", adminhandlers.GetAllRoles, []string{db.RoleAdminView, db.RoleJobTitleManagement})
	addAuthAPIRoute(s, "PUT", "/admin/roles/{id:[0-9]+}", adminhandlers.UpdateRole, []string{db.RoleJobTitleManagement})

	rProxyConfig := conf.Get().S3ReverseProxy
	downloadRoutePath := strings.Replace(rProxyConfig.PathPrefix, "/api", "", 1)
	downloadRoutePath = fmt.Sprintf("%s/*", downloadRoutePath)
	addAuthRoute(s, "GET", downloadRoutePath, handlers.DownloadFile, nil)

	addAuthAPIRoute(s, "PUT", "/admin/news-updates/articles/per-page-article-count", adminhandlers.UpdatePerPageArticleCount, []string{db.RoleNewsUpdatesManager})

	addAuthAPIRoute(s, "GET", "/news-updates/articles/store/{id:[0-9]+}", handlers.ArticleIndexByStore, []string{db.RoleNewsUpdatesManager, db.RoleNewsAndUpdatesView})
	addAuthAPIRoute(s, "GET", "/news-updates/categories/store/{id:[0-9]+}", handlers.CategoryIndexByStore, []string{db.RoleNewsUpdatesManager, db.RoleNewsAndUpdatesView})
	addAuthAPIRoute(s, "GET", "/news-updates/article/{id:[0-9]+}/attachments", handlers.AttachmentsByArticle, []string{db.RoleNewsUpdatesManager, db.RoleNewsAndUpdatesView})

	addAuthAPIRoute(s, "GET", "/admin/cancellations-dashboard/cancellations", adminhandlers.CancellationRequestsIndex, []string{db.RoleAdminView, db.RoleCancelDashboardManager})
	addAuthAPIRoute(s, "POST", "/admin/cancellations-dashboard/cancellations", adminhandlers.CancellationRequestCreate, []string{db.RoleAdminView, db.RoleCancelDashboardManager})
	addAuthAPIRoute(s, "GET", "/admin/cancellations-dashboard/cancellation/{id:[0-9]+}", adminhandlers.CancellationRequestGet, []string{db.RoleAdminView, db.RoleCancelDashboardManager})
	//addAuthAPIRoute(s, "PUT", "/admin/cancellations-dashboard/cancellation/{id:[0-9]+}", adminhandlers.CancellationRequestUpdate, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "PUT", "/admin/cancellations-dashboard/cancellation/{id:[0-9]+}/delete", adminhandlers.CancellationRequestDelete, []string{db.RoleAdminView, db.RoleCancelDashboardManager})
	addAuthAPIRoute(s, "PUT", "/admin/cancellations-dashboard/cancellation/{id:[0-9]+}/cancel", adminhandlers.CancellationRequestProcessCancel, []string{db.RoleAdminView, db.RoleCancelDashboardManager})
	addAuthAPIRoute(s, "GET", "/admin/cancellations-dashboard/cancellation/{id:[0-9]+}/attachments", adminhandlers.CancellationRequestAttachments, []string{db.RoleAdminView, db.RoleCancelDashboardManager})
	addAuthAPIRoute(s, "POST", "/admin/cancellations-dashboard/cancellation/{id:[0-9]+}/resubmit", adminhandlers.CancellationRequestResubmit, []string{db.RoleAdminView, db.RoleCancelDashboardManager})
	addAuthAPIRoute(s, "POST", "/admin/cancellations-dashboard/cancellations/assign", adminhandlers.CancellationRequestsAssign, []string{db.RoleCancelDashboardManager})

	addAuthAPIRoute(s, "GET", "/admin/pricing-formulas/supporting-data", adminhandlers.PricingFormulaSupportingData, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/admin/pricing-formulas", adminhandlers.PricingFormulaIndex, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "GET", "/admin/pricing-formulas/{id:[0-9]+}", adminhandlers.PricingFormulaByID, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "POST", "/admin/pricing-formulas", adminhandlers.PricingFormulaCreate, []string{db.RoleAdminView})
	addAuthAPIRoute(s, "PUT", "/admin/pricing-formulas/{id:[0-9]+}", adminhandlers.PricingFormulaUpdate, []string{db.RoleAdminView})

	addAuthAPIRoute(s, "GET", "/admin/clp-rates", adminhandlers.ClpRateIndex, []string{db.RoleAdminView})

	r.Mount("/api", s)
}
