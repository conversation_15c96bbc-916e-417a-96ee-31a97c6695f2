package routes

import (
	"net/http"

	"whiz/handlers"
	middlewareRatelimits "whiz/middleware/ratelimit"
	"whiz/nr"

	"github.com/go-chi/chi"
	newrelic "github.com/newrelic/go-agent"
)

var nrApp newrelic.Application

// nolint:gochecknoinits - Tried sync.Once, but that resulted
// in the code not being able to find the newrelic transaction
// from the context
func init() {
	nrApp = nr.GetApp()
}

// AddRoute wraps a route handler with middleware and adds it to the router
// nolint:unparam
func AddRoute(router chi.Router, method, route string, handler http.HandlerFunc) {
	// nolint:staticcheck
	sentryHandlerFunc := handlers.SentryHandler().HandleFunc(handler)
	nrRoute, nrHandlerFunc := newrelic.WrapHandleFunc(nrApp, route, sentryHandlerFunc)
	router.MethodFunc(method, nrRoute, nrHandlerFunc)
}

// nolint:unparam
func addAuthRoute(router chi.Router, method, route string, handler handlers.AuthenticatedHandlerFunc, roles []string) {
	// nolint:staticcheck
	sentryHandlerFunc := handlers.SentryHandler().HandleFunc(handlers.AuthenticatedHandler(handler, roles))
	nrRoute, nrHandlerFunc := newrelic.WrapHandleFunc(nrApp, route, sentryHandlerFunc)
	router.MethodFunc(method, nrRoute, nrHandlerFunc)
}

// addRateLimitedAPIRoute wraps an API route handler with a Rate Limited Check middleware and
// other API Route middleware and adds it to the router
func addRateLimitedAPIRoute(router chi.Router, method, route string, rlHandler middlewareRatelimits.MiddlewareFunc, handler handlers.APIHandlerFunc) {
	rl := chi.NewRouter()
	rl.Use(rlHandler)
	addAPIRoute(rl, method, "/", handler)
	router.Mount(route, rl)
}

// addAPIRoute wraps an API router handler with middleware and adds it to the router
func addAPIRoute(router chi.Router, method, route string, handler handlers.APIHandlerFunc) {
	// nolint:staticcheck
	sentryHandlerFunc := handlers.SentryHandler().HandleFunc(handlers.APIHandler(handler))
	nrRoute, nrHandlerFunc := newrelic.WrapHandleFunc(nrApp, route, sentryHandlerFunc)
	router.MethodFunc(method, nrRoute, nrHandlerFunc)
}

// addRateLimitedAuthAPIRoute wraps an authenticated API route handler with a Rate Limited Check middleware and
// other Auth API route middleware and adds it to the router
func addRateLimitedAuthAPIRoute(router chi.Router, method, route string, rlHandler middlewareRatelimits.MiddlewareFunc, handler handlers.AuthenticatedAPIHandlerFunc, roles []string) {
	rl := chi.NewRouter()
	rl.Use(rlHandler)
	addAuthAPIRoute(rl, method, "/", handler, roles)
	router.Mount(route, rl)
}

// addAuthAPIRoute wraps an authenticated API route handler with middleware and adds it to the router
func addAuthAPIRoute(router chi.Router, method, route string, handler handlers.AuthenticatedAPIHandlerFunc, roles []string) {
	// nolint:staticcheck
	sentryHandlerFunc := handlers.SentryHandler().HandleFunc(handlers.AuthenticatedAPIHandler(handler, roles))
	nrRoute, nrHandlerFunc := newrelic.WrapHandleFunc(nrApp, route, sentryHandlerFunc)
	router.MethodFunc(method, nrRoute, nrHandlerFunc)
}

// addRateLimitedXtkAPIRoute wraps an Xtk API route handler with a Rate Limited Check middleware and
// other Xtk API route middleware and adds it to the router
func addRateLimitedXtkAPIRoute(router chi.Router, method, route string, rlHandler middlewareRatelimits.MiddlewareFunc, handler handlers.XtkAPIHandlerFunc, roles []string) {
	rl := chi.NewRouter()
	rl.Use(rlHandler)
	addXtkAPIRoute(rl, method, "/", handler, roles)
	router.Mount(route, rl)
}

// addXtkAPIRoute wraps an Xtk API route handler with middleware and adds it to the router
func addXtkAPIRoute(router chi.Router, method, route string, handler handlers.XtkAPIHandlerFunc, impersonationRoles []string) {
	sentryHandlerFunc := handlers.SentryHandler().HandleFunc(handlers.XtkAuthAPIHandler(handler, impersonationRoles))
	nrRoute, nrHandlerFunc := newrelic.WrapHandleFunc(nrApp, route, sentryHandlerFunc)
	router.MethodFunc(method, nrRoute, nrHandlerFunc)
}

func addAwsAPIRoute(router chi.Router, method, route string, handler handlers.AwsAPIHandlerFunc, impersonationRoles []string) {
	sentryHandlerFunc := handlers.SentryHandler().HandleFunc(handlers.AwsAuthAPIHandler(handler, impersonationRoles))
	nrRoute, nrHandlerFunc := newrelic.WrapHandleFunc(nrApp, route, sentryHandlerFunc)
	router.MethodFunc(method, nrRoute, nrHandlerFunc)
}

// addOktaAPIRoute wraps an Okta-authenticated API router handler with middleware and adds it to the router
func addOktaAPIRoute(router chi.Router, method, route string, handler handlers.OktaAPIHandlerFunc) {
	oktaFunc := handlers.OktaAPIHandler(handler)
	sentryWrappedHandler := handlers.SentryHandler().Handle(http.HandlerFunc(oktaFunc))
	router.MethodFunc(method, route, sentryWrappedHandler.ServeHTTP)
}
