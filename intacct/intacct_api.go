package intacct

import (
	"bytes"
	"context"
	"encoding/xml"
	"io/ioutil"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"whiz/conf"
	"whiz/db"

	"github.com/pkg/errors"
)

const s3Path = "/transmittals/intacct/"

// GLBatchResponse struct for response for GLBatch api response
type GLBatchResponse struct {
	XMLName   xml.Name             `xml:"response"`
	Control   responseControlXML   `xml:"control"`
	Operation responseOperationXML `xml:"operation"`
}

// ARIntacctResponse struct for response for GLBatch api response
type ARIntacctResponse struct {
	XMLName   xml.Name               `xml:"response"`
	Control   responseControlXML     `xml:"control"`
	Operation responseOperationARXML `xml:"operation"`
}

// Control section establishes API credentials in order to allow access to the web services API gateway
type controlXML struct {
	XMLName    xml.Name `xml:"control"`
	SenderID   string   `xml:"senderid"`
	Password   string   `xml:"password"`
	ControlID  string   `xml:"controlid"`
	UniqueID   bool     `xml:"uniqueid"`
	DTDVersion string   `xml:"dtdversion"`
}

type responseControlXML struct {
	XMLName    xml.Name `xml:"control"`
	SenderID   string   `xml:"senderid"`
	ControlID  string   `xml:"controlid"`
	UniqueID   bool     `xml:"uniqueid"`
	DTDVersion string   `xml:"dtdversion"`
	Status     string   `xml:"status"`
}

type responseAuthenticationXML struct {
	XMLName          xml.Name  `xml:"authentication"`
	Status           string    `xml:"status"`
	UserID           string    `xml:"userid"`
	CompanyID        string    `xml:"companyid"`
	LocationID       string    `xml:"locationid"`
	SessionTimeStamp time.Time `xml:"sessiontimestamp"`
	SessionTimeOut   time.Time `xml:"sessiontimeout"`
}

type responseDataXML struct {
	XMLName         xml.Name          `xml:"data"`
	ResponseGlBatch []responseGlBatch `xml:"glbatch"`
}

type responseGlBatch struct {
	XMLName      xml.Name `xml:"glbatch"`
	RecordNumber int      `xml:"RECORDNO"`
}

type responseError struct {
	XMLName      xml.Name `xml:"error"`
	ErrorNumber  string   `xml:"errorno"`
	Description  string   `xml:"description"`
	Description2 string   `xml:"description2"`
	Correction   string   `xml:"correction"`
}

type responseDataARXML struct {
	XMLName           xml.Name            `xml:"data"`
	ResponseARInvoice []responseARInvoice `xml:"arinvoice"`
}

type responseARInvoice struct {
	XMLName      xml.Name `xml:"arinvoice"`
	RecordNumber int      `xml:"RECORDNO"`
}

type responseErrorMessage struct {
	XMLName xml.Name        `xml:"errormessage"`
	Error   []responseError `xml:"error"`
}

type responseResultXML struct {
	XMLName      xml.Name             `xml:"result"`
	Status       string               `xml:"status"`
	Function     string               `xml:"function"`
	ControlID    string               `xml:"controlid"`
	Data         responseDataXML      `xml:"data"`
	ErrorMessage responseErrorMessage `xml:"errormessage"`
}

type responseResultARXML struct {
	XMLName      xml.Name             `xml:"result"`
	Status       string               `xml:"status"`
	Function     string               `xml:"function"`
	ControlID    string               `xml:"controlid"`
	Data         responseDataARXML    `xml:"data"`
	ErrorMessage responseErrorMessage `xml:"errormessage"`
}

type responseOperationXML struct {
	XMLName        xml.Name                  `xml:"operation"`
	Authentication responseAuthenticationXML `xml:"authentication"`
	Result         responseResultXML         `xml:"result"`
}

type responseOperationARXML struct {
	XMLName        xml.Name                  `xml:"operation"`
	Authentication responseAuthenticationXML `xml:"authentication"`
	Result         responseResultARXML       `xml:"result"`
}

func getControlXML(controlID string) *controlXML {
	control := controlXML{
		SenderID:   conf.Get().Intacct.SenderID,
		Password:   conf.Get().Intacct.SenderPassword,
		ControlID:  controlID,
		DTDVersion: "3.0",
	}
	return &control
}

type authenticationXML struct {
	XMLName   xml.Name `xml:"authentication"`
	UserID    string   `xml:"login>userid"`
	CompanyID string   `xml:"login>companyid"`
	Password  string   `xml:"login>password"`
}

func getAuthenticationXML() *authenticationXML {
	authentication := authenticationXML{
		UserID:    conf.Get().Intacct.UserID,
		CompanyID: conf.Get().Intacct.CompanyID,
		Password:  conf.Get().Intacct.Password,
	}
	return &authentication
}

// Request function sends request XML to intacct webservice
func request(requestXML []byte) ([]byte, error) {
	body := url.Values{}
	body.Set("xmlrequest", string(requestXML))
	resp, err := http.Post(
		conf.Get().Intacct.Host,
		"application/x-www-form-urlencoded",
		bytes.NewBufferString(body.Encode()))

	if err != nil {
		return nil, errors.Wrap(err, "Intacct server communication error")
	}

	defer resp.Body.Close()

	response, _ := ioutil.ReadAll(resp.Body)
	//logRequestResponse(string(requestXML), string(response))
	return response, nil
}

type requestXML struct {
	XMLName        xml.Name           `xml:"request"`
	Control        *controlXML        `xml:"control"`
	Authentication *authenticationXML `xml:"operation>authentication"`
	Function       interface{}        `xml:"operation>content>function"`
}

// CreateGLBatchRequest function generates XML for create GLBATCH journal entries
func CreateGLBatchRequest(glBatch db.GLIntacct) ([]byte, error) {

	type glBatchFunction struct {
		XMLName   xml.Name     `xml:"function"`
		ControlID string       `xml:"controlid,attr"`
		GlBatch   db.GLIntacct `xml:"create"`
	}

	requestFunction := glBatchFunction{
		GlBatch:   glBatch,
		ControlID: time.Now().String(),
	}

	glBatchRequest := requestXML{}
	glBatchRequest.Authentication = getAuthenticationXML()
	glBatchRequest.Function = &requestFunction
	glBatchRequest.Control = getControlXML(time.Now().String())

	createGLBatchXML, err := xml.Marshal(glBatchRequest)
	if err != nil {
		return nil, errors.Wrap(err, "creating gl batch request failed")
	}

	return createGLBatchXML, nil
}

// CreateARBatchRequest function generates XML for create ARINVOICEITEMS journal entries
func CreateARBatchRequest(arInvoice db.ARIntacct) ([]byte, error) {

	type arBatchFunction struct {
		XMLName   xml.Name     `xml:"function"`
		ControlID string       `xml:"controlid,attr"`
		ARBatch   db.ARIntacct `xml:"create"`
	}

	requestFunction := arBatchFunction{
		ARBatch:   arInvoice,
		ControlID: time.Now().String(),
	}

	arBatchRequest := requestXML{}
	arBatchRequest.Authentication = getAuthenticationXML()
	arBatchRequest.Function = &requestFunction
	arBatchRequest.Control = getControlXML(time.Now().String())

	createARBatchXML, err := xml.Marshal(arBatchRequest)
	if err != nil {
		return nil, errors.Wrap(err, "creating ar batch request failed")
	}
	return createARBatchXML, nil
}

// InvokeGLBatchRequest method to invoke gl batch api
func InvokeGLBatchRequest(requestXML []byte) (GLBatchResponse, error) {
	var responseData GLBatchResponse
	response, err := request(requestXML)
	if err != nil {
		return responseData, err
	}

	err = xml.Unmarshal(response, &responseData)
	if err != nil {
		return responseData, errors.Wrap(err, "error parsing response.")
	}

	return responseData, nil
}

// InvokeARBatchRequest method to invoke ar batch api
func InvokeARBatchRequest(requestXML []byte) (ARIntacctResponse, error) {
	var responseData ARIntacctResponse
	response, err := request(requestXML)
	if err != nil {
		return responseData, err
	}

	err = xml.Unmarshal(response, &responseData)
	if err != nil {
		return responseData, errors.Wrap(err, "error parsing response.")
	}

	return responseData, nil
}

// InvokeRequest method to invoke ar batch api
func InvokeRequest(requestXML []byte) (*db.IntacctResult, error) {
	var responseData db.IntacctResult
	response, err := request(requestXML)
	if err != nil {
		return &responseData, err
	}
	err = xml.Unmarshal(response, &responseData)
	if err != nil {
		return &responseData, errors.Wrap(err, "error parsing intacct response.")
	}
	return &responseData, nil
}

// CreateBillRequest function generates XML for create bill
func CreateBillRequest(bill db.CreateBillRequest) ([]byte, error) {

	type createBillFunction struct {
		XMLName   xml.Name             `xml:"function"`
		ControlID string               `xml:"controlid,attr"`
		APBill    db.CreateBillRequest `xml:"create"`
	}

	createBillReq := requestXML{}
	createBillReq.Authentication = getAuthenticationXML()
	createBillReq.Function = &createBillFunction{
		APBill:    bill,
		ControlID: "create",
	}
	createBillReq.Control = getControlXML(time.Now().String())

	createBillXML, err := xml.Marshal(createBillReq)
	if err != nil {
		return nil, errors.Wrap(err, "creating bill request failed")
	}
	return createBillXML, nil
}

// GetBillRequest function generates XML for get bill
func GetBillRequest(billNum string) ([]byte, error) {
	type getPaymentDetail struct {
		XMLName   xml.Name        `xml:"function"`
		ControlID string          `xml:"controlid,attr"`
		Query     db.IntacctQuery `xml:"query"`
	}

	getBillReq := requestXML{}
	getBillReq.Authentication = getAuthenticationXML()
	getBillReq.Function = &getPaymentDetail{
		ControlID: "query",
		Query: db.IntacctQuery{
			Object: "apbill",
			Fields: []string{"RECORDNO"},
			Filter: struct {
				EqualTo struct {
					Field string `xml:"field"`
					Value string `xml:"value"`
				} `xml:"equalto"`
			}{
				EqualTo: struct {
					Field string `xml:"field"`
					Value string `xml:"value"`
				}{
					Field: "RECORDID",
					Value: billNum,
				},
			},
		},
	}
	getBillReq.Control = getControlXML(time.Now().String())

	getBillReqXML, err := xml.Marshal(getBillReq)
	if err != nil {
		return nil, errors.Wrap(err, "creating payment detail request failed")
	}
	return getBillReqXML, nil
}

// PaymentDetailRequest function generates XML for payment detail request
func PaymentDetailRequest(ctx context.Context, billRecNo int) ([]byte, error) {
	type getPaymentDetail struct {
		XMLName   xml.Name        `xml:"function"`
		ControlID string          `xml:"controlid,attr"`
		Query     db.IntacctQuery `xml:"query"`
	}

	paymentDetailReq := requestXML{}
	paymentDetailReq.Authentication = getAuthenticationXML()
	paymentDetailReq.Function = &getPaymentDetail{
		ControlID: "query",
		Query: db.IntacctQuery{
			Object: "APPYMTDETAIL",
			Fields: []string{"PAYMENTKEY"},
			Filter: struct {
				EqualTo struct {
					Field string `xml:"field"`
					Value string `xml:"value"`
				} `xml:"equalto"`
			}{
				EqualTo: struct {
					Field string `xml:"field"`
					Value string `xml:"value"`
				}{
					Field: "RECORDKEY",
					Value: strconv.Itoa(billRecNo),
				},
			},
		},
	}
	paymentDetailReq.Control = getControlXML(time.Now().String())

	paymentDetailReqXML, err := xml.Marshal(paymentDetailReq)
	if err != nil {
		return nil, errors.Wrap(err, "creating payment detail request failed")
	}
	return paymentDetailReqXML, nil
}

// PaymentRequest function generates XML for payment detail request
func PaymentRequest(ctx context.Context, paymentKey int) ([]byte, error) {
	type getPayment struct {
		XMLName   xml.Name        `xml:"function"`
		ControlID string          `xml:"controlid,attr"`
		Query     db.IntacctQuery `xml:"query"`
	}

	paymentReq := requestXML{}
	paymentReq.Authentication = getAuthenticationXML()
	paymentReq.Function = &getPayment{
		ControlID: "query",
		Query: db.IntacctQuery{
			Object: "APPYMT",
			Fields: []string{"DOCNUMBER", "WHENPAID", "TOTALPAID", "STATE"},
			Filter: struct {
				EqualTo struct {
					Field string `xml:"field"`
					Value string `xml:"value"`
				} `xml:"equalto"`
			}{
				EqualTo: struct {
					Field string `xml:"field"`
					Value string `xml:"value"`
				}{
					Field: "RECORDNO",
					Value: strconv.Itoa(paymentKey),
				},
			},
		},
	}
	paymentReq.Control = getControlXML(time.Now().String())

	paymentReqXML, err := xml.Marshal(paymentReq)
	if err != nil {
		return nil, errors.Wrap(err, "creating payment request failed")
	}
	return paymentReqXML, nil
}

// CreateContactRequest function generates XML for create contact
func CreateContactRequest(contacts []*db.ContactItem) ([]byte, error) {
	createContactReq := requestXML{}
	createContactReq.Authentication = getAuthenticationXML()
	contactFunctions := make([]db.CreateContactFunction, len(contacts))
	for index := range contacts {
		contactFunctions[index] = db.CreateContactFunction{
			ControlID: contacts[index].ContactName,
			Contact:   *contacts[index],
		}
	}
	createContactReq.Function = contactFunctions
	createContactReq.Control = getControlXML(time.Now().String())

	createContactXML, err := xml.Marshal(createContactReq)
	if err != nil {
		return nil, errors.Wrap(err, "creating Contact request failed")
	}
	return createContactXML, nil
}

// InvokeContactRequest method to invoke contact api
func InvokeContactRequest(requestXML []byte) (*db.IntacctContactResponse, error) {
	var responseData db.IntacctContactResponse
	response, err := request(requestXML)
	if err != nil {
		return &responseData, err
	}
	err = xml.Unmarshal(response, &responseData)
	if err != nil {
		return &responseData, errors.Wrap(err, "error parsing intacct response.")
	}
	return &responseData, nil
}
