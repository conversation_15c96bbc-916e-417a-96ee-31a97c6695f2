package adminhandlers

import (
	"database/sql"
	"encoding/json"
	"net/http"
	"strconv"
	"whiz/db"
	"whiz/handlers"
	"whiz/types"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"gopkg.in/guregu/null.v3"
)

// ContractCancellationQuotesList returns cancellation estimates for all contracts of a VIN
func ContractCancellationQuotesList(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	isAdmin := isAccountRepAdmin(user)
	isManager := isAccountRepManager(user)
	return handlers.CancellationEstimateList(w, req, user, false, isAdmin, isManager)
}

// ContractCancellationEstimateListAsPdf returns cancellation estimates for given contract list in PDF format
func ContractCancellationEstimateListAsPdf(w http.ResponseWriter, req *http.Request, user db.CurrentUser) {
	isAdmin := isAccountRepAdmin(user)
	isManager := isAccountRepManager(user)
	handlers.CancellationEstimateListAsPdf(w, req, user, false, isAdmin, isManager)
}

// ContractCancellationCheckRequestPdf returns cancellation check request for given contract list in pdf
func ContractCancellationCheckRequestPdf(w http.ResponseWriter, req *http.Request, user db.CurrentUser) {
	isAdmin := isAccountRepAdmin(user)
	isManager := isAccountRepManager(user)
	handlers.CancellationCheckRequestPdf(w, req, user, false, isAdmin, isManager)
}

// ContractCancellationUpdateRequest updates contract cancellations for all given contracts
func ContractCancellationUpdateRequest(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	isAdmin := isAccountRepAdmin(user)
	isManager := isAccountRepManager(user)
	return handlers.ContractCancel(w, req, user, false, isAdmin, isManager, true, nil)
}

// ContractCancellationUndoRequest reverts contract cancellation for given contract
func ContractCancellationUndoRequest(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	return handlers.ContractUndoCancel(w, req, user)
}

// ContractCancellationQuoteListAsPdf returns cancellation estimate quotes for given contract list in PDF format
func ContractCancellationQuoteListAsPdf(w http.ResponseWriter, req *http.Request, user db.CurrentUser) {
	isAdmin := isAccountRepAdmin(user)
	isManager := isAccountRepManager(user)
	handlers.CancellationQuoteListAsPdf(w, req, user, false, isAdmin, isManager)
}

// isAccountRepAdmin is used to determine if a user has admin privileges for canceling contracts
func isAccountRepAdmin(user db.CurrentUser) bool {
	isAdmin := user.HasRole(db.RoleAccountRepManager) || user.HasRole(db.RoleAccountRep)
	return isAdmin
}

// isAccountRepManager is used to determine if a user has manager privileges for canceling contracts
func isAccountRepManager(user db.CurrentUser) bool {
	isManager := user.HasRole(db.RoleAccountRepManager)
	return isManager
}

// ContractCancellationRequestForm returns contract cancellation form given contract list in PDF format
func ContractCancellationRequestForm(w http.ResponseWriter, req *http.Request, user db.CurrentUser) {
	handlers.CancellationRequestForm(w, req, user)
}

// ContractCancelSupportingData returns supporting data that is needed when canceling contracts
func ContractCancelSupportingData(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()

	var err error
	cancelReasons, err := db.GetCancelReasons(ctx)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting cancel reasons", nil)
	}

	attachmentTypes, err := db.GetAttachmentTypes(ctx)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting attachment types", nil)
	}

	productTypes, err := db.GetProductTypes(req.Context())
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error loading product types", nil)
	}

	var stores []db.CancelStore
	var customer db.ContractCancellationPayee
	var lender db.ContractCancellationPayee
	var store db.Store
	var payeeLenderIsDigitalReserves = false
	var payeeLenderDigitalReserveRules []db.DigitalReserveRule

	if req.FormValue("contract_id") != "" {
		contractID, err := strconv.Atoi(req.FormValue("contract_id"))
		if err != nil {
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Error reading contract id", nil)
		}

		stores, err = db.GetCancelStores(ctx)
		if err != nil {
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Error loading stores", nil)
		}

		customer, err = db.GetCancelPayeeCustomer(ctx, contractID)
		if err != nil {
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Error loading customer payee", nil)
		}

		store, err = db.GetCancelPayeeStore(ctx, contractID)
		if err != nil {
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Error loading store payee", nil)
		}

		lender, err = db.GetCancelPayeeLender(ctx, contractID)
		if err != nil {
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Error loading lender payee", nil)
		}

		if lender.ID != nil {
			payeeLenderIsDigitalReserves,
				payeeLenderDigitalReserveRules,
				err = db.GetLenderDigitalReserveInfo(
				ctx,
				*lender.ID,
				store.ID,
			)
			if err != nil {
				handlers.ReportError(req, err)
				return http.StatusInternalServerError, handlers.ErrorMessage("Error loading lender digital reserve info", nil)
			}
		}
	}

	return http.StatusOK, map[string]interface{}{
		"attachment_types": attachmentTypes,
		"cancel_reasons":   cancelReasons,
		"product_types":    productTypes,
		"cancel_payees": []string{
			db.CancelPayeeTypeLender,
			db.CancelPayeeTypeCustomer,
			db.CancelPayeeDownPayment,
			db.CancelPayeeReserves,
			db.CancelPayeeStoreIssuedRefund,
		},
		"cancel_stores":                      stores,
		"payee_customer":                     customer,
		"payee_lender":                       lender,
		"payee_lender_is_digital_reserves":   payeeLenderIsDigitalReserves,
		"payee_lender_digital_reserve_rules": payeeLenderDigitalReserveRules,
		"payee_store_id":                     store.ID,
		"contract_store_state":               store.StateCode,
		"manual_tax_states":                  db.ManualTaxStates,
		"states":                             db.States,
	}
}

// ContractCancellationReviewIndex returns contract cancellations list to be reviewed
func ContractCancellationReviewIndex(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()

	contractID := chi.URLParam(req, "id")
	if contractID == "" {
		return http.StatusBadRequest, handlers.ErrorMessage("Contract ID is empty", nil)
	}

	// Search contracts by contract ID
	contractIDs := []int{}

	vin := ""
	err := db.Get().Get(&vin, "select vin from vin_records where id = (select vin_record_id from contracts where id = $1)", contractID)
	if err != nil && err != sql.ErrNoRows {
		err = errors.Wrap(err, "error in getting vin for find contracts")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error finding contracts", nil)
	}

	err = db.Get().Select(&contractIDs, "select c.id from contracts c join vin_records v on c.vin_record_id = v.id where v.vin = $1", vin)
	if err != nil {
		handlers.ReportError(req, errors.Wrapf(err, "failed to fetch contracts with VIN ID %s", vin))
		return http.StatusInternalServerError, handlers.ErrorMessage("failed to fetch contracts with VIN", nil)
	}

	var contractCancellations []struct {
		ContractID       int                 `json:"contract_id" db:"contract_id"`
		CancelDate       types.JSPQDate      `json:"cancel_date" db:"cancel_date"`
		CancelMileage    int                 `json:"cancel_mileage" db:"cancel_mileage"`
		CancelReason     string              `json:"cancel_reason" db:"cancel_reason"`
		CancelFee        decimal.Decimal     `json:"cancel_fee" db:"cancel_fee"`
		CancelFactor     decimal.Decimal     `json:"cancel_factor" db:"cancel_factor"`
		CustomerRefund   decimal.Decimal     `json:"customer_refund" db:"customer_refund"`
		StoreRefund      decimal.Decimal     `json:"store_refund" db:"store_refund"`
		ClaimsPaidAmount decimal.NullDecimal `json:"claims_paid_amount" db:"claims_paid_amount"`
		UnusedVisits     null.Int            `json:"unused_visits" db:"unused_visits"`
		Code             string              `json:"code" db:"code"`
		StoreCode        string              `json:"store_code" db:"store_code"`
		ProductTypeName  string              `json:"product_type_name" db:"product_type_name"`
		ProductTypeCode  string              `json:"product_type_code" db:"product_type_code"`
		EffectiveDate    types.JSPQNullDate  `json:"effective_date" db:"effective_date"`
		ClaimsCount      int                 `json:"claims_count" db:"-"`
		ClaimsAmount     decimal.Decimal     `json:"claims_amount" db:"-"`
		CancelFactorUsed string              `db:"-" json:"cancel_factor_used"`
		SalesTax         decimal.Decimal     `json:"sales_tax" db:"sales_tax"`
	}

	cancellationQuery := `select cc.*,
							st.code as store_code,
							c.code,
							c.product_type_name,
							c.effective_date,
							c.product_type_code
						  from contract_cancellations cc
							join contracts c on c.id = cc.contract_id
							left join stores st on c.store_id = st.id
							left join transactions t on t.id = cc.transaction_id
						  where is_void = false and
								reviewed_at is null and
								reviewed_by_user_id is null and
								cc.contract_id in (?)`
	query, args, err := sqlx.In(cancellationQuery, contractIDs)
	if err != nil {
		handlers.ReportError(req, errors.Wrapf(err, "failed to fetch contracts with ID %s", contractID))
		return http.StatusInternalServerError, handlers.ErrorMessage("failed to contract cancellations", nil)
	}

	query = db.Get().Rebind(query)

	err = db.Get().Unsafe().Select(&contractCancellations, query, args...)
	if err != nil {
		handlers.ReportError(req, errors.Wrapf(err, "failed to fetch contracts with ID %s", contractID))
		return http.StatusInternalServerError, handlers.ErrorMessage("failed to contract cancellations", nil)
	}

	for i, cc := range contractCancellations {
		// contract claim information
		cc.ClaimsCount, cc.ClaimsAmount, _, err = handlers.Claims(ctx, cc.Code, cc.ProductTypeCode)
		if err != nil {
			handlers.ReportError(req, errors.Wrapf(err, "failed to fetch contracts claims with ID %s", cc.Code))
			return http.StatusInternalServerError, handlers.ErrorMessage("error finding contract claim details", nil)
		}

		// If we find cancellation then we need to set the used factor string to display on UI.
		if err != sql.ErrNoRows {
			contractCancellations[i].CancelFactorUsed = (decimal.NewFromFloat(1).Sub(cc.CancelFactor)).Mul(decimal.NewFromFloat(100)).String()
		}
	}

	return http.StatusOK, map[string]interface{}{"contractCancellations": contractCancellations}
}

// ReviewContractCancellation marks given list of contract cancellations as reviewed
func ReviewContractCancellation(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	reqPayload := struct {
		Contracts []int `json:"contracts"`
	}{}

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&reqPayload)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage("Bad request"+err.Error(), nil)
	}

	tx, err := db.Get().Begin()
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error in begin db transaction"))
		return http.StatusInternalServerError, handlers.ErrorMessage("failed to review contract cancellations", nil)
	}

	for _, id := range reqPayload.Contracts {
		_, err := tx.Exec(`update contract_cancellations set reviewed_at = now() at time zone 'utc', reviewed_by_user_id = $1 where contract_id = $2 and is_void = false`, user.ID, id)
		if err != nil {
			_ = tx.Rollback()
			handlers.ReportError(req, errors.Wrapf(err, "db error while updating cancellation review for contract ID %d", id))
			return http.StatusInternalServerError, handlers.ErrorMessage("failed to review contract cancellations", nil)
		}

		_, err = tx.Exec(`update contract_flags set cleared_at = now() at time zone 'utc', cleared_by_user_id = $1 where contract_id = $2 and flag_reason = $3 and cleared_at is NULL and deleted_at is NULL`, user.ID, id, db.ContractFlagPendingCancel)
		if err != nil {
			_ = tx.Rollback()
			handlers.ReportError(req, errors.Wrapf(err, "db error while updating contract flags for contract ID %d", id))
			return http.StatusInternalServerError, handlers.ErrorMessage("failed to update contract flags", nil)
		}

		query := `insert into contract_events(created_at, created_by_user_id, created_by_name, contract_id, description) values(
				now() at time zone 'utc', $1, $2, $3, $4)`
		_, err = tx.Exec(query, user.ID, user.FullName(), id, db.ContractEventCancelReviewed)
		if err != nil {
			_ = tx.Rollback()
			handlers.ReportError(req, errors.Wrapf(err, "error inserting contract event"))
			return http.StatusInternalServerError, handlers.ErrorMessage("Error while logging contract review cancellation event", nil)
		}
	}

	err = tx.Commit()
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error in committing db transaction"))
		return http.StatusInternalServerError, handlers.ErrorMessage("failed to review contract cancellations", nil)
	}

	return http.StatusOK, map[string]interface{}{"message": "reviewed successfully"}
}
