package adminhandlers

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/csv"
	"encoding/json"
	"html/template"
	"net/http"
	"strings"
	"time"
	"whiz/conf"
	"whiz/db"
	"whiz/email"
	"whiz/handlers"

	"github.com/go-chi/chi"
	"github.com/lib/pq"
	"github.com/pkg/errors"
	"gopkg.in/guregu/null.v3"
)

const (
	lenderNewDataTmplString = `<p>New Lender Added</p>

	<p>
	Name : {{ .Name }}<br>
	Intacct Vendor ID : {{ .IntacctVendorID }}<br>
	Asbury CDK Finance Code : {{ .AsburyCDKFinanceCode.String }}<br>
	LHM CDK Finance Code : {{ .LHMCDKFinanceCode.String }}<br>
	UCS Lender ID : {{ .UCSLenderID.String }}<br>
	Tekion Lender ID : {{ .TekionLenderID.String }}<br>
	Address : {{ .Address }}<br>
	City : {{ .City }}<br>
	State Code : {{ .StateCode }}<br>
	Postal Code : {{ .PostalCode }}<br>
	API Display Name : {{ .ESaleAPIDisplayName.String }}<br>
	Certifiable Makes : {{ .CertifiableMakes }}<br>
	</p>`
)

const (
	lenderEditDataTmplString = `<p>Existing Lender Modified</p>

	<p>
	Old Name : {{ .OldName }}, New Name : {{ .Name }}<br>
	Old Intacct Vendor ID : {{ .OldIntacctVendorID }}, New Intacct Vendor ID : {{ .IntacctVendorID }}<br>
	Old Asbury CDK Finance Code : {{ .OldAsburyCDKFinanceCode.String }}, New Asbury CDK Finance Code : {{ .AsburyCDKFinanceCode.String }}<br>
	Old LHM CDK Finance Code : {{ .OldLHMCDKFinanceCode.String }}, New LHM CDK Finance Code : {{ .LHMCDKFinanceCode.String }}<br>
	Old UCS Lender ID : {{ .OldUCSLenderID.String }}, New UCS Lender ID : {{ .UCSLenderID.String }}<br>
	Old Tekion Lender ID : {{ .OldTekionLenderID.String }}, New Tekion Lender ID : {{ .TekionLenderID.String }}<br>
	Old Address : {{ .OldAddress }}, New Address : {{ .Address }}<br>
	Old City : {{ .OldCity }}, New City : {{ .City }}<br>
	Old State Code : {{ .OldStateCode }}, New State Code : {{ .StateCode }}<br>
	Old Postal Code : {{ .OldPostalCode }}, New Postal Code : {{ .PostalCode }}<br>
	Old API Display Name : {{ .OldESaleAPIDisplayName.String }}, New API Display Name : {{ .ESaleAPIDisplayName.String }}<br>
	Old Certifiable Makes : {{ .OldCertifiableMakes }}, New Certifiable Makes :{{ .CertifiableMakes }}<br>
	</p>`
)

var (
	lenderEditDataTmpl *template.Template
	lenderNewDataTmpl  *template.Template
)

func init() {
	lenderEditDataTmpl = template.Must(template.New("lender-edit-data").Parse(lenderEditDataTmplString))
	lenderNewDataTmpl = template.Must(template.New("lender-new-data").Parse(lenderNewDataTmplString))
}

// LenderEditMail sends out email on edit
func LenderEditMail(ctx context.Context, req *http.Request, l lenderMailPayload) {
	buf := bytes.NewBuffer([]byte{})

	err := lenderEditDataTmpl.Execute(buf, l)
	if err != nil {
		err = errors.Wrap(err, "could not execute edit lender template to send an email")
		handlers.ReportError(req, err)
		return
	}

	email.SendHTML(ctx, conf.Get().ESale.LenderEmail, "Existing Lender Edited", buf.Bytes())
}

// LenderNewMail sends out email on create
func LenderNewMail(ctx context.Context, req *http.Request, l lenderMailPayload) {
	buf := bytes.NewBuffer([]byte{})

	err := lenderNewDataTmpl.Execute(buf, l)
	if err != nil {
		err = errors.Wrap(err, "could not execute new lender template to send an email")
		handlers.ReportError(req, err)
		return
	}

	email.SendHTML(ctx, conf.Get().ESale.LenderEmail, "New Lender Added", buf.Bytes())
}

// LenderIndex returns a list of lenders
func LenderIndex(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	lenders := []struct {
		ID                   int         `db:"id" json:"id"`
		IntacctVendorID      string      `db:"intacct_vendor_id" json:"intacct_vendor_id"`
		Name                 string      `db:"name" json:"name"`
		Address              string      `db:"address" json:"address"`
		City                 string      `db:"city" json:"city"`
		StateCode            string      `db:"state_code" json:"state_code"`
		PostalCode           string      `db:"postal_code" json:"postal_code"`
		LHMCDKFinanceCode    null.String `db:"lhm_cdk_finance_code" json:"lhm_cdk_finance_code"`
		AsburyCDKFinanceCode null.String `db:"asbury_cdk_finance_code" json:"asbury_cdk_finance_code"`
		UCSLenderID          null.String `db:"ucs_lender_id" json:"ucs_lender_id"`
		TekionLenderID       null.String `db:"tekion_lender_id" json:"tekion_lender_id"`
		VTAOverallowance     bool        `db:"add_vta_overallownce" json:"add_vta_overallownce"`
		IsActive             bool        `db:"is_active" json:"is_active"`
		ESaleAPIDisplayName  null.String `db:"e_sale_api_display_name" json:"e_sale_api_display_name"`
		CLName               null.String `db:"cl_name" json:"cl_name"`
		CLAddress            null.String `db:"cl_address" json:"cl_address"`
		CLCity               null.String `db:"cl_city" json:"cl_city"`
		CLStateCode          null.String `db:"cl_state_code" json:"cl_state_code"`
		CLPostalCode         null.String `db:"cl_postal_code" json:"cl_postal_code"`
	}{}

	ctx := req.Context()

	args := []interface{}{}
	wh := "where deleted_at is null"
	if len(req.FormValue("includeInactive")) == 0 {
		wh = "where deleted_at is null and is_active = true"
	}

	q := req.FormValue("q")
	if q != "" {
		wh += " and (l.name ilike $1 " +
			"or l.lhm_cdk_finance_code ilike $1 " +
			"or l.asbury_cdk_finance_code ilike $1 " +
			"or ucs_lender_id ilike $1 " +
			"or tekion_lender_id ilike $1 " +
			"or l.intacct_vendor_id ilike $1)"
		args = append(args, "%"+q+"%")
	}
	listQuery, countQuery := handlers.ListQueries(
		`l.id, l.intacct_vendor_id, l.name, l.address, l.city, l.state_code,
		l.postal_code, l.lhm_cdk_finance_code, l.asbury_cdk_finance_code,
		l.ucs_lender_id, l.add_vta_overallownce, l.is_active, l.e_sale_api_display_name,
		cl.name cl_name, cl.address cl_address, cl.city cl_city, cl.state_code cl_state_code,
		cl.postal_code cl_postal_code`,
		`lenders l
			left join cancellation_lenders cl
			on cl.id = l.cancellation_lender_id`,
		wh,
		"order by name asc",
		20,
		handlers.GetPage(req),
	)

	err := db.Get().SelectContext(ctx, &lenders, listQuery, args...)
	if err != nil {
		err = errors.Wrap(err, "Database error getting admin lenders list \""+listQuery+"\"")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting lenders data", nil)
	}
	count := 0
	err = db.Get().GetContext(ctx, &count, countQuery, args...)
	if err != nil {
		err = errors.Wrap(err, "Database error getting admin lenders count")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting lenders data", nil)
	}

	return http.StatusOK, map[string]interface{}{"lenders": lenders, "count": count}
}

// LenderCsv returns csv data
func LenderCsv(w http.ResponseWriter, req *http.Request, user db.CurrentUser) {
	lenders := []struct {
		IntacctVendorID      string      `db:"intacct_vendor_id" json:"intacct_vendor_id"`
		IsActive             bool        `db:"is_active" json:"is_active"`
		Name                 string      `db:"name" json:"name"`
		Address              string      `db:"address" json:"address"`
		City                 string      `db:"city" json:"city"`
		StateCode            string      `db:"state_code" json:"state_code"`
		PostalCode           string      `db:"postal_code" json:"postal_code"`
		LHMCDKFinanceCode    null.String `db:"lhm_cdk_finance_code" json:"lhm_cdk_finance_code"`
		AsburyCDKFinanceCode null.String `db:"asbury_cdk_finance_code" json:"asbury_cdk_finance_code"`
		UCSLenderID          null.String `db:"ucs_lender_id" json:"ucs_lender_id"`
		TekionLenderID       null.String `db:"tekion_lender_id" json:"tekion_lender_id"`
		CLName               null.String `db:"cl_name" json:"cl_name"`
		CLAttention          null.String `db:"cl_attention" json:"cl_attention"`
		CLAddress            null.String `db:"cl_address" json:"cl_address"`
		CLCity               null.String `db:"cl_city" json:"cl_city"`
		CLStateCode          null.String `db:"cl_state_code" json:"cl_state_code"`
		CLPostalCode         null.String `db:"cl_postal_code" json:"cl_postal_code"`
	}{}

	ctx := req.Context()
	r := handlers.Renderer()

	args := []interface{}{}
	wh := "where l.deleted_at is null"
	if len(req.FormValue("includeInactive")) == 0 {
		wh = "where l.deleted_at is null and l.is_active = true"
	}

	q := req.FormValue("q")
	if q != "" {
		wh += " and (l.name ilike $1 " +
			"or l.lhm_cdk_finance_code ilike $1 " +
			"or l.asbury_cdk_finance_code ilike $1 " +
			"or ucs_lender_id ilike $1 " +
			"or tekion_lender_id ilike $1 " +
			"or intacct_vendor_id ilike $1)"
		args = append(args, "%"+q+"%")
	}

	selectQuery := `
		select
			l.intacct_vendor_id, 
			l.is_active, 
			l.name, 
			l.address, 
			l.city, 
			l.state_code, 
			l.postal_code, 
			l.lhm_cdk_finance_code, 
			l.asbury_cdk_finance_code, 
			l.ucs_lender_id, 
			tekion_lender_id,
			cl.name cl_name, 
			cl.attention cl_attention, 
			cl.address cl_address, 
			cl.city cl_city, 
			cl.state_code cl_state_code, 
			cl.postal_code cl_postal_code
		from lenders l
			left join cancellation_lenders cl
			on l.cancellation_lender_id=cl.id
		` + wh + `
		order by name asc`

	err := db.Get().SelectContext(ctx, &lenders, selectQuery, args...)
	if err != nil {
		err = errors.Wrap(err, "database error getting admin lenders list")
		handlers.ReportError(req, err)
		_ = r.Text(w, http.StatusInternalServerError, "Error getting lenders data")
		return
	}

	w.Header().Set("Content-Type", "text/csv")
	w.Header().Set("Content-Disposition", "attachment; filename="+"Lenders.csv")
	csvw := csv.NewWriter(w)
	err = csvw.Write([]string{
		"Lender Status", "Name", "Intacct Vendor ID", "LHM CDK Finance Source", "Asbury Finance Source", "UCS Lender ID", "Tekion Lender ID", "Address", "City", "State", "Zip",
		"CL Name", "CL Attention", "CL Address", "CL City", "CL State", "CL Zip",
	})
	if err != nil {
		err = errors.Wrap(err, "error writing csv")
		handlers.ReportError(req, err)
		_ = r.Text(w, http.StatusInternalServerError, "Error writing CSV")
		return
	}

	for _, l := range lenders {
		lhm := ""
		if l.LHMCDKFinanceCode.Valid {
			lhm = l.LHMCDKFinanceCode.String
		}
		asbury := ""
		if l.AsburyCDKFinanceCode.Valid {
			asbury = l.AsburyCDKFinanceCode.String
		}
		ucs := ""
		if l.UCSLenderID.Valid {
			ucs = l.UCSLenderID.String
		}
		tekion := ""
		if l.TekionLenderID.Valid {
			tekion = l.TekionLenderID.String
		}
		status := "Active"
		if !l.IsActive {
			status = "Inactive"
		}
		record := []string{
			status, l.Name, l.IntacctVendorID, lhm, asbury, ucs, tekion, l.Address, l.City, l.StateCode, l.PostalCode,
			l.CLName.ValueOrZero(), l.CLAttention.ValueOrZero(), l.CLAddress.ValueOrZero(),
			l.CLCity.ValueOrZero(), l.CLStateCode.ValueOrZero(), l.CLPostalCode.ValueOrZero(),
		}
		if err := csvw.Write(record); err != nil {
			err = errors.Wrap(err, "error writing csv")
			handlers.ReportError(req, err)
			_ = r.Text(w, http.StatusInternalServerError, "Error writing CSV")
			return
		}
	}
	csvw.Flush()
}

// LenderDigitalReserveRulesCsv returns csv data for digital reserve rules
func LenderDigitalReserveRulesCsv(w http.ResponseWriter, req *http.Request, user db.CurrentUser) {
	lenderRules := []struct {
		LenderStatus  string      `db:"lender_status" json:"lender_status"`
		LenderName    string      `db:"lender_name" json:"lender_name"`
		LenderAddress string      `db:"lender_address" json:"lender_address"`
		LenderCity    string      `db:"lender_city" json:"lender_city"`
		LenderState   string      `db:"lender_state" json:"lender_state"`
		LenderZip     string      `db:"lender_zip" json:"lender_zip"`
		StoreCodes    null.String `db:"store_codes" json:"store_codes"`
		ProductTypes  null.String `db:"product_types" json:"product_types"`
		CancelReasons null.String `db:"cancel_reasons" json:"cancel_reasons"`
	}{}

	ctx := req.Context()
	r := handlers.Renderer()

	args := []interface{}{}
	wh := "where l.deleted_at is null"
	if len(req.FormValue("includeInactive")) == 0 {
		wh = "where l.deleted_at is null and l.is_active = true"
	}

	q := req.FormValue("q")
	if q != "" {
		wh += " and (l.name ilike $1 " +
			"or l.lhm_cdk_finance_code ilike $1 " +
			"or l.asbury_cdk_finance_code ilike $1 " +
			"or ucs_lender_id ilike $1 " +
			"or tekion_lender_id ilike $1 " +
			"or intacct_vendor_id ilike $1)"
		args = append(args, "%"+q+"%")
	}

	// Query to get digital reserve rules with lender information
	selectQuery := `
		with rule_data as (
			select
				drr.lender_id,
				drr.id as rule_id,
				array_agg(distinct s.code order by s.code) as store_codes,
				array_agg(distinct pt.name order by pt.name) as product_types,
				array_agg(distinct cr.name order by cr.name) as cancel_reasons
			from digital_reserve_rules drr
			join digital_reserve_rules_stores drrs on drr.id = drrs.digital_reserve_rule_id
			join stores s on drrs.store_id = s.id
			join digital_reserve_rules_product_types drrpt on drr.id = drrpt.digital_reserve_rule_id
			join product_types pt on drrpt.product_type_id = pt.id
			join digital_reserve_rules_cancel_reasons drrcr on drr.id = drrcr.digital_reserve_rule_id
			join cancel_reasons cr on drrcr.cancel_reason_id = cr.id
			group by drr.lender_id, drr.id
		)
		select
			case when l.is_active then 'Active' else 'Inactive' end as lender_status,
			l.name as lender_name,
			l.address as lender_address,
			l.city as lender_city,
			l.state_code as lender_state,
			l.postal_code as lender_zip,
			array_to_string(rd.store_codes, ', ') as store_codes,
			array_to_string(rd.product_types, ', ') as product_types,
			array_to_string(rd.cancel_reasons, ', ') as cancel_reasons
		from lenders l
		join rule_data rd on l.id = rd.lender_id
		` + wh + `
		order by l.name asc, rd.rule_id asc`

	err := db.Get().SelectContext(ctx, &lenderRules, selectQuery, args...)
	if err != nil {
		err = errors.Wrap(err, "database error getting digital reserve rules")
		handlers.ReportError(req, err)
		_ = r.Text(w, http.StatusInternalServerError, "Error getting digital reserve rules data")
		return
	}

	w.Header().Set("Content-Type", "text/csv")
	w.Header().Set("Content-Disposition", "attachment; filename="+"DigitalReserveRules.csv")
	csvw := csv.NewWriter(w)
	err = csvw.Write([]string{
		"Lender Status", "Name", "Address", "City", "State", "Zip", "Stores", "Product Types", "Cancellation Reasons",
	})
	if err != nil {
		err = errors.Wrap(err, "error writing csv")
		handlers.ReportError(req, err)
		_ = r.Text(w, http.StatusInternalServerError, "Error writing CSV")
		return
	}

	for _, rule := range lenderRules {
		record := []string{
			rule.LenderStatus,
			rule.LenderName,
			rule.LenderAddress,
			rule.LenderCity,
			rule.LenderState,
			rule.LenderZip,
			rule.StoreCodes.ValueOrZero(),
			rule.ProductTypes.ValueOrZero(),
			rule.CancelReasons.ValueOrZero(),
		}
		if err := csvw.Write(record); err != nil {
			err = errors.Wrap(err, "error writing csv")
			handlers.ReportError(req, err)
			_ = r.Text(w, http.StatusInternalServerError, "Error writing CSV")
			return
		}
	}
	csvw.Flush()
}

type lenderPayload struct {
	ID                   int                                `db:"id" json:"-"`
	CreatedAt            time.Time                          `db:"created_at" json:"-"`
	CreatedByUserID      int                                `db:"created_by_user_id" json:"-"`
	PreviousID           sql.NullInt64                      `db:"previous_id" json:"-"`
	IntacctVendorID      string                             `db:"intacct_vendor_id" json:"intacct_vendor_id"`
	Name                 string                             `db:"name" json:"name"`
	Address              string                             `db:"address" json:"address"`
	City                 string                             `db:"city" json:"city"`
	StateCode            string                             `db:"state_code" json:"state_code"`
	PostalCode           string                             `db:"postal_code" json:"postal_code"`
	LHMCDKFinanceCode    null.String                        `db:"lhm_cdk_finance_code" json:"lhm_cdk_finance_code"`
	AsburyCDKFinanceCode null.String                        `db:"asbury_cdk_finance_code" json:"asbury_cdk_finance_code"`
	UCSLenderID          null.String                        `db:"ucs_lender_id" json:"ucs_lender_id"`
	TekionLenderID       null.String                        `db:"tekion_lender_id" json:"tekion_lender_id"`
	VTAOverallowance     bool                               `db:"add_vta_overallownce" json:"add_vta_overallownce"`
	IsActive             bool                               `db:"is_active" json:"is_active"`
	ESaleAPIDisplayName  null.String                        `db:"e_sale_api_display_name" json:"e_sale_api_display_name"`
	CertifiableMakes     pq.StringArray                     `db:"certifiable_makes" json:"certifiable_makes"`
	CancellationLenderID null.Int                           `db:"cancellation_lender_id" json:"cancellation_lender_id"`
	IsDigitalReserves    bool                               `db:"is_digital_reserves" json:"is_digital_reserves"`
	DigitalReserveRules  []db.DigitalReserveRuleMultiselect `json:"digital_reserve_rules"`
}

type lenderMailPayload struct {
	IntacctVendorID         string      `db:"intacct_vendor_id" json:"intacct_vendor_id"`
	Name                    string      `db:"name" json:"name"`
	Address                 string      `db:"address" json:"address"`
	City                    string      `db:"city" json:"city"`
	StateCode               string      `db:"state_code" json:"state_code"`
	PostalCode              string      `db:"postal_code" json:"postal_code"`
	LHMCDKFinanceCode       null.String `db:"lhm_cdk_finance_code" json:"lhm_cdk_finance_code"`
	AsburyCDKFinanceCode    null.String `db:"asbury_cdk_finance_code" json:"asbury_cdk_finance_code"`
	UCSLenderID             null.String `db:"ucs_lender_id" json:"ucs_lender_id"`
	TekionLenderID          null.String `db:"tekion_lender_id" json:"tekion_lender_id"`
	ESaleAPIDisplayName     null.String `db:"e_sale_api_display_name" json:"e_sale_api_display_name"`
	CertifiableMakes        string      `db:"certifiable_makes" json:"certifiable_makes"`
	OldIntacctVendorID      string      `db:"old_intacct_vendor_id" json:"old_intacct_vendor_id"`
	OldName                 string      `db:"old_name" json:"old_name"`
	OldAddress              string      `db:"old_address" json:"old_address"`
	OldCity                 string      `db:"old_city" json:"old_city"`
	OldStateCode            string      `db:"old_state_code" json:"old_state_code"`
	OldPostalCode           string      `db:"old_postal_code" json:"old_postal_code"`
	OldLHMCDKFinanceCode    null.String `db:"old_lhm_cdk_finance_code" json:"old_lhm_cdk_finance_code"`
	OldAsburyCDKFinanceCode null.String `db:"old_asbury_cdk_finance_code" json:"old_asbury_cdk_finance_code"`
	OldUCSLenderID          null.String `db:"old_ucs_lender_id" json:"old_ucs_lender_id"`
	OldTekionLenderID       null.String `db:"old_tekion_lender_id" json:"old_tekion_lender_id"`
	OldESaleAPIDisplayName  null.String `db:"old_e_sale_api_display_name" json:"old_e_sale_api_display_name"`
	OldCertifiableMakes     string      `db:"old_certifiable_makes" json:"old_certifiable_makes"`
}

func (l *lenderPayload) clean() {
	l.IntacctVendorID = strings.TrimSpace(l.IntacctVendorID)
	l.Name = strings.TrimSpace(l.Name)
	l.Address = strings.TrimSpace(l.Address)
	l.City = strings.TrimSpace(l.City)
	l.StateCode = strings.TrimSpace(l.StateCode)
	l.PostalCode = strings.TrimSpace(l.PostalCode)
	if l.LHMCDKFinanceCode.Valid {
		l.LHMCDKFinanceCode.String = strings.TrimSpace(l.LHMCDKFinanceCode.String)
	}
	if l.AsburyCDKFinanceCode.Valid {
		l.AsburyCDKFinanceCode.String = strings.TrimSpace(l.AsburyCDKFinanceCode.String)
	}
	if l.UCSLenderID.Valid {
		l.UCSLenderID.String = strings.TrimSpace(l.UCSLenderID.String)
	}
	if l.TekionLenderID.Valid {
		l.TekionLenderID.String = strings.TrimSpace(l.TekionLenderID.String)
	}
	if l.ESaleAPIDisplayName.Valid {
		l.ESaleAPIDisplayName.String = strings.TrimSpace(l.ESaleAPIDisplayName.String)
	}
}

func (l lenderPayload) validate(ctx context.Context) (map[string]string, error) {
	validationErrs := map[string]string{}

	if l.CreatedByUserID <= 0 {
		validationErrs["created_by_user_id"] = "Created by user is required."
	} else {
		c := 0
		err := db.Get().GetContext(ctx, &c, "select count(*) from current_users where id = $1", l.CreatedByUserID)
		if err != nil {
			return nil, errors.Wrap(err, "Database error validating created_by_user_id on a lender record. ")
		}
		if c < 1 {
			validationErrs["created_by_user_id"] = "Created by user is invalid."
		}
	}

	if l.PreviousID.Valid {
		c := 0
		err := db.Get().GetContext(ctx, &c, "select count(*) from lenders where id = $1 and deleted_at is null", l.PreviousID)
		if err != nil {
			return nil, errors.Wrap(err, "Database error validating previous_id on a lender record. ")
		}
		if c < 1 {
			validationErrs["previous_id"] = "Previous lender is invalid."
		}
	}

	if l.IntacctVendorID != "" {
		var c int
		query := "select count(*) from lenders where intacct_vendor_id = $1 and deleted_at is null"
		args := []interface{}{l.IntacctVendorID}
		if l.PreviousID.Valid {
			query += " and id != $2"
			args = append(args, l.PreviousID.Int64)
		}
		err := db.Get().GetContext(ctx, &c, query, args...)
		if err != nil {
			return nil, errors.Wrap(err, "Database error validating intacct_vendor_id on a lender record. ")
		}
		if c > 0 {
			validationErrs["intacct_vendor_id"] = "Intacct vendor id already exists."
		}
	}

	if l.Name == "" {
		validationErrs["name"] = "Name is required."
	}

	if l.Address == "" {
		validationErrs["address"] = "Address is required."
	}

	if l.City == "" {
		validationErrs["city"] = "City is required."
	}

	if l.StateCode == "" {
		validationErrs["state_code"] = "State code is required."
	}

	if l.PostalCode == "" {
		validationErrs["postal_code"] = "Postal code is required."
	}

	if l.LHMCDKFinanceCode.String == "" && l.AsburyCDKFinanceCode.String == "" && l.UCSLenderID.String == "" && l.TekionLenderID.String == "" {
		validationErrs["finance_code"] = "A value must be provided for at least one of 'LHM CDK Finance Source', 'Asbury CDK Finance Source', `UCS Lender ID`, or 'Tekion Lender ID'"
	}

	if l.LHMCDKFinanceCode.String != "" {
		var id int
		query := `select id 
		from lenders 
		where lhm_cdk_finance_code = $1 and deleted_at is null`
		args := []interface{}{l.LHMCDKFinanceCode.String}
		if l.PreviousID.Valid {
			query += " and id != $2"
			args = append(args, l.PreviousID.Int64)
		}
		err := db.Get().GetContext(ctx, &id, query, args...)
		if err != nil && err != sql.ErrNoRows {
			return nil, errors.Wrap(err, "database error validating lhm_cdk_finance_code on a lender record. ")
		}
		if id > 0 {
			validationErrs["lhm_cdk_finance_code"] = "LHM CDK Finance Code already exists."
		}
	}

	if l.AsburyCDKFinanceCode.String != "" {
		var id int
		query := `select id 
		from lenders 
		where asbury_cdk_finance_code = $1 and deleted_at is null`
		args := []interface{}{l.AsburyCDKFinanceCode.String}
		if l.PreviousID.Valid {
			query += " and id != $2"
			args = append(args, l.PreviousID.Int64)
		}
		err := db.Get().GetContext(ctx, &id, query, args...)
		if err != nil && err != sql.ErrNoRows {
			return nil, errors.Wrap(err, "database error validating asbury_cdk_finance_code on a lender record. ")
		}
		if id > 0 {
			validationErrs["asbury_cdk_finance_code"] = "Asbury CDK Finance Code already exists."
		}
	}

	if l.UCSLenderID.String != "" {
		var id int
		query := `select id 
		from lenders 
		where ucs_lender_id = $1 and deleted_at is null`
		args := []interface{}{l.UCSLenderID.String}
		if l.PreviousID.Valid {
			query += " and id != $2"
			args = append(args, l.PreviousID.Int64)
		}
		err := db.Get().GetContext(ctx, &id, query, args...)
		if err != nil && err != sql.ErrNoRows {
			return nil, errors.Wrap(err, "database error validating ucs_lender_id on a lender record. ")
		}
		if id > 0 {
			validationErrs["ucs_lender_id"] = "UCS Lender ID already exists."
		}
	}

	if l.TekionLenderID.String != "" {
		var id int
		query := `select id 
		from lenders 
		where tekion_lender_id = $1 and deleted_at is null`
		args := []interface{}{l.TekionLenderID.String}
		if l.PreviousID.Valid {
			query += " and id != $2"
			args = append(args, l.PreviousID.Int64)
		}
		err := db.Get().GetContext(ctx, &id, query, args...)
		if err != nil && err != sql.ErrNoRows {
			return nil, errors.Wrap(err, "database error validating tekion_lender_id on a lender record. ")
		}
		if id > 0 {
			validationErrs["tekion_lender_id"] = "Tekion Lender ID already exists."
		}
	}

	return validationErrs, nil
}

// LenderCreate creates a lender
func LenderCreate(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	var lender lenderPayload
	ctx := req.Context()
	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&lender)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage("Bad request. Could not read data.", nil)
	}
	lender.CreatedByUserID = user.ID

	lender.clean()
	valErrs, err := lender.validate(ctx)
	if err != nil {
		err = errors.Wrap(err, "error on lender validate")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error creating lender", nil)
	}
	if len(valErrs) > 0 {
		return http.StatusBadRequest, handlers.ErrorMessage("There were some validation errors.", map[string]interface{}{"errors": valErrs})
	}

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		err = errors.Wrap(err, "error on beginning lenders insert transaction")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error creating lender", nil)
	}

	query := `insert into lenders (
		created_at, created_by_user_id, intacct_vendor_id, name,
		address, city, state_code, postal_code, lhm_cdk_finance_code, 
		asbury_cdk_finance_code, ucs_lender_id, tekion_lender_id, add_vta_overallownce, 
		is_active, e_sale_api_display_name, certifiable_makes, cancellation_lender_id,
		is_digital_reserves
	) values (
		now() at time zone 'utc', :created_by_user_id, :intacct_vendor_id, :name,
		:address, :city, :state_code, :postal_code, :lhm_cdk_finance_code, 
		:asbury_cdk_finance_code, :ucs_lender_id, :tekion_lender_id, :add_vta_overallownce, 
		:is_active, :e_sale_api_display_name, :certifiable_makes, :cancellation_lender_id,
		:is_digital_reserves
	) returning id, created_at`
	stmt, err := tx.PrepareNamedContext(ctx, query)
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "error on preparing lenders insert")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error creating lender", nil)
	}
	defer func() { _ = stmt.Close() }()
	err = stmt.Get(&lender, lender)
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "error on executing lenders insert")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error creating lender", nil)
	}

	// Save digital reserve rules (including when empty to clear existing rules)
	err = db.SaveDigitalReserveRulesCreate(ctx, tx, lender.ID, lender.DigitalReserveRules)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error saving digital reserve rules", nil)
	}

	var mailPayload lenderMailPayload
	mailPayload.Name = lender.Name
	mailPayload.IntacctVendorID = lender.IntacctVendorID
	mailPayload.Address = lender.Address
	mailPayload.AsburyCDKFinanceCode = lender.AsburyCDKFinanceCode
	mailPayload.UCSLenderID = lender.UCSLenderID
	mailPayload.City = lender.City
	mailPayload.LHMCDKFinanceCode = lender.LHMCDKFinanceCode
	mailPayload.TekionLenderID = lender.TekionLenderID
	mailPayload.PostalCode = lender.PostalCode
	mailPayload.StateCode = lender.StateCode
	mailPayload.CertifiableMakes = strings.Join(lender.CertifiableMakes, " ")
	LenderNewMail(ctx, req, mailPayload)

	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "error on committing lenders insert transaction")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error creating lender", nil)
	}

	return http.StatusOK, map[string]interface{}{"id": lender.ID}
}

// LenderUpdate updates a lender (delete and insert new one)
func LenderUpdate(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()

	var curLender db.Lender
	query := `select * from lenders where id = $1 and deleted_at is null`
	err := db.Get().Unsafe().GetContext(ctx, &curLender, query, chi.URLParam(req, "id"))
	if err != nil {
		if err != sql.ErrNoRows {
			err = errors.Wrap(err, "error finding current lender for update")
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Error updating lender", nil)
		}
		return http.StatusNotFound, handlers.ErrorMessage("Lender not found", nil)
	}

	var mailPayload lenderMailPayload
	mailPayload.OldName = curLender.Name
	mailPayload.OldIntacctVendorID = curLender.IntacctVendorID
	mailPayload.OldAddress = curLender.Address
	mailPayload.OldAsburyCDKFinanceCode = curLender.AsburyCDKFinanceCode
	mailPayload.OldUCSLenderID = curLender.UCSLenderID
	mailPayload.OldTekionLenderID = curLender.TekionLenderID
	mailPayload.OldCity = curLender.City
	mailPayload.OldLHMCDKFinanceCode = curLender.LHMCDKFinanceCode
	mailPayload.OldPostalCode = curLender.PostalCode
	mailPayload.OldStateCode = curLender.StateCode
	mailPayload.OldESaleAPIDisplayName = curLender.ESaleAPIDisplayName
	mailPayload.OldCertifiableMakes = strings.Join(curLender.CertifiableMakes, " ")

	var lender lenderPayload
	dec := json.NewDecoder(req.Body)
	err = dec.Decode(&lender)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage("Bad request. Could not read data.", nil)
	}
	curLender.DeletedByUserID = sql.NullInt64{
		Valid: true,
		Int64: int64(user.ID),
	}
	lender.CreatedByUserID = user.ID
	lender.PreviousID = sql.NullInt64{
		Valid: true,
		Int64: int64(curLender.ID),
	}
	lender.clean()
	valErrs, err := lender.validate(ctx)
	if err != nil {
		err = errors.Wrap(err, "error on lender validate")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error updating lender", nil)
	}
	if len(valErrs) > 0 {
		return http.StatusBadRequest, handlers.ErrorMessage("There were some validation errors.", map[string]interface{}{"errors": valErrs})
	}

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		err = errors.Wrap(err, "error on beginning lenders update transaction")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error updating lender", nil)
	}

	query = `update lenders set
		deleted_at = now() at time zone 'utc',
		deleted_by_user_id = :deleted_by_user_id
		where id = :id
		returning deleted_at`
	updateStmt, err := tx.PrepareNamedContext(ctx, query)
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "error on preparing lenders update")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error updating lender", nil)
	}
	defer func() { _ = updateStmt.Close() }()
	err = updateStmt.GetContext(ctx, &curLender, curLender)
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "error on executing lenders update")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error updating lender", nil)
	}

	query = `insert into lenders (
		created_at, created_by_user_id, previous_id, intacct_vendor_id, name,
		address, city, state_code, postal_code, lhm_cdk_finance_code, 
		asbury_cdk_finance_code,  ucs_lender_id, tekion_lender_id, add_vta_overallownce, 
		is_active, e_sale_api_display_name, certifiable_makes, cancellation_lender_id,
		is_digital_reserves
	) values (
		now() at time zone 'utc', :created_by_user_id, :previous_id, :intacct_vendor_id, :name,
		:address, :city, :state_code, :postal_code, :lhm_cdk_finance_code, 
		:asbury_cdk_finance_code, :ucs_lender_id, :tekion_lender_id, :add_vta_overallownce, 
		:is_active, :e_sale_api_display_name, :certifiable_makes, :cancellation_lender_id,
		:is_digital_reserves
	) returning id, created_at`
	insertStmt, err := tx.PrepareNamedContext(ctx, query)
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "error on preparing lenders insert")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error updating lender", nil)
	}
	defer func() { _ = insertStmt.Close() }()
	err = insertStmt.GetContext(ctx, &lender, lender)
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "error on executing lenders insert")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error updating lender", nil)
	}

	// Update digital reserve rules efficiently (update existing rules to new lender ID, only create new ones if needed)
	err = db.SaveDigitalReserveRulesUpdate(ctx, tx, curLender.ID, lender.ID, lender.DigitalReserveRules)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error saving digital reserve rules", nil)
	}

	if lender.PreviousID.Valid && lender.PreviousID.Int64 > 0 {
		query = `update sales
				set lender_id = $1
				where lender_id = $2`
		_, err = tx.ExecContext(ctx, query, lender.ID, lender.PreviousID.Int64)
		if err != nil {
			_ = tx.Rollback()
			err = errors.Wrap(err, "error on executing lender_id update in sales")
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Error updating lender", nil)
		}

		query = `update contracts
				set lender_id = $1
				where lender_id = $2`
		_, err = tx.ExecContext(ctx, query, lender.ID, lender.PreviousID.Int64)
		if err != nil {
			_ = tx.Rollback()
			err = errors.Wrap(err, "error on executing lender_id update in contracts")
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Error updating lender", nil)
		}

		query = `update contract_forms_lenders
			set lender_id = $1
			where lender_id = $2`
		_, err = tx.ExecContext(ctx, query, lender.ID, lender.PreviousID.Int64)
		if err != nil {
			_ = tx.Rollback()
			err = errors.Wrap(err, "error on executing lender_id update in contrat forms")
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Error updating lender", nil)
		}
	}

	// If lender name is changed then add new name to the product rules
	if curLender.Name != lender.Name {

		// Get all product rules which have previous lender name
		var productRules []db.ProductRule
		query := `select rule, id 
				from product_rules
				where rule::text like '%lender%'
					and rule ::text like  CONCAT( '%',$1::varchar,'%')
					and deleted_at is null`
		err = db.Get().SelectContext(ctx, &productRules, query, curLender.Name)
		if err != nil && err != sql.ErrNoRows {
			_ = tx.Rollback()
			err = errors.Wrap(err, "error on getting related product rules")
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Error updating lender", nil)
		}

		var validateUpdate = func(lenderName interface{}) bool {
			if x, ok := lenderName.([]interface{}); ok {
				for _, v := range x {
					if v.(string) == curLender.Name {
						return true
					}
				}
			}
			return false
		}

		// Go through all product rules
		for _, pr := range productRules {
			var rule struct {
				Items []ruleItem `json:"items,omitempty"`
				Logic string     `json:"logic"`
			}
			// Convert json rule to struct
			err := json.Unmarshal([]byte(pr.Rule), &rule)
			if err != nil {
				_ = tx.Rollback()
				err = errors.Wrap(err, "error on processing related product rules")
				handlers.ReportError(req, err)
				return http.StatusInternalServerError, handlers.ErrorMessage("Error updating lender", nil)
			}

			// Update the lender names for lender property
			for index, item := range rule.Items {
				item.updateRulePropertyValue(rule.Items, "lender", lender.Name, index, validateUpdate)
			}

			// Convert struct to json string
			jsonBytes, err := json.Marshal(rule)
			if err != nil {
				_ = tx.Rollback()
				err = errors.Wrap(err, "error on getting related product rules")
				handlers.ReportError(req, err)
				return http.StatusInternalServerError, handlers.ErrorMessage("Error updating lender", nil)
			}

			updateQuery := `update product_rules set rule = $1 where id = $2`
			_, err = tx.ExecContext(ctx, updateQuery, string(jsonBytes), pr.ID)
			if err != nil {
				_ = tx.Rollback()
				err = errors.Wrap(err, "error on updating related product rules")
				handlers.ReportError(req, err)
				return http.StatusInternalServerError, handlers.ErrorMessage("Error updating lender", nil)
			}
		}
	}

	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "error on committing lenders update transaction")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error updating lender", nil)
	}

	mailPayload.Name = lender.Name
	mailPayload.IntacctVendorID = lender.IntacctVendorID
	mailPayload.Address = lender.Address
	mailPayload.City = lender.City
	mailPayload.LHMCDKFinanceCode = lender.LHMCDKFinanceCode
	mailPayload.AsburyCDKFinanceCode = lender.AsburyCDKFinanceCode
	mailPayload.UCSLenderID = lender.UCSLenderID
	mailPayload.TekionLenderID = lender.TekionLenderID
	mailPayload.PostalCode = lender.PostalCode
	mailPayload.StateCode = lender.StateCode
	mailPayload.ESaleAPIDisplayName = lender.ESaleAPIDisplayName
	mailPayload.CertifiableMakes = strings.Join(lender.CertifiableMakes, " ")
	LenderEditMail(ctx, req, mailPayload)

	return http.StatusOK, map[string]interface{}{"id": lender.ID}
}

func (i *ruleItem) updateRulePropertyValue(items []ruleItem, propertyName string, valueToUpdate interface{}, itemIndex int, validateUpdate func(interface{}) bool) {
	if i.Property == propertyName && validateUpdate(items[itemIndex].Value) {
		switch v := items[itemIndex].Value.(type) {
		case []interface{}:
			items[itemIndex].Value = append(v, valueToUpdate)
		case string, int, bool, float64:
			items[itemIndex].Value = valueToUpdate
		default:
			items[itemIndex].Value = v
		}
	}

	if len(i.Items) > 0 {
		for index, it := range i.Items {
			it.updateRulePropertyValue(i.Items, propertyName, valueToUpdate, index, validateUpdate)
		}
	}
}

type ruleItem struct {
	Property string      `json:"property,omitempty"`
	Operator string      `json:"operator,omitempty"`
	Value    interface{} `json:"value,omitempty"`
	Logic    string      `json:"logic,omitempty"`
	Items    []ruleItem  `json:"items,omitempty"`
}

// LenderShow selects a lender
func LenderShow(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	var lender db.Lender
	ctx := req.Context()
	lenderID := chi.URLParam(req, "id")
	query := `select * from lenders where id = $1 and deleted_at is null`
	err := db.Get().Unsafe().GetContext(ctx, &lender, query, lenderID)
	if err != nil {
		if err != sql.ErrNoRows {
			err = errors.Wrap(err, "error finding current lender for update")
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Error updating lender", nil)
		}
		return http.StatusNotFound, handlers.ErrorMessage("Lender not found", nil)
	}

	// Fetch digital reserve rules using new schema
	multiselectRules, err := db.GetLenderDigitalReserveRules(ctx, lender.ID)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error loading lender digital reserve rules", nil)
	}

	// Convert lender struct to map and add digital reserve fields
	lenderMap, err := handlers.StructToAPIResponse(lender)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error processing lender data", nil)
	}
	lenderMap["digital_reserve_rules"] = multiselectRules

	return http.StatusOK, map[string]interface{}{
		"lender": lenderMap,
	}
}

// LenderSupportingData returns supporting data for lender form
func LenderSupportingData(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	var makes []string
	ctx := req.Context()
	err := db.Get().SelectContext(ctx, &makes, `select distinct(make) from dataone_vehicles order by make asc`)
	if err != nil {
		err = errors.Wrap(err, "error getting makes list for store supporting data")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error loading supporting data", nil)
	}

	var cancellationLenders []db.CancellationLender
	err = db.Get().Unsafe().SelectContext(ctx, &cancellationLenders, `select * from cancellation_lenders order by name asc`)
	if err != nil {
		err = errors.Wrap(err, "error getting cancellation lenders for store supporting data")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error loading supporting data", nil)
	}

	var stores []struct {
		ID   int    `json:"id" db:"id"`
		Code string `json:"code" db:"code"`
		Name string `json:"name" db:"name"`
	}
	err = db.Get().SelectContext(ctx, &stores, `select id, code, name from stores order by code asc`)
	if err != nil {
		err = errors.Wrap(err, "error getting stores for lender supporting data")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error loading supporting data", nil)
	}

	var productTypes []struct {
		ID   int    `json:"id" db:"id"`
		Name string `json:"name" db:"name"`
	}
	err = db.Get().SelectContext(ctx, &productTypes, `select id, name from product_types order by position asc`)
	if err != nil {
		err = errors.Wrap(err, "error getting product types for lender supporting data")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error loading supporting data", nil)
	}

	var cancelReasons []struct {
		ID   int    `db:"id" json:"id"`
		Name string `db:"name" json:"name"`
	}
	err = db.Get().SelectContext(ctx, &cancelReasons, `select id, name from cancel_reasons order by id`)
	if err != nil {
		err = errors.Wrap(err, "error getting cancel reasons list for lender supporting data")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error loading supporting data", nil)
	}

	return http.StatusOK, map[string]interface{}{
		"makes":                makes,
		"cancellation_lenders": cancellationLenders,
		"stores":               stores,
		"product_types":        productTypes,
		"cancel_reasons":       cancelReasons,
		"states":               db.States,
	}
}
