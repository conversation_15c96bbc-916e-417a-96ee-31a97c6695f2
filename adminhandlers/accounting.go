package adminhandlers

import (
	"context"
	"database/sql"
	"database/sql/driver"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"time"
	"whiz/conf"
	"whiz/db"
	"whiz/handlers"
	"whiz/intacct"
	"whiz/types"
	"whiz/util"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"github.com/lib/pq"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"gopkg.in/guregu/null.v3"
)

const (
	billsPerPage       = 20
	billDateFormat     = "2006/01/02"
	refundTypeCustomer = "Cancel"
	refundTypeSalesTax = "Sales Tax"
)

// AccountingRulesSupportingData returns supporting data for creating, editing and viewing accounting rules
func AccountingRulesSupportingData(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()
	const errMsg = "Error getting Accounting Rules supporting data."
	productTypes := []struct {
		ID   int    `db:"id" json:"id"`
		Name string `db:"name" json:"name"`
	}{}
	err := db.Get().SelectContext(ctx, &productTypes, "select id, name from product_types order by position")
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "unable to get product types"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	products := []struct {
		ID            int    `db:"id" json:"id"`
		Name          string `db:"name" json:"name"`
		ProductTypeID int    `db:"product_type_id" json:"product_type_id"`
	}{}
	sql := `select id, name, product_type_id
		from products
		order by name`
	err = db.Get().SelectContext(ctx, &products, sql)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "unable to get products"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	productVariants := []struct {
		ID            int    `db:"id" json:"id"`
		Name          string `db:"name" json:"name"`
		ProductTypeID int    `db:"product_type_id" json:"product_type_id"`
	}{}
	sql = `select pv.id, pv.name, p.product_type_id
		from product_variants pv
		join products p on p.id = pv.product_id
		order by pv.name`
	err = db.Get().SelectContext(ctx, &productVariants, sql)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "unable to get product variants"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	companies := []struct {
		ID             int      `db:"id" json:"id"`
		Name           string   `db:"name" json:"name"`
		CompanyGroupID null.Int `db:"company_group_id" json:"company_group_id"`
	}{}
	sql = `select id, code || ' - ' || name as name, company_group_id
	from companies
	order by name`
	err = db.Get().SelectContext(ctx, &companies, sql)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "unable to get companies"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	stores := []struct {
		ID             int      `db:"id" json:"id"`
		Name           string   `db:"name" json:"name"`
		CompanyID      int      `db:"company_id" json:"company_id"`
		CompanyGroupID null.Int `db:"company_group_id" json:"company_group_id"`
	}{}
	sql = `select s.id, s.code || ' - ' || s.name as name, c.id company_id, c.company_group_id
	from stores s
	join companies c on c.id = s.company_id
	order by s.name`
	err = db.Get().SelectContext(ctx, &stores, sql)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "unable to get stores"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	type idNamePair struct {
		ID   string `db:"id" json:"id"`
		Name string `db:"name" json:"name"`
	}

	type idCodeNameTuple struct {
		ID   int    `db:"id" json:"id"`
		Code string `db:"code" json:"code"`
		Name string `db:"name" json:"name"`
	}

	type intacctInvoice struct {
		ID   int    `db:"id" json:"id"`
		Name string `db:"name" json:"name"`
	}
	type intacctOpts struct {
		Accounts  []idNamePair     `json:"accounts"`
		Products  []idNamePair     `json:"products"`
		Invoices  []intacctInvoice `json:"invoices"`
		Vendors   []idNamePair     `json:"vendors"`
		Locations []idNamePair     `json:"locations"`
	}

	var accounts []idNamePair
	sql = `select id, name from intacct_gl_accounts order by id`
	err = db.Get().SelectContext(ctx, &accounts, sql)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "unable to get Intacct accounts"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	var intacctProducts []idNamePair
	sql = `select distinct ip.id, '(' || ip.id || ') ' || ip.name as name
		from intacct_products ip
		join intacct_product_invoices ipi on ipi.intacct_product_id = ip.id
		union 
		select ip.id, '(' || ip.id || ') ' || ip.name as name
		from intacct_products ip
		where ip.id = 'LCA_2000' -- RSA
		order by id`
	err = db.Get().SelectContext(ctx, &intacctProducts, sql)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "unable to get Intacct products"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	var intacctInvoices []intacctInvoice
	sql = `select id, '(' || intacct_product_id || ') ' || name as name
		from intacct_product_invoices
		order by position
	`
	err = db.Get().SelectContext(ctx, &intacctInvoices, sql)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "unable to get Intacct invoice products"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	var vendors []idNamePair
	sql = `select id, name from intacct_vendors order by id`
	err = db.Get().SelectContext(ctx, &vendors, sql)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "unable to get Intacct vendors"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	var locations []idNamePair
	sql = `select id, name from intacct_locations order by id`
	err = db.Get().SelectContext(ctx, &locations, sql)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "unable to get Intacct locations"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	intacctOptions := intacctOpts{
		Accounts:  accounts,
		Products:  intacctProducts,
		Invoices:  intacctInvoices,
		Vendors:   vendors,
		Locations: locations,
	}

	var transactionTypes []idCodeNameTuple
	sql = `select id, code, name from accounting_rule_transaction_types`
	err = db.Get().SelectContext(ctx, &transactionTypes, sql)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "unable to get accounting rule transactionTypes"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	var accountingValues []idCodeNameTuple
	sql = `select id, code, name from accounting_rule_values`
	err = db.Get().SelectContext(ctx, &accountingValues, sql)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "unable to get accounting rule values"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	var paymentTypes []idCodeNameTuple
	sql = `select id, code, name from payment_types`
	err = db.Get().SelectContext(ctx, &paymentTypes, sql)
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "unable to get payment types"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	var cancelPayees []idCodeNameTuple
	sql = `select id, code, name from cancel_payees`
	err = db.Get().SelectContext(ctx, &cancelPayees, sql)
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "unable to get cancel payees"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	var states []idCodeNameTuple
	sql = `select id, code, name from states order by name`
	err = db.Get().SelectContext(ctx, &states, sql)
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "unable to get states"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	var companyGroups []idCodeNameTuple
	sql = `select id, name from company_groups order by name`
	err = db.Get().SelectContext(ctx, &companyGroups, sql)
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "unable to get company groups"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	type mappingDescriptionData struct {
		ID          int    `db:"id" json:"id"`
		Description string `db:"description" json:"description"`
	}
	var transactionDescriptions []mappingDescriptionData
	sql = `select id, description
		from mapping_descriptions
		order by description`
	err = db.Get().SelectContext(ctx, &transactionDescriptions, sql)
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "unable to get transaction descriptions"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	return http.StatusOK, map[string]interface{}{
		"transaction_types":      transactionTypes,
		"product_types":          productTypes,
		"products":               products,
		"product_variants":       productVariants,
		"companies":              companies,
		"stores":                 stores,
		"payment_types":          paymentTypes,
		"intacct":                intacctOptions,
		"accounting_rule_values": accountingValues,
		"value_operators":        []string{OpCodePlus, OpCodeMinus},
		"cancel_payees":          cancelPayees,
		"states":                 states,
		"company_groups":         companyGroups,
		"mapping_descriptions":   transactionDescriptions,
	}
}

// AccountingRulesIndex returns a list of accounting rules
func AccountingRulesIndex(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()

	type accountingIndexResp struct {
		ID          int            `db:"id" json:"id"`
		Name        string         `db:"name" json:"name"`
		ProductType string         `db:"product_type" json:"product_type"`
		Products    pq.StringArray `db:"products" json:"products"`
		StartedOn   time.Time      `db:"started_on" json:"started_on"`
		EndedOn     null.Time      `db:"ended_on" json:"ended_on"`
	}

	sel := `id, name, started_on, ended_on, product_type, products`
	// ListQueries() does not work with group by, so nest the entire
	// query as a from clause
	frm := `(
		select ar.id, ar.name, ar.started_on, ar.ended_on,
			pt.name product_type,
			array_remove(array_agg(p.name), null) products
		from accounting_rules ar
		join product_types pt on pt.id = ar.product_type_id
		left join accounting_rule_products arp on arp.accounting_rule_id = ar.id
		left join products p on p.id = arp.product_id
		where ar.deleted_at is null
		group by ar.id, ar.id, pt.id
		order by pt.position, ar.started_on, ar.name
	) rules`
	wh := ""
	orderBy := ""

	page, err := strconv.Atoi(req.FormValue("page"))
	if err != nil {
		page = 1
	}
	pageSize, err := strconv.Atoi(req.FormValue("pageSize"))
	if err != nil {
		pageSize = 20
	}
	listQuery, countQuery := handlers.ListQueries(sel, frm, wh, orderBy, pageSize, page)

	var rules []accountingIndexResp
	err = db.Get().SelectContext(ctx, &rules, listQuery)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "unable to get accounting rules"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting Accounting Rules", nil)
	}
	count := 0
	err = db.Get().GetContext(ctx, &count, countQuery)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "unable to get accounting rules count"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting Accounting Rules", nil)
	}

	return http.StatusOK, map[string]interface{}{
		"rules": rules,
		"count": count,
	}
}

// constants that represent the possible op codes that can be used in an accounting rule calculation
const (
	OpCodePlus  = "+"
	OpCodeMinus = "-"
)

// constants that represent the possible types of accounting events that mappings can be
// created for.
const (
	EventTypeNew       = "NEW"
	EventTypeCancel    = "CANCEL"
	EventTypeReinstate = "REINSTATE"
	EventTypeAdjust    = "ADJUST"
)

type calculation struct {
	FormatVersion int         `json:"formatVersion"`
	Operations    []operation `json:"operations"`
}

type operation struct {
	OpCode   string `json:"opCode"`
	ValueRef string `json:"valueRef"`
}

func (c calculation) Value() (driver.Value, error) {
	return json.Marshal(c)
}

func (c *calculation) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	b, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(b, &c)
}

// AccountingRuleGet gets a specific accounting rule
func AccountingRuleGet(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()
	rule, status, err := getAccountingRule(ctx, chi.URLParam(req, "id"))
	if err != nil {
		handlers.ReportError(req, err)
		if status == http.StatusNotFound {
			return status, handlers.ErrorMessage("Not found.", nil)
		}
		return status, handlers.ErrorMessage("Error loading accounting rule.", nil)
	}

	return http.StatusOK, map[string]interface{}{
		"accounting_rule": rule,
	}
}

func getAccountingRule(ctx context.Context, ruleID string) (accountingRule, int, error) {
	var rule accountingRule
	query := `select ar.id, ar.name, ar.started_on, ar.ended_on, ar.payment_types, ar.created_by_user_id,
			ar.product_type_id, ar.intacct_product_invoice_id, 
			array_remove(array_agg(distinct p.id), null) product_ids,
			array_remove(array_agg(distinct pv.id), null) product_variant_ids,
			array_remove(array_agg(distinct c.id), null) company_ids,
			array_remove(array_agg(distinct s.id), null) store_ids,
			ar.is_products_inclusive, ar.is_product_variants_inclusive, ar.is_companies_inclusive,
			ar.is_stores_inclusive, ar.is_payment_types_inclusive
		from accounting_rules ar
			join product_types pt on pt.id = ar.product_type_id
			left join accounting_rule_products arp on arp.accounting_rule_id = ar.id
			left join products p on p.id = arp.product_id
			left join accounting_rule_product_variants arpv on arpv.accounting_rule_id = ar.id
			left join product_variants pv on pv.id = arpv.product_variant_id
			left join accounting_rule_companies arc on arc.accounting_rule_id = ar.id
			left join companies c on c.id = arc.company_id
			left join accounting_rule_stores ars on ars.accounting_rule_id = ar.id
			left join stores s on s.id = ars.store_id
		where ar.id = $1
		and ar.deleted_at is null
		group by ar.id, pt.id`
	err := db.Get().GetContext(ctx, &rule, query, ruleID)
	if err != nil {
		if err == sql.ErrNoRows {
			return rule, http.StatusNotFound, err
		}
		return rule, http.StatusInternalServerError, errors.Wrapf(err, "error loading accounting rule %s", ruleID)
	}

	sql := `select artm.id, md.description, artm.intacct_account_id, artm.mapping_description_id,
		artm.intacct_product_id, artm.intacct_vendor_id, artm.intacct_location_id,
		artm.accounting_rule_id, artm.accounting_rule_transaction_type_id,
		artm.calculation
		from accounting_rule_transaction_mappings artm
		join accounting_rule_transaction_types artt on artt.id = artm.accounting_rule_transaction_type_id
		join mapping_descriptions md on artm.mapping_description_id = md.id
		where artm.accounting_rule_id = $1
			and artt.code = $2 
		order by artm.intacct_account_id`

	newMapping := transactionMapping{
		TransactionType:                     EventTypeNew,
		AccountMappings:                     []accountMapping{},
		AccountingRuleTransactionVariations: []accountingRuleTransactionVariation{},
	}
	err = db.Get().SelectContext(ctx, &newMapping.AccountMappings, sql, ruleID, EventTypeNew)
	if err != nil {
		return rule, http.StatusInternalServerError, errors.Wrap(err, "error loading account mappings for new transactions")
	}
	err = getAccountingRuleVariations(ctx, &newMapping, ruleID, EventTypeNew)
	if err != nil {
		return rule, http.StatusInternalServerError, err
	}
	cancelMapping := transactionMapping{
		TransactionType:                     EventTypeCancel,
		AccountMappings:                     []accountMapping{},
		AccountingRuleTransactionVariations: []accountingRuleTransactionVariation{},
	}
	err = db.Get().SelectContext(ctx, &cancelMapping.AccountMappings, sql, ruleID, EventTypeCancel)
	if err != nil {
		return accountingRule{}, http.StatusInternalServerError, errors.Wrap(err, "error loading account mappings for cancel transactions")
	}
	err = getAccountingRuleVariations(ctx, &cancelMapping, ruleID, EventTypeCancel)
	if err != nil {
		return rule, http.StatusInternalServerError, err
	}
	reinstateMapping := transactionMapping{
		TransactionType:                     EventTypeReinstate,
		AccountMappings:                     []accountMapping{},
		AccountingRuleTransactionVariations: []accountingRuleTransactionVariation{},
	}
	err = db.Get().SelectContext(ctx, &reinstateMapping.AccountMappings, sql, ruleID, EventTypeReinstate)
	if err != nil {
		return accountingRule{}, http.StatusInternalServerError, errors.Wrap(err, "error loading account mappings for reinstate transactions")
	}
	err = getAccountingRuleVariations(ctx, &reinstateMapping, ruleID, EventTypeReinstate)
	if err != nil {
		return rule, http.StatusInternalServerError, err
	}
	adjustMapping := transactionMapping{
		TransactionType:                     EventTypeAdjust,
		AccountMappings:                     []accountMapping{},
		AccountingRuleTransactionVariations: []accountingRuleTransactionVariation{},
	}
	err = db.Get().SelectContext(ctx, &adjustMapping.AccountMappings, sql, ruleID, EventTypeAdjust)
	if err != nil {
		return accountingRule{}, http.StatusInternalServerError, errors.Wrap(err, "error loading account mappings for adjust transactions")
	}
	err = getAccountingRuleVariations(ctx, &adjustMapping, ruleID, EventTypeAdjust)
	if err != nil {
		return rule, http.StatusInternalServerError, err
	}

	rule.TransactionMappings = []transactionMapping{newMapping, cancelMapping, reinstateMapping, adjustMapping}
	return rule, http.StatusOK, nil
}

func getAccountingRuleVariations(ctx context.Context, mapping *transactionMapping, ruleID, eventType string) error {
	transactionVariationQuery := `select artv.id, artv.name, artv.accounting_rule_id,
		artv.accounting_rule_transaction_type_id, artv.is_payment_types_inclusive,
		artv.started_on, artv.ended_on, artv.is_company_groups_inclusive,
		artv.is_companies_inclusive, artv.is_stores_inclusive,
		artv.is_states_inclusive,
		array_remove(array_agg(distinct artvcg.company_group_id), null) company_group_ids,
		array_remove(array_agg(distinct artvc.company_id), null) company_ids,
		array_remove(array_agg(distinct artvs.store_id), null) store_ids,
		array_remove(array_agg(distinct artvpt.payment_type_id), null) payment_type_ids,
		array_remove(array_agg(distinct artvst.state_id), null) state_ids,
		array_remove(array_agg(distinct artvcp.cancel_payee_id), null) cancel_payee_ids
		from accounting_rule_transaction_variations artv
		join accounting_rule_transaction_types artt on artt.id = artv.accounting_rule_transaction_type_id
		left join accounting_rule_transaction_variation_company_groups artvcg on artv.id = artvcg.accounting_rule_transaction_variation_id
		left join accounting_rule_transaction_variation_companies artvc on artv.id = artvc.accounting_rule_transaction_variation_id
		left join accounting_rule_transaction_variation_stores artvs on artv.id = artvs.accounting_rule_transaction_variation_id
		left join accounting_rule_transaction_variation_payment_types artvpt on artv.id = artvpt.accounting_rule_transaction_variation_id
		left join accounting_rule_transaction_variation_states artvst on artv.id = artvst.accounting_rule_transaction_variation_id
		left join accounting_rule_transaction_variation_cancel_payees artvcp on artv.id = artvcp.accounting_rule_transaction_variation_id
		where artv.accounting_rule_id = $1
			and artt.code = $2
		group by artv.id
		order by artv.id`

	variationAccountsQuery := `select artvm.id, artvm.accounting_rule_transaction_variation_id, md.description,
       	artvm.mapping_description_id, artvm.intacct_account_id, artvm.intacct_product_id, artvm.intacct_vendor_id,
       	artvm.intacct_location_id, artvm.calculation, iga.normal_balance
		from accounting_rule_transaction_variation_mappings artvm
		join mapping_descriptions md on artvm.mapping_description_id = md.id
		join intacct_gl_accounts iga on iga.id = artvm.intacct_account_id
		where accounting_rule_transaction_variation_id = $1`

	err := db.Get().SelectContext(ctx, &mapping.AccountingRuleTransactionVariations, transactionVariationQuery, ruleID, eventType)
	if err != nil {
		return errors.Wrapf(err, "error loading account rule variations for %s transactions", eventType)
	}

	for index := range mapping.AccountingRuleTransactionVariations {
		variationID := mapping.AccountingRuleTransactionVariations[index].ID
		err = db.Get().SelectContext(ctx, &mapping.AccountingRuleTransactionVariations[index].TransactionVariationAccountMappings, variationAccountsQuery, variationID)
		if err != nil {
			return errors.Wrapf(err, "error loading variations accounts mapping for variation id %d", variationID)
		}
	}

	return nil
}

type accountingRule struct {
	ID                         int                  `db:"id" json:"id"`
	Name                       string               `db:"name" json:"name"`
	ProductTypeID              int                  `db:"product_type_id" json:"product_type_id"`
	ProductIDs                 pq.Int64Array        `db:"product_ids" json:"product_ids"`
	IsProductsInclusive        bool                 `db:"is_products_inclusive" json:"is_products_inclusive"`
	ProductVariantIDs          pq.Int64Array        `db:"product_variant_ids" json:"product_variant_ids"`
	IsProductVariantsInclusive bool                 `db:"is_product_variants_inclusive" json:"is_product_variants_inclusive"`
	CompanyIDs                 pq.Int64Array        `db:"company_ids" json:"company_ids"`
	IsCompaniesInclusive       bool                 `db:"is_companies_inclusive" json:"is_companies_inclusive"`
	PaymentTypes               pq.StringArray       `db:"payment_types" json:"payment_types"`
	IsPaymentTypesInclusive    bool                 `db:"is_payment_types_inclusive" json:"is_payment_types_inclusive"`
	StoreIDs                   pq.Int64Array        `db:"store_ids" json:"store_ids"`
	IsStoresInclusive          bool                 `db:"is_stores_inclusive" json:"is_stores_inclusive"`
	StartDate                  types.JSPQDate       `db:"started_on" json:"start_date"`
	EndDate                    types.JSPQNullDate   `db:"ended_on" json:"end_date"`
	CreatedByUserID            int                  `db:"created_by_user_id" json:"created_by_user_id"`
	TransactionMappings        []transactionMapping `db:"-" json:"transaction_mappings"`
	PreviousID                 null.Int             `db:"previous_id" json:"-"`
	IntacctProductInvoiceID    int                  `db:"intacct_product_invoice_id" json:"intacct_product_invoice_id"`
}

type accountingRuleClonePayload struct {
	Name      string             `db:"name" json:"name"`
	StartDate types.JSPQDate     `db:"started_on" json:"start_date"`
	EndDate   types.JSPQNullDate `db:"ended_on" json:"end_date"`
}

type transactionMapping struct {
	TransactionType                     string                               `db:"transaction_type" json:"transaction_type"`
	AccountMappings                     []accountMapping                     `db:"-" json:"account_mappings"`
	AccountingRuleTransactionVariations []accountingRuleTransactionVariation `db:"-" json:"accounting_rule_transaction_variations"`
}

type accountMapping struct {
	ID                              int         `db:"id" json:"id"`
	AccountingRuleID                int         `db:"accounting_rule_id" json:"accounting_rule_id"`
	Description                     null.String `db:"description" json:"description"`
	IntacctAccountID                string      `db:"intacct_account_id" json:"intacct_account_id"`
	IntacctProductID                string      `db:"intacct_product_id" json:"intacct_product_id"`
	IntacctVendorID                 null.String `db:"intacct_vendor_id" json:"intacct_vendor_id"`
	IntacctLocationID               null.String `db:"intacct_location_id" json:"intacct_location_id"`
	AccountingRuleTransactionTypeID int         `db:"accounting_rule_transaction_type_id" json:"accounting_rule_transaction_type_id"`
	Calculation                     calculation `db:"calculation" json:"calculation"`
	NormalBalance                   string      `db:"normal_balance" json:"-"`
	MappingDescriptionID            int         `db:"mapping_description_id" json:"mapping_description_id"`
}

type accountingRuleTransactionVariation struct {
	ID                                  int                                  `db:"id" json:"id"`
	Name                                string                               `db:"name" json:"name"`
	AccountingRuleID                    int                                  `db:"accounting_rule_id" json:"accounting_rule_id"`
	AccountingRuleTransactionTypeID     int                                  `db:"accounting_rule_transaction_type_id" json:"accounting_rule_transaction_type_id"`
	CancelPayeeIDs                      pq.Int64Array                        `db:"cancel_payee_ids" json:"cancel_payee_ids"`
	IsPaymentTypesInclusive             bool                                 `db:"is_payment_types_inclusive" json:"is_payment_types_inclusive"`
	PaymentTypeIDs                      pq.Int64Array                        `db:"payment_type_ids" json:"payment_type_ids"`
	StartDate                           types.JSPQDate                       `db:"started_on" json:"start_date"`
	EndDate                             types.JSPQNullDate                   `db:"ended_on" json:"end_date"`
	IsCompanyGroupsInclusive            bool                                 `db:"is_company_groups_inclusive" json:"is_company_groups_inclusive"`
	CompanyGroupIDs                     pq.Int64Array                        `db:"company_group_ids" json:"company_group_ids"`
	IsCompaniesInclusive                bool                                 `db:"is_companies_inclusive" json:"is_companies_inclusive"`
	CompanyIDs                          pq.Int64Array                        `db:"company_ids" json:"company_ids"`
	IsStoresInclusive                   bool                                 `db:"is_stores_inclusive" json:"is_stores_inclusive"`
	StoreIDs                            pq.Int64Array                        `db:"store_ids" json:"store_ids"`
	IsStatesInclusive                   bool                                 `db:"is_states_inclusive" json:"is_states_inclusive"`
	StateIDs                            pq.Int64Array                        `db:"state_ids" json:"state_ids"`
	CreatedByUserID                     int                                  `db:"created_by_user_id" json:"created_by_user_id"`
	TransactionVariationAccountMappings []transactionVariationAccountMapping `db:"-" json:"transaction_variation_account_mappings"`
}

type transactionVariationAccountMapping struct {
	ID                                   int         `db:"id" json:"id"`
	AccountingRuleTransactionVariationID int         `db:"accounting_rule_transaction_variation_id" json:"accounting_rule_transaction_variation_id"`
	Description                          null.String `db:"description" json:"description"`
	IntacctAccountID                     string      `db:"intacct_account_id" json:"intacct_account_id"`
	IntacctProductID                     string      `db:"intacct_product_id" json:"intacct_product_id"`
	IntacctVendorID                      null.String `db:"intacct_vendor_id" json:"intacct_vendor_id"`
	IntacctLocationID                    null.String `db:"intacct_location_id" json:"intacct_location_id"`
	Calculation                          calculation `db:"calculation" json:"calculation"`
	MappingDescriptionID                 int         `db:"mapping_description_id" json:"mapping_description_id"`
	NormalBalance                        string      `db:"normal_balance" json:"-"`
}

type mappingDescriptions struct {
	ID          int    `db:"id" json:"id"`
	Description string `db:"description" json:"description"`
}

func (p *accountingRule) clean() {
	p.Name = strings.TrimSpace(p.Name)
}

func (p accountingRule) validate() (map[string]string, error) {
	errs := map[string]string{}
	if p.Name == "" {
		errs["name"] = "Name is required."
	}

	if p.ProductTypeID == 0 {
		errs["product_type_id"] = "Product Type is required."
	}
	if (p.StartDate.Time == time.Time{}) {
		errs["start_date"] = "Start Date is required."
	}
	if p.EndDate.Valid && p.StartDate.Time.After(p.EndDate.Time) {
		errs["end_date"] = "End Date must be after Start Date."
	}

	for i, tMap := range p.TransactionMappings {
		for j, aMap := range tMap.AccountMappings {
			if aMap.Description.String == "" {
				key := fmt.Sprintf("transaction_mappings[%d].account_mappings[%d].description", i, j)
				errs[key] = "Description is Required."
			}

			if aMap.IntacctAccountID == "" {
				key := fmt.Sprintf("transaction_mappings[%d].account_mappings[%d].intacct_account_id", i, j)
				errs[key] = "Intacct Account is Required."
			}

			if aMap.IntacctProductID == "" {
				key := fmt.Sprintf("transaction_mappings[%d].account_mappings[%d].intacct_product_id", i, j)
				errs[key] = "Intacct Product is Required."
			}

			if len(aMap.Calculation.Operations) == 0 {
				key := fmt.Sprintf("transaction_mappings[%d].account_mappings[%d].calculation", i, j)
				errs[key] = "Calculation is Required."
			}
		}

		// validate variations
		for j, variation := range tMap.AccountingRuleTransactionVariations {
			if variation.Name == "" {
				errs["variation_name"] = "Name is required for variation."
			}
			if len(variation.CancelPayeeIDs) == 0 {
				errs["cancel_payee_id"] = "Cancel Payee is required for variation."
			}
			if (variation.StartDate.Time == time.Time{}) {
				errs["start_date"] = "Start Date is required for variation."
			}
			if variation.EndDate.Valid && variation.StartDate.Time.After(variation.EndDate.Time) {
				errs["end_date"] = "End Date must be after Start Date for variation."
			}
			if len(variation.TransactionVariationAccountMappings) == 0 {
				errs["transaction_variation_account_mappings"] = "At least one transaction is required for variation."
			}
			for k, variationAccount := range variation.TransactionVariationAccountMappings {
				if variationAccount.Description.String == "" {
					key := fmt.Sprintf("transaction_mappings[%d].accounting_rule_variations[%d].variation_account_mappings[%d].description", i, j, k)
					errs[key] = "Description is Required for variation."
				}

				if variationAccount.IntacctAccountID == "" {
					key := fmt.Sprintf("transaction_mappings[%d].accounting_rule_variations[%d].variation_account_mappings[%d].intacct_account_id", i, j, k)
					errs[key] = "Intacct Account is Required for variation."
				}

				if variationAccount.IntacctProductID == "" {
					key := fmt.Sprintf("transaction_mappings[%d].accounting_rule_variations[%d].variation_account_mappings[%d].intacct_product_id", i, j, k)
					errs[key] = "Intacct Product is Required for variation."
				}

				if len(variationAccount.Calculation.Operations) == 0 {
					key := fmt.Sprintf("transaction_mappings[%d].accounting_rule_variations[%d].variation_account_mappings[%d].calculation", i, j, k)
					errs[key] = "Calculation is Required for variation."
				}
			}
		}

		// TODO: validate that calculations balance
	}
	return errs, nil
}

// AccountingRuleCreate creates a new accounting rule
func AccountingRuleCreate(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()

	payload, err := getAccountingRulePayload(req)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusBadRequest, handlers.ErrorMessage("Bad request", nil)
	}
	payload.clean()
	validationErrors, err := payload.validate()
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("error during validation", nil)
	}
	if len(validationErrors) > 0 {
		return http.StatusBadRequest, handlers.ErrorMessage("Validation error(s)", map[string]interface{}{"validation_errors": validationErrors})
	}
	payload.CreatedByUserID = user.ID
	id, err := saveAccountingRule(ctx, payload, req)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error saving accounting rule", nil)
	}

	return http.StatusOK, map[string]interface{}{"id": id}
}

func saveAccountingRule(ctx context.Context, payload accountingRule, req *http.Request) (int, error) {
	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		return 0, errors.Wrap(err, "error starting transaction")
	}
	id, err := accountingRuleCreate(ctx, tx, req, payload)
	if err != nil {
		tx.Rollback()
		return 0, errors.Wrap(err, "error creating accounting rule")
	}

	err = tx.Commit()
	if err != nil {
		return 0, errors.Wrap(err, "error committing transaction")
	}
	return id, nil
}

// AccountingRuleClone clones an existing accounting rule
func AccountingRuleClone(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()
	rule, status, err := getAccountingRule(ctx, chi.URLParam(req, "id"))
	if err != nil {
		handlers.ReportError(req, err)
		if status == http.StatusNotFound {
			return status, handlers.ErrorMessage("Not found.", nil)
		}
		return status, handlers.ErrorMessage("Error loading accounting rule.", nil)
	}

	payload, err := getAccountingRuleClonePayload(req)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusBadRequest, handlers.ErrorMessage("Bad request", nil)
	}
	rule.ID = 0
	rule.Name = payload.Name
	rule.StartDate = payload.StartDate
	rule.EndDate = payload.EndDate
	rule.clean()
	validationErrors, err := rule.validate()
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("error during validation", nil)
	}
	if len(validationErrors) > 0 {
		return http.StatusBadRequest, handlers.ErrorMessage("Validation error(s)", map[string]interface{}{"validation_errors": validationErrors})
	}
	rule.CreatedByUserID = user.ID

	id, err := saveAccountingRule(ctx, rule, req)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error creating accounting rule", nil)
	}

	return http.StatusOK, map[string]interface{}{"id": id}
}

// AccountingRuleDelete marks an accounting rule as deleted
func AccountingRuleDelete(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()

	const errMsg = "Error deleting accounting rule."
	id, err := strconv.Atoi(chi.URLParam(req, "id"))
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage("Invalid Accounting Rule ID", nil)
	}

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error starting transaction"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	err = accountingRuleDelete(ctx, tx, user, id)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error deleting accounting rule"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	err = tx.Commit()
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error comitting transaction"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}
	return http.StatusOK, map[string]interface{}{}
}

// AccountingRuleUpdate updates an accounting rule
func AccountingRuleUpdate(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()

	payload, err := getAccountingRulePayload(req)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage("Bad request.", nil)
	}

	const errMsg = "Error saving accounting rule."
	var prevID int
	err = db.Get().GetContext(ctx, &prevID, `select id from accounting_rules where id = $1 and deleted_at is null`, chi.URLParam(req, "id"))
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage("Not found.", nil)
		}
		handlers.ReportError(req, errors.Wrap(err, "error loading accounting rule id"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	payload.clean()
	validationErrors, err := payload.validate()
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "errors during validation"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}
	if len(validationErrors) > 0 {
		return http.StatusBadRequest, handlers.ErrorMessage("Validation error(s)", map[string]interface{}{"validation_errors": validationErrors})
	}
	payload.CreatedByUserID = user.ID
	payload.PreviousID = null.IntFrom(int64(prevID))

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error starting transaction"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	// "update" by soft deleting the previous record and creating a new one
	err = accountingRuleDelete(ctx, tx, user, prevID)
	if err != nil {
		tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "error replacing accounting rule"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	id, err := accountingRuleCreate(ctx, tx, req, payload)
	if err != nil {
		tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "error inserting new rule in accounting rule update"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}
	err = tx.Commit()
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error committing tranaction"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	return http.StatusOK, map[string]interface{}{
		"id": id,
	}
}

func getAccountingRulePayload(req *http.Request) (accountingRule, error) {
	decoder := json.NewDecoder(req.Body)
	payload := accountingRule{}
	err := decoder.Decode(&payload)
	if err != nil {
		err = errors.Wrap(err, "error decodeing accounting rule payload")
		return payload, err
	}

	return payload, nil
}

func getAccountingRuleClonePayload(req *http.Request) (accountingRuleClonePayload, error) {
	decoder := json.NewDecoder(req.Body)
	payload := accountingRuleClonePayload{}
	err := decoder.Decode(&payload)
	if err != nil {
		err = errors.Wrap(err, "error decodeing accounting rule payload")
		return payload, err
	}

	return payload, nil
}

func accountingRuleCreate(ctx context.Context, tx *sqlx.Tx, req *http.Request, rule accountingRule) (int, error) {
	var id int
	const sql = `insert into accounting_rules (
		created_at, created_by_user_id, name, product_type_id,
		is_products_inclusive, is_product_variants_inclusive,
		is_companies_inclusive, is_stores_inclusive, is_payment_types_inclusive,
		started_on, ended_on, payment_types, previous_id, intacct_product_invoice_id
	) values (
		now() at time zone 'utc', :created_by_user_id, :name, :product_type_id,
		:is_products_inclusive, :is_product_variants_inclusive,
		:is_companies_inclusive, :is_stores_inclusive, :is_payment_types_inclusive,
		:started_on, :ended_on, :payment_types, :previous_id, :intacct_product_invoice_id
	) returning id`

	stmt, err := tx.PrepareNamedContext(ctx, sql)
	if err != nil {
		return id, errors.Wrap(err, "error creating prepared statement to udpate accounting rule")
	}
	defer stmt.Close()

	err = stmt.GetContext(ctx, &id, rule)
	if err != nil {
		return id, errors.Wrap(err, "error inserting accounting rule")
	}

	// Link accounting rule to products
	if len(rule.ProductIDs) > 0 {
		q := db.BuildBulkInsertSQL("accounting_rule_products",
			[]string{"accounting_rule_id", "product_id"}, len(rule.ProductIDs))
		args := make([]interface{}, len(rule.ProductIDs)*2)
		for i, v := range rule.ProductIDs {
			args[2*i], args[2*i+1] = id, v
		}
		if _, err = tx.ExecContext(ctx, q, args...); err != nil {
			return id, errors.Wrap(err, "error linking accounting rule to products")
		}
	}

	// Link accounting rule to product variants
	if len(rule.ProductVariantIDs) > 0 {
		q := db.BuildBulkInsertSQL("accounting_rule_product_variants",
			[]string{"accounting_rule_id", "product_variant_id"}, len(rule.ProductVariantIDs))
		args := make([]interface{}, len(rule.ProductVariantIDs)*2)
		for i, v := range rule.ProductVariantIDs {
			args[2*i], args[2*i+1] = id, v
		}
		if _, err = tx.ExecContext(ctx, q, args...); err != nil {
			return id, errors.Wrap(err, "error linking accounting rule to product variants")
		}
	}

	// Link accounting rule to companies
	if len(rule.CompanyIDs) > 0 {
		q := db.BuildBulkInsertSQL("accounting_rule_companies",
			[]string{"accounting_rule_id", "company_id"}, len(rule.CompanyIDs))
		args := make([]interface{}, len(rule.CompanyIDs)*2)
		for i, v := range rule.CompanyIDs {
			args[2*i], args[2*i+1] = id, v
		}
		if _, err = tx.ExecContext(ctx, q, args...); err != nil {
			return id, errors.Wrap(err, "error linking accounting rule to companies")
		}
	}

	// Link accounting rule to stores
	if len(rule.StoreIDs) > 0 {
		q := db.BuildBulkInsertSQL("accounting_rule_stores",
			[]string{"accounting_rule_id", "store_id"}, len(rule.StoreIDs))
		args := make([]interface{}, len(rule.StoreIDs)*2)
		for i, v := range rule.StoreIDs {
			args[2*i], args[2*i+1] = id, v
		}
		if _, err = tx.ExecContext(ctx, q, args...); err != nil {
			return id, errors.Wrap(err, "error linking accounting rule to stores")
		}
	}
	descriptionMap, err := getDescriptionMap(ctx, tx)
	if err != nil {
		return id, err
	}

	// Save accounting rule transaction mappings
	numRows := 0
	for _, tranType := range rule.TransactionMappings {
		numRows += len(tranType.AccountMappings)
		for _, variation := range tranType.AccountingRuleTransactionVariations {
			variation.CreatedByUserID = rule.CreatedByUserID
			variation.AccountingRuleID = id
			_, err = accountingRuleTransactionVariationCreate(ctx, tx, variation, descriptionMap)
			if err != nil {
				return id, err
			}
		}
	}
	if numRows > 0 {
		cols := []string{
			"accounting_rule_id",
			"mapping_description_id",
			"intacct_account_id",
			"intacct_product_id",
			"intacct_vendor_id",
			"intacct_location_id",
			"accounting_rule_transaction_type_id",
			"calculation",
			"created_by_user_id",
		}
		numCols := len(cols)

		q := db.BuildBulkInsertSQL("accounting_rule_transaction_mappings", cols, numRows)
		args := make([]interface{}, numRows*numCols)
		row := 0
		for _, tranType := range rule.TransactionMappings {
			for _, accountMapping := range tranType.AccountMappings {
				descriptionID, ok := descriptionMap[accountMapping.Description.String]
				if !ok {
					descriptionID, err = mappingDescriptionCreate(ctx, tx, accountMapping.Description.String)
					if err != nil {
						return id, err
					}
					descriptionMap[accountMapping.Description.String] = descriptionID
				}
				args[numCols*row] = id
				args[numCols*row+1] = descriptionID
				args[numCols*row+2] = accountMapping.IntacctAccountID
				args[numCols*row+3] = accountMapping.IntacctProductID
				args[numCols*row+4] = accountMapping.IntacctVendorID
				args[numCols*row+5] = accountMapping.IntacctLocationID
				args[numCols*row+6] = accountMapping.AccountingRuleTransactionTypeID
				args[numCols*row+7] = accountMapping.Calculation
				args[numCols*row+8] = rule.CreatedByUserID

				row++
			}
		}
		if _, err = tx.ExecContext(ctx, q, args...); err != nil {
			return id, errors.Wrap(err, "error saving accounting_rule_transaction_mappings")
		}
	}

	return id, nil
}

func getDescriptionMap(ctx context.Context, tx *sqlx.Tx) (map[string]int, error) {
	var descriptions []mappingDescriptions
	query := `select id, description from mapping_descriptions`
	err := tx.SelectContext(ctx, &descriptions, query)
	if err != nil {
		return nil, errors.Wrap(err, "error loading mapping descriptions")
	}
	descriptionMap := make(map[string]int, len(descriptions))
	for index := range descriptions {
		descriptionMap[descriptions[index].Description] = descriptions[index].ID
	}
	return descriptionMap, nil
}

func accountingRuleDelete(ctx context.Context, tx *sqlx.Tx, user db.CurrentUser, id int) error {
	q := `update accounting_rules
		set deleted_at = now() at time zone 'utc',
		deleted_by_user_id = $1
		where id = $2`
	_, err := tx.ExecContext(ctx, q, user.ID, id)
	if err != nil {
		return errors.Wrap(err, "error deleting accounting rule")
	}
	err = accountingRuleTransactionMappingDelete(ctx, tx, user, id)
	if err != nil {
		return err
	}
	err = accountingRuleTransactionVariationMappingDelete(ctx, tx, user, id)
	if err != nil {
		return err
	}
	return nil
}

func accountingRuleTransactionMappingDelete(ctx context.Context, tx *sqlx.Tx, user db.CurrentUser, accountingRuleID int) error {
	q := `update accounting_rule_transaction_mappings
		set deleted_at = now() at time zone 'utc',
		deleted_by_user_id = $1
		where accounting_rule_id = $2`
	_, err := tx.ExecContext(ctx, q, user.ID, accountingRuleID)
	if err != nil {
		return errors.Wrap(err, "error deleting transaction mappings for accounting rule")
	}
	return nil
}

func accountingRuleTransactionVariationMappingDelete(ctx context.Context, tx *sqlx.Tx, user db.CurrentUser, accountingRuleID int) error {
	q := `update accounting_rule_transaction_variation_mappings
		set deleted_at = now() at time zone 'utc',
		deleted_by_user_id = $1
		where accounting_rule_transaction_variation_id in (
			select id from accounting_rule_transaction_variations
			where accounting_rule_id = $2
	)`
	_, err := tx.ExecContext(ctx, q, user.ID, accountingRuleID)
	if err != nil {
		return errors.Wrap(err, "error deleting transaction variation mappings for accounting rule")
	}
	return nil
}

func accountingRuleTransactionVariationCreate(
	ctx context.Context,
	tx *sqlx.Tx,
	ruleVariation accountingRuleTransactionVariation,
	descriptionMap map[string]int,
) (int, error) {
	var id int
	const sql = `insert into accounting_rule_transaction_variations (
		created_at, created_by_user_id, accounting_rule_id, accounting_rule_transaction_type_id,
		name, is_payment_types_inclusive,
		started_on, ended_on, is_company_groups_inclusive, is_companies_inclusive,
		is_stores_inclusive, is_states_inclusive
	) values (
		now() at time zone 'utc', :created_by_user_id, :accounting_rule_id, :accounting_rule_transaction_type_id,
		:name, :is_payment_types_inclusive,
		:started_on, :ended_on, :is_company_groups_inclusive, :is_companies_inclusive,
		:is_stores_inclusive, :is_states_inclusive
	) returning id`

	stmt, err := tx.PrepareNamedContext(ctx, sql)
	if err != nil {
		return id, errors.Wrap(err, "error creating prepared statement to udpate accounting rule")
	}
	defer stmt.Close()

	err = stmt.GetContext(ctx, &id, ruleVariation)
	if err != nil {
		return id, errors.Wrap(err, "error inserting accounting rule transaction variation")
	}

	// Link accounting rule variation to cancel payees
	if len(ruleVariation.CancelPayeeIDs) > 0 {
		q := db.BuildBulkInsertSQL("accounting_rule_transaction_variation_cancel_payees",
			[]string{"accounting_rule_transaction_variation_id", "cancel_payee_id"}, len(ruleVariation.CancelPayeeIDs))
		args := make([]interface{}, len(ruleVariation.CancelPayeeIDs)*2)
		for i, value := range ruleVariation.CancelPayeeIDs {
			args[2*i], args[2*i+1] = id, value
		}
		if _, err = tx.ExecContext(ctx, q, args...); err != nil {
			return id, errors.Wrap(err, "error linking accounting rule transaction variation to cancel payees")
		}
	}

	// Link accounting rule variation to payment types
	if len(ruleVariation.PaymentTypeIDs) > 0 {
		q := db.BuildBulkInsertSQL("accounting_rule_transaction_variation_payment_types",
			[]string{"accounting_rule_transaction_variation_id", "payment_type_id"}, len(ruleVariation.PaymentTypeIDs))
		args := make([]interface{}, len(ruleVariation.PaymentTypeIDs)*2)
		for i, value := range ruleVariation.PaymentTypeIDs {
			args[2*i], args[2*i+1] = id, value
		}
		if _, err = tx.ExecContext(ctx, q, args...); err != nil {
			return id, errors.Wrap(err, "error linking accounting rule transaction variation to payment types")
		}
	}

	// Link accounting rule variation to states
	if len(ruleVariation.StateIDs) > 0 {
		q := db.BuildBulkInsertSQL("accounting_rule_transaction_variation_states",
			[]string{"accounting_rule_transaction_variation_id", "state_id"}, len(ruleVariation.StateIDs))
		args := make([]interface{}, len(ruleVariation.StateIDs)*2)
		for i, value := range ruleVariation.StateIDs {
			args[2*i], args[2*i+1] = id, value
		}
		if _, err = tx.ExecContext(ctx, q, args...); err != nil {
			return id, errors.Wrap(err, "error linking accounting rule transaction variation to states")
		}
	}

	// Link accounting rule variation to company groups
	if len(ruleVariation.CompanyGroupIDs) > 0 {
		q := db.BuildBulkInsertSQL("accounting_rule_transaction_variation_company_groups",
			[]string{"accounting_rule_transaction_variation_id", "company_group_id"}, len(ruleVariation.CompanyGroupIDs))
		args := make([]interface{}, len(ruleVariation.CompanyGroupIDs)*2)
		for i, value := range ruleVariation.CompanyGroupIDs {
			args[2*i], args[2*i+1] = id, value
		}
		if _, err = tx.ExecContext(ctx, q, args...); err != nil {
			return id, errors.Wrap(err, "error linking accounting rule transaction variation to company groups")
		}
	}

	// Link accounting rule variation to companies
	if len(ruleVariation.CompanyIDs) > 0 {
		q := db.BuildBulkInsertSQL("accounting_rule_transaction_variation_companies",
			[]string{"accounting_rule_transaction_variation_id", "company_id"}, len(ruleVariation.CompanyIDs))
		args := make([]interface{}, len(ruleVariation.CompanyIDs)*2)
		for i, value := range ruleVariation.CompanyIDs {
			args[2*i], args[2*i+1] = id, value
		}
		if _, err = tx.ExecContext(ctx, q, args...); err != nil {
			return id, errors.Wrap(err, "error linking accounting rule transaction variation to companies")
		}
	}

	// Link accounting rule variation to stores
	if len(ruleVariation.StoreIDs) > 0 {
		q := db.BuildBulkInsertSQL("accounting_rule_transaction_variation_stores",
			[]string{"accounting_rule_transaction_variation_id", "store_id"}, len(ruleVariation.StoreIDs))
		args := make([]interface{}, len(ruleVariation.StoreIDs)*2)
		for i, value := range ruleVariation.StoreIDs {
			args[2*i], args[2*i+1] = id, value
		}
		if _, err = tx.ExecContext(ctx, q, args...); err != nil {
			return id, errors.Wrap(err, "error linking accounting rule transaction variation to stores")
		}
	}

	variationTransactionCount := len(ruleVariation.TransactionVariationAccountMappings)
	if variationTransactionCount > 0 {
		cols := []string{
			"accounting_rule_transaction_variation_id",
			"mapping_description_id",
			"intacct_account_id",
			"intacct_product_id",
			"intacct_vendor_id",
			"intacct_location_id",
			"calculation",
			"created_by_user_id",
		}
		numCols := len(cols)

		q := db.BuildBulkInsertSQL(
			"accounting_rule_transaction_variation_mappings",
			cols,
			variationTransactionCount,
		)
		args := make([]interface{}, variationTransactionCount*numCols)
		row := 0
		for _, accountMapping := range ruleVariation.TransactionVariationAccountMappings {
			descriptionID, ok := descriptionMap[accountMapping.Description.String]
			if !ok {
				descriptionID, err = mappingDescriptionCreate(ctx, tx, accountMapping.Description.String)
				if err != nil {
					return id, err
				}
				descriptionMap[accountMapping.Description.String] = descriptionID
			}
			args[numCols*row] = id
			args[numCols*row+1] = descriptionID
			args[numCols*row+2] = accountMapping.IntacctAccountID
			args[numCols*row+3] = accountMapping.IntacctProductID
			args[numCols*row+4] = accountMapping.IntacctVendorID
			args[numCols*row+5] = accountMapping.IntacctLocationID
			args[numCols*row+6] = accountMapping.Calculation
			args[numCols*row+7] = ruleVariation.CreatedByUserID

			row++
		}
		if _, err = tx.ExecContext(ctx, q, args...); err != nil {
			return id, errors.Wrap(err, "error saving accounting_rule_transaction_variation_mappings")
		}
	}

	return id, nil
}

type intacctProductInvoice struct {
	Name     string `json:"name" db:"name"`
	Position int    `json:"position" db:"position"`
	Prefix   string `json:"prefix" db:"prefix"`
}
type intacctProduct struct {
	ID                    string                `json:"id" db:"id"`
	Name                  string                `json:"name" db:"name"`
	IntacctLocationID     int                   `json:"intacct_location_id" db:"intacct_location_id"`
	IntacctProductInvoice intacctProductInvoice `json:"intacct_product_invoice" db:"-"`
}

func getIntacctProductPayload(req *http.Request) (intacctProduct, error) {
	decoder := json.NewDecoder(req.Body)
	payload := intacctProduct{}
	err := decoder.Decode(&payload)
	if err != nil {
		err = errors.Wrap(err, "error decoding intacct product payload")
		return payload, err
	}
	return payload, nil
}

func (p *intacctProduct) clean() {
	p.ID = strings.TrimSpace(p.ID)
	p.Name = strings.TrimSpace(p.Name)
	p.IntacctProductInvoice.Name = strings.TrimSpace(p.IntacctProductInvoice.Name)
	p.IntacctProductInvoice.Prefix = strings.TrimSpace(p.IntacctProductInvoice.Prefix)
}

func (p intacctProduct) validate() (map[string]string, error) {
	errs := map[string]string{}
	if p.ID == "" {
		errs["id"] = "Intacct Product ID is required."
	} else {
		id := ""
		err := db.Get().Get(&id, `select id from intacct_products where id = $1`, p.ID)
		if err != nil && err != sql.ErrNoRows {
			return errs, err
		}
		if id != "" {
			errs["id"] = "Intacct Product ID already exists."
		}
	}
	if p.Name == "" {
		errs["name"] = "Intacct Product Name is required."
	} else {
		id := ""
		err := db.Get().Get(&id, `select id from intacct_products where name = $1`, p.Name)
		if err != nil && err != sql.ErrNoRows {
			return errs, err
		}
		if id != "" {
			errs["name"] = "Intacct Product Name already exists."
		}
	}

	return errs, nil
}

// AccountingIntacctProductsCreate creates a new intacct product
func AccountingIntacctProductsCreate(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()

	payload, err := getIntacctProductPayload(req)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusBadRequest, handlers.ErrorMessage("Bad request", nil)
	}
	payload.clean()
	validationErrors, err := payload.validate()
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("error during validation", nil)
	}
	if len(validationErrors) > 0 {
		return http.StatusBadRequest, handlers.ErrorMessage("Validation error(s)", map[string]interface{}{"validation_errors": validationErrors})
	}

	const errMsg = "Error saving intacct product"
	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error starting transaction"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	query := `insert into intacct_products(
		id,name,intacct_location_id) values(
		:id,:name,:intacct_location_id
	)`

	stmt, err := tx.PrepareNamedContext(ctx, query)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error creating preparing named context"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}
	defer stmt.Close()

	_, err = stmt.ExecContext(ctx, payload)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error executing insert into intacct_products"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	query = `insert into intacct_product_invoices(
		intacct_product_id,name,position,prefix) values(
			:intacct_product_id,:name,:position,:prefix
	)`

	stmt, err = tx.PrepareNamedContext(ctx, query)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error preparing named context"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}
	defer stmt.Close()

	intacctInvoice := struct {
		intacctProductInvoice
		IntacctProductID string `db:"intacct_product_id" json:"-"`
	}{
		intacctProductInvoice: payload.IntacctProductInvoice,
		IntacctProductID:      payload.ID,
	}

	_, err = stmt.ExecContext(ctx, intacctInvoice)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error executing insert into intacct_product_invoices"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	err = tx.Commit()
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error committing transaction"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	return http.StatusOK, map[string]interface{}{"id": intacctInvoice.IntacctProductID}
}

type intacctGLAccount struct {
	ID            string `json:"id" db:"id"`
	Name          string `json:"name" db:"name"`
	NormalBalance string `json:"normal_balance" db:"normal_balance"`
}

func getIntacctGLAccountPayload(req *http.Request) (intacctGLAccount, error) {
	decoder := json.NewDecoder(req.Body)
	payload := intacctGLAccount{}
	err := decoder.Decode(&payload)
	if err != nil {
		err = errors.Wrap(err, "error decoding accounting rule payload")
		return payload, err
	}
	return payload, nil
}

func (p *intacctGLAccount) clean() {
	p.ID = strings.TrimSpace(p.ID)
	p.Name = strings.TrimSpace(p.Name)
}

func (p intacctGLAccount) validate() (map[string]string, error) {
	errs := map[string]string{}
	if p.ID == "" {
		errs["id"] = "Intacct Account ID is required"
	} else {
		id := ""
		err := db.Get().Get(&id, `select id from intacct_gl_accounts where id = $1`, p.ID)
		if err != nil && err != sql.ErrNoRows {
			return errs, err
		}
		if id != "" {
			errs["id"] = "Intacct Account ID already exists."
		}
	}
	if p.Name == "" {
		errs["name"] = "Intacct Account Name is required."
	} else {
		id := ""
		err := db.Get().Get(&id, `select id from intacct_gl_accounts where name = $1`, p.Name)
		if err != nil && err != sql.ErrNoRows {
			return errs, err
		}
		if id != "" {
			errs["name"] = "Intacct Account Name already exists."
		}
	}
	return errs, nil
}

// AccountingIntacctAccountsCreate creates a new intacct account
func AccountingIntacctAccountsCreate(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()

	payload, err := getIntacctGLAccountPayload(req)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusBadRequest, handlers.ErrorMessage("Bad request", nil)
	}
	payload.clean()
	validationErrors, err := payload.validate()
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("error during validation", nil)
	}
	if len(validationErrors) > 0 {
		return http.StatusBadRequest, handlers.ErrorMessage("Validation error(s)", map[string]interface{}{"validation_errors": validationErrors})
	}

	const errMsg = "Error saving intacct account"

	query := `insert into intacct_gl_accounts(
		id,name,normal_balance) values(
		:id,:name,:normal_balance
	)`

	stmt, err := db.Get().PrepareNamedContext(ctx, query)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error creating preparing named context"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}
	defer stmt.Close()

	_, err = stmt.ExecContext(ctx, payload)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error executing insert intacct_gl_accounts"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	return http.StatusOK, map[string]interface{}{"id": payload.ID}
}

type intacctVendor struct {
	ID   string `json:"id" db:"id"`
	Name string `json:"name" db:"name"`
}

func getIntacctVendorPayload(req *http.Request) (intacctVendor, error) {
	decoder := json.NewDecoder(req.Body)
	payload := intacctVendor{}
	err := decoder.Decode(&payload)
	if err != nil {
		err = errors.Wrap(err, "error decoding intacct vendor payload")
		return payload, err
	}
	return payload, nil
}

func (p *intacctVendor) clean() {
	p.ID = strings.TrimSpace(p.ID)
	p.Name = strings.TrimSpace(p.Name)
}

func (p intacctVendor) validate() (map[string]string, error) {
	errs := map[string]string{}
	if p.ID == "" {
		errs["id"] = "Intacct Vendor ID is required"
	} else {
		id := ""
		err := db.Get().Get(&id, `select id from intacct_vendors where id = $1`, p.ID)
		if err != nil && err != sql.ErrNoRows {
			return errs, err
		}
		if id != "" {
			errs["id"] = "Intacct Vendor ID already exists."
		}
	}
	if p.Name == "" {
		errs["name"] = "Intacct Vendor Name is required."
	} else {
		id := ""
		err := db.Get().Get(&id, `select id from intacct_vendors where name = $1`, p.Name)
		if err != nil && err != sql.ErrNoRows {
			return errs, err
		}
		if id != "" {
			errs["name"] = "Intacct Vendor Name already exists."
		}
	}
	return errs, nil
}

// AccountingIntacctVendorsCreate creates a new vendor
func AccountingIntacctVendorsCreate(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()

	payload, err := getIntacctVendorPayload(req)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusBadRequest, handlers.ErrorMessage("Bad request", nil)
	}
	payload.clean()
	validationErrors, err := payload.validate()
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("error during validation", nil)
	}
	if len(validationErrors) > 0 {
		return http.StatusBadRequest, handlers.ErrorMessage("Validation error(s)", map[string]interface{}{"validation_errors": validationErrors})
	}

	const errMsg = "Error saving intacct vendor"

	query := `insert into intacct_vendors(
		id,name) values(
		:id,:name
	)`

	stmt, err := db.Get().PrepareNamedContext(ctx, query)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error creating preparing named context"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}
	defer stmt.Close()

	_, err = stmt.ExecContext(ctx, payload)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error in insert intacct_vendors"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	return http.StatusOK, map[string]interface{}{"id": payload.ID}
}

// GetRuleTransactionDescriptions retrieves the list existing transaction mapping descriptions for the accounting rule
func GetRuleTransactionDescriptions(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()

	var descriptions []string
	query := `select description
		from accounting_rule_transaction_mappings
		where accounting_rule_id = $1
		and accounting_rule_transaction_type_id = $2
		order by description`

	err := db.Get().SelectContext(ctx, &descriptions, query, chi.URLParam(req, "id"), chi.URLParam(req, "transaction_type"))
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error loading transaction mapping descriptions"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error loading existing descriptions", nil)
	}

	return http.StatusOK, map[string]interface{}{"descriptions": descriptions}
}

// mappingDescriptionCreate creates a new mapping description for accounting rules
func mappingDescriptionCreate(ctx context.Context, tx *sqlx.Tx, description string) (int, error) {
	var id int
	mappingDescription := mappingDescriptions{
		Description: description,
	}
	const insertQuery = `insert into mapping_descriptions (description) values (:description) returning id`

	stmt, err := tx.PrepareNamedContext(ctx, insertQuery)
	if err != nil {
		return 0, errors.Wrap(err, "error creating prepared statement to insert description")
	}
	defer stmt.Close()

	err = stmt.GetContext(ctx, &id, mappingDescription)
	if err != nil {
		return 0, errors.Wrap(err, "error inserting description")
	}

	return id, nil
}

// The Bill number needs to be created in the format below,
// C for customer refund or L for Lender refund, Last 8 of the VIN #, the invoice Date and customer’s Last Name
// Example: C.********.042524.Nuttall
func createCancelSubmitBillNumber(ctx context.Context, contractID int, payeeType string) (string, error) {

	query := `
	select 
		ib.id invoice_batch_id,
		t.invoiced_at
	from contracts c 
		join transactions t 
			on t.contract_id = c.id
		join invoice_items ii
			on ii.transaction_id = t.id
		join invoices i
			on i.id = ii.invoice_id
		join invoice_batches ib
			on ib.id = i.invoice_batch_id
	where c.id = $1 and t.transaction_type = $2
	order by t.invoiced_at desc limit 1`

	invData := struct {
		InvoiceBatchID int       `db:"invoice_batch_id"`
		InvoicedAt     time.Time `db:"invoiced_at"`
	}{}
	err := db.Get().GetContext(ctx, &invData, query, contractID, db.TranTypeCancel)
	if err != nil {
		return "", errors.Wrap(err, "error getting invoiced at time")
	}

	vin := ""
	err = db.Get().GetContext(ctx, &vin, "select vin from vin_records where id = (select vin_record_id from contracts where id = $1)", contractID)
	if err != nil && err != sql.ErrNoRows {
		err = errors.Wrap(err, "error in getting vin")
		return "", err
	}

	customerName := struct {
		LastName     string `db:"last_name"`
		IsBusiness   bool   `db:"is_business"`
		BusinessName string `db:"business_name"`
	}{}

	query = `select 
		cu.last_name, cu.is_business, cu.business_name
	from contracts c
		join customers cu 
		on c.customer_id = cu.id 
	where c.id = $1`

	lastName := ""
	err = db.Get().GetContext(ctx, &customerName, query, contractID)
	if err != nil {
		err = errors.Wrap(err, "error getting customer name")
		return "", err
	}
	if customerName.IsBusiness {
		// remove special characters from business name
		acceptableExp := regexp.MustCompile(`[^a-zA-Z0-9 ]`)
		lastName = acceptableExp.ReplaceAllString(customerName.BusinessName, "")
		// replace spaces with underscore
		lastName = strings.ReplaceAll(lastName, " ", "_")
	} else {
		lastName = customerName.LastName
	}

	var prefix string
	if payeeType == db.CancelPayeeTypeLender {
		prefix = "L"
	} else {
		prefix = "C"
	}
	billNum := fmt.Sprintf("%s.%s.%s.%d.%s", prefix, vin[len(vin)-8:], invData.InvoicedAt.Format("010206"), invData.InvoiceBatchID, lastName)
	return billNum, nil

}

type cancelContractBill struct {
	ID             int             `json:"id"`
	Code           string          `json:"code"`
	CustomerName   string          `json:"customer_name"`
	BusinessName   string          `json:"business_name"`
	DmsNumber      string          `json:"dms_number"`
	CustomerRefund decimal.Decimal `json:"customer_refund"`
	SalesTax       decimal.Decimal `json:"sales_tax"`
	BillNumber     string          `json:"bill_number"`
	InvoiceDate    string          `json:"invoice_date"`
}

type groupedByDMSCancelContract struct {
	IDs               pq.Int64Array   `db:"ids"`
	Codes             pq.StringArray  `db:"codes"`
	InvoiceDates      pq.StringArray  `db:"invoice_dates"`
	CustomerName      string          `db:"customer_name"`
	BusinessName      string          `db:"business_name"`
	DmsNumber         string          `db:"dms_number"`
	CustomerRefunds   pq.Float64Array `db:"customer_refunds"`
	SalesTaxes        pq.Float64Array `db:"sales_taxes"`
	CRCustomerRefunds pq.Float64Array `db:"cr_customer_refunds"`
	CRSalesTaxes      pq.Float64Array `db:"cr_sales_taxes"`
	CRCCInvoiceDates  pq.StringArray  `db:"cr_cc_invoice_dates"`
	SalesTaxStartDate null.Time       `db:"sales_tax_start_date"`
	CCInvoiceIDs      pq.Int64Array   `db:"cc_invoice_ids"`
	CRInvoiceIDs      pq.Int64Array   `db:"cr_invoice_ids"`
}

func calculateBillWiseTotal(contractList []cancelContractBill) {
	var billRecordIndex int
	var billCustRefund decimal.Decimal
	var billSalesTax decimal.Decimal
	// calculate bill wise customer refund and sales tax
	for i, c := range contractList {
		if c.ID == 0 {
			billRecordIndex = i
		}
		billCustRefund = billCustRefund.Add(c.CustomerRefund)
		billSalesTax = billSalesTax.Add(c.SalesTax)

		if i == len(contractList)-1 || contractList[i+1].ID == 0 {
			contractList[billRecordIndex].CustomerRefund = billCustRefund
			contractList[billRecordIndex].SalesTax = billSalesTax
			billCustRefund = decimal.Zero
			billSalesTax = decimal.Zero
		}
	}
}

func processCancelContractList(ctx context.Context, gbdc []groupedByDMSCancelContract, payeeType string) ([]cancelContractBill, error) {
	// flatten the contract list and also add bill number
	var conList []cancelContractBill
	const dateTime = "2006-01-02 15:04:05"
	for _, c := range gbdc {
		// Extra record for customer name
		conList = append(conList, cancelContractBill{
			CustomerName: c.CustomerName,
			DmsNumber:    c.DmsNumber,
			BusinessName: c.BusinessName,
		})
		for j, id := range c.IDs {
			billNum, err := createCancelSubmitBillNumber(ctx, int(id), payeeType)
			if err != nil {
				return conList, errors.Wrap(err, "error creating bill number")
			}
			if j == 0 {
				conList[len(conList)-1].BillNumber = billNum
			}
			customerRefund := decimal.NewFromFloat(c.CustomerRefunds[j])
			salesTax := decimal.NewFromFloat(c.SalesTaxes[j])
			// check if there is an invoiced reinstate, the contract is cancelled and invoiced after the sales tax start date
			// then only subtract the customer refund and sales tax from the resinstate values
			if c.CRCCInvoiceDates[j] != "" && c.SalesTaxStartDate.Valid {
				invDt, _ := time.Parse(dateTime, c.CRCCInvoiceDates[j])
				if invDt.After(c.SalesTaxStartDate.Time) && c.CCInvoiceIDs[j] == c.CRInvoiceIDs[j] {
					customerRefund = decimal.NewFromFloat(c.CustomerRefunds[j]).Sub(decimal.NewFromFloat(c.CRCustomerRefunds[j]))
					salesTax = decimal.NewFromFloat(c.SalesTaxes[j]).Sub(decimal.NewFromFloat(c.CRSalesTaxes[j]))
				}
			}
			conList = append(conList, cancelContractBill{
				ID:             int(id),
				Code:           c.Codes[j],
				CustomerName:   c.CustomerName,
				BusinessName:   c.BusinessName,
				DmsNumber:      c.DmsNumber,
				CustomerRefund: customerRefund,
				SalesTax:       salesTax,
				BillNumber:     billNum,
				InvoiceDate:    c.InvoiceDates[j],
			})
		}
	}
	return conList, nil
}

// CancelledContractSubmitIndex returns the list of contracts that are invoiced and not voided
func CancelledContractSubmitIndex(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {

	query := `with results as (
		select c.id,
		c.code,
		cu.first_name || ' ' || cu.last_name as customer_name,
		cu.business_name,
		c.dms_number,
		cc.customer_refund,
		cc.sales_tax,
		t.invoiced_at::::date,
	    i.invoice_batch_id,
		st.sales_tax_start_date::::date,
		t.invoice_id
	from contracts c
		join contract_cancellations cc 
			on c.id=cc.contract_id
		join transactions t
			on t.id=cc.transaction_id
	    join invoices i
        	on t.invoice_id=i.id
		join customers cu 
			on c.customer_id = cu.id 
		join stores st
			on st.id = c.store_id
		join companies cp on cp.id=st.company_id
	where cc.is_void=false and
		cc.payee_type = :payee_type and
		cc.cancel_status= :cancel_status and 
		(cc.sales_tax > 0 or cc.customer_refund > 0)
		%s
	)
	select
		array_agg(results.invoiced_at) invoice_dates,
		array_agg(results.id) ids,
		array_agg(results.code) codes,
		results.customer_name,
		results.business_name,
		results.dms_number,
		results.sales_tax_start_date,
		array_agg(results.customer_refund) customer_refunds,
		array_agg(results.sales_tax) sales_taxes,
		(case when cc2.payee_type = :payee_type then array_agg(coalesce(cr.customer_amount,0)) else array_agg(0) end) cr_customer_refunds,
		(case when cc2.payee_type = :payee_type then array_agg(coalesce(cr.sales_tax,0)) else array_agg(0) end) cr_sales_taxes,
		array_agg(coalesce(t.invoiced_at,'infinity')) cr_cc_invoice_dates,
		array_agg(coalesce(t2.invoice_id,0)) cr_invoice_ids,
		array_agg(results.invoice_id) cc_invoice_ids
	from results
		left join contract_reinstatements cr
				on cr.contract_id = results.id
		left join contract_cancellations cc2
				on (cc2.id = cr.contract_cancellation_id)
		left join transactions t
				on t.id = cc2.transaction_id
		left join transactions t2
				on t2.id = cr.transaction_id
	where cr.is_void = false or cr.is_void is null
	%s`

	args := struct {
		PayeeType        string    `db:"payee_type"`
		CancelStatus     string    `db:"cancel_status"`
		CompanyGroupID   int       `db:"company_group_id"`
		StoreID          int       `db:"store_id"`
		State            string    `db:"state"`
		InvoiceBeginDate time.Time `db:"invoice_begin_date"`
		InvoiceEndDate   time.Time `db:"invoice_end_date"`
	}{
		PayeeType:    db.CancelPayeeTypeLender,
		CancelStatus: db.CancelStatusInvoicedPendingCheckRequest,
	}

	var err error
	var wh2 string
	if v := req.FormValue("company_group_id"); v != "" {
		wh2 = `and cp.company_group_id = :company_group_id `
		args.CompanyGroupID, err = strconv.Atoi(v)
		if err != nil {
			return http.StatusBadRequest, handlers.ErrorMessage("Bad request. Company Group ID invalid.", nil)
		}
	}

	if v := req.FormValue("store_id"); v != "" {
		wh2 = wh2 + ` and st.id = :store_id `
		args.StoreID, err = strconv.Atoi(v)
		if err != nil {
			return http.StatusBadRequest, handlers.ErrorMessage("Bad request. StoreID invalid.", nil)
		}
	}

	if v := req.FormValue("state"); v != "" {
		wh2 = wh2 + ` and st.state_code = :state `
		args.State = v
	}

	if v := req.FormValue("invoice_begin_date"); v != "" {
		wh2 = wh2 + ` and t.invoiced_at >= :invoice_begin_date`
		args.InvoiceBeginDate, err = time.Parse(time.RFC3339, v)
		if err != nil {
			handlers.ReportError(req, errors.Wrap(err, "error parsing begin date"))
			return http.StatusBadRequest, handlers.ErrorMessage("Incorrect value for invoice begin date", nil)
		}
	}
	if v := req.FormValue("invoice_end_date"); v != "" {
		wh2 = wh2 + ` and t.invoiced_at <= :invoice_end_date`
		args.InvoiceEndDate, err = time.Parse(time.RFC3339, v)
		if err != nil {
			handlers.ReportError(req, errors.Wrap(err, "error parsing end date"))
			return http.StatusBadRequest, handlers.ErrorMessage("Incorrect value for invoice end date", nil)
		}
	}

	query = fmt.Sprintf(query, wh2, ` group by customer_name, business_name, dms_number,
		cc2.payee_type, invoice_batch_id, sales_tax_start_date`)
	ctx := req.Context()

	var lenderGroupedContracts []groupedByDMSCancelContract
	stmt, err := db.Get().PrepareNamedContext(ctx, query)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error creating preparing named context"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error in getting data", nil)
	}
	defer stmt.Close()
	err = stmt.SelectContext(ctx, &lenderGroupedContracts, args)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error in getting cancel contract data"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error in getting data", nil)
	}

	lenderContractList, err := processCancelContractList(ctx, lenderGroupedContracts, db.CancelPayeeTypeLender)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error processing cancel contract list"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error in getting data", nil)
	}

	calculateBillWiseTotal(lenderContractList)

	stmt2, err := db.Get().PrepareNamedContext(ctx, query)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error creating preparing named context"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error in getting cancel contract list for lender payee", nil)
	}
	defer stmt2.Close()

	args.PayeeType = db.CancelPayeeTypeCustomer
	args.CancelStatus = db.CancelStatusInvoicedPendingCheckRequest
	var customerGroupedContracts []groupedByDMSCancelContract
	err = stmt2.SelectContext(ctx, &customerGroupedContracts, args)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error in getting cancel contract list for customer payee"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error in getting cancel contract list for customer payee", nil)
	}

	customerContractList, err := processCancelContractList(ctx, customerGroupedContracts, db.CancelPayeeTypeCustomer)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error processing cancel contract list"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error in getting cancel contract data", nil)
	}

	calculateBillWiseTotal(customerContractList)

	return http.StatusOK, map[string]interface{}{
		"lender_contracts":   lenderContractList,
		"customer_contracts": customerContractList,
	}
}

// CancelledContractSupportingData returns support data as list of company groups, stores and states
func CancelledContractSupportingData(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()

	// Get all the companies
	companyGroups := []struct {
		ID   int    `json:"id" db:"id"`
		Name string `json:"name" db:"name"`
	}{}
	err := db.Get().SelectContext(ctx, &companyGroups, `select id, name from company_groups order by name`)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error getting company groups"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error in getting company groups", nil)
	}

	// Get all the stores
	stores := []struct {
		ID   int    `json:"id" db:"id"`
		Code string `json:"code" db:"code"`
		Name string `json:"name" db:"name"`
	}{}
	err = db.Get().SelectContext(ctx, &stores, `select id, code, name from stores order by code`)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error getting stores"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error in getting stores", nil)
	}

	storeStates := []string{}
	err = db.Get().SelectContext(ctx, &storeStates, `select distinct(state_code) from stores order by state_code`)
	if err != nil {
		err = errors.Wrap(err, "db error getting states list from stores")
		handlers.ReportError(req, errors.Wrap(err, "error getting states list from stores"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting states list from stores", nil)
	}

	return http.StatusOK, map[string]interface{}{
		"company_groups": companyGroups,
		"stores":         stores,
		"states":         db.States,
		"store_states":   storeStates,
	}

}

type cancelContact struct {
	ContractID      int    `db:"contract_id"`
	PayeeType       string `db:"payee_type"`
	PayeeName       string `db:"payee_name"`
	PayeeAddress    string `db:"payee_address"`
	PayeeCity       string `db:"payee_city"`
	PayeeStateCode  string `db:"payee_state_code"`
	PayeePostalCode string `db:"payee_postal_code"`
	PayeeEmail      string `db:"payee_email"`
}

func getIntacctContacts(
	ctx context.Context,
	ids []string,
	companyGroupID, storeID, state string,
) ([]*cancelContact, int, string, error) {
	const errMsg = "Error in getting vendor upload data"
	const contactCountry = "USA"
	query := `
	select distinct on(cc.payee_name, c.dms_number, i.invoice_batch_id) cc.payee_name,
		cc.payee_type,
		cc.payee_address,
		cc.payee_city,
		cc.payee_state_code,
		cc.payee_postal_code,
	    coalesce(cc.email, '') payee_email,
		c.id contract_id
	from contracts c
		join contract_cancellations cc
			on c.id=cc.contract_id
		join transactions t
		    on t.id=cc.transaction_id
        join invoices i
			on t.invoice_id=i.id`

	args := struct {
		PayeeTypeLender   string `db:"payee_type_lender"`
		PayeeTypeCustomer string `db:"payee_type_customer"`
		CancelStatus      string `db:"cancel_status"`
		CompanyGroupID    int    `db:"company_group_id"`
		StoreID           int    `db:"store_id"`
		State             string `db:"state"`
	}{
		PayeeTypeLender:   db.CancelPayeeTypeLender,
		PayeeTypeCustomer: db.CancelPayeeTypeCustomer,
		CancelStatus:      db.CancelStatusInvoicedPendingCheckRequest,
	}

	var err error
	var wh2 string
	if companyGroupID != "" {
		wh2 = `and cp.company_group_id = :company_group_id `
		args.CompanyGroupID, err = strconv.Atoi(companyGroupID)
		if err != nil {
			return nil, http.StatusBadRequest, "Bad request: Company Group ID invalid", err
		}
	}

	if storeID != "" {
		wh2 = wh2 + ` and st.id = :store_id `
		args.StoreID, err = strconv.Atoi(storeID)
		if err != nil {
			return nil, http.StatusBadRequest, "Bad request: StoreID invalid", err
		}
	}

	if state != "" {
		wh2 = wh2 + ` and st.state_code = :state `
		args.State = state
	}

	contacts := []*cancelContact{}

	if len(ids) > 0 {
		contactArgs := []interface{}{db.CancelPayeeTypeLender, db.CancelPayeeTypeCustomer, db.CancelStatusInvoicedPendingCheckRequest}
		contactArgs = append(contactArgs, ids)
		wh := `
		where cc.is_void=false and
			(cc.payee_type = ? or cc.payee_type = ?) and
			cc.cancel_status= ? and
			(cc.sales_tax > 0 or cc.customer_refund > 0)`

		query = strings.Join([]string{query, wh, ` and c.id in (?) `}, " ")

		query, args, err := sqlx.In(query, contactArgs...)
		if err != nil {
			err = errors.Wrap(err, "error in sqlx.In")
			return nil, http.StatusInternalServerError, errMsg, err
		}

		query = db.Get().Rebind(query)
		err = db.Get().SelectContext(ctx, &contacts, query, args...)
		if err != nil {
			err = errors.Wrap(err, "error querying cancel bills data")
			return nil, http.StatusInternalServerError, errMsg, err
		}
	} else {
		wh := `
		where cc.is_void=false and
			(cc.payee_type = :payee_type_lender or cc.payee_type = :payee_type_customer) and
			cc.cancel_status= :cancel_status and
			(cc.sales_tax > 0 or cc.customer_refund > 0) `
		query = strings.Join([]string{query, wh, wh2}, " ")

		stmt, err := db.Get().PrepareNamedContext(ctx, query)
		if err != nil {
			err = errors.Wrap(err, "database error preparing cancel contacts query")
			return nil, http.StatusInternalServerError, errMsg, err
		}
		defer func() { _ = stmt.Close() }()

		err = stmt.SelectContext(ctx, &contacts, args)
		if err != nil {
			err = errors.Wrap(err, "error querying cancel contacts")
			return nil, http.StatusInternalServerError, errMsg, err
		}
	}

	return contacts, http.StatusOK, "", nil
}

// SubmitCancelContactDownload will return vendor upload data in csv format
func SubmitCancelContactDownload(w http.ResponseWriter, req *http.Request, user db.CurrentUser) {
	const errMsg = "Error in getting vendor upload data"
	const contactCountry = "USA"

	ctx := req.Context()
	ids := req.FormValue("ids")
	var filterIDs []string
	if ids != "" {
		filterIDs = strings.Split(ids, ",")
	}
	companyGroupID := req.FormValue("company_group_id")
	storeID := req.FormValue("store_id")
	state := req.FormValue("state")
	contacts, status, msg, err := getIntacctContacts(ctx, filterIDs, companyGroupID, storeID, state)
	if err != nil {
		if status != http.StatusBadRequest {
			util.ReportError(ctx, err)
		}
		w.WriteHeader(status)
		fmt.Fprint(w, msg)
		return

	}

	query := `
	select ib.id,
		t.invoiced_at
	from transactions t
		join invoice_items ii
			on ii.transaction_id = t.id
		join invoices i
			on i.id = ii.invoice_id
		join invoice_batches ib
			on ib.id = i.invoice_batch_id
	where t.transaction_type = $1 and t.contract_id = $2
	order by t.created_at desc limit 1`

	data := struct {
		ID         int       `db:"id"`
		InvoicedAt time.Time `db:"invoiced_at"`
	}{}

	if len(contacts) > 0 {
		err = db.Get().GetContext(ctx, &data, query, db.TranTypeCancel, contacts[0].ContractID)
		if err != nil {
			w.WriteHeader(http.StatusInternalServerError)
			err = errors.Wrap(err, "error getting invoice batch id")
			handlers.ReportError(req, err)
			fmt.Fprint(w, errMsg)
			return
		}
	}

	w.Header().Set("Content-Type", "text/csv")
	fileName := fmt.Sprintf("Contacts_%s_%d.csv", data.InvoicedAt.Format(db.ShortDateFormat), data.ID)
	w.Header().Set("Content-Disposition", "attachment; filename="+fileName)
	csvw := csv.NewWriter(w)

	err = csvw.Write([]string{
		"VENDOR_ID", "NAME",
		"CONTACT_NAME", "COMPANY_NAME", "FIRST_NAME", "LAST_NAME", "PRINT_AS", "EMAIL1",
		"ADDRESS1", "CITY", "STATE", "ZIP", "COUNTRY",
	})
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		err = errors.Wrap(err, "error writing csv")
		handlers.ReportError(req, err)
		fmt.Fprint(w, errMsg)
		return
	}

	var companyName, firstName, lastName, vendorID, vendorName string
	for _, c := range contacts {
		companyName = ""
		firstName = ""
		lastName = ""
		billNum, err := createCancelSubmitBillNumber(ctx, c.ContractID, c.PayeeType)
		if err != nil {
			w.WriteHeader(http.StatusInternalServerError)
			err = errors.Wrap(err, "error creating bill number")
			handlers.ReportError(req, err)
			fmt.Fprint(w, errMsg)
			return
		}

		if c.PayeeType == db.CancelPayeeTypeLender {
			companyName = c.PayeeName
			vendorID = db.GetCancelContractLenderVendorID()
			vendorName = db.GetCancelContractLenderVendorName()
		} else {
			if len(strings.Split(c.PayeeName, " ")) < 2 {
				lastName = strings.Split(c.PayeeName, " ")[0]
			} else {
				firstName = strings.Split(c.PayeeName, " ")[0]
				lastName = strings.Split(c.PayeeName, " ")[1]
			}
			vendorID = db.GetCancelContractCustomerVendorID()
			vendorName = db.GetCancelContractCustomerVendorName()
		}

		record := []string{
			vendorID,
			vendorName,
			strings.Join(
				[]string{c.PayeeName, billNum}, "."),
			companyName,
			firstName,
			lastName,
			c.PayeeName,
			c.PayeeEmail,
			c.PayeeAddress,
			c.PayeeCity,
			c.PayeeStateCode,
			c.PayeePostalCode,
			contactCountry,
		}

		if err := csvw.Write(record); err != nil {
			w.WriteHeader(http.StatusInternalServerError)
			err = errors.Wrap(err, "error writing csv")
			handlers.ReportError(req, err)
			fmt.Fprint(w, errMsg)
			return
		}
	}
	csvw.Flush()
}

// SubmitCancelBillsDownload will return bills doc in csv
func SubmitCancelBillsDownload(w http.ResponseWriter, req *http.Request, user db.CurrentUser) {
	const errMsg = "Error in getting bills doc"
	intacctConfig := conf.Get().Intacct

	query := `
	    select c.id,
		c.code,
		cc.payee_type,
		cc.payee_name,
		cc.customer_refund,
		cc.sales_tax,
		(case when cc2.payee_type = cc.payee_type then coalesce(cr.customer_amount,0) else 0 end) cr_customer_refund,
		(case when cc2.payee_type = cc.payee_type then coalesce(cr.sales_tax,0) else 0 end) cr_sales_tax,
		vr.vin,
		t1.invoiced_at as cr_invoice_date,
		st.sales_tax_start_date,
		t.invoice_id cc_invoice_id,
		t2.invoice_id cr_invoice_id
	from contracts c
		join stores st
			on c.store_id = st.id
		join vin_records vr
			on c.vin_record_id = vr.id
		join contract_cancellations cc
			on c.id=cc.contract_id
		join transactions t
			on t.id=cc.transaction_id
		left join contract_reinstatements cr
			on cr.contract_id  = c.id
		left join contract_cancellations cc2
			on (cc2.id = cr.contract_cancellation_id and cc2.payee_type = cc.payee_type)
		left join transactions t1
			on cc2.transaction_id=t1.id
		left join transactions t2
			on cr.transaction_id=t2.id
	where cc.is_void=false and c.id in (?)
		and (cr.is_void =false or cr.is_void is null)`

	ctx := req.Context()

	contracts := []struct {
		ID                int             `db:"id"`
		Code              string          `db:"code"`
		PayeeType         string          `db:"payee_type"`
		PayeeName         string          `db:"payee_name"`
		CustomerRefund    decimal.Decimal `db:"customer_refund"`
		SalesTax          decimal.Decimal `db:"sales_tax"`
		CRCustomerRefund  decimal.Decimal `db:"cr_customer_refund"`
		CRSalesTax        decimal.Decimal `db:"cr_sales_tax"`
		VIN               string          `db:"vin"`
		CRInvoiceDate     null.Time       `db:"cr_invoice_date"`
		SalesTaxStartDate null.Time       `db:"sales_tax_start_date"`
		CCInvoiceID       int             `db:"cc_invoice_id"`
		CRInvoiceID       null.Int        `db:"cr_invoice_id"`
	}{}

	if req.FormValue("ids") != "" {
		ids := strings.Split(req.FormValue("ids"), ",")
		query, args, err := sqlx.In(query, ids)
		if err != nil {
			w.WriteHeader(http.StatusInternalServerError)
			err = errors.Wrap(err, "error in sqlx.In")
			handlers.ReportError(req, err)
			fmt.Fprint(w, errMsg)
			return
		}
		query = db.Get().Rebind(query)
		err = db.Get().SelectContext(ctx, &contracts, query, args...)
		if err != nil {
			w.WriteHeader(http.StatusInternalServerError)
			err = errors.Wrap(err, "error querying cancel bills data")
			handlers.ReportError(req, err)
			fmt.Fprint(w, errMsg)
			return
		}
	}

	w.Header().Set("Content-Type", "text/csv")
	fileName := fmt.Sprintf("Bills_%s.csv", time.Now().Format("********"))
	w.Header().Set("Content-Disposition", "attachment; filename="+fileName)
	csvw := csv.NewWriter(w)

	err := csvw.Write([]string{
		"Bill Number", "Vendor ID", "Pay To", "Created Date", "Due Date",
		"Description", "Line No", "Memo", "Account No", "Location ID",
		"Amount", "AP Bill Item Vendor ID",
	})
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		err = errors.Wrap(err, "error writing csv")
		handlers.ReportError(req, err)
		fmt.Fprint(w, errMsg)
		return
	}

	currentDate := time.Now().Format(db.ShortDateFormat)
	csvRecord := func(lineNum, billNum, vendorID, payeeName, memo, glNo string, amount decimal.Decimal) []string {
		return []string{
			billNum,
			vendorID,
			strings.Join(
				[]string{payeeName, billNum}, "."),
			currentDate,
			currentDate,
			strings.Join([]string{currentDate, "Invoicing Cancellation Refund"}, " - "),
			lineNum,
			memo,
			glNo,
			strconv.Itoa(db.CancelContractLocationID),
			amount.StringFixed(2),
			vendorID,
		}
	}

	var lineNum int = 1
	var billNumbers = []string{}
	for i, c := range contracts {
		billNum, err := createCancelSubmitBillNumber(ctx, c.ID, c.PayeeType)
		if err != nil {
			w.WriteHeader(http.StatusInternalServerError)
			err = errors.Wrap(err, "error creating bill number")
			handlers.ReportError(req, err)
			fmt.Fprint(w, errMsg)
			return
		}
		billNumbers = append(billNumbers, billNum)
		var vendorID string
		if c.PayeeType == db.CancelPayeeTypeLender {
			vendorID = db.GetCancelContractLenderVendorID()
		} else {
			vendorID = db.GetCancelContractCustomerVendorID()
		}

		// line 1 cancel refund
		if c.CustomerRefund.GreaterThanOrEqual(decimal.Zero) {
			if i > 0 {
				if billNumbers[i] == billNumbers[i-1] {
					lineNum++
				} else {
					lineNum = 1
				}
			}
			customerRefund := c.CustomerRefund
			if c.CRInvoiceID.Valid && c.CCInvoiceID == int(c.CRInvoiceID.Int64) &&
				c.CRInvoiceDate.Valid && c.SalesTaxStartDate.Valid &&
				c.CRInvoiceDate.Time.After(c.SalesTaxStartDate.Time) {
				customerRefund = c.CustomerRefund.Sub(c.CRCustomerRefund)
			}
			record1 := csvRecord(
				strconv.Itoa(lineNum),
				billNum,
				vendorID,
				c.PayeeName,
				getCancelBillMemoString(c.Code, billNum, refundTypeCustomer),
				intacctConfig.CancelContractGLAccountNumber,
				customerRefund,
			)

			if err := csvw.Write(record1); err != nil {
				w.WriteHeader(http.StatusInternalServerError)
				err = errors.Wrap(err, "error writing csv")
				handlers.ReportError(req, err)
				fmt.Fprint(w, errMsg)
				return
			}
		}

		salesTax := c.SalesTax
		if c.CRInvoiceID.Valid && int(c.CRInvoiceID.Int64) == c.CCInvoiceID && c.CRInvoiceDate.Valid && c.SalesTaxStartDate.Valid &&
			c.CRInvoiceDate.Time.After(c.SalesTaxStartDate.Time) {
			salesTax = c.SalesTax.Sub(c.CRSalesTax)
		}
		// line 2 sales tax refund
		if c.SalesTax.GreaterThanOrEqual(decimal.Zero) {
			lineNum++
			record2 := csvRecord(
				strconv.Itoa(lineNum),
				billNum,
				vendorID,
				c.PayeeName,
				getCancelBillMemoString(c.Code, billNum, refundTypeSalesTax),
				intacctConfig.CancelContractGLAccountNumber,
				salesTax,
			)

			if err := csvw.Write(record2); err != nil {
				w.WriteHeader(http.StatusInternalServerError)
				err = errors.Wrap(err, "error writing csv")
				handlers.ReportError(req, err)
				fmt.Fprint(w, errMsg)
				return
			}
		}
	}
	csvw.Flush()
}

func getSubmitBillPayload(req *http.Request) ([]string, error) {
	decoder := json.NewDecoder(req.Body)
	payload := struct {
		IDs string `json:"ids"`
	}{}
	err := decoder.Decode(&payload)
	if err != nil {
		err = errors.Wrap(err, "error decoding cancel submit bill payload")
		return []string{}, err
	}
	return strings.Split(strings.TrimSpace(payload.IDs), ","), nil
}

type createCancelBillData struct {
	APBillItems              []db.BillItem
	PayeeType                string
	PayeeName                string
	RequestXML               []byte
	BillResponseRecordNumber int
	NoPayment                bool
}

// SubmitCancelContract will submit the cancel contract
func SubmitCancelContract(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	const errMsg = "Error in submitting bills to intacct"
	query := `
	select c.id, 
		c.code,
		cc.id contract_cancellation_id,
		cc.payee_type,
		cc.payee_name,
		cc.customer_refund,
		cc.sales_tax,
		case when cc2.payee_type = cc.payee_type then cr.customer_amount else 0 end cr_customer_refund,
		case when cc2.payee_type = cc.payee_type then cr.sales_tax else 0 end cr_sales_tax,
		vr.vin,
		t1.invoiced_at as cr_invoice_date,
		st.sales_tax_start_date,
		t.invoice_id cc_invoice_id,
		t2.invoice_id cr_invoice_id
	from contracts c
		join stores st
			on c.store_id = st.id
		join vin_records vr
			on vr.id = c.vin_record_id
		join contract_cancellations cc
			on c.id=cc.contract_id
		join transactions t
			on t.id=cc.transaction_id
		left join contract_reinstatements cr
			on cr.contract_id = c.id
		left join contract_cancellations cc2
			on (cc2.id = cr.contract_cancellation_id and cc2.payee_type = cc.payee_type)
		left join transactions t1
			on cc2.transaction_id=t1.id
		left join transactions t2
			on cr.transaction_id=t2.id
	where cc.is_void=false and c.id in (?) and
		(cr.is_void = false or cr.is_void is null)`

	ctx := req.Context()
	intacctConfig := conf.Get().Intacct

	contracts := []struct {
		ID                     int             `db:"id"`
		ContractCancellationID int             `db:"contract_cancellation_id"`
		Code                   string          `db:"code"`
		PayeeType              string          `db:"payee_type"`
		PayeeName              string          `db:"payee_name"`
		CustomerRefund         decimal.Decimal `db:"customer_refund"`
		SalesTax               decimal.Decimal `db:"sales_tax"`
		CRCustomerRefund       decimal.Decimal `db:"cr_customer_refund"`
		CRSalesTax             decimal.Decimal `db:"cr_sales_tax"`
		VIN                    string          `db:"vin"`
		CRInvoiceDate          null.Time       `db:"cr_invoice_date"`
		SalesTaxStartDate      null.Time       `db:"sales_tax_start_date"`
		CCInvoiceID            int             `db:"cc_invoice_id"`
		CRInvoiceID            null.Int        `db:"cr_invoice_id"`
	}{}

	ids, err := getSubmitBillPayload(req)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusBadRequest, handlers.ErrorMessage("Bad request", nil)
	}

	if len(ids) == 0 {
		return http.StatusBadRequest, handlers.ErrorMessage("Bad request: No data", nil)
	}
	query, args, err := sqlx.In(query, ids)
	if err != nil {
		err = errors.Wrap(err, "error in sqlx.In query")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}
	query = db.Get().Rebind(query)
	err = db.Get().SelectContext(ctx, &contracts, query, args...)
	if err != nil {
		err = errors.Wrap(err, "error querying cancel bills data")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	var itemsByBillNum = make(map[string]createCancelBillData)

	for _, c := range contracts {
		billNum, err := createCancelSubmitBillNumber(ctx, c.ID, c.PayeeType)
		if err != nil {
			err = errors.Wrap(err, "error creating bill number")
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
		}
		var billItems []db.BillItem

		// customer cancel refund
		customerRefund := c.CustomerRefund
		salesTax := c.SalesTax
		if c.CRInvoiceID.Valid && int(c.CRInvoiceID.Int64) == c.CCInvoiceID && c.CRInvoiceDate.Valid && c.SalesTaxStartDate.Valid &&
			c.CRInvoiceDate.Time.After(c.SalesTaxStartDate.Time) {
			customerRefund = c.CustomerRefund.Sub(c.CRCustomerRefund)
			salesTax = c.SalesTax.Sub(c.CRSalesTax)
		}
		if c.CustomerRefund.GreaterThanOrEqual(decimal.Zero) {
			billItems = append(billItems, db.BillItem{
				AccountNo:              intacctConfig.CancelContractGLAccountNumber,
				TrxAmount:              customerRefund,
				EntryDescription:       getCancelBillMemoString(c.Code, billNum, refundTypeCustomer),
				LocationID:             db.CancelContractLocationID,
				ContractID:             c.ID,
				ContractCancellationID: c.ContractCancellationID,
			})
		}

		//sales tax refund
		if c.SalesTax.GreaterThanOrEqual(decimal.Zero) {
			billItems = append(billItems, db.BillItem{
				AccountNo:              intacctConfig.CancelContractGLAccountNumber,
				TrxAmount:              salesTax,
				EntryDescription:       getCancelBillMemoString(c.Code, billNum, refundTypeSalesTax),
				LocationID:             db.CancelContractLocationID,
				ContractID:             c.ID,
				ContractCancellationID: c.ContractCancellationID,
			})
		}

		cbd, ok := itemsByBillNum[billNum]
		if !ok {
			itemsByBillNum[billNum] = createCancelBillData{
				APBillItems: billItems,
				PayeeType:   c.PayeeType,
				PayeeName:   c.PayeeName,
			}
		} else {
			cbd.APBillItems = append(cbd.APBillItems, billItems...)
			itemsByBillNum[billNum] = cbd
		}
	}
	sumOfBillItems := func(items []db.BillItem) decimal.Decimal {
		var total decimal.Decimal
		for _, item := range items {
			total = total.Add(item.TrxAmount)
		}
		return total
	}
	var vendorID string
	for billNum, cbd := range itemsByBillNum {
		if cbd.APBillItems == nil {
			continue
		}
		if cbd.PayeeType == db.CancelPayeeTypeLender {
			vendorID = db.GetCancelContractLenderVendorID()
		} else {
			vendorID = db.GetCancelContractCustomerVendorID()
		}

		if sumOfBillItems(cbd.APBillItems).LessThan(decimal.Zero) {
			cbd.NoPayment = true
			itemsByBillNum[billNum] = cbd
			continue
		}
		bill := db.CreateBillRequest{
			WhenCreated:               time.Now().Format(db.ShortDateFormat),
			WhenPosted:                time.Now().Format(db.ShortDateFormat),
			BillToPayToContactName:    strings.Join([]string{cbd.PayeeName, billNum}, "."),
			ShipToRetrunToContactName: strings.Join([]string{cbd.PayeeName, billNum}, "."),
			RecordID:                  billNum,
			VendorID:                  vendorID,
			Description:               "Cancel Contract Refund",
			TermName:                  db.CancelContractBillTermName,
			APBillItems:               cbd.APBillItems,
		}

		xml, err := intacct.CreateBillRequest(bill)
		if err != nil {
			return http.StatusInternalServerError, nil
		}
		cbd.RequestXML = xml
		itemsByBillNum[billNum] = cbd
	}

	err = updateNoPayment(ctx, itemsByBillNum, user.ID)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error updating no payment"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	failedBills, err := submitCancelBillsToIntacct(ctx, itemsByBillNum, user.ID)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error submitting cancel bills to intacct"))
		return http.StatusInternalServerError, handlers.ErrorMessage(strings.Join([]string{errMsg, err.Error()}, " "), nil)
	}
	if len(failedBills) > 0 {
		var failMsgs, billList []string
		var errMsg string
		for _, fb := range failedBills {
			if len(fb.Errors) > 0 {
				errMsg = fb.Errors[0].ErrorMessage
			}
			failMsgs = append(failMsgs, fmt.Sprintf("Bill Number: %s, Error: %s", fb.BillNumber, errMsg))
			billList = append(billList, fb.BillNumber)
		}
		failMsg := strings.Join(failMsgs, "\n")
		billMsg := strings.Join(billList, "\n")
		handlers.ReportError(req, errors.New("error in submitting bills to intacct"+failMsg))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error in submitting bills to intacct:"+billMsg, nil)
	}
	return http.StatusOK, nil
}

// ClearCancelContract will clear the cancel contract, set status to Bill Cleared in Cancellations
func ClearCancelContract(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	const errMsg = "Error in clearing cancel contracts"
	query := `
	select c.id, 
		cc.id contract_cancellation_id
	from contracts c
		join contract_cancellations cc
			on c.id=cc.contract_id
	where cc.is_void=false and c.id in (?)`

	ctx := req.Context()

	contracts := []struct {
		ID                     int `db:"id"`
		ContractCancellationID int `db:"contract_cancellation_id"`
	}{}

	ids, err := getSubmitBillPayload(req)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusBadRequest, handlers.ErrorMessage("Bad request", nil)
	}

	if len(ids) == 0 {
		return http.StatusBadRequest, handlers.ErrorMessage("Bad request: No data", nil)
	}
	query, args, err := sqlx.In(query, ids)
	if err != nil {
		err = errors.Wrap(err, "error in sqlx.In query")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}
	query = db.Get().Rebind(query)
	err = db.Get().SelectContext(ctx, &contracts, query, args...)
	if err != nil {
		err = errors.Wrap(err, "error querying cancel bills data")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		err = errors.Wrap(err, "error starting transaction in clear cancel contract")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	for _, c := range contracts {
		updateCC := `update contract_cancellations set cancel_status = $1 where id = $2`
		_, err := tx.ExecContext(ctx, updateCC, db.CancelStatusBillCleared, c.ContractCancellationID)
		if err != nil {
			_ = tx.Rollback()
			err = errors.Wrap(err, "error updating contract cancellations")
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
		}
	}
	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "error committing transaction for no payment cancel bill request")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}
	return http.StatusOK, nil
}

type intacctError struct {
	BillNumber string
	Errors     []db.IntacctError
}

func submitCancelBillsToIntacct(ctx context.Context, itemsByBillNum map[string]createCancelBillData, userID int) ([]intacctError, error) {
	var failedBills []intacctError
	for billNum, cbd := range itemsByBillNum {
		xml := cbd.RequestXML
		if cbd.NoPayment || xml == nil {
			continue
		}
		result, err := intacct.InvokeRequest(xml)
		if err != nil {
			return failedBills, errors.Wrap(err, "error invoking bill request")
		}

		if result.ResultStatus != "success" { // failure in creating bill
			if len(result.Errors) > 0 && result.Errors[0].ErrorNumber == db.IntacctErrorNoBillAlreadyExists {
				// if bill already exists then get the bill record number
				reqXML, err := intacct.GetBillRequest(billNum)
				if err != nil {
					util.LogError(ctx, errors.Wrap(err, "error creating get bill request xml"))
					continue
				}
				result, err = intacct.InvokeRequest(reqXML)
				if err != nil {
					util.LogError(ctx, errors.Wrap(err, "error invoking get bill request"))
					continue
				}
				if result.ResultStatus != "success" {
					intacctErr := intacctError{
						BillNumber: billNum,
						Errors:     result.Errors,
					}
					failedBills = append(failedBills, intacctErr)
					continue
				}
			} else {
				intacctErr := intacctError{
					BillNumber: billNum,
					Errors:     result.Errors,
				}
				failedBills = append(failedBills, intacctErr)
				continue
			}
		}
		cbd.BillResponseRecordNumber = result.BillRecordNo
		tx, err := db.Get().BeginTxx(ctx, nil)
		if err != nil {
			return failedBills, errors.Wrap(err, "error starting transaction in submit cancel bill")
		}
		err = cancelContractIntacctDBUpdate(ctx, tx, billNum, cbd, userID)
		if err != nil {
			return failedBills, errors.Wrap(err, "db error in updating intacct info for cancel bill request")
		}
		err = tx.Commit()
		if err != nil {
			return failedBills, errors.Wrap(err, "error committing transaction for cancel bill request")
		}
	}
	return failedBills, nil
}

func updateNoPayment(ctx context.Context, itemsByBillNum map[string]createCancelBillData, userID int) error {
	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		return errors.Wrap(err, "error starting transaction in update no payment cancel bill request")
	}
	updateCC := `update contract_cancellations set cancel_status = $1 where id = $2`
	for _, cbd := range itemsByBillNum {
		if cbd.NoPayment {
			for _, item := range cbd.APBillItems {
				_, err := tx.ExecContext(ctx, updateCC, db.CancelStatusBillCleared, item.ContractCancellationID)
				if err != nil {
					_ = tx.Rollback()
					return errors.Wrap(err, "error updating contract cancellations for bill cleared")
				}
			}
		}
	}
	err = tx.Commit()
	if err != nil {
		return errors.Wrap(err, "error committing transaction for no payment cancel bill request")
	}
	return nil
}

func cancelContractIntacctDBUpdate(ctx context.Context, tx *sqlx.Tx, billNum string, cbd createCancelBillData, userID int) error {
	insertQuery := `insert into cancel_contract_intacct_bills(
		bill_number,
		bill_item_memo,
		bill_item_amount,
		contract_id,
		contract_cancellation_id,
		created_at,
		created_by_user_id,
		intacct_bill_record_id) values (
		:bill_number,
		:bill_item_memo,
		:bill_item_amount,
		:contract_id,
		:contract_cancellation_id,
		now() at time zone 'utc',
		:created_by_user_id,
		:intacct_bill_record_id
		)`

	stmt, err := tx.PrepareNamedContext(ctx, insertQuery)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "error preparing insert statement for cancel contract intacct bills.")
	}
	defer func() { _ = stmt.Close() }()

	updateCC := `update contract_cancellations set cancel_status = $1 where id = $2`
	for _, item := range cbd.APBillItems {
		dbBill := db.CancelContractIntacctBill{
			BillNumber:             billNum,
			BillItemMemo:           item.EntryDescription,
			BillItemAmount:         item.TrxAmount,
			ContractID:             item.ContractID,
			ContractCancellationID: item.ContractCancellationID,
			CreatedByUserID:        userID,
			IntacctBillRecordID:    cbd.BillResponseRecordNumber,
		}
		_, err = stmt.ExecContext(ctx, dbBill)
		if err != nil {
			_ = tx.Rollback()
			return errors.Wrap(err, "error inserting cancel contract intacct bills.")
		}
		_, err = tx.ExecContext(ctx, updateCC, db.CancelStatusCheckRequestedPendingApproval, item.ContractCancellationID)
		if err != nil {
			_ = tx.Rollback()
			return errors.Wrap(err, "error updating contract cancellations")
		}
	}
	return nil
}

// bill is linked to PaymentDetail and PaymentDetail is linked to Payment
// first get PaymentDetail using BillNumber, if there is a valid PaymentKey
// then get Payment using PaymentKey
func getApPaymentIntacctInfo(ctx context.Context, billNum int) (*db.IntacctPaidInfo, error) {
	xml, err := intacct.PaymentDetailRequest(ctx, billNum)
	if err != nil {
		return &db.IntacctPaidInfo{}, errors.Wrap(err, "error getting payment detail request")
	}
	result, err := intacct.InvokeRequest(xml)
	if err != nil {
		return &db.IntacctPaidInfo{}, errors.Wrap(err, "error invoking get payment detail request")
	}
	if result.ResultStatus != "success" {
		errMsg := " "
		for _, e := range result.Errors {
			errMsg = errMsg + e.ErrorMessage + "\n"
		}
		return &db.IntacctPaidInfo{}, errors.New("error in intacct response for payment detail request: " + errMsg)
	}
	// if no payment key then return
	if result.ApPaymentDetailPaymentKey == 0 {
		return &db.IntacctPaidInfo{}, nil
	}

	var ipi db.IntacctPaidInfo
	ipi.PaymentRecordKey = result.ApPaymentDetailPaymentKey

	xml2, err := intacct.PaymentRequest(ctx, result.ApPaymentDetailPaymentKey)
	if err != nil {
		return &db.IntacctPaidInfo{}, errors.Wrap(err, "error getting payment request")
	}
	result, err = intacct.InvokeRequest(xml2)
	if err != nil {
		return &db.IntacctPaidInfo{}, errors.Wrap(err, "error invoking get payment request")
	}
	if result.ResultStatus != "success" {
		errMsg := " "
		for _, e := range result.Errors {
			errMsg = errMsg + e.ErrorMessage + "\n"
		}
		return &db.IntacctPaidInfo{}, errors.New("error in intacct response for payment request: " + errMsg)
	}

	ipi.CheckNumber = result.ApPaymentCheckNumber
	ipi.PaidAmount = result.ApPaymentPaidAmount
	ipi.PaidDate = result.ApPaymentPaidDate
	ipi.PaymentState = result.ApPaymentState
	return &ipi, nil
}

// UpdateCancelContractBillPayments will update the cancel contract bill payments
func UpdateCancelContractBillPayments(ctx context.Context) error {
	query := `
	select
		intacct_bill_record_id,
		contract_cancellation_id
	from cancel_contract_intacct_bills
	where intacct_payment_record_id is null
		and intacct_bill_record_id !=0
		and created_at > now() - interval '180 days'`

	var ccData = []struct {
		IntacctBillRecordID    int `db:"intacct_bill_record_id"`
		ContractCancellationID int `db:"contract_cancellation_id"`
	}{}
	err := db.Get().SelectContext(ctx, &ccData, query)
	if err != nil {
		return errors.Wrap(err, "db error getting pending cancel contract intacct bills")
	}

	update1 := `
	update cancel_contract_intacct_bills
		set updated_at = now() at time zone 'utc',
		check_number = $1,
		check_amount = $2,
		payment_date = $3,
		intacct_payment_record_id = $4
	where intacct_bill_record_id = $5`

	update2 := `
	update contract_cancellations
		set cancel_status = $1
	where id = $2`

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		return errors.Wrap(err, "error starting transaction in update cancel contract bill payments")
	}
	for i, cc := range ccData {
		paidInfo, err := getApPaymentIntacctInfo(ctx, ccData[i].IntacctBillRecordID)
		if err != nil {
			return errors.Wrap(err, "error getting ap payment intacct info")
		}
		if paidInfo.PaymentState == db.IntacctPaymentStateComplete {
			_, err = tx.ExecContext(ctx, update1, paidInfo.CheckNumber,
				paidInfo.PaidAmount,
				paidInfo.PaidDate,
				paidInfo.PaymentRecordKey,
				ccData[i].IntacctBillRecordID)
			if err != nil {
				_ = tx.Rollback()
				return errors.Wrap(err, "error updating cancel contract payment")
			}
			_, err = tx.ExecContext(ctx, update2,
				db.CancelStatusCheckSentCancelComplete, cc.ContractCancellationID)
			if err != nil {
				_ = tx.Rollback()
				return errors.Wrap(err, "error updating contract cancellations")
			}
		}
	}
	err = tx.Commit()
	if err != nil {
		return errors.Wrap(err, "error committing transaction for cancel contract payment")
	}
	return nil
}

func getApBillRecordNoFromBillNumber(ctx context.Context, billNum string) (int, error) {
	var billRecordNo int
	reqXML, err := intacct.GetBillRequest(billNum)
	if err != nil {
		return billRecordNo, errors.Wrap(err, "error creating get bill request xml")
	}
	result, err := intacct.InvokeRequest(reqXML)
	if err != nil {
		return billRecordNo, errors.Wrap(err, "error invoking get bill request")
	}
	if result.ResultStatus == "success" {
		billRecordNo = result.BillRecordNo
	}
	return billRecordNo, nil
}

// UpdateCancelContractBillRecordNumbers will update the cancel contract bill record numbers from intacct
func UpdateCancelContractBillRecordNumbers(ctx context.Context) error {
	query := `
	select
		bill_number,
		contract_cancellation_id
	from cancel_contract_intacct_bills
	where intacct_payment_record_id is null
		and intacct_bill_record_id = 0
		and created_at > now() - interval '180 days'`

	var ccData = []struct {
		BillNumber             string `db:"bill_number"`
		ContractCancellationID int    `db:"contract_cancellation_id"`
	}{}
	err := db.Get().SelectContext(ctx, &ccData, query)
	if err != nil {
		return errors.Wrap(err, "db error getting zero bill id cancel contract intacct bills")
	}

	update := `
	update cancel_contract_intacct_bills
		set updated_at = now() at time zone 'utc',
		intacct_bill_record_id = $1
	where bill_number = $2`

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		return errors.Wrap(err, "error starting transaction in update cancel contract bill payments")
	}

	for _, cc := range ccData {
		billRecordNo, err := getApBillRecordNoFromBillNumber(ctx, cc.BillNumber)
		if err != nil {
			return errors.Wrap(err, "error getting ap bill no intacct info")
		}
		_, err = tx.ExecContext(ctx, update, billRecordNo, cc.BillNumber)
		if err != nil {
			_ = tx.Rollback()
			return errors.Wrap(err, "error updating contract cancellations")
		}
	}
	err = tx.Commit()
	if err != nil {
		return errors.Wrap(err, "error committing transaction for cancel contract bill payment")
	}
	return nil
}

type canceledBills struct {
	BatchDate      time.Time       `db:"batch_date" json:"batch_date"`
	PayableTo      string          `db:"payable_to" json:"payable_to"`
	BillNumber     string          `db:"bill_number" json:"bill_number"`
	CustomerName   string          `db:"customer_name" json:"customer_name"`
	ContractNumber pq.StringArray  `db:"contract_number" json:"contract_number"`
	RefundAmount   pq.Float64Array `db:"refund_amount" json:"refund_amount"`
	SalesTaxes     pq.Float64Array `db:"sales_taxes" json:"sales_taxes"`
	CheckNumber    string          `db:"check_number" json:"check_number"`
	Status         string          `db:"status" json:"status"`
	BillAmount     decimal.Decimal `db:"bill_amount" json:"bill_amount"`
	PayeeType      string          `db:"payee_type" json:"-"`
	PayeeEmail     string          `db:"payee_email" json:"-"`
	PayeeAddress   string          `db:"payee_address" json:"-"`
	StoreCode      string          `db:"store_code" json:"-"`
	StoreState     string          `db:"store_state" json:"-"`
	CompanyName    string          `db:"company_name" json:"-"`
	CompanyGroup   string          `db:"company_group" json:"-"`
	DMSNumber      string          `db:"dms_number" json:"-"`
}

func cancelBillsData(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, []canceledBills, int, error) {
	ctx := req.Context()

	query := `
	with results as (
		select ccib.created_at::::date batch_date,
			cc.payee_name payable_to,
			bill_number,
			cu.first_name || ' ' || cu.last_name customer_name,
			c.code contract_number,
			(case when ccib.bill_item_memo like '%Cancel%' then ccib.bill_item_amount end) refund_amount,
			(case when ccib.bill_item_memo like '%Sales Tax%' then ccib.bill_item_amount end) sales_taxes,
			ccib.check_number,
			cc.cancel_status as status,
			cc.payee_type,
			(case when cc.is_electronic_check then coalesce(cc.email,'') else '' end) payee_email,
			cc.payee_address,
			st.code store_code,
			st.state_code store_state,
			cp.name company_name,
			coalesce(cg.name,'') company_group,
			s.dms_number
		from cancel_contract_intacct_bills ccib
			join contract_cancellations cc
				on cc.id = ccib.contract_cancellation_id
			join contracts c
				on cc.contract_id = c.id
			join stores st
				on c.store_id = st.id
			join companies cp
				on st.company_id = cp.id
			left join company_groups cg
				on cp.company_group_id = cg.id
			join customers cu
				on cu.id = c.customer_id
			join sales s
				on c.sale_id = s.id
			where cc.is_void = false)
		select batch_date,
			payable_to,
			bill_number,
			customer_name,
			array_agg(contract_number) contract_number,
			array_agg(coalesce(results.refund_amount,0)) refund_amount,
			array_agg(coalesce(results.sales_taxes,0)) sales_taxes,
			check_number,
			status,
			payee_type,
			payee_email,
			payee_address,
			store_code,
			store_state,
			company_name,
			company_group,
			dms_number
		from results
		`

	countQuery := `select count(distinct(ccib.bill_number))
		from cancel_contract_intacct_bills ccib
			join contract_cancellations cc
			  on cc.id = ccib.contract_cancellation_id
			join contracts c
			  on c.id = ccib.contract_id
			join customers cu
			  on c.customer_id = cu.id
		where cc.is_void = false `

	args := struct {
		PayeeType      string    `db:"payee_type"`
		SearchType     string    `db:"search_type"`
		BeginDate      time.Time `db:"begin_date"`
		EndDate        time.Time `db:"end_date"`
		BillNumber     string    `db:"bill_number"`
		ContractNumber string    `db:"contract_number"`
	}{}
	var countArgs []interface{}

	searchKey := req.FormValue("search_type")
	if searchKey == "" {
		util.ReportError(ctx, errors.New("search type is required"))
		return http.StatusBadRequest, nil, 0, errors.New("Search type is required")
	}

	var err error
	if searchKey == "date" {
		beginDate := req.FormValue("begin_date")
		if beginDate == "" {
			util.ReportError(ctx, errors.New("begin date is required"))
			return http.StatusBadRequest, nil, 0, errors.New("Begin date is required")
		}
		args.BeginDate, err = time.Parse(billDateFormat, beginDate)
		if err != nil {
			util.ReportError(ctx, errors.Wrap(err, "error parsing begin date"))
			return http.StatusBadRequest, nil, 0, errors.New("Incorrect value for begin date")
		}
		endDate := req.FormValue("end_date")
		if endDate == "" {
			util.ReportError(ctx, errors.New("end date is required"))
			return http.StatusBadRequest, nil, 0, errors.New("End date is required")
		}
		args.EndDate, err = time.Parse(billDateFormat, endDate)
		if err != nil {
			util.ReportError(ctx, errors.Wrap(err, "error parsing end date"))
			return http.StatusBadRequest, nil, 0, errors.New("Incorrect value for end date")
		}
		query += ` where batch_date >= :begin_date and batch_date <= :end_date `
		countQuery += ` and ccib.created_at::date >= ? and ccib.created_at::date <= ? `
		countArgs = append(countArgs, args.BeginDate, args.EndDate)
	} else if searchKey == "billNumber" {
		if value := req.FormValue("bill_number"); value != "" {
			query += ` where bill_number = :bill_number `
			countQuery += ` and bill_number = ? `
			args.BillNumber = value
			countArgs = append(countArgs, value)
		}
	} else if searchKey == "contractNumber" {
		if value := req.FormValue("contract_number"); value != "" {
			query += ` where contract_number = :contract_number `
			countQuery += ` and c.code = ? `
			args.ContractNumber = value
			countArgs = append(countArgs, value)
		}
	}

	if value := req.FormValue("payee_type"); value != "" {
		query += ` and payee_type = :payee_type `
		countQuery += ` and cc.payee_type = ? `
		args.PayeeType = value
		countArgs = append(countArgs, value)
	}
	query += ` group by batch_date, payable_to, bill_number, customer_name,
         check_number, status, payee_type, payee_email, payee_address, 
		 store_code, store_state, company_name, company_group, dms_number`

	if sortBy := req.FormValue("sort_by"); sortBy != "" {
		query += ` order by ` + sortBy
		if sortOrder := req.FormValue("sort_order"); sortOrder != "" {
			query += ` ` + sortOrder
		}
	} else {
		query += ` order by batch_date desc`
	}
	query += ` limit ` + strconv.Itoa(billsPerPage)
	if value := req.FormValue("page"); value != "" {
		page, err := strconv.Atoi(value)
		if err != nil {
			return http.StatusBadRequest, nil, 0, errors.New("Incorrect value for page")
		}
		query += ` offset ` + strconv.Itoa((page-1)*billsPerPage)
	}

	var count int
	countQuery = db.Get().Rebind(countQuery)
	err = db.Get().GetContext(ctx, &count, countQuery, countArgs...)
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error getting count of cancel bills"))
		return http.StatusInternalServerError, nil, 0, errors.New("Error getting count of cancel bills")
	}

	var bills []canceledBills
	stmt, err := db.Get().PrepareNamedContext(ctx, query)
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error creating preparing named context"))
		return http.StatusInternalServerError, nil, 0, errors.New("Error in getting data")
	}
	defer stmt.Close()
	err = stmt.SelectContext(ctx, &bills, args)
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error in getting cancel contract data"))
		return http.StatusInternalServerError, nil, 0, errors.New("Error in getting data")
	}
	if bills == nil {
		bills = []canceledBills{}
	}
	return http.StatusOK, bills, count, nil
}

// CancelledBillsIndex returns the list of bills submitted to intacct
func CancelledBillsIndex(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	httpStatus, bills, count, _ := cancelBillsData(w, req, user)

	return httpStatus, map[string]interface{}{
		"cancel_bills": bills,
		"count":        count,
		"page_size":    billsPerPage,
	}
}

type manualCancelPaymentUpdate struct {
	ContractCancellationID int                `json:"contract_cancellation_id"`
	Status                 string             `json:"status"`
	BillNumber             string             `json:"bill_number"`
	CheckNumber            string             `json:"check_number"`
	PaidDate               types.JSPQNullDate `json:"paid_date"`
	CheckAmount            decimal.Decimal    `json:"check_amount"`
	RefundAmount           decimal.Decimal    `json:"refund_amount"`
	SalesTaxAmount         decimal.Decimal    `json:"sales_tax_amount"`
	Notes                  string             `json:"notes"`
	IsElectronicCheck      bool               `json:"is_electronic_check"`
	Email                  string             `json:"email"`
}

func getManualCancelPaymentUpdatePayload(req *http.Request) (manualCancelPaymentUpdate, error) {
	decoder := json.NewDecoder(req.Body)
	payload := manualCancelPaymentUpdate{}
	err := decoder.Decode(&payload)
	if err != nil {
		err = errors.Wrap(err, "error decoding cancel submit bill payload")
		return payload, err
	}
	return payload, nil
}

func (p manualCancelPaymentUpdate) validate(cancelStatus string) map[string]string {
	// check new status is valid
	valErrs := map[string]string{}

	switch p.Status {
	case db.CancelStatusCheckSentCancelComplete,
		db.CancelStatusPaymentNotIssued,
		db.CancelStatusPaymentVoided:

	default:
		valErrs["status"] = "Invalid cancel status"
		return valErrs
	}

	// previous cancel_status blank is allowed for cancels done before this feature
	if p.Status == db.CancelStatusCheckSentCancelComplete {
		if cancelStatus != "" &&
			cancelStatus != db.CancelStatusCheckRequestedPendingApproval &&
			cancelStatus != db.CancelStatusCheckSentCancelComplete &&
			cancelStatus != db.CancelStatusCancelSubmittedPendingInvoice &&
			cancelStatus != db.CancelStatusCanceledPendingInvoicing {
			valErrs["status"] = "Invalid cancel status for Status Cancel Complete"
		}
		if p.CheckNumber == "" {
			valErrs["check_number"] = "Check Number is required"
		}
		if !p.PaidDate.Valid {
			valErrs["paid_date"] = "Paid date is required"
		}
		if p.PaidDate.Time.After(time.Now().UTC()) {
			valErrs["paid_date"] = "Paid date cannot be future date"
		}
	}
	return valErrs
}

func (p *manualCancelPaymentUpdate) clean() {
	p.Status = strings.TrimSpace(p.Status)
	p.BillNumber = strings.TrimSpace(p.BillNumber)
	p.CheckNumber = strings.TrimSpace(p.CheckNumber)
	p.Notes = strings.TrimSpace(p.Notes)
	p.Email = strings.TrimSpace(p.Email)
}

// ManualUpdateCancelContract updates cancel payment with manual user input
func ManualUpdateCancelContract(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (
	int, map[string]interface{}) {
	const errMsg = "Error in updating cancel contract payment"

	payload, err := getManualCancelPaymentUpdatePayload(req)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error in getting payload"))
		return http.StatusBadRequest, handlers.ErrorMessage("Bad request", nil)
	}

	payload.clean()
	ctx := req.Context()

	query := `select cc.contract_id, cc.cancel_status, cc.cancelled_without_intacct_data, coalesce(ccib.bill_number,'') bill_number
	from contract_cancellations cc
		left join cancel_contract_intacct_bills ccib
			on ccib.contract_cancellation_id=cc.id
	where cc.id=$1 limit 1`
	data := struct {
		ContractID                 int    `db:"contract_id"`
		CancelStatus               string `db:"cancel_status"`
		CanceledWithoutIntacctData bool   `db:"cancelled_without_intacct_data"`
		BillNumber                 string `db:"bill_number"`
	}{}

	err = db.Get().GetContext(ctx, &data, query, payload.ContractCancellationID)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error in getting cancel status"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	validationErrors := payload.validate(data.CancelStatus)
	if len(validationErrors) > 0 {
		return http.StatusBadRequest, handlers.ErrorMessage("Validation error(s)", map[string]interface{}{"validation_errors": validationErrors})
	}

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error in begin transaction"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	cwid := false
	if data.CancelStatus == "" ||
		data.CancelStatus == db.CancelStatusCancelSubmittedPendingInvoice ||
		data.CancelStatus == db.CancelStatusCanceledPendingInvoicing ||
		data.CanceledWithoutIntacctData {
		cwid = true
	}
	query = `
	update contract_cancellations
		set cancel_status=$1, cancelled_without_intacct_data=$2, is_electronic_check=$3, email=$4
	where id=$5`
	_, err = tx.ExecContext(
		ctx,
		query,
		payload.Status,
		cwid,
		payload.IsElectronicCheck,
		payload.Email,
		payload.ContractCancellationID)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error in updating cancel_status"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	if payload.Status == db.CancelStatusCheckSentCancelComplete {
		if data.CancelStatus == "" ||
			data.CancelStatus == db.CancelStatusCancelSubmittedPendingInvoice ||
			data.CancelStatus == db.CancelStatusCanceledPendingInvoicing {
			// insert new record
			query = `
		insert into cancel_contract_intacct_bills(
			bill_number,
			bill_item_memo,
			bill_item_amount,
			contract_id,
			contract_cancellation_id,
			created_at,
			created_by_user_id,
			intacct_bill_record_id,
			check_number,
			check_amount,
			payment_date,
			manual_update_notes
		) values (
			$9,
			'%s',
			$2,
			$8,
			$7,
			now() at time zone 'utc',
			$6,
			0,
			$1,
			$3,
			$4,
			$5
		)
		returning id
		`
		} else {
			query = `
		update cancel_contract_intacct_bills
			set check_number = $1,
			bill_item_amount = $2,
			check_amount = $3,
			payment_date = $4,
			manual_update_notes = $5,
			updated_at = now() at time zone 'utc',
			updated_by_user_id = $6,
			bill_number = $9
		where contract_cancellation_id = $7 and
			contract_id = $8 and
			bill_item_memo like '%s'
		returning id
		`
		}

		var crQuery string
		var billNumber string
		if data.CancelStatus != "" &&
			data.CancelStatus != db.CancelStatusCancelSubmittedPendingInvoice &&
			data.CancelStatus != db.CancelStatusCanceledPendingInvoicing {
			crQuery = fmt.Sprintf(query, "%Cancel%")
			if data.CanceledWithoutIntacctData {
				billNumber = payload.BillNumber
			} else {
				billNumber = data.BillNumber
			}
		} else {
			crQuery = fmt.Sprintf(query, "Manual Cancel Refund")
			billNumber = payload.BillNumber
		}
		var ccibID int
		err = tx.GetContext(ctx, &ccibID, crQuery,
			payload.CheckNumber,
			payload.RefundAmount,
			payload.CheckAmount,
			payload.PaidDate,
			payload.Notes,
			user.ID,
			payload.ContractCancellationID,
			data.ContractID,
			billNumber,
		)
		if err != nil || ccibID == 0 {
			_ = tx.Rollback()
			handlers.ReportError(req, errors.Wrap(err, "error in updating refund amount in cancel_contract_intacct_bills"))
			return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
		}
		ccibID = 0
		var stQuery string
		if data.CancelStatus != "" {
			stQuery = fmt.Sprintf(query, "%Sales Tax%")
		} else {
			stQuery = fmt.Sprintf(query, "Manual Sales Tax Refund")
		}
		err = tx.GetContext(ctx, &ccibID, stQuery,
			payload.CheckNumber,
			payload.SalesTaxAmount,
			payload.CheckAmount,
			payload.PaidDate,
			payload.Notes,
			user.ID,
			payload.ContractCancellationID,
			data.ContractID,
			billNumber,
		)
		if err != nil || ccibID == 0 {
			_ = tx.Rollback()
			handlers.ReportError(req, errors.Wrap(err, "error in updating sales tax in cancel_contract_intacct_bills"))
			return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
		}

	}
	err = tx.Commit()
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(errMsg, nil)
	}

	return http.StatusOK, map[string]interface{}{"id": payload.ContractCancellationID}
}

// SubmitCancelAllDownload downloads bills and contacts data to csv file
func SubmitCancelAllDownload(w http.ResponseWriter, req *http.Request, user db.CurrentUser) {
	const errMsg = "Error in getting bills doc"
	intacctConfig := conf.Get().Intacct

	query := `
	    select
		cp.name company_name,
		coalesce(cg.name,'') company_group_name,
		st.code store_code,
		st.state_code,
		cu.first_name || ' ' || cu.last_name customer_name,
		c.id,
		c.code,
		cc.payee_type,
		cc.payee_name,
    	(case when cc.is_electronic_check then coalesce(cc.email,'') else '' end) payee_email,
		cc.payee_address,
		cc.payee_city,
		cc.payee_state_code,
		cc.payee_postal_code,
		c.dms_number,
		cc.customer_refund,
		cc.sales_tax,
		(case when cc2.payee_type = cc.payee_type then coalesce(cr.customer_amount,0) else 0 end) cr_customer_refund,
		(case when cc2.payee_type = cc.payee_type then coalesce(cr.sales_tax,0) else 0 end) cr_sales_tax,
		vr.vin,
		t1.invoiced_at as cr_invoice_date,
		st.sales_tax_start_date,
		t.invoice_id cc_invoice_id,
		t2.invoice_id cr_invoice_id
	from contracts c
		join customers cu
			on c.customer_id=cu.id
		join stores st 
			on c.store_id = st.id
		join companies cp
			on st.company_id = cp.id
		left join company_groups cg
			on cp.company_group_id = cg.id
		join vin_records vr
			on c.vin_record_id = vr.id
		join contract_cancellations cc
			on c.id=cc.contract_id
		join transactions t
			on cc.transaction_id=t.id
		left join contract_reinstatements cr
			on cr.contract_id  = c.id
		left join contract_cancellations cc2
			on (cc2.id = cr.contract_cancellation_id and cc2.payee_type = cc.payee_type)
		left join transactions t1
			on cc2.transaction_id=t1.id
		left join transactions t2
			on cr.transaction_id=t2.id
	where cc.is_void=false and c.id in (?)
		and (cr.is_void =false or cr.is_void is null)`

	ctx := req.Context()

	type contract struct {
		CompanyName       string          `db:"company_name"`
		CompanyGroupName  string          `db:"company_group_name"`
		StoreCode         string          `db:"store_code"`
		StateCode         string          `db:"state_code"`
		CustomerName      string          `db:"customer_name"`
		ID                int             `db:"id"`
		Code              string          `db:"code"`
		PayeeType         string          `db:"payee_type"`
		PayeeName         string          `db:"payee_name"`
		PayeeEmail        string          `db:"payee_email"`
		PayeeAddress      string          `db:"payee_address"`
		PayeeCity         string          `db:"payee_city"`
		PayeeStateCode    string          `db:"payee_state_code"`
		PayeePostalCode   string          `db:"payee_postal_code"`
		DMSNumber         string          `db:"dms_number"`
		CustomerRefund    decimal.Decimal `db:"customer_refund"`
		SalesTax          decimal.Decimal `db:"sales_tax"`
		CRCustomerRefund  decimal.Decimal `db:"cr_customer_refund"`
		CRSalesTax        decimal.Decimal `db:"cr_sales_tax"`
		VIN               string          `db:"vin"`
		CRInvoiceDate     null.Time       `db:"cr_invoice_date"`
		SalesTaxStartDate null.Time       `db:"sales_tax_start_date"`
		CCInvoiceID       int             `db:"cc_invoice_id"`
		CRInvoiceID       null.Int        `db:"cr_invoice_id"`
	}
	var contracts []contract

	if req.FormValue("ids") != "" {
		ids := strings.Split(req.FormValue("ids"), ",")
		query, args, err := sqlx.In(query, ids)
		if err != nil {
			w.WriteHeader(http.StatusInternalServerError)
			err = errors.Wrap(err, "error in sqlx.In")
			handlers.ReportError(req, err)
			fmt.Fprint(w, errMsg)
			return
		}
		query = db.Get().Rebind(query)
		err = db.Get().SelectContext(ctx, &contracts, query, args...)
		if err != nil {
			w.WriteHeader(http.StatusInternalServerError)
			err = errors.Wrap(err, "error querying cancel bills data")
			handlers.ReportError(req, err)
			fmt.Fprint(w, errMsg)
			return
		}
	}

	w.Header().Set("Content-Type", "text/csv")
	fileName := fmt.Sprintf("BillsWithContacts%s.csv", time.Now().Format("********"))
	w.Header().Set("Content-Disposition", "attachment; filename="+fileName)
	csvw := csv.NewWriter(w)

	err := csvw.Write([]string{
		"Refund Payable To",
		"Company", "Company Group", "Store Code", "Store State", "Contract#",
		"Customer Name", "Deal/RO#", "TCA Invoicing Date", "Contact Name", "Company Name",
		"First Name", "Last Name", "Print As", "Email Address", "Address", "City", "State", "Zip",
		"Bill Number", "Vendor ID", "Pay To", "Created Date", "Due Date",
		"Description", "Line No", "Memo", "Account No", "Location ID",
		"Amount", "AP Bill Item Vendor ID",
	})
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		err = errors.Wrap(err, "error writing csv")
		handlers.ReportError(req, err)
		fmt.Fprint(w, errMsg)
		return
	}

	currentDate := time.Now().Format(db.ShortDateFormat)
	csvRecord := func(c contract, companyName, firstName, lastName, lineNum,
		billNum, vendorID, payeeName, memo, glNo string, amount decimal.Decimal) []string {
		date := strings.Split(billNum, ".")[2]

		invDate := fmt.Sprintf("%s/%s/%s", date[0:len(date)-4], date[len(date)-4:len(date)-2], date[len(date)-2:])

		return []string{
			c.PayeeType,
			c.CompanyName,
			c.CompanyGroupName,
			c.StoreCode,
			c.StateCode,
			c.Code,
			c.CustomerName,
			c.DMSNumber,
			invDate,
			payeeName,
			companyName,
			firstName,
			lastName,
			c.PayeeName,
			c.PayeeEmail,
			c.PayeeAddress,
			c.PayeeCity,
			c.PayeeStateCode,
			c.PayeePostalCode,
			billNum,
			vendorID,
			strings.Join(
				[]string{payeeName, billNum}, "."),
			currentDate,
			currentDate,
			strings.Join([]string{currentDate, "Invoicing Cancellation Refund"}, " - "),
			lineNum,
			memo,
			glNo,
			strconv.Itoa(db.CancelContractLocationID),
			amount.StringFixed(2),
			vendorID,
		}
	}

	var lineNum int = 1
	var billNumbers = []string{}
	for i, c := range contracts {
		billNum, err := createCancelSubmitBillNumber(ctx, c.ID, c.PayeeType)
		if err != nil {
			w.WriteHeader(http.StatusInternalServerError)
			err = errors.Wrap(err, "error creating bill number")
			handlers.ReportError(req, err)
			fmt.Fprint(w, errMsg)
			return
		}
		billNumbers = append(billNumbers, billNum)

		var vendorID, companyName, firstName, lastName string
		if c.PayeeType == db.CancelPayeeTypeLender {
			vendorID = db.GetCancelContractLenderVendorID()
			companyName = c.PayeeName
		} else {
			vendorID = db.GetCancelContractCustomerVendorID()
			if len(strings.Split(c.PayeeName, " ")) < 2 {
				lastName = strings.Split(c.PayeeName, " ")[0]
			} else {
				firstName = strings.Split(c.PayeeName, " ")[0]
				lastName = strings.Split(c.PayeeName, " ")[1]
			}
		}

		// line 1 cancel refund
		if c.CustomerRefund.GreaterThanOrEqual(decimal.Zero) {
			if i > 0 {
				if billNumbers[i] == billNumbers[i-1] {
					lineNum++
				} else {
					lineNum = 1
				}
			}
			customerRefund := c.CustomerRefund
			if c.CRInvoiceID.Valid && int(c.CRInvoiceID.Int64) == c.CCInvoiceID && c.CRInvoiceDate.Valid && c.SalesTaxStartDate.Valid &&
				c.CRInvoiceDate.Time.After(c.SalesTaxStartDate.Time) {
				customerRefund = c.CustomerRefund.Sub(c.CRCustomerRefund)
			}
			record1 := csvRecord(
				c,
				companyName,
				firstName,
				lastName,
				strconv.Itoa(lineNum),
				billNum,
				vendorID,
				c.PayeeName,
				getCancelBillMemoString(c.Code, billNum, refundTypeCustomer),
				intacctConfig.CancelContractGLAccountNumber,
				customerRefund,
			)

			if err := csvw.Write(record1); err != nil {
				w.WriteHeader(http.StatusInternalServerError)
				err = errors.Wrap(err, "error writing csv")
				handlers.ReportError(req, err)
				fmt.Fprint(w, errMsg)
				return
			}
		}

		// line 2 sales tax refund
		if c.SalesTax.GreaterThanOrEqual(decimal.Zero) {
			lineNum++
			salesTax := c.SalesTax
			if c.CRInvoiceID.Valid && int(c.CRInvoiceID.Int64) == c.CCInvoiceID && c.CRInvoiceDate.Valid && c.SalesTaxStartDate.Valid &&
				c.CRInvoiceDate.Time.After(c.SalesTaxStartDate.Time) {
				salesTax = c.SalesTax.Sub(c.CRSalesTax)
			}
			record2 := csvRecord(
				c,
				companyName,
				firstName,
				lastName,
				strconv.Itoa(lineNum),
				billNum,
				vendorID,
				c.PayeeName,
				getCancelBillMemoString(c.Code, billNum, refundTypeSalesTax),
				intacctConfig.CancelContractGLAccountNumber,
				salesTax,
			)

			if err := csvw.Write(record2); err != nil {
				w.WriteHeader(http.StatusInternalServerError)
				err = errors.Wrap(err, "error writing csv")
				handlers.ReportError(req, err)
				fmt.Fprint(w, errMsg)
				return
			}
		}
	}
	csvw.Flush()
}

// CancelledBillsDownload returns the list of bills submitted to intacct
func CancelledBillsDownload(w http.ResponseWriter, req *http.Request, user db.CurrentUser) {
	const errMsg = "Error in download of cancel bills"
	httpStatus, bills, _, err := cancelBillsData(w, req, user)
	if httpStatus != http.StatusOK {
		w.WriteHeader(httpStatus)
		handlers.ReportError(req, err)
		fmt.Fprint(w, err)
		return
	}
	w.Header().Set("Content-Type", "text/csv")
	fileName := fmt.Sprintf("CanceledBatchBills_%s.csv", time.Now().Format("********"))
	w.Header().Set("Content-Disposition", "attachment; filename="+fileName)
	csvw := csv.NewWriter(w)

	err = csvw.Write([]string{
		"Payee Type", "Batch Date", "Payable To", "Payee Email", "Payee Address", "Bill#",
		"Customer Name", "Contract#", "Refund Amount", "Sales Tax",
		"Check#", "Status", "Store Code", "Store State", "Company", "Company Group",
		"TCA Invoicing Date", "Deal/RO#",
	})
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		err = errors.Wrap(err, "error writing csv")
		handlers.ReportError(req, err)
		fmt.Fprint(w, errMsg)
		return
	}

	for _, c := range bills {

		for i := range c.ContractNumber {
			date := strings.Split(c.BillNumber, ".")[2]
			invDate := fmt.Sprintf("%s/%s/%s", date[0:len(date)-4], date[len(date)-4:len(date)-2], date[len(date)-2:])

			record := []string{
				c.PayeeType,
				c.BatchDate.Format("********"),
				c.PayableTo,
				c.PayeeEmail,
				c.PayeeAddress,
				c.BillNumber,
				c.CustomerName,
				c.ContractNumber[i],
				strconv.FormatFloat(c.RefundAmount[i], 'f', -1, 64),
				strconv.FormatFloat(c.SalesTaxes[i], 'f', -1, 64),
				c.CheckNumber,
				c.Status,
				c.StoreCode,
				c.StoreState,
				c.CompanyName,
				c.CompanyGroup,
				invDate,
				c.DMSNumber,
			}

			if err := csvw.Write(record); err != nil {
				w.WriteHeader(http.StatusInternalServerError)
				err = errors.Wrap(err, "error writing csv")
				handlers.ReportError(req, err)
				fmt.Fprint(w, errMsg)
				return
			}
		}

	}
	csvw.Flush()
}

// CreateIntacctContacts will create the cancellation contacts in Intacct
func CreateIntacctContacts(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()
	ids, err := getSubmitBillPayload(req)
	if err != nil {
		util.ReportError(ctx, err)
		return http.StatusBadRequest, handlers.ErrorMessage("Bad request", nil)
	}
	contacts, status, msg, err := getIntacctContacts(ctx, ids, "", "", "")
	if err != nil {
		if status != http.StatusBadRequest {
			util.ReportError(ctx, err)
		}
		return status, handlers.ErrorMessage(msg, nil)

	}
	bills := make([]string, len(contacts))
	var intacctContacts []*db.ContactItem
	for index, contact := range contacts {
		iContact := db.ContactItem{
			PrintAs: contact.PayeeName,
			Email1:  contact.PayeeEmail,
			MailAddress: db.MailAddress{
				Address1: contact.PayeeAddress,
				City:     contact.PayeeCity,
				State:    contact.PayeeStateCode,
				Zip:      contact.PayeePostalCode,
				Country:  db.CancelContractCountry,
			},
		}
		billNum, err := createCancelSubmitBillNumber(ctx, contact.ContractID, contact.PayeeType)
		if err != nil {
			util.ReportError(ctx, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Error in creating bill number", nil)
		}
		iContact.ContactName = strings.Join([]string{contact.PayeeName, billNum}, ".")

		if contact.PayeeType == db.CancelPayeeTypeLender {
			iContact.CompanyName = contact.PayeeName
		} else {
			if len(strings.Split(contact.PayeeName, " ")) < 2 {
				iContact.LastName = strings.Split(contact.PayeeName, " ")[0]
			} else {
				iContact.FirstName = strings.Split(contact.PayeeName, " ")[0]
				iContact.LastName = strings.Split(contact.PayeeName, " ")[1]
			}
		}
		intacctContacts = append(intacctContacts, &iContact)
		bills[index] = billNum
	}

	xml, err := intacct.CreateContactRequest(intacctContacts)
	if err != nil {
		return http.StatusInternalServerError, nil
	}

	result, err := intacct.InvokeContactRequest(xml)
	if err != nil {
		return http.StatusInternalServerError, nil
	}

	var failedBills []string
	for index := range result.Result {
		if len(result.Result[index].Errors) > 0 &&
			result.Result[index].Errors[0].Correction != db.IntacctErrorTypeAlreadyExists {
			failedBills = append(failedBills, bills[index])
		}
	}

	return http.StatusOK, map[string]interface{}{
		"failed_bills": failedBills,
	}
}

func getCancelBillMemoString(contractCode, billNum, memoType string) string {
	// billNum has ("%s.%s.%s.%d.%s", L/C, Vin8, invDt, invBatchID, lastName)
	billParts := strings.Split(billNum, ".")
	return strings.Join([]string{contractCode, memoType, billParts[1], billParts[4]}, " - ")
}
