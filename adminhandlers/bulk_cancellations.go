package adminhandlers

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/base64"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"image/jpeg"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"whiz/conf"
	"whiz/db"
	"whiz/handlers"
	"whiz/s3util"
	"whiz/util"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	newrelic "github.com/newrelic/go-agent"
	"github.com/pkg/errors"
	"golang.org/x/image/tiff"
	"gopkg.in/guregu/null.v3"
)

const (
	// RequestSourceTCAConnect is used when the users drag and drop documents into the TCA Connect portal
	RequestSourceTCAConnect = "TCA Connect"
)
const (
	// RequestStatusProcessing is the status of a newly created AI cancellation request currently read by the AI engine
	RequestStatusProcessing = "Processing"
	// RequestStatusProcessed is the status of a AI cancellation request which is processed by the AI engine
	RequestStatusProcessed = "Processed"
	// RequestStatusCancelled is the status of a cancelled AI cancellation request
	RequestStatusCancelled = "Cancelled"
	// RequestStatusVerified is the status of a verified AI cancellation request
	RequestStatusVerified = "Verified"
)

// AttachmentTypeCancelForm indicates if the cancellation form attachment
const AttachmentTypeCancelForm = "Cancel Contract Form"

// AttachmentTypeCancelContract indicates the cancellation contract attachment type
const AttachmentTypeCancelContract = "Cancel Contract"

// AttachmentTypeCancelQuote indicates the cancellation quote attachment type
const AttachmentTypeCancelQuote = "Cancel Quote"

// AICancellationAttachment represents a single attachment on an AI cancellation request
type AICancellationAttachment struct {
	ID               int    `db:"id" json:"id"`
	AICancellationID int    `db:"ai_cancellation_id" json:"ai_cancellation_id"`
	S3Bucket         string `db:"s3_bucket" json:"s3_bucket"`
	S3FileName       string `db:"s3_file_name" json:"s3_file_name"`
	FileName         string `db:"file_name" json:"file_name"`
	ContentType      string `db:"content_type" json:"content_type"`
	Description      string `db:"description" json:"description"`
	CreatedByUserID  int    `db:"created_by_user_id" json:"created_by_user_id"`
	URL              string `db:"-" json:"url"`
}

type aiCancellationRequestPayload struct {
	FileContent string `json:"file_content"`
	Name        string `json:"name"`
	Description string `json:"description"`
	ContentType string `json:"content_type"`
}

type customerInformation struct {
	Name        string `json:"name"`
	PhoneNumber string `json:"phone_number"`
	Address     string `json:"address"`
	City        string `json:"city"`
	State       string `json:"state"`
	Zip         string `json:"zip"`
	Email       string `json:"email"`
}

type vehicleInformation struct {
	VIN            string `json:"vin"`
	Year           string `json:"year"`
	Make           string `json:"make"`
	Model          string `json:"model"`
	CurrentMileage string `json:"mileage"`
}

type contractSelection struct {
	IsSelected bool   `json:"is_selected"`
	Code       string `json:"number_code"`
}

type contractInformation struct {
	Service             contractSelection `json:"vehicle_service_contract"`
	Maintenance         contractSelection `json:"maintenance_contract"`
	GAP                 contractSelection `json:"gap_addendum_contract"`
	DrivePur            contractSelection `json:"drivepur_contract"`
	TireAndWheel        contractSelection `json:"tire_and_wheel_contract"`
	LeaseWearAndTear    contractSelection `json:"wear_and_tear_contract"`
	PaintlessDentRepair contractSelection `json:"pdr_contract"`
	KeyReplacement      contractSelection `json:"key_contract"`
	Other               contractSelection `json:"other_contract"`
}

type financeInformation struct {
	FinancedContract bool   `json:"is_financed"`
	Lender           string `json:"lender_name"`
}

type cancellationInformation struct {
	Reason              string `json:"reason"`
	FundsForDownPayment bool   `json:"funds_for_down_payment"`
	Date                string `json:"signature_date"`
}

type refundInformation struct {
	DealNumber        string `json:"deal_code"`
	StoreCode         string `json:"store_code"`
	Dealership        string `json:"dealership"`
	IsElectronicCheck bool   `json:"is_electronic_check"`
}

type aiProcessingResult struct {
	CustomerInformation     customerInformation     `json:"customer_information"`
	VehicleInformation      vehicleInformation      `json:"vehicle_information"`
	ContractInformation     contractInformation     `json:"contract_information"`
	FinanceInformation      financeInformation      `json:"finance_information"`
	CancellationInformation cancellationInformation `json:"cancellation_information"`
	RefundInformation       refundInformation       `json:"refund_information"`
}

type aiProcessingError struct {
	ErrorMessage string `json:"error_message"`
}

type aiProcessingResponse struct {
	FileID           int                 `json:"file_id"`
	FilePath         string              `json:"file_path"`
	Page             int                 `json:"page"`
	PageType         string              `json:"page_type"`
	Version          string              `json:"version"`
	ProcessingResult *aiProcessingResult `json:"processing_result"`
	ProcessingError  *aiProcessingError  `json:"processing_error"`
	ProcessingLogs   string              `json:"processing_logs"`
}

type payloadFiles struct {
	FileID   int    `json:"file_id"`
	FilePath string `json:"file_path"`
}

type processingLambdaPayload struct {
	RequestID       int            `json:"request_id"`
	Environment     string         `json:"environment"`
	ConnectHostname string         `json:"connect_hostname"`
	S3Bucket        string         `json:"s3_bucket"`
	UserID          int            `json:"user_id"`
	Files           []payloadFiles `json:"files"`
}

// CancellationRequestsIndex returns a list of all cancellation requests
func CancellationRequestsIndex(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()
	cancellationRequests := []struct {
		db.AICancellation
		CustomerName string `db:"customer_name" json:"customer_name"`
		FileNames    string `db:"file_names" json:"file_names"`
		AssignedTo   string `db:"assigned_to" json:"assigned_to"`
	}{}
	var err error

	query := `select aic.id, aic.source, aic.status, aic.created_at, aic.updated_at,
			aic.assigned_to_user_id,
       		coalesce(processing_result->'customer_information'->>'name', '') AS customer_name,
			STRING_AGG(aica.file_name, ', ') AS file_names,
			cu.first_name || ' ' || cu.last_name as assigned_to
		from ai_cancellations aic
		join ai_cancellation_attachments aica on aica.ai_cancellation_id = aic.id
		join current_users cu on cu.id = aic.assigned_to_user_id
		where `

	if user.HasRole(db.RoleCancelDashboardManager) {
		// If the user is a cancellation dashboard manager, show all requests
		query += `(aic.assigned_to_user_id = ? or true)`
	} else {
		query += `aic.assigned_to_user_id = ?`
	}
	args := []interface{}{user.ID}
	status := req.FormValue("status")
	if status != "" {
		args = append(args, status)
		query += ` and aic.status = ?`
	} else {
		query += ` and aic.status in ('` + RequestStatusProcessing + `', '` + RequestStatusProcessed + `')`
	}
	beginDate := req.FormValue("begin_date")
	if beginDate != "" {
		date, err := time.Parse(time.RFC3339, beginDate)
		if err != nil {
			return http.StatusBadRequest, handlers.ErrorMessage("error parsing begin date", nil)
		}
		args = append(args, date)
		query += ` and aic.created_at >= ?`
	}
	endDate := req.FormValue("end_date")
	if endDate != "" {
		date, err := time.Parse(time.RFC3339, endDate)
		if err != nil {
			return http.StatusBadRequest, handlers.ErrorMessage("error parsing end date", nil)
		}
		args = append(args, date)
		query += ` and aic.created_at <= ?`
	}
	query += ` and aic.deleted_at is null 
		group by aic.id, aic.source, aic.status, aic.created_at, assigned_to`
	searchKey := req.FormValue("search_text")
	if searchKey != "" {
		args = append(args, "%"+searchKey+"%")
		query += ` having STRING_AGG(aica.file_name, ', ') ilike ?`
	}
	query += ` order by aic.status, aic.created_at desc`
	query = db.Get().Rebind(query)
	err = db.Get().SelectContext(ctx, &cancellationRequests, query, args...)
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error getting requests"))
		return http.StatusInternalServerError, handlers.ErrorMessage("error getting requests", nil)
	}

	q2 := `select id, first_name, last_name
		from current_users
		where roles ?|  array[$1,$2]`
	users := []struct {
		ID        int    `db:"id" json:"id"`
		FirstName string `db:"first_name" json:"first_name"`
		LastName  string `db:"last_name" json:"last_name"`
	}{}
	err = db.Get().SelectContext(ctx, &users, q2, db.RoleAccountRep, db.RoleAccountRepII)
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error getting users"))
		return http.StatusInternalServerError, handlers.ErrorMessage("error getting users", nil)
	}

	return http.StatusOK, map[string]interface{}{
		"requests": cancellationRequests,
		"users":    users,
	}
}

// CancellationRequestGet returns the details of a single cancellation request
func CancellationRequestGet(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()
	id := chi.URLParam(req, "id")
	var cancellationRequest db.AICancellation

	query := `select id, source, status, created_at, processing_result, processing_error
		from ai_cancellations
		where id = $1 and deleted_at is null`
	args := []interface{}{id}

	if !user.HasRole(db.RoleCancelDashboardManager) {
		// Other users can only see requests assigned to them
		query += ` and assigned_to_user_id = $2`
		args = append(args, user.ID)
	}

	err := db.Get().GetContext(ctx, &cancellationRequest, query, args...)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return http.StatusBadRequest, handlers.ErrorMessage("incorrect request id", nil)
		}
		util.ReportError(ctx, errors.Wrapf(err, "error getting request with ID %s", id))
		return http.StatusInternalServerError, handlers.ErrorMessage("error getting request", nil)
	}

	var processingResult aiProcessingResult
	if len(cancellationRequest.ProcessingResult) > 0 {
		err = json.Unmarshal(cancellationRequest.ProcessingResult, &processingResult)
		if err != nil {
			util.ReportError(ctx, errors.Wrap(err, "error unmarshaling processing result"))
			return http.StatusInternalServerError, handlers.ErrorMessage("Error while processing result", nil)
		}
	}
	var processingError aiProcessingError
	if len(cancellationRequest.ProcessingError) > 0 {
		err = json.Unmarshal(cancellationRequest.ProcessingError, &processingError)
		if err != nil {
			util.ReportError(ctx, errors.Wrap(err, "error unmarshaling processing error"))
			return http.StatusInternalServerError, handlers.ErrorMessage("Error while processing error", nil)
		}
	}

	return http.StatusOK, map[string]interface{}{
		"id":                cancellationRequest.ID,
		"source":            cancellationRequest.Source,
		"status":            cancellationRequest.Status,
		"created_at":        cancellationRequest.CreatedAt,
		"processing_result": processingResult,
		"processing_error":  processingError,
	}
}

// getCancellationRequestNotes gets the notes for a cancellation request
func getCancellationRequestNotes(ctx context.Context, id string) ([]db.AICancellationNote, error) {
	var notes []db.AICancellationNote
	query := `select aicn.id, aicn.ai_cancellation_id, aicn.comment, aicn.created_at, concat(uv.first_name, ' ', uv.last_name) as created_by
		from ai_cancellation_notes aicn
		join user_versions uv on uv.user_id = aicn.created_by_user_id and current = true
		where ai_cancellation_id = $1
		order by created_at desc`

	err := db.Get().SelectContext(ctx, &notes, query, id)
	if err != nil {
		return nil, errors.Wrap(err, "error getting notes")
	}
	return notes, nil
}

// CancellationRequestAttachments gets the attachments for a cancellation request
func CancellationRequestAttachments(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()
	id := chi.URLParam(req, "id")

	var attachments []AICancellationAttachment
	query := `select aica.id, aica.ai_cancellation_id, aica.s3_bucket, aica.s3_file_name,
       		aica.file_name, aica.content_type, aica.description
		from ai_cancellation_attachments aica
		where ai_cancellation_id = $1
		order by description desc`

	err := db.Get().SelectContext(ctx, &attachments, query, id)
	if err != nil {
		util.ReportError(ctx, errors.Wrapf(err, "error getting attachments for request with ID %s", id))
		return http.StatusInternalServerError, handlers.ErrorMessage("error getting attachments", nil)
	}
	for index := range attachments {
		attachments[index].URL, err = GetDocumentAccessURL(
			attachments[index].FileName,
			attachments[index].S3FileName,
			attachments[index].S3Bucket,
			attachments[index].ContentType,
			user,
		)
		if err != nil {
			util.ReportError(ctx, errors.Wrapf(err, "error getting secure URL for attachment %d", attachments[index].ID))
			return http.StatusInternalServerError, handlers.ErrorMessage("error getting secure URL", nil)
		}
	}
	return http.StatusOK, map[string]interface{}{
		"attachments": attachments,
	}
}

// GetDocumentAccessURL returns a secure pre-signed URL for accessing a document
func GetDocumentAccessURL(fileName, s3FileName, s3Bucket, contentType string, user db.CurrentUser) (string, error) {
	// Use url.QueryEscape for display filename (for Content-Disposition header)
	escapedFileName := url.QueryEscape(fileName)
	// Use custom escape function for S3 key to handle both '+' and '#' correctly
	escapedS3FileName := s3util.EscapeS3Key(s3FileName)

	reverseProxy := s3util.GetS3ReverseProxy()
	signedURL, err := reverseProxy.GetSecureURL(
		s3util.DefaultRegion, s3Bucket, escapedS3FileName, escapedFileName,
		contentType, user, time.Minute*conf.Get().S3ReverseProxy.DefaultLinkTimeoutMinutes)
	if err != nil {
		return "", errors.Wrap(err, "error getting secure URL")
	}
	return signedURL, nil
}

// CancellationRequestCreate creates a new cancellation request
func CancellationRequestCreate(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()

	payload, err := getCancellationRequestPayload(req)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage("Error getting request payload", nil)
	}
	txn := w.(newrelic.Transaction)
	var aica db.AICancellationAttachment
	var attachments []db.AICancellationAttachment
	for index := range payload {
		aica.FileName = strings.TrimSpace(payload[index].Name)
		if aica.FileName == "" {
			return http.StatusBadRequest, handlers.ErrorMessage("Invalid file name", nil)
		}
		if len(payload[index].FileContent) == 0 {
			return http.StatusBadRequest, handlers.ErrorMessage("Invalid file content", nil)
		}

		// cancellation attachment payload
		aica.CreatedByUserID = user.ID
		aica.S3Bucket = s3util.Bucket()
		timeStamp := time.Now().Format("20060102150405")
		// Use custom escape function for S3 key to handle both '+' and '#' correctly
		encodedName := s3util.EscapeS3Key(payload[index].Name)
		aica.S3FileName = fmt.Sprintf("ai-cancellations/%s_%s_%s", strconv.Itoa(user.ID), timeStamp, encodedName)
		aica.ContentType = payload[index].ContentType
		aica.Description = payload[index].Description

		// decode base64 encoded file content
		data, err := base64.StdEncoding.DecodeString(payload[index].FileContent)
		if err != nil {
			util.ReportError(ctx, errors.Wrap(err, "error decoding file content"))
			return http.StatusInternalServerError, handlers.ErrorMessage("Error decoding file content", nil)
		}

		// upload attachment to S3 bucket
		err = s3util.Put(txn, bytes.NewReader(data), s3util.DefaultRegion, aica.S3Bucket, aica.S3FileName)
		if err != nil {
			err = errors.Wrapf(err, "error uploading cancellation attachment file to %s/%s", aica.S3Bucket, aica.S3FileName)
			util.ReportError(ctx, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Error uploading file", nil)
		}

		// convert TIFF image to JPEG as they are not supported in the browser
		if payload[index].ContentType == "image/tiff" {

			jpegData, err := convertTIFFToJPEG(ctx, data)
			if err != nil {
				util.ReportError(ctx, errors.Wrap(err, "Error converting TIFF to JPEG"))
			}

			// update content type and file name & content
			aica.OriginalFileName = null.StringFrom(aica.FileName)
			aica.OriginalContentType = null.StringFrom(aica.ContentType)
			aica.OriginalS3FileName = null.StringFrom(aica.S3FileName)
			payload[index].Name = strings.TrimSpace(payload[index].Name)
			aica.FileName = strings.Replace(payload[index].Name, ".tiff", ".jpg", -1)
			aica.ContentType = "image/jpeg"
			aica.S3FileName = fmt.Sprintf("ai-cancellations/%s_%s_%s", strconv.Itoa(user.ID), timeStamp, aica.FileName)

			// upload JPEG attachment to S3 bucket
			err = s3util.Put(txn, bytes.NewReader(jpegData), s3util.DefaultRegion, aica.S3Bucket, aica.S3FileName)
			if err != nil {
				err = errors.Wrapf(err, "error uploading cancellation attachment file to %s/%s", aica.S3Bucket, aica.S3FileName)
				util.ReportError(ctx, err)
				return http.StatusInternalServerError, handlers.ErrorMessage("Error uploading file", nil)
			}
		}

		attachments = append(attachments, aica)
	}

	tx, err := db.Get().Beginx()
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error starting transaction"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error starting database transaction", nil)
	}
	defer func() { _ = tx.Rollback() }()

	cancellationRequest := &db.AICancellation{
		Source:           RequestSourceTCAConnect,
		Status:           RequestStatusProcessing,
		CreatedByUserID:  user.ID,
		AssignedToUserID: user.ID, // Assign to the current user by default
	}

	// insert cancellation request into database
	query := `insert into ai_cancellations (source, status, created_by_user_id, assigned_to_user_id)
		values (:source, :status, :created_by_user_id, :assigned_to_user_id) returning id`
	stmt, err := tx.PrepareNamedContext(ctx, query)
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error preparing insert statement"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error preparing insert statement", nil)
	}
	defer func() { _ = stmt.Close() }()
	var id int
	err = stmt.GetContext(ctx, &id, cancellationRequest)
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error inserting cancellation request"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error inserting request", nil)
	}

	// create processing lambda payload
	var fileList []payloadFiles
	lambdaPayload := processingLambdaPayload{
		RequestID:       id,
		Environment:     conf.Get().AppEnv,
		ConnectHostname: conf.Get().Server,
		S3Bucket:        s3util.Bucket(),
		UserID:          user.ID,
		Files:           fileList,
	}

	// insert attachment into database
	for index := range attachments {
		attachment := &db.AICancellationAttachment{
			AICancellationID:    id,
			S3Bucket:            attachments[index].S3Bucket,
			S3FileName:          attachments[index].S3FileName,
			FileName:            attachments[index].FileName,
			ContentType:         attachments[index].ContentType,
			Description:         attachments[index].Description,
			CreatedByUserID:     user.ID,
			OriginalS3FileName:  attachments[index].OriginalS3FileName,
			OriginalFileName:    attachments[index].OriginalFileName,
			OriginalContentType: attachments[index].OriginalContentType,
		}
		attachmentQuery := `insert into ai_cancellation_attachments (ai_cancellation_id, s3_bucket, s3_file_name, file_name,
			content_type, description, created_by_user_id, original_s3_file_name, original_file_name, original_content_type)
		values (:ai_cancellation_id, :s3_bucket, :s3_file_name, :file_name, :content_type, :description,
			:created_by_user_id, :original_s3_file_name, :original_file_name, :original_content_type) returning id`
		stmt, err = tx.PrepareNamedContext(ctx, attachmentQuery)
		if err != nil {
			util.ReportError(ctx, errors.Wrap(err, "error preparing insert statement"))
			return http.StatusInternalServerError, handlers.ErrorMessage("error preparing insert statement", nil)
		}
		var attachmentID int
		err = stmt.GetContext(ctx, &attachmentID, attachment)
		if err != nil {
			util.ReportError(ctx, errors.Wrap(err, "error inserting cancellation request"))
			return http.StatusInternalServerError, handlers.ErrorMessage("error inserting request", nil)
		}
		lambdaPayload.Files = append(lambdaPayload.Files, payloadFiles{
			FileID:   attachmentID,
			FilePath: url.QueryEscape(attachment.S3FileName),
		})
	}
	defer func() { _ = stmt.Close() }()

	err = tx.Commit()
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error committing transaction"))
		return http.StatusInternalServerError, handlers.ErrorMessage("error committing transaction", nil)
	}

	err = sendProcessingRequest(&lambdaPayload)
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error sending processing request"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error connecting to the processing server", nil)
	}

	return http.StatusOK, map[string]interface{}{
		"id": id,
	}
}

func convertTIFFToJPEG(ctx context.Context, tiffData []byte) ([]byte, error) {
	img, err := tiff.Decode(bytes.NewReader(tiffData))
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error decoding TIFF image"))
	}

	var jpegBuffer bytes.Buffer
	err = jpeg.Encode(&jpegBuffer, img, &jpeg.Options{Quality: 90})
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error encoding JPEG image"))
	}

	return jpegBuffer.Bytes(), nil
}

func getCancellationRequestPayload(req *http.Request) ([]*aiCancellationRequestPayload, error) {
	var reqPayload []*aiCancellationRequestPayload
	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&reqPayload)
	if err != nil {
		return nil, errors.Wrap(err, "error decoding request payload")
	}

	return reqPayload, nil
}

// CancellationRequestProcessCancel cancels the contracts associated with the cancellation request
func CancellationRequestProcessCancel(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()
	id := chi.URLParam(req, "id")

	var payload = struct {
		CorrectedData *aiProcessingResult `json:"corrected_data"`
		PayeeStoreID  int                 `json:"payee_store_id"`
		Contracts     []int               `json:"contracts"`
	}{}
	requestBody, err := io.ReadAll(req.Body)
	if err != nil && !errors.Is(err, io.EOF) {
		util.ReportError(ctx, errors.Wrap(err, "error reading request body"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error reading request body", nil)
	}
	err = json.Unmarshal(requestBody, &payload)
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error decoding request payload"))
		return http.StatusBadRequest, handlers.ErrorMessage("Error decoding request payload", nil)
	}
	req.Body = io.NopCloser(bytes.NewBuffer(requestBody))

	tx, err := db.Get().Beginx()
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error starting transaction"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Failed to cancel contract", nil)
	}
	// Update status and save changed data
	query := `update ai_cancellations set status = $1, updated_at = now() at time zone 'utc' where id = $2`
	args := []interface{}{RequestStatusCancelled, id}
	if payload.CorrectedData != nil {
		correctedData, err := json.Marshal(payload.CorrectedData)
		if err != nil {
			_ = tx.Rollback()
			util.ReportError(ctx, errors.Wrap(err, "error marshaling corrected data"))
			return http.StatusInternalServerError, handlers.ErrorMessage("Error reading corrected data", nil)
		}
		query = `update ai_cancellations set status = $1, updated_at = now() at time zone 'utc', user_corrected_result = $2 where id = $3`
		args = []interface{}{RequestStatusCancelled, correctedData, id}
	}
	_, err = tx.ExecContext(ctx, query, args...)
	if err != nil {
		_ = tx.Rollback()
		util.ReportError(ctx, errors.Wrapf(err, "error updating request with ID %s", id))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error updating cancellation request", nil)
	}
	// Store attachments to the contract attachments table
	var requestAttachments []AICancellationAttachment
	query = `select id, s3_bucket, s3_file_name, file_name, content_type, description from ai_cancellation_attachments where ai_cancellation_id = $1`
	err = tx.SelectContext(ctx, &requestAttachments, query, id)
	if err != nil {
		_ = tx.Rollback()
		util.ReportError(ctx, errors.Wrap(err, "error getting attachments"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error saving attachments", nil)
	}
	var CancelFormTypeID int
	query = `select id from attachment_types where name = $1`
	err = tx.GetContext(ctx, &CancelFormTypeID, query, AttachmentTypeCancelForm)
	if err != nil {
		_ = tx.Rollback()
		util.ReportError(ctx, errors.Wrap(err, "error getting attachment type"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting attachment type", nil)
	}
	var typeID null.Int
	typeID.SetValid(int64(CancelFormTypeID))

	for _, attachment := range requestAttachments {
		for _, contract := range payload.Contracts {
			// Fallback logic for store ID
			storeID := payload.PayeeStoreID
			if storeID == 0 {
				err := tx.GetContext(ctx, &storeID, "select store_id from contracts where id = $1", contract)
				if err != nil {
					_ = tx.Rollback()
					util.ReportError(ctx, errors.Wrap(err, "error fetching store_id for contract"))
					return http.StatusInternalServerError, handlers.ErrorMessage("Error fetching store for contract", nil)
				}
			}
			dbAttachment := &db.ContractAttachment{
				ContractID:       contract,
				StoreID:          storeID,
				S3Bucket:         attachment.S3Bucket,
				S3FileName:       attachment.S3FileName,
				FileName:         attachment.FileName,
				ContentType:      attachment.ContentType,
				Description:      attachment.Description,
				AttachmentTypeID: typeID,
				CreatedByUserID:  user.ID,
			}
			if attachment.CreatedByUserID != 0 {
				dbAttachment.CreatedByUserID = attachment.CreatedByUserID
			}
			// check for existing attachment
			query = `select id from contract_attachments where contract_id = $1 and store_id = $2 and s3_file_name = $3`
			var attachmentID int
			err = tx.GetContext(ctx, &attachmentID, query, contract, storeID, attachment.S3FileName)
			if err != nil && !errors.Is(err, sql.ErrNoRows) {
				_ = tx.Rollback()
				util.ReportError(ctx, errors.Wrap(err, "error checking for existing attachment"))
				return http.StatusInternalServerError, handlers.ErrorMessage("Error checking for existing attachment", nil)
			}
			if attachmentID != 0 {
				continue
			}
			_, err = db.CreateContractAttachment(tx, *dbAttachment)
			if err != nil {
				_ = tx.Rollback()
				util.ReportError(ctx, errors.Wrap(err, "error saving attachment"))
				return http.StatusInternalServerError, handlers.ErrorMessage("Error saving attachment", nil)
			}
		}
	}

	isAdmin := isAccountRepAdmin(user)
	isManager := isAccountRepManager(user)
	return handlers.ContractCancel(w, req, user, false, isAdmin, isManager, true, tx)
}

func getAIResponsePayload(req *http.Request) (*aiProcessingResponse, error) {
	var reqPayload *aiProcessingResponse
	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&reqPayload)
	if err != nil {
		return nil, errors.Wrap(err, "error decoding request payload")
	}

	return reqPayload, nil
}

// CancellationRequestUpdate saves the response from AI pipeline
func CancellationRequestUpdate(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()
	id := chi.URLParam(req, "id")
	var cancellationRequest db.AICancellation

	query := `select id, source, status, created_at
		from ai_cancellations
		where id = $1 and deleted_at is null`

	err := db.Get().GetContext(ctx, &cancellationRequest, query, id)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return http.StatusOK, map[string]interface{}{
				"id":     id,
				"status": "Cancellation request is not found or deleted",
			}
		}
		util.ReportError(ctx, errors.Wrapf(err, "error getting request with ID %s", id))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting request", nil)
	}

	payload, err := getAIResponsePayload(req)
	if err != nil {
		err = errors.WithMessage(err, "error getting request payload")
		util.ReportError(ctx, err)
		return http.StatusBadRequest, handlers.ErrorMessage("Error getting request payload", nil)
	}
	processingResult, err := json.Marshal(payload.ProcessingResult)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage("Error marshaling processing result", nil)
	}
	processingError, err := json.Marshal(payload.ProcessingError)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage("Error marshaling processing error", nil)
	}

	tx, err := db.Get().Beginx()
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error starting transaction"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error starting database transaction", nil)
	}
	defer func() { _ = tx.Rollback() }()
	query = `update ai_cancellations set status = $1, processed_at = $2,
		processing_result = $3, processing_error = $4, updated_at = $5,
		processing_logs = $6, file_id = $7, page = $8, page_type = $9, version = $10, processing_time = $2 - created_at where id = $11`
	_, err = tx.ExecContext(
		ctx,
		query,
		RequestStatusProcessed,
		time.Now(),
		processingResult,
		processingError,
		time.Now(),
		payload.ProcessingLogs,
		payload.FileID,
		payload.Page,
		payload.PageType,
		payload.Version,
		id,
	)
	if err != nil {
		util.ReportError(ctx, errors.Wrapf(err, "error updating request with ID %s", id))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error updating request", nil)
	}
	attachmentQuery := `update ai_cancellation_attachments set description = $1 where id = $2`
	_, err = tx.ExecContext(
		ctx,
		attachmentQuery,
		AttachmentTypeCancelForm,
		payload.FileID,
	)
	if err != nil {
		util.ReportError(ctx, errors.Wrapf(err, "error updating attachment for request with ID %s", id))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error updating attachment", nil)
	}

	err = tx.Commit()
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error committing transaction"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error committing transaction", nil)
	}

	return http.StatusOK, nil
}

// CancellationRequestDelete marks a cancellation request as deleted
func CancellationRequestDelete(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()
	id := chi.URLParam(req, "id")

	payload := struct {
		Note string `json:"note"`
	}{}
	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&payload)
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error decoding request payload"))
		return http.StatusBadRequest, handlers.ErrorMessage("Error decoding request payload", nil)
	}

	query := `update ai_cancellations
	set deleted_at = $1,
	deleted_by_user_id = $2,
	delete_note = $3
	where id = $4 and status != $5`

	_, err = db.Get().ExecContext(ctx, query, time.Now(), user.ID, payload.Note, id, RequestStatusVerified)
	if err != nil {
		util.ReportError(ctx, errors.Wrapf(err, "error deleting request with ID %s", id))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error deleting request", nil)
	}

	return http.StatusOK, map[string]interface{}{
		"status": "Success",
	}
}

func sendProcessingRequest(payload *processingLambdaPayload) error {
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return errors.Wrap(err, "error marshaling payload")
	}
	resp, err := http.Post(
		conf.Get().AICancellation.CancellationServer+"/prod/cancel",
		"application/json",
		bytes.NewReader(payloadBytes),
	)
	if err != nil {
		return errors.Wrap(err, "error sending request to processing lambda")
	}
	defer func() { _ = resp.Body.Close() }()
	if resp.StatusCode != http.StatusOK {
		return errors.New("bad response from processing lambda")
	}
	return nil
}

// CancellationRequestResubmit resubmits a cancellation request for processing
func CancellationRequestResubmit(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()
	idStr := chi.URLParam(req, "id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage("Invalid request id", nil)
	}

	// Get the cancellation request and its attachments
	var cancellationRequest db.AICancellation
	query := `select id, source, status, created_at
		from ai_cancellations
		where id = $1 and created_by_user_id = $2 and deleted_at is null`

	err = db.Get().GetContext(ctx, &cancellationRequest, query, id, user.ID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return http.StatusBadRequest, handlers.ErrorMessage("Incorrect request id", nil)
		}
		util.ReportError(ctx, errors.Wrapf(err, "error getting request with ID %d", id))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting request", nil)
	}

	// Get attachments for the request
	var attachments []AICancellationAttachment
	query = `select id, s3_bucket, s3_file_name, file_name, content_type
		from ai_cancellation_attachments
		where ai_cancellation_id = $1`

	err = db.Get().SelectContext(ctx, &attachments, query, id)
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error getting attachments"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting attachments", nil)
	}

	if len(attachments) == 0 {
		return http.StatusBadRequest, handlers.ErrorMessage("No attachments found for the request", nil)
	}

	// Create processing lambda payload
	var fileList []payloadFiles
	lambdaPayload := processingLambdaPayload{
		RequestID:       id,
		Environment:     conf.Get().AppEnv,
		ConnectHostname: conf.Get().Server,
		S3Bucket:        s3util.Bucket(),
		UserID:          user.ID,
		Files:           fileList,
	}

	// Add files to the lambda payload
	for _, attachment := range attachments {
		lambdaPayload.Files = append(lambdaPayload.Files, payloadFiles{
			FileID:   attachment.ID,
			FilePath: url.PathEscape(attachment.S3FileName),
		})
	}

	// Update request status to Processing
	tx, err := db.Get().Beginx()
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error starting transaction"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error starting database transaction", nil)
	}
	defer func() { _ = tx.Rollback() }()

	query = `update ai_cancellations 
		set status = $1, 
			processing_result = null,
			processing_error = null,
			updated_at = now() at time zone 'utc'
		where id = $2`

	_, err = tx.ExecContext(ctx, query, RequestStatusProcessing, id)
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error updating request status"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error updating request status", nil)
	}

	err = tx.Commit()
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error committing transaction"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error committing transaction", nil)
	}

	// Send processing request
	err = sendProcessingRequest(&lambdaPayload)
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error sending processing request"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error connecting to the processing server", nil)
	}

	return http.StatusOK, map[string]interface{}{
		"id":     id,
		"status": "Resubmitted for processing",
	}
}

// UploadVinForAIPipeline uploads VIN data for AI pipeline processing
func UploadVinForAIPipeline(ctx context.Context) error {
	// Query to get distinct VINs
	query := `select distinct vin
		from vin_records vr
		where
			LENGTH(TRIM(vr.vin)) = 17
			and raw_data != ''`

	var vins []string
	err := db.Get().SelectContext(ctx, &vins, query)
	if err != nil {
		return errors.Wrap(err, "error fetching VIN records")
	}

	// Create CSV buffer
	var csvBuffer bytes.Buffer
	writer := csv.NewWriter(&csvBuffer)

	// Write header
	err = writer.Write([]string{"vin"})
	if err != nil {
		return errors.Wrap(err, "error writing CSV header")
	}

	// Write VIN records
	for _, vin := range vins {
		err = writer.Write([]string{vin})
		if err != nil {
			return errors.Wrap(err, "error writing VIN to CSV")
		}
	}
	writer.Flush()
	if err := writer.Error(); err != nil {
		return errors.Wrap(err, "error flushing CSV writer")
	}

	// Upload to S3
	s3Path := conf.Get().AICancellation.VINDataS3Path
	err = s3util.Put(nil, bytes.NewReader(csvBuffer.Bytes()), s3util.DefaultRegion, s3util.Bucket(), s3Path)
	if err != nil {
		return errors.Wrapf(err, "error uploading CSV to S3 path %s", s3Path)
	}

	return nil
}

type assignPayload struct {
	UserID     string   `json:"user_id"`
	RequestIDs []string `json:"request_ids"`
}

// decode assignPayload decodes the JSON payload for assigning cancellation requests
func decodeAssignPayload(req *http.Request) (*assignPayload, error) {
	var payload assignPayload
	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&payload)
	if err != nil {
		return nil, errors.Wrap(err, "error decoding assign payload")
	}
	return &payload, nil
}

// CancellationRequestsAssign assigns cancellation requests to a user
func CancellationRequestsAssign(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()
	var payload *assignPayload
	var err error

	if payload, err = decodeAssignPayload(req); err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error decoding assign payload"))
		return http.StatusBadRequest, handlers.ErrorMessage("Error decoding assign payload", nil)
	}

	if payload.UserID == "" || len(payload.RequestIDs) == 0 {
		return http.StatusBadRequest, handlers.ErrorMessage("User ID and request IDs are required", nil)
	}

	// Validate assigned user ID
	var userExists bool
	query := `select exists(select 1 from current_users where id = $1 and roles ?| ARRAY[$2, $3])`
	err = db.Get().GetContext(ctx, &userExists, query, payload.UserID, db.RoleAccountRep, db.RoleAccountRepII)
	if err != nil {
		util.ReportError(ctx, errors.Wrapf(err, "error checking if user with ID %s exists", payload.UserID))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error checking user existence", nil)
	}
	if !userExists {
		return http.StatusBadRequest, handlers.ErrorMessage("Assigned user does not exist or is not an Account Rep", nil)
	}
	// Validate request IDs
	for _, idStr := range payload.RequestIDs {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return http.StatusBadRequest, handlers.ErrorMessage("Invalid request ID: "+idStr, nil)
		}
		var requestExists bool
		query = `select exists(select 1 from ai_cancellations where id = $1 and deleted_at is null)`
		err = db.Get().GetContext(ctx, &requestExists, query, id)
		if err != nil {
			util.ReportError(ctx, errors.Wrapf(err, "error checking if request with ID %s exists", idStr))
			return http.StatusInternalServerError, handlers.ErrorMessage("Error checking request existence", nil)
		}
		if !requestExists {
			return http.StatusBadRequest, handlers.ErrorMessage("Request with ID "+idStr+" does not exist or is not yours", nil)
		}
	}

	query, args, err := sqlx.In(`
		update ai_cancellations
		set assigned_to_user_id = ?, updated_at = now() at time zone 'utc'
		where id in (?)`, payload.UserID, payload.RequestIDs)
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error preparing update query"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error preparing update query", nil)
	}
	query = db.Get().Rebind(query)
	_, err = db.Get().ExecContext(ctx, query, args...)
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error updating cancellation requests"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error updating cancellation requests", nil)
	}

	return http.StatusOK, map[string]interface{}{
		"status": "Request assigned successfully",
	}
}
