package adminhandlers

import (
	"database/sql"
	"encoding/json"
	"net/http"
	"whiz/db"
	"whiz/handlers"
	"whiz/util"

	"github.com/pkg/errors"
	"gopkg.in/guregu/null.v3"
)

// AsburyAPIKeyShow retrieves the API key details for the Asbury client.
func AsburyAPIKeyShow(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()
	resp := struct {
		KeyHash   string    `json:"key_hash" db:"key_hash"`
		ExpiresAt null.Time `json:"expires_at" db:"expires_at"`
		IsActive  bool      `json:"is_active" db:"is_active"`
	}{}

	err := db.Get().GetContext(ctx, &resp, "select key_hash, expires_at, is_active from api_keys where client=$1 and is_active=true", db.APIClientNameAsbury)
	if err != nil {
		if err == sql.ErrNoRows {
			util.ReportError(ctx, errors.New("API key not found for Asbury"))
			return http.StatusNotFound, handlers.ErrorMessage("API key not found", nil)
		}
		util.ReportError(ctx, errors.Wrap(err, "error fetching API key"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error fetching API key", nil)
	}
	return http.StatusOK, map[string]interface{}{"api_key": resp}
}
func getAPIKeyPayload(req *http.Request) (string, error) {
	var payload struct {
		KeyHash string `json:"key_hash"`
	}

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&payload)
	if err != nil {
		return "", errors.Wrap(err, "error decoding request payload")
	}
	if payload.KeyHash == "" {
		return "", errors.New("API key is required")
	}
	return payload.KeyHash, nil
}

// AsburyAPIKeyUpdate updates the API key for the Asbury client.
func AsburyAPIKeyUpdate(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()

	newAPIKeyHash, err := getAPIKeyPayload(req)
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error getting API key payload"))
		return http.StatusBadRequest, handlers.ErrorMessage("Invalid API key payload", nil)
	}

	if len(newAPIKeyHash) != 64 {
		util.ReportError(ctx, errors.New("API key must be 64 characters long"))
		return http.StatusBadRequest, handlers.ErrorMessage("API key must be 64 characters long", nil)
	}

	_, err = db.Get().ExecContext(ctx, "update api_keys set key_hash=$1 where client=$2 and is_active=true", newAPIKeyHash, db.APIClientNameAsbury)
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error updating API key"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error updating API key", nil)
	}

	return http.StatusOK, map[string]interface{}{"message": "API key updated successfully"}
}

// AsburyAPIKeyCreate creates the API key for the Asbury client.
func AsburyAPIKeyCreate(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()

	apiKey, apiKeyHash, err := util.GenerateAPIKey()
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error generating API key"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error generating API key", nil)
	}

	// begin transaction
	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error starting transaction"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error starting transaction", nil)
	}

	// expire the old API key if it exists
	_, err = tx.ExecContext(ctx, "update api_keys set is_active=false where client=$1 and is_active=true", db.APIClientNameAsbury)
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error expiring old API key"))
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage("Error expiring old API key", nil)
	}

	// insert the new API key
	query := `insert into api_keys(
	key_hash, created_by_user_id, client, is_active)
	values ($1,$2,$3,true)`

	_, err = tx.ExecContext(ctx, query, apiKeyHash, user.ID, db.APIClientNameAsbury)
	if err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error updating API key"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error updating API key", nil)
	}
	if err = tx.Commit(); err != nil {
		util.ReportError(ctx, errors.Wrap(err, "error committing transaction"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error committing transaction", nil)
	}
	return http.StatusOK, map[string]interface{}{"message": "API key created: " + apiKey}
}
