package adminhandlers

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"whiz/db"
	"whiz/handlers"
	"whiz/randstr"
	"whiz/s3util"
	"whiz/types"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"github.com/lib/pq"
	newrelic "github.com/newrelic/go-agent"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"gopkg.in/guregu/null.v3"
)

type reinstatePayload struct {
	Attachments []struct {
		FileContent      string   `json:"file_content"`
		Name             string   `json:"name"`
		Description      string   `json:"description"`
		ContentType      string   `json:"content_type"`
		AttachmentTypeID null.Int `json:"attachment_type_id"`
	} `json:"attachments"`
	Contracts []int `json:"contracts"`
}

func uploadReinstateAttachments(ctx context.Context, reqPayload *reinstatePayload, userID int) (attachments []db.ContractAttachment, err error) {

	var contractCodes []string
	for _, contractID := range reqPayload.Contracts {
		var contractCode string
		err = db.Get().GetContext(ctx, &contractCode, `select code from contracts where id = $1`, contractID)
		if err != nil {
			err = errors.Wrap(err, "error looking up code for contract")
			return attachments, err
		}
		contractCodes = append(contractCodes, contractCode)
	}

	var ca db.ContractAttachment
	for _, att := range reqPayload.Attachments {
		ca.FileName = strings.TrimSpace(att.Name)
		if len(ca.FileName) == 0 {
			continue
		}

		// reinstate attachment payload
		ca.CreatedByUserID = userID
		ca.S3Bucket = s3util.Bucket()
		ca.S3FileName = fmt.Sprintf("contract-reinstate-attachments/%s_%s_%s", strings.Join(contractCodes, "_"), randstr.StringN(6), att.Name)
		if ca.FileName == "" {
			return attachments, errors.New("attachment file name is required")
		}
		ca.ContentType = att.ContentType
		ca.Description = att.Description
		ca.AttachmentTypeID = att.AttachmentTypeID

		if att.FileContent == "" {
			return attachments, errors.New("attachment is required")
		}

		// upload attachment to S3 bucket
		data, err := base64.StdEncoding.DecodeString(att.FileContent)
		if err != nil {
			err = errors.Wrap(err, "error base64 decoding reinstate attachment")
			return attachments, err
		}
		txn := newrelic.FromContext(ctx)
		err = s3util.Put(txn, bytes.NewReader(data), s3util.DefaultRegion, ca.S3Bucket, ca.S3FileName)
		if err != nil {
			err = errors.Wrapf(err, "error uploading reinstate attachment file to %s/%s", ca.S3Bucket, ca.S3FileName)
			return attachments, err
		}
		attachments = append(attachments, ca)
	}
	return attachments, nil
}

// ContractReinstateRequest reverts contract cancellation for given contract
func ContractReinstateRequest(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()

	var reqPayload reinstatePayload

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&reqPayload)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage("Bad request"+err.Error(), nil)
	}

	for _, contractID := range reqPayload.Contracts {
		if err := validateReinstateRequest(ctx, contractID); err != nil {
			err = errors.Wrapf(err, "for contractID %d", contractID)
			handlers.ReportError(req, err)
			return http.StatusBadRequest, handlers.ErrorMessage(err.Error(), nil)
		}
	}

	attachments, err := uploadReinstateAttachments(ctx, &reqPayload, user.ID)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error in uploading reinstate attachments", nil)
	}

	for _, contractID := range reqPayload.Contracts {
		tx, err := db.Get().BeginTxx(ctx, nil)
		if err != nil {
			handlers.ReportError(req, errors.Wrap(err, "error beginning transaction"))
			return http.StatusInternalServerError, handlers.ErrorMessage("Transaction error", nil)
		}

		contract, err := handlers.ContractInfo(ctx, contractID, nil)
		// Don't throw an error if there was an issue with getting data from CDK when loading the contract data.
		if err != nil && !errors.Is(err, handlers.ErrDMSLookUpFailed) {
			tx.Rollback()
			handlers.ReportError(req, errors.Wrapf(err, "failed to fetch contract details with ID %d", contract.ID))
			return http.StatusInternalServerError, handlers.ErrorMessage("Failed to fetch contract details", nil)
		}

		if contract.Status != db.ContractStatusCancelled {
			tx.Rollback()
			handlers.ReportError(req, errors.Wrapf(err, "invalid contract for reinstate with ID %d", contract.ID))
			return http.StatusInternalServerError, handlers.ErrorMessage("Failed to reinstate contract", nil)
		}

		// create contract logs
		err = db.CreateContractLog(tx, contractID, user.ID)
		if err != nil {
			tx.Rollback()
			handlers.ReportError(req, errors.Wrapf(err, "error contract_logs insert for contract ID %d", contractID))
			return http.StatusInternalServerError, handlers.ErrorMessage("Error in create contract logs", nil)
		}

		store := db.Store{}
		query := `select s.* from stores s join contracts c on c.store_id = s.id where c.id = $1`
		err = db.Get().Unsafe().GetContext(ctx, &store, query, contractID)
		if err != nil {
			err = errors.Wrap(err, "error looking up store for contract")
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Error looking up store for contract", nil)
		}

		var cancellationInfo struct {
			CancellationID        int             `db:"id"`
			StoreRefund           decimal.Decimal `db:"store_refund"`
			StoreChargeback       decimal.Decimal `db:"store_chargeback"`
			SPPRefund             decimal.Decimal `db:"spp_refund"`
			CustomerRefund        decimal.Decimal `db:"customer_refund"`
			CancellationBreakouts []struct {
				RateBucketID int             `db:"rate_bucket_id"`
				Amount       decimal.Decimal `db:"amount"`
			}
			SalesTax     decimal.Decimal `db:"sales_tax"`
			SalesTaxRate decimal.Decimal `db:"sales_tax_rate"`
		}
		query = `select id, store_refund, customer_refund, store_chargeback, spp_refund, sales_tax, sales_tax_rate 
			from contract_cancellations where contract_id = $1 and is_void = false`
		err = db.Get().GetContext(ctx, &cancellationInfo, query, contractID)
		if err != nil {
			tx.Rollback()
			handlers.ReportError(req, errors.Wrapf(err, "error getting cancellation details %d", contractID))
			return http.StatusInternalServerError, handlers.ErrorMessage("Error in getting cancellation details", nil)
		}

		query = `select ccb.rate_bucket_id, ccb.amount
				from contract_cancellation_breakouts ccb where ccb.contract_cancellation_id = $1`
		err = db.Get().SelectContext(ctx, &cancellationInfo.CancellationBreakouts, query, cancellationInfo.CancellationID)
		if err != nil && err != sql.ErrNoRows {
			tx.Rollback()
			handlers.ReportError(req, errors.Wrapf(err, "error getting cancellation breakout details %d", contractID))
			return http.StatusInternalServerError, handlers.ErrorMessage("Error in getting cancellation breakout details", nil)
		}

		// Add cancel transaction
		tran := db.Transaction{}
		tran.ContractID = contractID
		tran.CreatedByUserID = user.ID
		tran.StoreID = store.ID
		tran.VehicleYear = contract.VehicleYear
		tran.VehicleMake = contract.VehicleMake
		tran.VehicleModel = contract.VehicleModel
		tran.TransactionType = db.TranTypeReinstatement
		tran.Amount = cancellationInfo.StoreRefund
		tran.ContractPrice = contract.Price
		err = db.CreateTransaction(ctx, tx, &tran)
		if err != nil {
			tx.Rollback()
			err = errors.Wrapf(err, "could not create transaction for contract %s", contract.Code)
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Failed to cancel contract", nil)
		}

		contractReinstate := struct {
			ReInstateID            int             `db:"id" json:"reinstate_id"`
			CreatedByUserID        int             `db:"created_by_user_id" json:"created_by_user_id"`
			ContractID             int             `db:"contract_id" json:"contract_id"`
			CustomerAmount         decimal.Decimal `db:"customer_amount" json:"customer_amount"`
			StoreAmount            decimal.Decimal `db:"store_amount" json:"store_amount"`
			IsVoid                 bool            `db:"is_void" json:"is_void"`
			TransactionID          int             `db:"transaction_id" json:"transaction_id"`
			ContractCancellationID int             `db:"contract_cancellation_id" json:"contract_cancellation_id"`
			SalesTax               decimal.Decimal `db:"sales_tax" json:"sales_tax"`
			SalesTaxRate           decimal.Decimal `db:"sales_tax_rate" json:"sales_tax_rate"`
		}{
			CreatedByUserID: user.ID,
			ContractID:      contract.ID,
			CustomerAmount:  cancellationInfo.CustomerRefund,
			StoreAmount: db.GetReinstatementInvoiceStoreAmount(
				cancellationInfo.StoreRefund,
				cancellationInfo.StoreChargeback,
				cancellationInfo.SPPRefund,
			),
			IsVoid:                 false,
			TransactionID:          tran.ID,
			ContractCancellationID: cancellationInfo.CancellationID,
			SalesTax:               cancellationInfo.SalesTax,
			SalesTaxRate:           cancellationInfo.SalesTaxRate,
		}

		query = `update contract_reinstatements 
			 set is_void = true, voided_at = now() at time zone 'utc', voided_by_user_id = $1 
			 where contract_id = $2 and is_void = false`
		_, err = tx.ExecContext(ctx, query, user.ID, contract.ID)
		if err != nil {
			_ = tx.Rollback()
			handlers.ReportError(req, errors.Wrapf(err, "failed to void last reinstate for contract %d", contract.ID))
			return http.StatusInternalServerError, handlers.ErrorMessage("Failed to void last reinstate", nil)
		}

		query = `insert into contract_reinstatements
			(contract_id, created_at, created_by_user_id, reinstate_date,
			customer_amount, store_amount, transaction_id, contract_cancellation_id, is_void, sales_tax, sales_tax_rate)
			values(:contract_id, now() at time zone 'utc', :created_by_user_id, now() at time zone 'utc',
			:customer_amount, :store_amount, :transaction_id, :contract_cancellation_id, :is_void, :sales_tax, :sales_tax_rate) returning id`
		stmt, err := tx.PrepareNamedContext(ctx, query)
		if err != nil {
			tx.Rollback()
			err = errors.Wrapf(err, "error preparing reinstate insert statement for contract %s", contract.Code)
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Failed to reinstate contract", nil)
		}
		defer func() { stmt.Close() }()

		err = stmt.GetContext(ctx, &contractReinstate.ReInstateID, contractReinstate)
		if err != nil {
			tx.Rollback()
			err = errors.Wrapf(err, "error inserting reinstate for contract %s", contract.Code)
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Failed to reinstate contract", nil)
		}

		provider, err := db.GetContractProvider(tx, contract.ID)
		if err != nil {
			tx.Rollback()
			err = errors.Wrapf(err, "error getting provider for contract %s", contract.Code)
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Failed to reinstate contract", nil)
		}

		// Load contract as db.Contract
		dbContract, err := db.GetContractByID(ctx, tx, contract.ID)
		if err != nil {
			_ = tx.Rollback()
			msg := fmt.Sprintf("failed to get contract %s", contract.Code)
			err = errors.WithMessage(err, msg)
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Failed to reinstate contract", nil)
		}

		var providerRemitBucketID int
		if provider.Name != "TCA" {
			query = `select distinct rb.id 
			from product_variants pv
				join products p on pv.product_id = p.id
				join providers p2 on p.provider_id = p2.id
				join rate_buckets rb on p2.id = rb.provider_id and is_refundable = true
			where p2.id = $1`
			err = db.Get().GetContext(ctx, &providerRemitBucketID, query, provider.ID)
			if err != nil {
				tx.Rollback()
				err = errors.Wrapf(err, "error getting provider rate bucket for contract %s", contract.Code)
				handlers.ReportError(req, err)
				return http.StatusInternalServerError, handlers.ErrorMessage("Failed to reinstate contract", nil)
			}
		}

		err = db.CreateSPPReinstateTransactionRemittal(tx, dbContract, tran, cancellationInfo.SPPRefund)
		if err != nil {
			tx.Rollback()
			err = errors.Wrapf(err, "could not create spp transaction_remittal for contract %s", contract.Code)
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("failed to reinstate contract", nil)
		}

		for _, v := range cancellationInfo.CancellationBreakouts {
			// TODO: Stop saving contract_reinstatement_breakouts
			reinstateDate := types.JSPQNullDate{
				NullTime: pq.NullTime{
					Valid: true,
					Time:  time.Now(),
				},
			}

			rs := db.GetRateBucketReinstateSettingsByID(contract.RateBucketReinstateSettings, v.RateBucketID, reinstateDate)
			bo := db.ContractReInstateBreakout{}
			bo.CreatedByUserID = user.ID
			bo.ContractReInstateID = contractReinstate.ReInstateID
			bo.Amount = v.Amount
			bo.RateBucketID = rs.ReinstateRateBucketID
			err = db.SaveContractReInstateBreakout(ctx, tx, bo)
			if err != nil {
				tx.Rollback()
				err = errors.Wrapf(err, "error inserting reinstate breakout for contract %s", contract.Code)
				handlers.ReportError(req, err)
				return http.StatusInternalServerError, handlers.ErrorMessage("Failed to reinstate contract", nil)
			}

			var tbo db.TransactionBreakout
			tbo.TransactionID = tran.ID
			tbo.CreatedByUserID = user.ID
			tbo.RateBucketID = rs.ReinstateRateBucketID
			tbo.Amount = v.Amount
			err = db.InsertTransactionBreakout(ctx, tx, tbo)
			if err != nil {
				tx.Rollback()
				err = errors.Wrapf(err, "error inserting transaction breakout for contract %s", contract.Code)
				handlers.ReportError(req, err)
				return http.StatusInternalServerError, handlers.ErrorMessage("Failed to reinstate contract", nil)
			}

			// Create a transaction remittal entry if the rate bucket is used for a 3rd-party provider
			if (provider.Name != "TCA" && v.RateBucketID == providerRemitBucketID) ||
				v.RateBucketID == db.RateBucketRsaID {
				var tr db.TransactionRemittal
				tr.TransactionID = tran.ID
				tr.Amount = v.Amount
				tr.ProviderID = provider.ID
				if v.RateBucketID == db.RateBucketRsaID {
					tr.ProviderID = int(contract.RSAProviderID.ValueOrZero())
				}
				err = db.CreateTransactionRemittal(tx, dbContract, &tr)
				if err != nil {
					tx.Rollback()
					err = errors.Wrapf(err, "could not create transaction remittal for contract %s", contract.Code)
					handlers.ReportError(req, err)
					return http.StatusInternalServerError, handlers.ErrorMessage("Failed to reinstate contract", nil)
				}
			}
		}

		var updateContract struct {
			ID              int       `db:"id" json:"id"`
			UpdatedAt       time.Time `db:"updated_at" json:"-"`
			UpdatedByUserID int       `db:"updated_by_user_id" json:"-"`
			Status          string    `db:"status" json:"-"`
			Event           string    `db:"event" json:"-"`
			EventNotes      string    `db:"event_notes" json:"event_notes"`
		}

		updateContract.ID = contractID
		updateContract.Status = db.ContractStatusActive
		updateContract.UpdatedByUserID = user.ID
		updateContract.Event = db.ContractEventReinstate
		updateContract.EventNotes = ""

		query = `update contracts
		set version = version + 1,
		updated_at = now() at time zone 'utc', updated_by_user_id = :updated_by_user_id,
		status = :status, event = :event, event_notes = :event_notes
		where id = :id`

		contractStmt, err := tx.PrepareNamedContext(ctx, query)
		if err != nil {
			tx.Rollback()
			err = errors.Wrap(err, "error preparing contracts update")
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Error reinstating contract", nil)
		}
		defer contractStmt.Close()

		_, err = contractStmt.ExecContext(ctx, updateContract)
		if err != nil {
			tx.Rollback()
			err = errors.Wrap(err, "error executing contracts update")
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Error reinstating contract", nil)
		}

		query = `insert into contract_events(
				created_at, created_by_user_id, created_by_name, contract_id, description
			)
			values(now() at time zone 'utc', $1, $2, $3, $4)`
		_, err = tx.ExecContext(ctx, query, user.ID, user.FullName(), updateContract.ID, db.ContractEventReinstate)
		if err != nil {
			tx.Rollback()
			err = errors.Wrap(err, "error inserting contract event")
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Error reinstating contract", nil)
		}

		// save attachment
		if !user.HasRole(db.RoleTestAutomation) || len(attachments) > 0 {
			for _, att := range attachments {
				att.ContractID = contractID
				att.StoreID = store.ID
				_, err = db.CreateContractAttachment(tx, att)
				if err != nil {
					_ = tx.Rollback()
					err = errors.Wrapf(err, "error saving attachment %s for contract %d", att.FileName, att.ContractID)
					handlers.ReportError(req, err)
					return http.StatusInternalServerError, handlers.ErrorMessage("error saving attachment for contract reinstate", nil)
				}
			}
		}
		tx.Commit()
	}

	return http.StatusOK, map[string]interface{}{
		"message": "Contract reinstated successfully!",
		"count":   len(reqPayload.Contracts),
	}
}

func validateReinstateRequest(ctx context.Context, id int) error {
	status := ""
	err := db.Get().GetContext(ctx, &status, `select status from contracts where id = $1`, id)
	if err != nil {
		return err
	}
	if status != db.ContractStatusCancelled {
		return errors.New("can not reinstate contract on non-canceled contract")
	}
	// Verify cancellation is invoiced
	invoiceID := sql.NullInt64{}
	err = db.Get().Get(&invoiceID,
		`select t.invoice_id from contract_cancellations cc
			join transactions t on cc.transaction_id = t.id
			where cc.contract_id = $1 and cc.is_void = false`,
		id)
	if err != nil && err != sql.ErrNoRows {
		return err
	}
	if !invoiceID.Valid {
		return errors.New("contract is not invoiced")
	}
	return nil
}

// reinstateContractInfo contains reinstate details for contract
// that will be or has been reinstated
type reinstateContractInfo struct {
	ID              int                `json:"id" db:"id"`
	Code            string             `json:"code" db:"code"`
	ProductTypeCode string             `json:"product_type_code"`
	ProductTypeName string             `json:"product_type_name" db:"product_type_name"`
	ProductTypeID   int                `json:"product_type_id"`
	StoreCode       string             `json:"store_code" db:"store_code"`
	EffectiveDate   types.JSPQNullDate `json:"effective_date" db:"effective_date"`
	CustomerRefund  decimal.Decimal    `json:"customer_refund" db:"customer_refund"`
	StoreRefund     decimal.Decimal    `json:"store_refund" db:"store_refund"`
	OriginalCode    string             `json:"original_code" db:"original_code"`
	CancelDate      types.JSPQDate     `json:"cancel_date" db:"cancel_date"`
}

// ContractReinstateInfo returns list of contract reinstate with information
func ContractReinstateInfo(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()

	id, err := strconv.Atoi(chi.URLParam(req, "id"))
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage("Invalid contact ID", nil)
	}

	if err := validateReinstateRequest(ctx, id); err != nil {
		handlers.ReportError(req, err)
		return http.StatusBadRequest, handlers.ErrorMessage("Contract validation failed for reinstate.", nil)
	}

	contracts := []reinstateContractInfo{}

	query := `
	select c2.id
    from contracts c
    join vin_records vr on vr.id = c.vin_record_id
    join customers cust on cust.id = c.customer_id
    -- get all vin_records with the same vin
    join vin_records vr2 on vr2.vin = vr.vin
    join contracts c2 on c2.vin_record_id = vr2.id
    join customers cust2 on cust2.id = c2.customer_id
    join contract_cancellations cc on c2.id = cc.contract_id
    join transactions t on cc.transaction_id = t.id 
    where c.id = $1
    and c2.status = $2
    and c2.source != $3
    and lower(cust.first_name) = lower(cust2.first_name)
    and lower(cust.last_name) = lower(cust2.last_name)
    and cc.is_void = false
	and cc.cancel_status not in ($4, $5)
	`
	ids := []int{}
	err = db.Get().SelectContext(ctx, &ids, query, id, db.ContractStatusCancelled,
		db.ContractSourceDealerPortal,
		db.CancelStatusCheckRequestedPendingApproval,
		db.CancelStatusInvoicedPendingCheckRequest)
	if err != nil {
		handlers.ReportError(req, errors.Wrapf(err, "failed to fetch contracts related to contract with ID %d", id))
		return http.StatusInternalServerError, handlers.ErrorMessage("Failed to fetch related contracts", nil)
	}

	query = `select c.id, c.code, c.original_code, c.effective_date, 
		pt.name product_type_name,
		st.code store_code,
		cc.store_refund, cc.customer_refund, cc.cancel_date
		from contracts c 
		join contract_cancellations cc on cc.contract_id = c.id
		-- if the cancellation has not been invoiced yet, then it can't be reinstated.
		join transactions t on cc.transaction_id = t.id
			and t.invoiced_at is not null
		join product_types pt on pt.id = c.product_type_id
		join stores st on c.store_id = st.id
		where c.id in (?) 
		and cc.is_void = false
		order by pt.position`

	query, args, err := sqlx.In(query, ids)
	if err != nil {
		err = errors.Wrap(err, "error getting related contracts in reinstate info")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Failed to get related contracts in reinstate info", nil)
	}

	query = db.Get().Rebind(query)
	err = db.Get().SelectContext(ctx, &contracts, query, args...)
	if err != nil {
		handlers.ReportError(req, errors.Wrapf(err, "failed to fetch contracts related to contract with ID %d", id))
		return http.StatusInternalServerError, handlers.ErrorMessage("Failed to fetch related contracts in reinstate", nil)
	}

	attachmentTypes, err := db.GetAttachmentTypes(ctx)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting supporting data for contract reinstate", nil)
	}

	productTypes, err := db.GetProductTypes(req.Context())
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error loading product types", nil)
	}

	return http.StatusOK, map[string]interface{}{
		"estimates":        contracts,
		"attachment_types": attachmentTypes,
		"product_types":    productTypes,
	}
}
