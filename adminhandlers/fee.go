package adminhandlers

import (
	"context"
	"database/sql"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"
	"whiz/db"
	"whiz/handlers"
	"whiz/intacct"
	"whiz/types"
	"whiz/util"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"github.com/lib/pq"
	newrelic "github.com/newrelic/go-agent"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"gopkg.in/guregu/null.v3"
)

const (
	transactionTypeCancellation = "Cancellation"
)

type feeDealData struct {
	DMSNumber             string    `db:"dms_number"`
	CustomerName          string    `db:"customer_name"`
	SaleType              string    `db:"sale_type"`
	IsDMSDeal             bool      `db:"is_dms_deal"`
	ContractDate          time.Time `db:"contract_date"`
	TransactionType       string    `db:"transaction_type"`
	TransactionTypeID     int       `db:"transaction_type_id"`
	DMSCustomerNumber     string    `db:"dms_customer_number"`
	VehicleStockNumber    string    `db:"vehicle_stock_number"`
	StoreCode             string    `db:"store_code"`
	StoreID               int       `db:"store_id"`
	CompanyID             int       `db:"company_id"`
	PaymentTypeID         int       `db:"payment_type_id"`
	PaymentType           string    `db:"payment_type"`
	IntacctCustomerNumber string    `db:"intacct_customer_number"`
	InvoiceDate           time.Time `db:"invoice_date"`
	ApplyFees             bool      `db:"apply_fees"`
	VINRecordID           int       `db:"vin_record_id"`
}

type feeInvoiceArgs struct {
	InvoiceBatchID int       `db:"invoice_batch_id"`
	FeeID          int       `db:"fee_id"`
	FeeName        string    `db:"fee_name"`
	StoreID        int       `db:"store_id"`
	StoreCode      string    `db:"store_code"`
	InvoiceDate    time.Time `db:"invoice_date"`
	FeeCode        string    `db:"fee_code"`
}

type feeInvoiceDetail struct {
	ReferenceNumber string
	InvoiceID       int
	InvoiceItemID   []int
}

type feeSearchArgs struct {
	CompanyID         int    `db:"company_id"`
	StoreID           int    `db:"store_id"`
	ProductTypeID     int    `db:"product_type_id"`
	ProductID         int    `db:"product_id"`
	ProductVariantID  int    `db:"product_variant_id"`
	StateCode         string `db:"state_code"`
	PaymentTypeID     int    `db:"payment_type_id"`
	TransactionTypeID int    `db:"transaction_type_id"`
}

func (a *feeSearchArgs) containsValues() bool {
	return a.CompanyID > 0 || a.StoreID > 0 || a.ProductTypeID > 0 || a.ProductID > 0 || a.ProductVariantID > 0 || a.StateCode != "" || a.PaymentTypeID > 0 || a.TransactionTypeID > 0
}

type feePayload struct {
	ID                         int                `json:"id" db:"id"`
	Name                       string             `db:"name" json:"name"`
	Code                       string             `db:"code" json:"code"`
	CreatedByUserID            int                `db:"created_by_user_id" json:"-"`
	DeletedByUserID            null.Int           `db:"deleted_by_user_id" json:"-"`
	PreviousFeeID              null.Int           `db:"previous_fee_id" json:"-"`
	StartedOn                  types.JSPQDate     `json:"started_on" db:"started_on"`
	EndedOn                    types.JSPQNullDate `json:"ended_on" db:"ended_on"`
	TransactionTypeID          int                `db:"transaction_type_id" json:"transaction_type_id"`
	FeeCalculationMethodID     int                `db:"fee_calculation_method_id" json:"fee_calculation_method_id"`
	Amount                     decimal.Decimal    `db:"amount" json:"amount"`
	IsInvoiceable              bool               `db:"is_invoiceable" json:"is_invoiceable"`
	IntacctFeeProductID        int                `db:"intacct_fee_product_id" json:"intacct_fee_product_id"`
	IsStoresInclusive          bool               `db:"is_stores_inclusive" json:"-"`
	IsProductsInclusive        bool               `db:"is_products_inclusive" json:"-"`
	IsProductVariantsInclusive bool               `db:"is_product_variants_inclusive" json:"-"`
	States                     pq.StringArray     `db:"states" json:"states"`
	Companies                  []int              `json:"companies" db:"-"`
	Stores                     []int              `json:"stores" db:"-"`
	ProductTypes               []int              `json:"product_types" db:"-"`
	Products                   []int              `json:"products" db:"-"`
	ProductVariants            []int              `json:"product_variants" db:"-"`
	PaymentTypes               []int              `json:"payment_types" db:"payment_types"`
}

func (f *feePayload) clean() {
	f.Name = strings.TrimSpace(f.Name)
	f.Code = strings.TrimSpace(f.Code)
}

func (f *feePayload) validate(ctx context.Context) (map[string]string, error) {
	v := map[string]string{}

	if len(f.Name) < 1 {
		v["name"] = "Name is required"
	}

	if len(f.Code) < 1 {
		v["code"] = "Code is required"
	}

	var codeCount int
	cq := `select count(*) from fees where code = $1 and id != $2 and deleted_at is null`
	err := db.Get().GetContext(ctx, &codeCount, cq, f.Code, f.ID)
	if err != nil {
		err = errors.Wrap(err, "error validating fee code")
		return v, err
	}
	if codeCount > 0 {
		v["code"] = "Code is already in use, must be unique."
	}

	for _, stateCode := range f.States {
		if _, ok := db.States[stateCode]; !ok {
			v["states"] = fmt.Sprintf("At least one state is not valid (%s)", stateCode)
		}
	}

	if len(f.Companies) > 0 {
		var count int
		q := `select count(*) from companies where id in (?)`
		q, args, err := sqlx.In(q, f.Companies)
		if err != nil {
			err = errors.Wrap(err, "error validating companies")
			return v, err
		}
		q = db.Get().Rebind(q)
		err = db.Get().GetContext(ctx, &count, q, args...)
		if err != nil {
			err = errors.Wrap(err, "error validating companies")
			return v, err
		}
		if count < len(f.Companies) {
			v["companies"] = "At least one company is invalid."
		}
	}

	if len(f.Stores) > 0 {
		var count int
		q := `select count(*) from stores where id in (?)`
		q, args, err := sqlx.In(q, f.Stores)
		if err != nil {
			err = errors.Wrap(err, "error validating stores")
			return v, err
		}
		q = db.Get().Rebind(q)
		err = db.Get().GetContext(ctx, &count, q, args...)
		if err != nil {
			err = errors.Wrap(err, "error validating stores")
			return v, err
		}
		if count < len(f.Stores) {
			v["stores"] = "At least one store is invalid."
		}
	}

	if len(f.ProductTypes) > 0 {
		var count int
		q := `select count(*) from product_types where id in (?)`
		q, args, err := sqlx.In(q, f.ProductTypes)
		if err != nil {
			err = errors.Wrap(err, "error validating product types")
			return v, err
		}
		q = db.Get().Rebind(q)
		err = db.Get().GetContext(ctx, &count, q, args...)
		if err != nil {
			err = errors.Wrap(err, "error validating product types")
			return v, err
		}
		if count < len(f.ProductTypes) {
			v["product_types"] = "At least one product type is invalid."
		}
	}

	if len(f.Products) > 0 {
		var count int
		q := `select count(*) from products where id in (?)`
		q, args, err := sqlx.In(q, f.Products)
		if err != nil {
			err = errors.Wrap(err, "error validating products")
			return v, err
		}
		q = db.Get().Rebind(q)
		err = db.Get().GetContext(ctx, &count, q, args...)
		if err != nil {
			err = errors.Wrap(err, "error validating products")
			return v, err
		}
		if count < len(f.Products) {
			v["products"] = "At least one product is invalid."
		}
	}

	if len(f.ProductVariants) > 0 {
		var count int
		q := `select count(*) from product_variants where id in (?)`
		q, args, err := sqlx.In(q, f.ProductVariants)
		if err != nil {
			err = errors.Wrap(err, "error validating product variants")
			return v, err
		}
		q = db.Get().Rebind(q)
		err = db.Get().GetContext(ctx, &count, q, args...)
		if err != nil {
			err = errors.Wrap(err, "error validating product variants")
			return v, err
		}
		if count < len(f.ProductVariants) {
			v["product_variants"] = "At least one product variant is invalid."
		}
	}

	if len(f.PaymentTypes) > 0 {
		var count int
		q := `select count(*) from payment_types where id in (?)`
		q, args, err := sqlx.In(q, f.PaymentTypes)
		if err != nil {
			err = errors.Wrap(err, "error validating payment types")
			return v, err
		}
		q = db.Get().Rebind(q)
		err = db.Get().GetContext(ctx, &count, q, args...)
		if err != nil {
			err = errors.Wrap(err, "error validating payment types")
			return v, err
		}
		if count < len(f.PaymentTypes) {
			v["payment_types"] = "At least one payment type is invalid."
		}
	}

	return v, nil
}

// FeeSupportingData returns the supporting data for the fee form
func FeeSupportingData(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()
	productTypes := []struct {
		ID   int    `db:"id" json:"id"`
		Name string `db:"name" json:"name"`
	}{}
	productTypesQ := `
		select 
			id, 
			name 
		from product_types 
		order by 
			position asc`
	err := db.Get().SelectContext(ctx, &productTypes, productTypesQ)
	if err != nil {
		err = errors.Wrap(err, "database error getting product types for cancel stop rule supporting data")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting fee formsupporting data", nil)
	}

	products := []struct {
		ID            int    `db:"id" json:"id"`
		Name          string `db:"name" json:"name"`
		ProductTypeID int    `db:"product_type_id" json:"product_type_id"`
	}{}

	query := `
		select 
			p.id, 
			p.name, 
			pt.id as product_type_id
		from products p 
		join product_types pt on pt.id = p.product_type_id 
		order by 
			pt.id asc, 
			p.name asc`
	err = db.Get().SelectContext(ctx, &products, query)
	if err != nil {
		err = errors.Wrap(err, "error getting product types for fee")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting fee form supporting data", nil)
	}

	productVariants := []struct {
		ID        int    `json:"id" db:"id"`
		Name      string `json:"name" db:"name"`
		ProductID int    `json:"product_id" db:"product_id"`
	}{}
	productVariantsQ := `
		select 
			pv.id, 
			pv.name, 
			p.id as product_id
		from product_variants pv 
		join products p on pv.product_id = p.id 
		order by 
			pv.name asc`
	err = db.Get().SelectContext(ctx, &productVariants, productVariantsQ)
	if err != nil {
		err = errors.Wrap(err, "error loading product variants for fee")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error loading fee formsupporting data", nil)
	}

	storesQ := `
		select 
			s.id, 
			s.code, 
			s.name, 
			s.company_id
		from stores s
		join companies c on c.id = s.company_id
		order by 
			upper(s.code) asc`
	stores := []struct {
		ID        int    `db:"id" json:"id"`
		Name      string `db:"name" json:"name"`
		Code      string `db:"code" json:"code"`
		CompanyID int    `db:"company_id" json:"company_id"`
	}{}
	err = db.Get().SelectContext(ctx, &stores, storesQ)
	if err != nil {
		err = errors.Wrap(err, "error getting store list for fee")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting fee form supporting data", nil)
	}

	paymentTypesQ := `
		select 
			id, 
			name 
		from payment_types 
		order by 
			name asc`
	paymentTypes := []struct {
		ID   int    `db:"id" json:"id"`
		Name string `db:"name" json:"name"`
	}{}
	err = db.Get().SelectContext(ctx, &paymentTypes, paymentTypesQ)
	if err != nil {
		err = errors.Wrap(err, "error getting payment types list for fee")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting fee form supporting data", nil)
	}

	companies := []struct {
		ID   int    `db:"id" json:"id"`
		Name string `db:"name" json:"name"`
		Code string `db:"code" json:"code"`
	}{}
	companiesQ := `
		select distinct on (upper(c.code), id) 
			c.id, 
			c.code, 
			c.name 
		from companies c
		order by 
			upper(c.code) asc, 
			id`
	err = db.Get().SelectContext(ctx, &companies, companiesQ)
	if err != nil {
		err = errors.Wrap(err, "error getting companies from database for fee")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("A error occurred loading the form supporting data.", nil)
	}

	transactionTypes := []struct {
		ID   int    `db:"id" json:"id"`
		Name string `db:"name" json:"name"`
	}{}
	transactionTypesQ := `
		select 
			id, 
			name 
		from transaction_types 
		order by 
			name asc`
	err = db.Get().SelectContext(ctx, &transactionTypes, transactionTypesQ)
	if err != nil {
		err = errors.Wrap(err, "error getting transaction types from database for fee")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("A error occurred loading the form supporting data.", nil)
	}

	feeCalculationMethods := []struct {
		ID   int    `db:"id" json:"id"`
		Name string `db:"name" json:"name"`
	}{}
	feeCalculationMethodsQ := `
		select 
			id, 
			name 
		from fee_calculation_methods 
		order by 
			name asc`
	err = db.Get().SelectContext(ctx, &feeCalculationMethods, feeCalculationMethodsQ)
	if err != nil {
		err = errors.Wrap(err, "error getting fee calculation methods from database for fee")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("A error occurred loading the form supporting data.", nil)
	}

	intacctFeeProducts := []struct {
		ID   int    `db:"id" json:"id"`
		Name string `db:"name" json:"name"`
	}{}
	intacctFeeProductsQ := `
		select
			id,
			intacct_id || ' - ' || name as name
		from intacct_fee_products
		order by 
			name asc`
	err = db.Get().SelectContext(ctx, &intacctFeeProducts, intacctFeeProductsQ)
	if err != nil {
		err = errors.Wrap(err, "error getting intacct fee products from database for fee")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("A error occurred loading the form supporting data.", nil)
	}

	return http.StatusOK, map[string]interface{}{
		"companies":               companies,
		"products":                products,
		"product_types":           productTypes,
		"product_variants":        productVariants,
		"payment_types":           paymentTypes,
		"states":                  db.States,
		"stores":                  stores,
		"transaction_types":       transactionTypes,
		"fee_calculation_methods": feeCalculationMethods,
		"intacct_fee_products":    intacctFeeProducts,
	}
}

// FeesIndexSupportingData returns the supporting data for the fee index page
func FeesIndexSupportingData(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()

	companies := []struct {
		ID   int    `db:"id" json:"id"`
		Code string `db:"code" json:"code"`
		Name string `db:"name" json:"name"`
	}{}
	companiesQ := `
		select 
			c.id, 
			c.code, 
			c.name
		from companies c
		order by c.code asc`
	err := db.Get().SelectContext(ctx, &companies, companiesQ)
	if err != nil {
		err = errors.Wrap(err, "database error getting companies for fee supporting data")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting supporting data", nil)
	}

	stores := []struct {
		ID        int    `db:"id" json:"id"`
		Code      string `db:"code" json:"code"`
		Name      string `db:"name" json:"name"`
		CompanyID int    `db:"company_id" json:"company_id"`
	}{}
	storesQ := `
		select
			s.id,
			s.code,
			s.name,
			s.company_id
		from stores s
		order by s.code asc`
	err = db.Get().SelectContext(ctx, &stores, storesQ)
	if err != nil {
		err = errors.Wrap(err, "database error getting stores for fee supporting data")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting supporting data", nil)
	}

	productTypes := []struct {
		ID   int    `db:"id" json:"id"`
		Name string `db:"name" json:"name"`
	}{}
	productTypesQ := `
		select
			pt.id,
			pt.name
		from product_types pt
		order by pt.name asc`
	err = db.Get().SelectContext(ctx, &productTypes, productTypesQ)
	if err != nil {
		err = errors.Wrap(err, "database error getting product types for fee supporting data")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting supporting data", nil)
	}

	products := []struct {
		ID            int    `db:"id" json:"id"`
		Name          string `db:"name" json:"name"`
		ProductTypeID int    `db:"product_type_id" json:"product_type_id"`
	}{}
	productsQ := `
		select
			p.id,
			p.name,
			p.product_type_id
		from products p
		order by p.name asc`
	err = db.Get().SelectContext(ctx, &products, productsQ)
	if err != nil {
		err = errors.Wrap(err, "database error getting products for fee supporting data")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting supporting data", nil)
	}

	productVariants := []struct {
		ID        int    `db:"id" json:"id"`
		Name      string `db:"name" json:"name"`
		ProductID int    `db:"product_id" json:"product_id"`
	}{}
	productVariantsQ := `
		select
			pv.id,
			pv.name,
			pv.product_id
		from product_variants pv
		order by pv.name asc`
	err = db.Get().SelectContext(ctx, &productVariants, productVariantsQ)
	if err != nil {
		err = errors.Wrap(err, "database error getting product variants for fee supporting data")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting supporting data", nil)
	}

	paymentTypes := []struct {
		ID   int    `db:"id" json:"id"`
		Name string `db:"name" json:"name"`
	}{}
	paymentTypesQ := `
		select
			pt.id,
			pt.name
		from payment_types pt
		order by pt.name asc`
	err = db.Get().SelectContext(ctx, &paymentTypes, paymentTypesQ)
	if err != nil {
		err = errors.Wrap(err, "database error getting payment types for fee supporting data")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting supporting data", nil)
	}

	storeStates := []string{}
	storeStateQ := `
		select
			distinct(state_code)
		from stores
		order by
			state_code`
	err = db.Get().SelectContext(ctx, &storeStates, storeStateQ)
	if err != nil {
		err = errors.Wrap(err, "db error getting states list from stores")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting supporting data", nil)
	}

	transactionTypes := []struct {
		ID   int    `db:"id" json:"id"`
		Name string `db:"name" json:"name"`
	}{}
	transactionTypesQ := `
		select
			tt.id,
			tt.name
		from transaction_types tt
		order by
			tt.name asc`
	err = db.Get().SelectContext(ctx, &transactionTypes, transactionTypesQ)
	if err != nil {
		err = errors.Wrap(err, "db error getting transaction types")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting supporting data", nil)
	}

	return http.StatusOK, map[string]interface{}{
		"companies":         companies,
		"stores":            stores,
		"product_types":     productTypes,
		"states":            db.States,
		"payment_types":     paymentTypes,
		"products":          products,
		"product_variants":  productVariants,
		"store_states":      storeStates,
		"transaction_types": transactionTypes,
	}
}

// FeeIndex returns a list of fees
func FeeIndex(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()

	fees := []struct {
		ID                         int             `db:"id" json:"id"`
		Name                       string          `db:"name" json:"name"`
		Code                       string          `db:"code" json:"code"`
		TransactionType            string          `db:"transaction_type" json:"transaction_type"`
		StartedOn                  time.Time       `db:"started_on" json:"started_on"`
		EndedOn                    null.Time       `db:"ended_on" json:"ended_on"`
		CompanyNames               pq.StringArray  `db:"company_names" json:"company_names"`
		IsStoresInclusive          bool            `db:"is_stores_inclusive" json:"is_stores_inclusive"`
		StoreCodes                 pq.StringArray  `db:"store_codes" json:"store_codes"`
		States                     pq.StringArray  `db:"states" json:"states"`
		Amount                     decimal.Decimal `db:"amount" json:"amount"`
		ProductTypeNames           pq.StringArray  `db:"product_type_names" json:"product_type_names"`
		IsProductsInclusive        bool            `db:"is_products_inclusive" json:"is_products_inclusive"`
		ProductNames               pq.StringArray  `db:"product_names" json:"product_names"`
		IsProductVariantsInclusive bool            `db:"is_product_variants_inclusive" json:"is_product_variants_inclusive"`
		ProductVariantNames        pq.StringArray  `db:"product_variant_names" json:"product_variant_names"`
		PaymentTypeNames           pq.StringArray  `db:"payment_type_names" json:"payment_type_names"`
		FeeCalculationMethod       string          `db:"fee_calculation_method" json:"fee_calculation_method"`
		IsInvoiceable              bool            `db:"is_invoiceable" json:"is_invoiceable"`
		CreatedAt                  time.Time       `db:"created_at" json:"created_at"`
		CreatedBy                  string          `db:"created_by_user_name" json:"created_by_user_name"`
	}{}

	selectClause, fromClause, whereClause, orderByClause, args, err := getFeesSearchQueryAndArgs(req)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err.Error(), nil)
	}

	listQuery, countQuery := handlers.ListQueries(
		selectClause,
		fromClause,
		whereClause,
		orderByClause,
		20,
		handlers.GetPage(req),
	)
	stmt, err := db.Get().PrepareNamedContext(ctx, listQuery)
	if err != nil {
		err = errors.Wrap(err, "Database error preparing admin fees query")
		handlers.LogMessagef(ctx, "Query: %s", listQuery)
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting fees", nil)
	}
	defer func() { _ = stmt.Close() }()
	err = stmt.Unsafe().SelectContext(ctx, &fees, args)
	if err != nil {
		err = errors.Wrap(err, "Database error getting admin fees")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting fees", nil)
	}
	stmt2, err := db.Get().PrepareNamedContext(ctx, countQuery)
	if err != nil {
		err = errors.Wrap(err, "Database error preparing admin fees count query")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting fees", nil)
	}
	defer func() { _ = stmt2.Close() }()
	count := 0
	err = stmt2.GetContext(ctx, &count, args)
	if err != nil {
		err = errors.Wrap(err, "Database error getting admin fees count")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting fees", nil)
	}

	return http.StatusOK, map[string]interface{}{"fees": fees, "count": count}
}

func getFeesSearchQueryAndArgs(req *http.Request) (string, string, string, string, feeSearchArgs, error) {
	wheres := []string{"f.deleted_at is null"}
	args := feeSearchArgs{}
	whereClause := ""

	selectClause := `
		f.*,
		tt.name as transaction_type,
		fcm.name as fee_calculation_method,
		cu.last_name || ' ' || cu.first_name as created_by_user_name,
		array(
			select
				c.code
			from fee_companies fc
			join companies c on c.id = fc.company_id
			where
				fc.fee_id = f.id
		) as company_names,
		array(
			select
				s.code
			from fee_stores fs
			join stores s on s.id = fs.store_id
			where
				fs.fee_id = f.id
		) as store_codes,
		array(
			select
				pt.name
			from fee_product_types fpt
			join product_types pt on pt.id = fpt.product_type_id
			where
				fpt.fee_id = f.id
		) as product_type_names,
		array(
			select
				p.name
			from fee_products fp
			join products p on p.id = fp.product_id
			where
				fp.fee_id = f.id
		) as product_names,
		array(
			select
				pv.name
			from fee_product_variants fpv
			join product_variants pv on pv.id = fpv.product_variant_id
			where
				fpv.fee_id = f.id
		) as product_variant_names,
		array(
			select
				pt.name
			from fee_payment_types fpt
			join payment_types pt on pt.id = fpt.payment_type_id
			where
				fpt.fee_id = f.id
		) as payment_type_names`

	fromClause := `
		fees f
		join transaction_types tt on tt.id = f.transaction_type_id
		join fee_calculation_methods fcm on fcm.id = f.fee_calculation_method_id
		join current_users cu on cu.id = f.created_by_user_id`

	orderByClause := "order by f.name asc"

	var err error
	if v := req.FormValue("show_inactive"); v != "1" {
		q := `(f.ended_on is null or f.ended_on >= CAST((now() at time zone 'utc') as date))`
		wheres = append(wheres, q)
	}

	if v := req.FormValue("company_id"); v != "" {
		q := `
			(
				exists (
					select
						fc.fee_id
					from fee_companies fc
					where
						fc.fee_id = f.id
						and fc.company_id = :company_id
				)
				or not exists (
					select
						fc.fee_id
					from fee_companies fc
					where
						fc.fee_id = f.id
						and fc.company_id != :company_id
				)
			)`
		wheres = append(wheres, q)
		args.CompanyID, err = strconv.Atoi(v)
		if err != nil {
			return selectClause, fromClause, whereClause, orderByClause, args, errors.New("error company ID invalid")
		}
	}

	if v := req.FormValue("store_id"); v != "" {
		q := `
			(
				(
					f.is_stores_inclusive
					and exists (
						select
							fs.fee_id
						from fee_stores fs
						where
							fs.fee_id = f.id
							and fs.store_id = :store_id
					)
				) or (
					not f.is_stores_inclusive
					and not exists (
						select
							fs.fee_id
						from fee_stores fs
						where
							fs.fee_id = f.id
							and fs.store_id = :store_id
					)
				)
			)`

		if req.FormValue("show_excluded_stores") == "1" {
			q = `
				(
					f.is_stores_inclusive
					and exists (
						select
							fs.fee_id
						from fee_stores fs
						where
							fs.fee_id = f.id
							and fs.store_id = :store_id
					)
				) `
		}

		wheres = append(wheres, q)
		args.StoreID, err = strconv.Atoi(v)
		if err != nil {
			return selectClause, fromClause, whereClause, orderByClause, args, errors.New("error store ID invalid")
		}
	}

	if v := req.FormValue("state"); v != "" {
		q := `
			(
				coalesce(array_length(f.states, 1), 0) < 1
				or :state_code = ANY(f.states)
			)`
		wheres = append(wheres, q)
		args.StateCode = v
	}

	if v := req.FormValue("product_type_id"); v != "" {
		q := `
			(
				exists (
					select
						fpt.fee_id
					from fee_product_types fpt
					where
						fpt.fee_id = f.id
						and fpt.product_type_id = :product_type_id
				) or not exists (
					select
						fpt.fee_id
					from fee_product_types fpt
					where
						fpt.fee_id = f.id
						and fpt.product_type_id != :product_type_id
				)
			)`
		wheres = append(wheres, q)
		args.ProductTypeID, err = strconv.Atoi(v)
		if err != nil {
			return selectClause, fromClause, whereClause, orderByClause, args, errors.New("error product type ID invalid")
		}
	}

	if v := req.FormValue("product_id"); v != "" {
		q := `
			(
				(
					f.is_products_inclusive
					and exists (
						select
							fp.fee_id
						from fee_products fp
						where
							fp.fee_id = f.id
							and fp.product_id = :product_id
					)
				) or (
					not f.is_products_inclusive
					and not exists (
						select
							fp.fee_id
						from fee_products fp
						where
							fp.fee_id = f.id
							and fp.product_id = :product_id
					)
				)
			)`

		if req.FormValue("show_excluded_products") == "1" {
			q = `
				(
					f.is_products_inclusive
					and exists (
						select
							fp.fee_id
						from fee_products fp
						where
							fp.fee_id = f.id
							and fp.product_id = :product_id
					)
				) `
		}
		wheres = append(wheres, q)
		args.ProductID, err = strconv.Atoi(v)
		if err != nil {
			return selectClause, fromClause, whereClause, orderByClause, args, errors.New("error product ID invalid")
		}
	}

	if v := req.FormValue("product_variant_id"); v != "" {
		q := `
			(
				(
					f.is_product_variants_inclusive
					and exists (
						select
							fpv.fee_id
						from fee_product_variants fpv
						where
							fpv.fee_id = f.id
							and fpv.product_variant_id = :product_variant_id
					)
				) or (
					not f.is_product_variants_inclusive
					and not exists (
						select
							fpv.fee_id
						from fee_product_variants fpv
						where
							fpv.fee_id = f.id
							and fpv.product_variant_id = :product_variant_id
					)
				)
			)`

		if req.FormValue("show_excluded_product_variants") == "1" {
			q = `
				(
					f.is_product_variants_inclusive
					and exists (
						select
							fpv.fee_id
						from fee_product_variants fpv
						where
							fpv.fee_id = f.id
							and fpv.product_variant_id = :product_variant_id
					)
				)`
		}
		wheres = append(wheres, q)
		args.ProductVariantID, err = strconv.Atoi(v)
		if err != nil {
			return selectClause, fromClause, whereClause, orderByClause, args, errors.New("error product variant ID invalid")
		}
	}

	if v := req.FormValue("payment_type_id"); v != "" {
		q := `
			(
				exists (
					select
						fpt.fee_id
					from fee_payment_types fpt
					where
						fpt.fee_id = f.id
						and fpt.payment_type_id = :payment_type_id
				)
			)`
		wheres = append(wheres, q)
		args.PaymentTypeID, err = strconv.Atoi(v)
		if err != nil {
			return selectClause, fromClause, whereClause, orderByClause, args, errors.New("error payment type ID invalid")
		}
	}

	if v := req.FormValue("transaction_type_id"); v != "" {
		q := `
			(
				f.transaction_type_id = :transaction_type_id
			)`
		wheres = append(wheres, q)
		args.TransactionTypeID, err = strconv.Atoi(v)
		if err != nil {
			return selectClause, fromClause, whereClause, orderByClause, args, errors.New("error transaction type ID invalid")
		}
	}

	if len(wheres) > 0 {
		whereClause = "where " + strings.Join(wheres, " and ")
	}

	return selectClause, fromClause, whereClause, orderByClause, args, nil
}

// FeeShow returns the fee with the specified ID
func FeeShow(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	fee := struct {
		ID                         int             `db:"id" json:"id"`
		Name                       string          `db:"name" json:"name"`
		Code                       string          `db:"code" json:"code"`
		CreatedAt                  time.Time       `db:"created_at" json:"created_at"`
		CreatedByUserID            int             `db:"created_by_user_id" json:"created_by_user_id"`
		CreatedByUserEmail         string          `db:"created_by_user_email" json:"created_by_user_email"`
		UpdatedAt                  time.Time       `db:"updated_at" json:"updated_at"`
		UpdatedByUserID            int             `db:"updated_by_user_id" json:"updated_by_user_id"`
		DeletedAt                  null.Time       `db:"deleted_at" json:"deleted_at"`
		DeletedByUserID            null.Int        `db:"deleted_by_user_id" json:"deleted_by_user_id"`
		StartedOn                  time.Time       `db:"started_on" json:"started_on"`
		EndedOn                    null.Time       `db:"ended_on" json:"ended_on"`
		TransactionTypeID          int             `db:"transaction_type_id" json:"transaction_type_id"`
		FeeCalculationMethodID     int             `db:"fee_calculation_method_id" json:"fee_calculation_method_id"`
		Amount                     decimal.Decimal `db:"amount" json:"amount"`
		IsInvoiceable              bool            `db:"is_invoiceable" json:"is_invoiceable"`
		IsStoresInclusive          bool            `db:"is_stores_inclusive" json:"is_stores_inclusive"`
		IsProductsInclusive        bool            `db:"is_products_inclusive" json:"is_products_inclusive"`
		IsProductVariantsInclusive bool            `db:"is_product_variants_inclusive" json:"is_product_variants_inclusive"`
		States                     pq.StringArray  `json:"states" db:"states"`
		TransactionType            struct {
			ID   int    `db:"id" json:"id"`
			Name string `db:"name" json:"name"`
		} `json:"transaction_type" db:"-"`
		FeeCalculationMethod struct {
			ID   int    `db:"id" json:"id"`
			Name string `db:"name" json:"name"`
		} `json:"fee_calculation_method" db:"-"`
		PaymentTypes []struct {
			ID   int    `db:"id" json:"id"`
			Name string `db:"name" json:"name"`
		} `json:"payment_types" db:"-"`
		Companies []struct {
			ID   int    `json:"id" db:"id"`
			Code string `json:"code" db:"code"`
		} `json:"companies" db:"-"`
		ProductTypes []struct {
			ID   int    `json:"id" db:"id"`
			Name string `json:"name" db:"name"`
		} `json:"product_types" db:"-"`
		Stores []struct {
			ID   int    `json:"id" db:"id"`
			Code string `json:"code" db:"code"`
		} `json:"stores" db:"-"`
		Products []struct {
			ID   int    `json:"id" db:"id"`
			Name string `json:"name" db:"name"`
		} `json:"products" db:"-"`
		ProductVariants []struct {
			ID   int    `json:"id" db:"id"`
			Name string `json:"name" db:"name"`
		} `json:"product_variants" db:"-"`
	}{
		States: pq.StringArray{},
		TransactionType: struct {
			ID   int    `db:"id" json:"id"`
			Name string `db:"name" json:"name"`
		}{},
		FeeCalculationMethod: struct {
			ID   int    `db:"id" json:"id"`
			Name string `db:"name" json:"name"`
		}{},
		PaymentTypes: []struct {
			ID   int    `db:"id" json:"id"`
			Name string `db:"name" json:"name"`
		}{},
		Companies: []struct {
			ID   int    `json:"id" db:"id"`
			Code string `json:"code" db:"code"`
		}{},
		ProductTypes: []struct {
			ID   int    `json:"id" db:"id"`
			Name string `json:"name" db:"name"`
		}{},
		Stores: []struct {
			ID   int    `json:"id" db:"id"`
			Code string `json:"code" db:"code"`
		}{},
		Products: []struct {
			ID   int    `json:"id" db:"id"`
			Name string `json:"name" db:"name"`
		}{},
		ProductVariants: []struct {
			ID   int    `json:"id" db:"id"`
			Name string `json:"name" db:"name"`
		}{},
	}

	ctx := req.Context()

	query := `
		select
			f.*,
			u.email created_by_user_email
		from fees f
		join current_users u on f.created_by_user_id = u.id
		where
			f.id = $1
			and f.deleted_at is null`
	err := db.Get().Unsafe().GetContext(ctx, &fee, query, chi.URLParam(req, "id"))
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage("Not found", nil)
		}
		handlers.ReportError(req, errors.WithMessage(err, "error loading fee"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error loading fee", nil)
	}

	query = `
		select
			tt.id,
			tt.name
		from transaction_types tt
		where
			tt.id = $1`
	err = db.Get().GetContext(ctx, &fee.TransactionType, query, fee.TransactionTypeID)
	if err != nil {
		if err == sql.ErrNoRows {
			err = errors.Wrap(err, "transaction type not found")
		} else {
			err = errors.Wrap(err, "error loading transaction type for fee")
		}
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error loading fee", nil)
	}

	query = `
		select
			fcm.id,
			fcm.name
		from fee_calculation_methods fcm
		where
			fcm.id = $1`
	err = db.Get().GetContext(ctx, &fee.FeeCalculationMethod, query, fee.FeeCalculationMethodID)
	if err != nil {
		if err == sql.ErrNoRows {
			err = errors.Wrap(err, "calculation method not found")
		} else {
			err = errors.Wrap(err, "error loading calculation method for fee")
		}
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error loading fee", nil)
	}

	query = `
		select
			pt.id,
			pt.name
		from fee_payment_types fpt
		join payment_types pt on pt.id = fpt.payment_type_id
		where
			fpt.fee_id = $1`
	err = db.Get().SelectContext(ctx, &fee.PaymentTypes, query, fee.ID)
	if err != nil {
		if err == sql.ErrNoRows {
			err = errors.Wrap(err, "payment type not found")
		} else {
			err = errors.Wrap(err, "error loading payment types for fee")
		}
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error loading fee", nil)
	}

	stores := []struct {
		ID   int    `json:"id" db:"id"`
		Code string `json:"code" db:"code"`
	}{}
	query = `
		select
			s.id,
			s.code
		from fee_stores fs
		join stores s on s.id = fs.store_id
		where
			fs.fee_id = $1`
	err = db.Get().SelectContext(ctx, &stores, query, fee.ID)
	if err != nil {
		err = errors.Wrap(err, "error loading stores for fee")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error loading fee", nil)
	}
	fee.Stores = stores

	companies := []struct {
		ID   int    `json:"id" db:"id"`
		Code string `json:"code" db:"code"`
	}{}
	query = `
		select
			c.id,
			c.code
		from fee_companies fc
		join companies c on c.id = fc.company_id
		where
			fc.fee_id = $1`
	err = db.Get().SelectContext(ctx, &companies, query, fee.ID)
	if err != nil {
		err = errors.Wrap(err, "error loading companies for fee")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error loading fee", nil)
	}
	fee.Companies = companies

	query = `
		select
			pt.id,
			pt.name
		from fee_product_types fpt
		join product_types pt on pt.id = fpt.product_type_id
		where
			fpt.fee_id = $1`
	err = db.Get().SelectContext(ctx, &fee.ProductTypes, query, fee.ID)
	if err != nil {
		err = errors.Wrap(err, "error loading product types for fee")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error loading fee", nil)
	}

	products := []struct {
		ID   int    `json:"id" db:"id"`
		Name string `json:"name" db:"name"`
	}{}
	query = `
		select
			p.id,
			p.name
		from fee_products fp
		join products p on p.id = fp.product_id
		where
			fp.fee_id = $1`
	err = db.Get().SelectContext(ctx, &products, query, fee.ID)
	if err != nil {
		err = errors.Wrap(err, "error loading products for fee")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error loading fee", nil)
	}
	fee.Products = products

	productVariants := []struct {
		ID   int    `json:"id" db:"id"`
		Name string `json:"name" db:"name"`
	}{}
	query = `
		select
			pv.id,
			pv.name
		from fee_product_variants fpv
		join product_variants pv on pv.id = fpv.product_variant_id
		where
			fpv.fee_id = $1`
	err = db.Get().SelectContext(ctx, &productVariants, query, fee.ID)
	if err != nil {
		err = errors.Wrap(err, "error loading product variants for fee")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error loading fee", nil)
	}
	fee.ProductVariants = productVariants

	return http.StatusOK, map[string]interface{}{"fee": fee}
}

// FeeCreate creates a fee
func FeeCreate(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	return feeCreateOrUpdate(req, 0, user.ID)
}

// FeeUpdate updates a fee
func FeeUpdate(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	id := 0
	ctx := req.Context()
	q := `
		select
			id
		from fees
		where
			id = $1
			and deleted_at is null`
	err := db.Get().GetContext(ctx, &id, q, chi.URLParam(req, "id"))
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage("not found", nil)
		}
		err = errors.Wrap(err, "error looking up fee")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error loading fee", nil)
	}

	return feeCreateOrUpdate(req, id, user.ID)
}

func feeCreateOrUpdate(req *http.Request, updateID int, userID int) (int, map[string]interface{}) {
	ctx := req.Context()

	fee, err := feePayloadFromReq(req, userID)
	if err != nil {
		handlers.ReportError(req, errors.WithMessage(err, "error decoding JSON"))
		return http.StatusBadRequest, handlers.ErrorMessage("Bad request", nil)
	}

	fee.clean()

	validationErrs, err := fee.validate(ctx)
	if err != nil {
		err = errors.WithMessage(err, "error validating fee")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error saving fee", nil)
	}
	if len(validationErrs) > 0 {
		return http.StatusBadRequest, handlers.ErrorMessage("Invalid data", map[string]interface{}{"validation_errors": validationErrs})
	}

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		err = errors.Wrap(err, "error starting transaction")
		return http.StatusInternalServerError, handlers.ErrorMessage("Error creating fee", nil)
	}

	feeID, err := saveFee(ctx, tx, fee, updateID, userID)
	if err != nil {
		_ = tx.Rollback()
		err = errors.WithMessage(err, "database error saving fee")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error creating fee", nil)
	}

	err = tx.Commit()
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "error committing transaction")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error saving fee", nil)
	}

	return http.StatusOK, map[string]interface{}{"id": feeID}
}

func feePayloadFromReq(req *http.Request, userID int) (feePayload, error) {
	fee := feePayload{
		CreatedByUserID: userID,
	}

	decoder := json.NewDecoder(req.Body)
	err := decoder.Decode(&fee)
	if err != nil {
		err = errors.Wrap(err, "error decoding JSON")
		return fee, err
	}

	if len(fee.Stores) > 0 {
		fee.IsStoresInclusive = true
	}

	if len(fee.Products) > 0 {
		fee.IsProductsInclusive = true
	}

	if len(fee.ProductVariants) > 0 {
		fee.IsProductVariantsInclusive = true
	}

	return fee, nil
}

func saveFee(ctx context.Context, tx *sqlx.Tx, fee feePayload, updateID int, userID int) (int, error) {
	var feeID int
	const query = `
		insert into fees (
			"name",
			"code",
			"created_at",
			"created_by_user_id",
			"started_on",
			"ended_on",
			"transaction_type_id",
			"fee_calculation_method_id",
			"amount",
			"states",
			"is_invoiceable",
			"is_stores_inclusive",
			"is_products_inclusive",
			"is_product_variants_inclusive"
		) values (
		 	:name,
			:code,
			now() at time zone 'utc',
			:created_by_user_id,
			:started_on,
			:ended_on,
			:transaction_type_id,
			:fee_calculation_method_id,
			:amount,
			:states,
			:is_invoiceable,
			:is_stores_inclusive,
			:is_products_inclusive,
			:is_product_variants_inclusive
		)
		returning id;`

	if updateID > 0 {
		updateQuery := `
			update fees
			set
				deleted_at = now() at time zone 'utc',
				deleted_by_user_id = $1
			where
				id = $2`
		_, err := tx.ExecContext(ctx, updateQuery, userID, updateID)
		if err != nil {
			err = errors.Wrapf(err, "error soft deleting fee for updateID %d", updateID)
			return feeID, err
		}
		fee.PreviousFeeID.Valid = true
		fee.PreviousFeeID.Int64 = int64(updateID)
	}

	stmt, err := tx.PrepareNamedContext(ctx, query)
	if err != nil {
		err = errors.Wrap(err, "error creating prepared named statement")
		return feeID, err
	}
	defer func() { _ = stmt.Close() }()

	err = stmt.GetContext(ctx, &feeID, fee)
	if err != nil {
		err = errors.Wrap(err, "error inserting fee")
		return feeID, err
	}

	// Insert fee_companies
	for _, companyID := range fee.Companies {
		q := `
			insert into fee_companies (
				fee_id,
				company_id
			) values (
				$1,
				$2
			)`
		_, err := tx.ExecContext(ctx, q, feeID, companyID)
		if err != nil {
			err = errors.Wrap(err, "error inserting fee - adding companies")
			return feeID, err
		}
	}

	// Insert fee_stores
	for _, storeID := range fee.Stores {
		q := `
			insert into fee_stores (
				fee_id,
				store_id
			) values (
				$1,
				$2
			)`
		_, err := tx.ExecContext(ctx, q, feeID, storeID)
		if err != nil {
			err = errors.Wrap(err, "error inserting fee - adding stores")
			return feeID, err
		}
	}

	// Insert fee_product_types
	for _, productTypeID := range fee.ProductTypes {
		q := `
			insert into fee_product_types (
				fee_id,
				product_type_id
			) values (
				$1,
				$2
			)`
		_, err := tx.ExecContext(ctx, q, feeID, productTypeID)
		if err != nil {
			err = errors.Wrap(err, "error inserting fee - adding product types")
			return feeID, err
		}
	}

	// Insert fee_products
	for _, productID := range fee.Products {
		q := `
			insert into fee_products (
				fee_id,
				product_id
			) values (
				$1,
				$2
			)`
		_, err := tx.ExecContext(ctx, q, feeID, productID)
		if err != nil {
			err = errors.Wrap(err, "error inserting fee - adding products")
			return feeID, err
		}
	}

	// Insert fee_product_variants
	for _, productVariantID := range fee.ProductVariants {
		q := `
			insert into fee_product_variants (
				fee_id,
				product_variant_id
			) values (
				$1,
				$2
			)`
		_, err := tx.ExecContext(ctx, q, feeID, productVariantID)
		if err != nil {
			err = errors.Wrap(err, "error inserting fee - adding product variants")
			return feeID, err
		}
	}

	// Insert fee_payment_types
	for _, paymentTypeID := range fee.PaymentTypes {
		q := `
			insert into fee_payment_types (
				fee_id,
				payment_type_id
			) values (
				$1,
				$2
			)`
		_, err := tx.ExecContext(ctx, q, feeID, paymentTypeID)
		if err != nil {
			err = errors.Wrap(err, "error inserting fee - adding payment types")
			return feeID, err
		}
	}

	return feeID, nil
}

// FeesCsv returns a CSV of fees
func FeesCsv(w http.ResponseWriter, req *http.Request, user db.CurrentUser) {
	ctx := req.Context()
	r := handlers.Renderer()

	fees := []struct {
		ID                         int             `db:"id" json:"id"`
		Name                       string          `db:"name" json:"name"`
		Code                       string          `db:"code" json:"code"`
		TransactionType            string          `db:"transaction_type" json:"transaction_type"`
		StartedOn                  time.Time       `db:"started_on" json:"started_on"`
		EndedOn                    null.Time       `db:"ended_on" json:"ended_on"`
		CompanyNames               pq.StringArray  `db:"company_names" json:"company_names"`
		IsStoresInclusive          bool            `db:"is_stores_inclusive" json:"is_stores_inclusive"`
		StoreCodes                 pq.StringArray  `db:"store_codes" json:"store_codes"`
		States                     pq.StringArray  `db:"states" json:"states"`
		Amount                     decimal.Decimal `db:"amount" json:"amount"`
		ProductTypeNames           pq.StringArray  `db:"product_type_names" json:"product_type_names"`
		IsProductsInclusive        bool            `db:"is_products_inclusive" json:"is_products_inclusive"`
		ProductNames               pq.StringArray  `db:"product_names" json:"product_names"`
		IsProductVariantsInclusive bool            `db:"is_product_variants_inclusive" json:"is_product_variants_inclusive"`
		ProductVariantNames        pq.StringArray  `db:"product_variant_names" json:"product_variant_names"`
		PaymentTypeNames           pq.StringArray  `db:"payment_type_names" json:"payment_type_names"`
		FeeCalculationMethod       string          `db:"fee_calculation_method" json:"fee_calculation_method"`
		IsInvoiceable              bool            `db:"is_invoiceable" json:"is_invoiceable"`
		CreatedAt                  time.Time       `db:"created_at" json:"created_at"`
		CreatedBy                  string          `db:"created_by_user_name" json:"created_by_user_name"`
	}{}

	selectClause, fromClause, whereClause, orderByClause, args, err := getFeesSearchQueryAndArgs(req)
	if err != nil {
		handlers.ReportError(req, err)
		_ = r.Text(w, http.StatusBadRequest, err.Error())
		return
	}

	listQuery := fmt.Sprintf("select %s from %s %s %s", selectClause, fromClause, whereClause, orderByClause)

	util.LogMessagef(ctx, "listQuery: %s", listQuery)
	util.LogMessagef(ctx, "args: %+v", args)

	stmt, err := db.Get().PrepareNamedContext(ctx, listQuery)
	if err != nil {
		err = errors.Wrap(err, "database error preparing admin fees query")
		handlers.ReportError(req, err)
		_ = r.Text(w, http.StatusInternalServerError, "Error getting fees")
		return
	}
	defer func() { _ = stmt.Close() }()
	if args.containsValues() {
		err = stmt.Unsafe().SelectContext(ctx, &fees, args)
	} else {
		err = stmt.Unsafe().SelectContext(ctx, &fees, args)
	}
	if err != nil {
		err = errors.Wrap(err, "database error getting admin fees")
		handlers.ReportError(req, err)
		_ = r.Text(w, http.StatusInternalServerError, "Error getting fees")
		return
	}

	w.Header().Set("Content-Type", "text/csv")
	w.Header().Set("Content-Disposition", "attachment; filename=fees.csv")
	csvw := csv.NewWriter(w)

	err = csvw.Write([]string{
		"Fee Name",
		"Fee Code",
		"Timeframe",
		"Calculation Method",
		"Transaction Type",
		"Amount",
		"Invoiceable",
		"Companies",
		"States",
		"Stores",
		"Not Stores",
		"Product Types",
		"Products",
		"Not Products",
		"Product Variants",
		"Not Product Variants",
		"Payment Types",
		"Created At",
		"Created By",
	})
	if err != nil {
		err = errors.WithMessage(err, "error writing csv header")
		handlers.ReportError(req, err)
		return
	}

	for _, f := range fees {
		timeframe := f.StartedOn.String()
		if !f.EndedOn.Valid {
			timeframe = timeframe + " - forever"
		} else {
			timeframe = timeframe + " - " + f.EndedOn.Time.String()
		}
		stores := f.StoreCodes
		notStores := []string{}
		if !f.IsStoresInclusive {
			stores = []string{}
			notStores = f.StoreCodes
		}
		productNames := f.ProductNames
		notProductNames := []string{}
		if !f.IsProductsInclusive {
			productNames = []string{}
			notProductNames = f.ProductNames
		}
		productVariantNames := f.ProductVariantNames
		notProductVariantNames := []string{}
		if !f.IsProductVariantsInclusive {
			productVariantNames = []string{}
			notProductVariantNames = f.ProductVariantNames
		}
		record := []string{
			f.Name,
			f.Code,
			timeframe,
			f.FeeCalculationMethod,
			f.TransactionType,
			f.Amount.String(),
			strconv.FormatBool(f.IsInvoiceable),
			strings.Join(f.CompanyNames, ", "),
			strings.Join(f.States, ", "),
			strings.Join(stores, ", "),
			strings.Join(notStores, ", "),
			strings.Join(f.ProductTypeNames, ", "),
			strings.Join(productNames, ", "),
			strings.Join(notProductNames, ", "),
			strings.Join(productVariantNames, ", "),
			strings.Join(notProductVariantNames, ", "),
			strings.Join(f.PaymentTypeNames, ", "),
			f.CreatedAt.String(),
			f.CreatedBy,
		}

		err = csvw.Write(record)
		if err != nil {
			err = errors.Wrap(err, "error writing csv")
			handlers.ReportError(req, err)
			return
		}
	}

	csvw.Flush()
}

func getFeesForInvoicing(ctx context.Context, invoiceDate time.Time) ([]*db.Fee, error) {
	var fees []*db.Fee
	var feeIDs []int
	localEndDate, err := types.InMountainTime(invoiceDate)
	if err != nil {
		return fees, errors.Wrapf(err, "convert to mountain time %q", invoiceDate)
	}

	handlers.LogMessagef(ctx, "Getting fees for invoicing date %s", localEndDate.Format("2006-01-02"))
	query := `
		select
			f.id
		from fees f
		where
			deleted_at is null
			and started_on <= $1
			and (ended_on is null or ended_on > $1)
			and is_invoiceable = true`
	err = db.Get().SelectContext(ctx, &feeIDs, query, localEndDate)
	if err != nil {
		err = errors.Wrap(err, "error getting fees for invoicing")
		return fees, err
	}

	handlers.LogMessagef(ctx, "Found fees: %v", len(feeIDs))
	for _, id := range feeIDs {
		fee, err := db.GetFeeByID(ctx, id)
		if err != nil {
			if err != sql.ErrNoRows {
				return nil, errors.Wrapf(err, "error getting fee by id %d", id)
			}
			continue
		}
		fees = append(fees, fee)
	}

	handlers.LogMessagef(ctx, "Found %d fees for invoicing", len(fees))
	return fees, nil
}

func processFeeForValidator(
	ctx context.Context,
	invoiceBatchID int,
	invoiceDate time.Time,
	fee *db.Fee,
) ([]intacct.JournalEntry, error) {
	txn := newrelic.FromContext(ctx)
	defer newrelic.StartSegment(txn, "Process Fee for Invoicing").End()

	handlers.LogMessagef(ctx, "Processing fee %s for invoicing", fee.Name)

	deals, err := getFeeDealData(ctx, fee, invoiceBatchID)
	if err != nil {
		err = errors.WithMessagef(err, "error getting deal data for fee %s", fee.Name)
		return nil, err
	}

	handlers.LogMessagef(ctx, "Found %d deals for fee %s", len(deals), fee.Name)

	rules, err := getAllAccountFeeRules(ctx, fee)
	if err != nil {
		err = errors.WithMessagef(err, "error getting account fee rules for fee %s", fee.Name)
		return nil, err
	}

	// If there's no rules for the fee, then we can skip processing the fee
	if len(rules) == 0 {
		handlers.LogMessagef(ctx, "No accounting fee rules found, skipping processing fee %s (%d)", fee.Name, fee.ID)
		return nil, nil
	}

	handlers.LogMessagef(ctx, "Found %d deals for fee %s", len(deals), fee.Name)

	storeJournalEntries := make(map[string][]intacct.JournalEntry)
	for _, d := range deals {
		// If fees aren't to be applied for the deal, then move onto the next deal
		if !d.ApplyFees {
			continue
		}

		handlers.LogMessagef(ctx, "Getting applicable rules for deal %s", d.DMSNumber)

		applicableRules := getApplicableFeeRules(rules, d)
		// If there's no applicable rules for the deal, then move onto the next deal
		if len(applicableRules) == 0 {
			handlers.LogMessagef(ctx, "No applicable accounting fee rules found for deal %s", d.DMSNumber)
			continue
		}

		referenceNumber := fmt.Sprintf("%s %s", d.StoreCode, fee.Code)

		// Create the Journal Entry for the deal
		for _, ar := range applicableRules {
			entry, err := processFee(d, fee, ar, referenceNumber, invoiceDate)
			if err != nil {
				err = errors.WithMessagef(err, "failted to process fee %s for deal %s", fee.Code, d.DMSNumber)
				return nil, err
			}

			if entry != nil {
				if res, ok := storeJournalEntries[d.StoreCode]; ok {
					res = aggregator(res, []intacct.JournalEntry{*entry})
					storeJournalEntries[d.StoreCode] = res
				} else {
					storeJournalEntries[d.StoreCode] = []intacct.JournalEntry{*entry}
				}
			}
		}
	}

	var output []intacct.JournalEntry
	for _, v := range storeJournalEntries {
		lineCounter := 1
		var arOut []intacct.JournalEntry
		arOut = append(arOut, v...)
		for _, pv := range arOut {
			pv.LineNumber = lineCounter
			output = append(output, pv)
			lineCounter++
		}
	}

	handlers.LogMessagef(ctx, "Completed processing fee %s for validator", fee.Name)

	return output, nil
}

func processFeeForInvoicing(
	ctx context.Context,
	tx *sqlx.Tx,
	invoiceBatchID int,
	invoiceDate time.Time,
	fee *db.Fee,
) ([]intacct.JournalEntry, map[string]feeInvoiceDetail, error) {
	txn := newrelic.FromContext(ctx)
	defer newrelic.StartSegment(txn, "Process Fee for Invoicing").End()

	handlers.LogMessagef(ctx, "Processing fee %s for invoicing", fee.Name)

	invoicing := make(map[string]feeInvoiceDetail)

	txn.AddAttribute("accounting.feeName", fee.Name)
	txn.AddAttribute("accounting.feeId", fee.ID)
	txn.AddAttribute("accounting.invoiceBatchID", invoiceBatchID)

	deals, err := getFeeDealData(ctx, fee, invoiceBatchID)
	if err != nil {
		err = errors.WithMessagef(err, "error getting deal data for fee %s", fee.Name)
		return nil, nil, err
	}

	handlers.LogMessagef(ctx, "Found %d deals for fee %s", len(deals), fee.Name)

	rules, err := getAllAccountFeeRules(ctx, fee)
	if err != nil {
		err = errors.WithMessagef(err, "error getting account fee rules for fee %s", fee.Name)
		return nil, nil, err
	}

	// If there's no rules for the fee, then we can skip processing the fee
	if len(rules) == 0 {
		handlers.LogMessagef(ctx, "No accounting fee rules found, skipping processing fee %s (%d)", fee.Name, fee.ID)
		return nil, invoicing, nil
	}

	handlers.LogMessagef(ctx, "Found %d deals for fee %s", len(deals), fee.Name)

	txn.AddAttribute("accounting.dealsCount", len(deals))

	var invoiceID int
	storeJournalEntries := make(map[string][]intacct.JournalEntry)
	sgmt := newrelic.StartSegment(txn, "Process Fee Deals")
	for _, d := range deals {
		// If fees aren't to be applied for the deal, then move onto the next deal
		if !d.ApplyFees {
			continue
		}

		handlers.LogMessagef(ctx, "Getting applicable rules for deal %s", d.DMSNumber)

		applicableRules := getApplicableFeeRules(rules, d)
		// If there's no applicable rules for the deal, then move onto the next deal
		if len(applicableRules) == 0 {
			handlers.LogMessagef(ctx, "No applicable accounting fee rules found for deal %s", d.DMSNumber)
			continue
		}
		// Changed to have the key be just the store ID. This will ensure that each store
		// has only one fee invoice for the fee that is being processed.
		invoicingStoreKey := fmt.Sprintf("%d", d.StoreID)

		var referenceNumber string

		if ref, ok := invoicing[invoicingStoreKey]; !ok {
			insertQuery := `
				insert into fee_invoices (
					invoice_number, 
					created_at, 
					invoice_date, 
					fee_id, 
					fee_name, 
					store_id, 
					store_code, 
					invoice_batch_id
				) values (
				 	:fee_code || currval(pg_get_serial_sequence('fee_invoices', 'id')), 
					now() at time zone 'utc', 
					:invoice_date, 
					:fee_id, 
					:fee_name, 
					:store_id, 
					:store_code, 
					:invoice_batch_id
				) 
				returning id;`
			in := feeInvoiceArgs{
				InvoiceBatchID: invoiceBatchID,
				FeeID:          fee.ID,
				FeeName:        fee.Name,
				StoreID:        d.StoreID,
				StoreCode:      d.StoreCode,
				InvoiceDate:    invoiceDate,
				FeeCode:        fee.Code,
			}

			stmt, err := tx.PrepareNamedContext(ctx, insertQuery)
			if err != nil {
				_ = tx.Rollback()
				return nil, nil, errors.Wrap(err, "error preparing insert fee invoice in fee invoices.")
			}
			defer func() { _ = stmt.Close() }()

			err = stmt.Get(&invoiceID, in)
			if err != nil {
				_ = tx.Rollback()
				return nil, nil, errors.Wrap(err, "error inserting fee invoice in fee invoices.")
			}

			invoicingDetail := feeInvoiceDetail{
				ReferenceNumber: fmt.Sprintf("%s%d", fee.Code, invoiceID),
				InvoiceID:       invoiceID,
			}
			referenceNumber = invoicingDetail.ReferenceNumber
			invoicing[invoicingStoreKey] = invoicingDetail
		} else {
			referenceNumber = ref.ReferenceNumber
		}

		if referenceNumber != "" {
			invoiceID = invoicing[invoicingStoreKey].InvoiceID
			invoiceItemReferenceNumber := d.DMSNumber
			if d.SaleType == SaleTypeServiceRO {
				invoiceItemReferenceNumber = "R" + d.DMSCustomerNumber
			}
			var feeInvoiceItemID int
			query := `
				insert into fee_invoice_items (
					fee_invoice_id,
					dms_number,
					customer_name,
					sale_type,
					is_dms_deal,
					contract_date,
					transaction_type,
					amount,
					reference_number,
					dms_customer_number,
					vin_record_id,
					vehicle_stock_number
				) values (
				 	:fee_invoice_id,
					:dms_number,
					:customer_name,
					:sale_type,
					:is_dms_deal,
					:contract_date,
					:transaction_type,
					:amount,
					:reference_number,
					:dms_customer_number,
					:vin_record_id,
					:vehicle_stock_number
				)
				returning id;`

			feeItem := db.FeeInvoiceItem{
				FeeInvoiceID:       invoiceID,
				DMSNumber:          d.DMSNumber,
				CustomerName:       d.CustomerName,
				SaleType:           d.SaleType,
				IsDMSDeal:          d.IsDMSDeal,
				ContractDate:       d.ContractDate,
				TransactionType:    d.TransactionType,
				ReferenceNumber:    invoiceItemReferenceNumber,
				DMSCustomerNumber:  d.DMSCustomerNumber,
				VINRecordID:        d.VINRecordID,
				VehicleStockNumber: d.VehicleStockNumber,
			}

			amt, err := getFeeAmount(fee)
			if err != nil {
				err = errors.WithMessagef(err, "error getting fee amount for fee %s", fee.Name)
				util.ReportError(ctx, err)
			}

			// If the amount for the fee is zero, then move onto the next fee item
			if amt.IsZero() {
				continue
			}

			feeItem.Amount = amt
			stmt, err := tx.PrepareNamedContext(ctx, query)
			if err != nil {
				_ = tx.Rollback()
				return nil, nil, errors.Wrap(err, "error preparing insert invoices to add new invoice")
			}
			defer func() { _ = stmt.Close() }()

			err = stmt.Get(&feeInvoiceItemID, feeItem)
			if err != nil {
				_ = tx.Rollback()
				return nil, nil, errors.Wrap(err, "error inserting invoice in invoices.")
			}
			inv := invoicing[invoicingStoreKey]
			inv.InvoiceItemID = append(inv.InvoiceItemID, feeInvoiceItemID)
			invoicing[invoicingStoreKey] = inv

			// Create the Journal Entry for the deal
			for _, ar := range applicableRules {
				entry, err := processFee(d, fee, ar, referenceNumber, invoiceDate)
				if err != nil {
					err = errors.WithMessagef(err, "failted to process fee %s for deal %s", fee.Code, d.DMSNumber)
					return nil, nil, err
				}

				if entry != nil {
					if res, ok := storeJournalEntries[invoicingStoreKey]; ok {
						res = aggregator(res, []intacct.JournalEntry{*entry})
						storeJournalEntries[invoicingStoreKey] = res
					} else {
						storeJournalEntries[invoicingStoreKey] = []intacct.JournalEntry{*entry}
					}
				}
			}
		}
	}
	sgmt.End()

	var output []intacct.JournalEntry
	for _, v := range storeJournalEntries {
		lineCounter := 1
		var arOut []intacct.JournalEntry
		arOut = append(arOut, v...)
		for _, pv := range arOut {
			pv.LineNumber = lineCounter
			output = append(output, pv)
			lineCounter++
		}
	}

	handlers.LogMessagef(ctx, "Completed processing fee %s for invoicing", fee.Name)

	return output, invoicing, nil
}

func getFeeDealData(ctx context.Context, fee *db.Fee, invoiceBatchID int) ([]*feeDealData, error) {
	var deals []*feeDealData
	var err error

	whereFilter := ""

	var args []interface{}
	args = append(args, invoiceBatchID)
	args = append(args, fee.TransactionType.Name)
	whereContainsInClause := false

	if len(fee.CompanyIDs) > 0 {
		companyIDs := make([]interface{}, len(fee.CompanyIDs))
		for idx, id := range fee.CompanyIDs {
			companyIDs[idx] = id
		}
		args = append(args, companyIDs)
		whereFilter = whereFilter + ` and st.company_id in (?)`
		whereContainsInClause = true
	}

	if len(fee.StoreIDs) > 0 {
		storeIDs := make([]interface{}, len(fee.StoreIDs))
		for idx, id := range fee.StoreIDs {
			storeIDs[idx] = id
		}
		args = append(args, storeIDs)
		if fee.IsStoresInclusive {
			whereFilter = whereFilter + ` and c.store_id in (?)`
		} else {
			whereFilter = whereFilter + ` and c.store_id not in (?)`
		}
		whereContainsInClause = true
	}

	if len(fee.ProductTypeIDs) > 0 {
		productTypeIDs := make([]interface{}, len(fee.ProductTypeIDs))
		for idx, id := range fee.ProductTypeIDs {
			productTypeIDs[idx] = id
		}
		args = append(args, productTypeIDs)
		whereFilter = whereFilter + ` and c.product_type_id in (?)`
		whereContainsInClause = true
	}

	if len(fee.ProductIDs) > 0 {
		productIDs := make([]interface{}, len(fee.ProductIDs))
		for idx, id := range fee.ProductIDs {
			productIDs[idx] = id
		}
		args = append(args, productIDs)
		if fee.IsProductsInclusive {
			whereFilter = whereFilter + ` and pv.product_id in (?)`
		} else {
			whereFilter = whereFilter + ` and pv.product_id not in (?)`
		}
		whereContainsInClause = true
	}

	if len(fee.ProductVariantIDs) > 0 {
		productVariantIDs := make([]interface{}, len(fee.ProductVariantIDs))
		for idx, id := range fee.ProductVariantIDs {
			productVariantIDs[idx] = id
		}
		args = append(args, productVariantIDs)
		if fee.IsProductVariantsInclusive {
			whereFilter = whereFilter + ` and c.product_variant_id in (?)`
		} else {
			whereFilter = whereFilter + ` and c.product_variant_id not in (?)`
		}
		whereContainsInClause = true
	}

	if len(fee.PaymentTypeIDs) > 0 {
		paymentTypeIDs := make([]interface{}, len(fee.PaymentTypeIDs))
		for idx, id := range fee.PaymentTypeIDs {
			paymentTypeIDs[idx] = id
		}
		args = append(args, paymentTypeIDs)
		whereFilter = whereFilter + ` and pt.id in (?)`
		whereContainsInClause = true
	}

	if len(fee.States) > 0 {
		states := make([]interface{}, len(fee.States))
		for idx, state := range fee.States {
			states[idx] = state
		}
		args = append(args, states)
		whereFilter = whereFilter + ` and st.state in (?)`
		whereContainsInClause = true
	}

	query := `
		select
			deals.dms_number,
			deals.customer_name,
			deals.sale_type,
			deals.is_dms_deal,
			deals.contract_date,
			deals.transaction_type,
			deals.transaction_type_id,
			deals.dms_customer_number,
			deals.vehicle_stock_number,
			deals.store_code,
			deals.store_id,
			deals.company_id,
			deals.payment_type,
			deals.payment_type_id,
			deals.intacct_customer_number,
			deals.invoice_date,
			deals.apply_fees,
			deals.vin_record_id
		from (
			-- This second select distinct is to ensure that if there were multiple sales for a customer with the same
			-- deal number and same contract date will be consolidated into a single deal data row.
			select distinct
					dd.dms_number,
				dd.customer_name,
				dd.sale_type,
				dd.is_dms_deal,
				dd.contract_date,
				dd.transaction_type,
				dd.transaction_type_id,
				dd.dms_customer_number,
				dd.vehicle_stock_number,
				dd.store_code,
				dd.store_id,
				dd.company_id,
				dd.payment_type,
				dd.payment_type_id,
				dd.intacct_customer_number,
				dd.invoice_date,
				dd.vin_record_id,
				dd.apply_fees
			from (
				select distinct
					ii.dms_number,
					ii.customer_name,
					ii.sale_type,
					ii.is_dms_deal,
					case 
						when c.sale_id is null then max(c.effective_date)
						else max(s.contract_date)
					end as contract_date,
					ii.transaction_type,
					tt.id as transaction_type_id,
					ii.dms_customer_number,
					coalesce(s.dms_stock_number, '') as vehicle_stock_number,
					i.store_code,
					i.store_id,
					st.company_id,
					c.payment_type,
					pmt.id as payment_type_id,
					st.intacct_customer_number,
					i.invoice_date,
					c.vin_record_id,
					case
						when tt.name = 'Cancellation' then bool_or(cc.apply_fee)
							else true
					end as apply_fees
				from invoice_items ii
				join invoices i on i.id = ii.invoice_id
				join transactions t on t.id = ii.transaction_id
				join transaction_types tt on tt.name = ii.transaction_type
				join contracts c on c.id = t.contract_id
				join product_variants pv on pv.id = c.product_variant_id
				join products p on p.id = pv.product_id
				join product_types pt on pt.id = p.product_type_id
				left join sales s on s.id = c.sale_id
				join stores st on st.id = c.store_id
				join payment_types pmt on pmt.name = c.payment_type
				left join contract_cancellations cc on cc.transaction_id = t.id
				where 
					i.invoice_batch_id = ? 
					and ii.transaction_type = ?
					` + whereFilter + `
				group by
					ii.dms_number,
					ii.customer_name,
					ii.sale_type,
					ii.is_dms_deal,
					ii.transaction_type,
					tt.id,
					ii.dms_customer_number,
					i.store_code,
					i.store_id,
					st.company_id,
					c.payment_type,
					c.sale_id,
					pmt.id,
					st.intacct_customer_number,
					i.invoice_date,
					c.vin_record_id,
					coalesce(s.dms_stock_number, '')
			) dd
		) as deals
		where
			apply_fees = true`

	if whereContainsInClause {
		query, args, err = sqlx.In(query, args...)
		if err != nil {
			return nil, errors.Wrap(err, "error preparing query")
		}
	}

	query = db.Get().Rebind(query)
	err = db.Get().SelectContext(ctx, &deals, query, args...)
	if err != nil {
		return nil, err
	}

	return deals, nil
}

func getFeeAmount(fee *db.Fee) (decimal.Decimal, error) {
	amount := decimal.Zero

	switch fee.CalculationMethod.Name {
	case "Dollar":
		amount = fee.Amount
	default:
		return decimal.Zero, errors.Errorf("unknown fee calculation method: %s", fee.CalculationMethod.Name)
	}

	return amount, nil
}

func processFee(fd *feeDealData, f *db.Fee, r *accountingFeeRule, ref string, endDate time.Time) (*intacct.JournalEntry, error) {
	var err error

	entry := intacct.JournalEntry{}
	err = setDefaultFeeValues(&entry, f, r, fd, 1, ref, endDate)
	if err != nil {
		err = errors.WithMessagef(err, "failed to set default values for fee %s and deal %s", f.Code, fd.DMSNumber)
		return nil, err
	}

	applyFeeCalculation(r.Calculation, f, &entry)

	return &entry, nil
}

func setDefaultFeeValues(currentAccount *intacct.JournalEntry, f *db.Fee, r *accountingFeeRule, fd *feeDealData, lineNumber int, ref string, endDate time.Time) error {

	endDate, err := types.InMountainTime(endDate)
	if err != nil {
		return errors.WithMessage(err, "failed to convert endDate to Mountain timezone")
	}

	currentAccount.AccountNumber = r.IntacctAccountID
	currentAccount.StoreID = fd.StoreID
	currentAccount.Journal = "ARJ"
	currentAccount.Date = endDate.Format(db.LongDateFormat)
	currentAccount.Description = fmt.Sprint(f.Code, "Inv upload", endDate.Format(db.ShortDateFormat))
	currentAccount.ReferenceNumber = ref
	currentAccount.LineNumber = lineNumber + 1
	currentAccount.LocationID = strconv.FormatInt(r.IntacctLocationID.ValueOrZero(), 10)
	currentAccount.Memo = fmt.Sprint(f.Code, "Inv upload", endDate.Format(db.ShortDateFormat))
	currentAccount.GlentryProjectID = r.IntacctFeeProduct
	currentAccount.GlentryVendorID = r.IntacctVendorID.ValueOrZero()
	currentAccount.GlentryCutomerID = fd.IntacctCustomerNumber
	currentAccount.TransactionID = 0
	currentAccount.ContractID = fd.DMSNumber
	currentAccount.TransactionType = fd.TransactionType
	currentAccount.TransactionSubtype = ""
	currentAccount.ContractCode = ""

	return nil
}

func applyFeeCalculation(c calculation, f *db.Fee, ca *intacct.JournalEntry) {
	for _, op := range c.Operations {
		if op.OpCode == OpCodeMinus {
			ca.Credit = ca.Credit.Add(f.Amount).Round(2)
		} else if op.OpCode == OpCodePlus {
			ca.Debit = ca.Debit.Add(f.Amount).Round(2)
		}
	}

	// Adjust both credit and debit because we don't want both entries for any account either account gt credited or debited
	if ca.Credit != decimal.Zero && ca.Debit != decimal.Zero {
		if ca.Debit.GreaterThan(ca.Credit) {
			ca.Debit = ca.Debit.Sub(ca.Credit).Round(2)
			ca.Credit = decimal.Zero
		} else {
			ca.Credit = ca.Credit.Sub(ca.Debit).Round(2)
			ca.Debit = decimal.Zero
		}
	}
}
