package adminhandlers

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"
	"whiz/db"
	"whiz/dms"
	"whiz/handlers"
	"whiz/pdftk"
	"whiz/randstr"
	"whiz/s3util"
	"whiz/util"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"github.com/lib/pq/hstore"
	newrelic "github.com/newrelic/go-agent"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"gopkg.in/guregu/null.v3"
)

type productVariant struct {
	db.ProductVariant
	DealerPVDisplayName string             `db:"dealer_pv_display_name" json:"dealer_pv_display_name"`
	RateSheetID         int                `db:"rate_sheet_id" json:"rate_sheet_id"`
	ClassificationCode  null.String        `db:"classification_code" json:"classification_code"`
	ClassificationID    null.Int           `db:"classification_id" json:"classification_id"`
	ProductTypeID       int                `db:"product_type_id" json:"product_type_id"`
	AdditiveMileage     bool               `db:"additive_mileage" json:"additive_mileage"`
	Options             []variantOption    `db:"-" json:"options"`
	Surcharges          []variantSurcharge `db:"-" json:"surcharges"`
	Plans               []variantPlan      `db:"-" json:"plans"`
}

// rateable is the common fields for `variantOption`, and `variantSurcharge`.
type rateable struct {
	ID                      int             `db:"id" json:"id"`
	ProductVariantID        int             `db:"product_variant_id" json:"product_variant_id"`
	Code                    string          `db:"code" json:"code"`
	Cost                    decimal.Decimal `db:"cost" json:"cost"`
	Tags                    hstore.Hstore   `db:"tags" json:"tags"`
	FilteredByProductRuleID null.Int        `db:"filtered_by_product_rule_id" json:"filtered_by_product_rule_id"`
}

type variantOption struct {
	rateable
	Name             null.String     `json:"name"`
	OptionGroup      null.String     `json:"option_group"`
	Selectable       bool            `json:"selectable"`
	Preselected      bool            `json:"preselected"`
	PlanDuration     null.Int        `db:"plan_duration" json:"plan_duration"`
	QuoteOptionGroup []variantOption `db:"-" json:"quote_option_group"`
}

func newVariantOption(o db.QuoteOption) variantOption {
	return variantOption{
		rateable: rateable{
			ID:                      o.OptionID,
			Code:                    o.Code.String,
			Cost:                    o.Cost,
			Tags:                    o.Tags,
			FilteredByProductRuleID: null.Int{NullInt64: o.FilteredByProductRuleID},
		},
		Name:         null.String{NullString: o.Name},
		OptionGroup:  null.String{NullString: o.OptionGroup},
		Selectable:   o.Selectable,
		Preselected:  o.Preselected,
		PlanDuration: o.PlanDuration,
	}
}

type variantSurcharge struct {
	rateable
	Name         null.String `json:"name"`
	Selectable   bool        `json:"selectable"`
	PlanDuration null.Int    `json:"plan_duration"`
}

func newVariantSurcharge(s db.QuoteSurcharge) variantSurcharge {
	return variantSurcharge{
		rateable: rateable{
			ID:                      s.SurchargeID,
			Code:                    s.Code.String,
			Cost:                    s.Cost,
			Tags:                    s.Tags,
			FilteredByProductRuleID: null.Int{NullInt64: s.FilteredByProductRuleID},
		},
		Name:         null.String{NullString: s.Name},
		Selectable:   s.Selectable,
		PlanDuration: s.PlanDuration,
	}
}

type variantPlan struct {
	ID                      int             `db:"id" json:"id"`
	Name                    string          `db:"name" json:"name"`
	TotalCost               decimal.Decimal `db:"total_cost" json:"total_cost"`
	Tags                    hstore.Hstore   `db:"tags" json:"tags"`
	FilteredByProductRuleID null.Int        `db:"-" json:"-"`
	Duration                int             `db:"duration" json:"duration"`
	Mileage                 null.Int        `db:"mileage" json:"mileage"`
	OdometerMin             null.Int        `db:"odometer_min" json:"odometer_min"`
	OdometerMax             null.Int        `db:"odometer_max" json:"odometer_max"`
}

// ContractEditView returns the data needed for editing a contract
func ContractEditView(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()

	errLoading := "Error loading contract"
	contractCode := chi.URLParam(req, "code")
	var effectiveMileageChange int
	var err error

	contract, err := handlers.LoadContractDetails(contractCode)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage("Contract not found", nil)
		}
		err = errors.Wrapf(err, "error loading contract %s", contractCode)
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(errLoading, nil)
	}

	if v := req.FormValue("effective_mileage_change"); v != "" {
		effectiveMileageChange, err = strconv.Atoi(v)
		if err != nil {
			return http.StatusBadRequest, handlers.ErrorMessage("Bad request. StoreID invalid.", nil)
		}
		if effectiveMileageChange < 0 {
			return http.StatusBadRequest, handlers.ErrorMessage("Bad request. Effective mileage invalid.", nil)
		}
	}

	productVariants, err := loadProductVariants(ctx, contract, effectiveMileageChange)
	if err != nil {
		err = errors.Wrap(err, "error loading product variants")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(errLoading, nil)
	}

	// Configure what edits can be done on the contract
	editOptions := struct {
		RemoveOptions bool `json:"remove_options"`
	}{false}

	// If MOD edit, then allow options to be removed
	if isMODEdit(contract.ProductType.Code, contract.SaleType) {
		editOptions.RemoveOptions = true
	}
	return http.StatusOK, map[string]interface{}{
		"data": map[string]interface{}{
			"contract":         contract,
			"edit_options":     editOptions,
			"product_variants": productVariants,
		},
	}
}

type contractEditSavePayload struct {
	ContractID             int             `db:"id" json:"contract_id"`
	StoreID                int             `db:"store_id" json:"store_id"`
	SaleID                 int             `db:"sale_id" json:"sale_id"`
	ProductVariantID       int             `db:"product_variant_id" json:"product_variant_id"`
	PlanID                 int             `db:"plan_id" json:"plan_id"`
	EffectiveDateChange    int             `db:"-" json:"effective_date_change"`
	EffectiveMileageChange int             `db:"-" json:"effective_mileage_change"`
	OptionIDs              []int64         `db:"-" json:"option_ids"`
	SurchargeIDs           []int64         `db:"-" json:"surcharge_ids"`
	ExpectedTotalCost      decimal.Decimal `db:"-" json:"expected_total_cost"`
}

type previouslySavedData struct {
	ProductVariantDisplayName string          `json:"product_variant_display_name"`
	PlanName                  string          `json:"plan_name"`
	EffectiveDate             time.Time       `json:"effective_date"`
	EffectiveMileage          int             `json:"effective_mileage"`
	TotalCost                 decimal.Decimal `json:"price"`
	PlanID                    int             `json:"plan_id"`
}

type editEndorsementOriginalCoverageData struct {
	Deductible                string    `db:"deductible" json:"deductible"`
	ProductVariantDisplayName string    `db:"product_variant_display_name" json:"product_variant_display_name"`
	PlanName                  string    `db:"plan_name" json:"plan_name"`
	ExpirationMileage         int       `db:"expiration_mileage" json:"expiration_mileage"`
	ExpirationDate            time.Time `db:"expiration_date" json:"expiration_date"`
}

// ContractEditSave saves changes to a contract
func ContractEditSave(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	ctx := req.Context()
	const errNoSave = "Unable to save contract"
	contractCode := chi.URLParam(req, "code")
	var payload contractEditSavePayload
	decoder := json.NewDecoder(req.Body)
	err := decoder.Decode(&payload)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage("invalid input", nil)
	}

	contract, err := handlers.LoadContractDetails(contractCode)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage("Contract not found", nil)
		}
		err = errors.Wrapf(err, "error loading contract %s", contractCode)
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error loading contract", nil)
	}
	var eeocd editEndorsementOriginalCoverageData
	// get original info for endorsement form for service contract
	if contract.ProductType.Code == db.ProductTypeCodeService {
		err = db.Get().GetContext(ctx, &eeocd, `select plan_name, product_variant_display_name,
			expiration_mileage, expiration_date from contracts where id = $1`, contract.ID)
		if err != nil {
			err = errors.Wrapf(err, "error getting original coverage for %d", contract.ID)
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Error getting original coverage", nil)
		}

		err = db.Get().GetContext(ctx, &eeocd, `select name deductible from contract_options
			where name ilike '%deductible%' and contract_id = $1`, contract.ID)
		if err != nil && err != sql.ErrNoRows {
			err = errors.Wrapf(err, "error getting original deductible %d", contract.ID)
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Error getting original coverage", nil)
		}
	}

	var plan db.Plan
	err = db.Get().Unsafe().GetContext(ctx, &plan, "select * from plans where id = $1", payload.PlanID)
	if err != nil {
		err = errors.Wrapf(err, "error getting plan %d", payload.PlanID)
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting plan", nil)
	}
	planName := plan.GetName()
	if planName != "" {
		plan.Name = sql.NullString{Valid: true, String: planName}
	} else {
		plan.Name = sql.NullString{}
	}

	var productVariant db.ProductVariant
	err = db.Get().Unsafe().GetContext(ctx, &productVariant, "select * from product_variants where id = $1", payload.ProductVariantID)
	if err != nil {
		err = errors.Wrapf(err, "error getting product variant %d", payload.ProductVariantID)
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting product variant", nil)
	}

	var product db.Product
	err = db.Get().Unsafe().GetContext(ctx, &product, "select * from products where id = $1", productVariant.ProductID)
	if err != nil {
		err = errors.Wrapf(err, "error getting product %d", productVariant.ProductID)
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting product", nil)
	}

	rbMap, err := db.GetRateBucketMapByID(ctx)
	if err != nil {
		err = errors.WithMessage(err, "error getting rate bucket map")
		// Report the error but continue.  It is non-fatal.
		handlers.ReportError(req, err)
	}

	qpa, err := handlers.NewQuotePlanWithAdjustments(contract, plan, payload.ProductVariantID, rbMap)
	if err != nil {
		err = errors.Wrap(err, "error during NewQuotePlanWithAdjustments")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error getting new quote plan with adjustments", nil)
	}

	var historicalData previouslySavedData
	historicalData.ProductVariantDisplayName = contract.ProductVariantDisplayName
	historicalData.PlanName = contract.PlanName
	historicalData.EffectiveDate = contract.EffectiveDate
	historicalData.TotalCost = contract.CurrentCost
	historicalData.PlanID = contract.Plan.ID
	historicalData.EffectiveMileage = contract.EffectiveMileage
	contract.UpdatedByUserID = user.ID
	contract.ProductVariantID = null.IntFrom(int64(payload.ProductVariantID))
	contract.ProductVariantName = productVariant.Name
	contract.ProductVariantDisplayName = productVariant.DisplayName
	contract.ProductName = product.Name
	contract.PlanID = null.IntFrom(int64(plan.ID))
	contract.PlanCost = plan.Cost
	contract.PlanName = plan.Name.String
	contract.PlanCode = null.String{NullString: plan.Code}
	contract.PlanMileage = null.Int{NullInt64: plan.Mileage}
	contract.PlanDuration = plan.Duration
	contract.MaintenanceVisits = null.Int{NullInt64: plan.MaintenanceVisits}
	contract.MaintenanceVisitValue = plan.MaintenanceVisitValue

	// If the UI element for changing the effective date was not enabled, then EffectiveDateChange will be -1.
	// In this case, we do not want to change any previously calculated effective date.
	if payload.EffectiveDateChange >= 0 {
		contract.EffectiveDate = contract.Sale.ContractDate.AddDate(0, payload.EffectiveDateChange, 0)
		contract.ExpirationDate = contract.EffectiveDate.AddDate(0, plan.Duration, 0)
		if contract.ProductTypeCode == db.ProductTypeCodeMaintenance && contract.Status == db.ContractStatusExpired && contract.ExpirationDate.After(time.Now()) {
			contract.Status = db.ContractStatusActive
		}
	}

	if payload.EffectiveMileageChange >= 0 {
		contract.EffectiveMileage = payload.EffectiveMileageChange
	}

	// If plan is changed and there duration is not same then increase/decrease expiration date accordingly
	if historicalData.PlanID != plan.ID {
		contract.ExpirationDate = contract.EffectiveDate.AddDate(0, plan.Duration, 0)
	}

	if plan.Mileage.Valid {
		contract.ExpirationMileage = null.IntFrom(plan.Mileage.Int64)
		if product.AdditiveMileage {
			currentMileage := contract.Sale.Odometer
			if payload.EffectiveMileageChange >= 0 {
				currentMileage = payload.EffectiveMileageChange
			}
			contract.ExpirationMileage = null.IntFrom(int64(currentMileage) + plan.Mileage.Int64)
		}
	} else {
		contract.ExpirationMileage = null.Int{}
	}
	contract.Event = db.ContractEventEdit
	contract.EventNotes = ""

	var totalPrice decimal.Decimal
	updatePrice := contract.ProductTypeCode == db.ProductTypeCodeMaintenance
	if updatePrice {
		totalPrice = totalPrice.Add(plan.Cost)
	}

	currentOptions := []int64{}
	for _, opt := range contract.Options {
		// TODO: Figure out how to handle this more gracefully
		if !opt.OptionID.Valid {
			err = errors.New(fmt.Sprintf("found unexpected null option_id for contract_option %d", opt.ID))
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Contract Option missing ID", nil)
		}
		currentOptions = append(currentOptions, opt.OptionID.Int64)
		if updatePrice {
			totalPrice = totalPrice.Add(opt.Cost)
		}
	}

	currentSurcharges := []int64{}
	for _, surcharge := range contract.Surcharges {
		// TODO: Figure out how to handle this more gracefully
		if !surcharge.SurchargeID.Valid {
			err = errors.New(fmt.Sprintf("found unexpected null surcharge_id for contract_surcharge %d", surcharge.ID))
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Contract Surcharge missing ID", nil)
		}
		currentSurcharges = append(currentSurcharges, surcharge.SurchargeID.Int64)
		if updatePrice {
			totalPrice = totalPrice.Add(surcharge.Cost)
		}
	}

	currentAdjustments := []int64{}
	for _, adj := range contract.Adjustments {
		// TODO: Figure out how to handle this more gracefully
		if !adj.AdjustmentID.Valid {
			err = errors.New(fmt.Sprintf("found unexpected null adjustment_id for contract_adjustments %d", adj.ID))
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Contract Adjustment missing ID", nil)
		}
		currentAdjustments = append(currentAdjustments, adj.AdjustmentID.Int64)
		if updatePrice {
			totalPrice = totalPrice.Add(adj.Cost)
		}
	}

	if updatePrice {
		contract.Price = totalPrice
	}

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		err = errors.Wrap(err, "could not begin transaction")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Unable to start database transaction for updating contract", nil)
	}

	err = db.CreateContractLog(tx, contract.ID, user.ID)
	if err != nil {
		tx.Rollback()
		err = errors.Wrapf(err, "error creating contract_logs for %s", contract.Code)
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Unable to create contract log", nil)
	}

	originalContractCost, err := handlers.GetContractCost(tx, contract.ID)
	if err != nil {
		tx.Rollback()
		err = errors.Wrapf(err, "could not get contract cost for contract %s", contract.Code)
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Unable to get contract cost", nil)
	}
	originalThirdPartyRemit, err := db.GetContractThirdPartyAmt(tx, contract.ID)
	if err != nil {
		tx.Rollback()
		err = errors.Wrapf(err, "could not get contract 3rd-party cost for contract %s", contract.Code)
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Unable to get contract 3rd-party cost", nil)
	}
	originalProvider, err := db.GetContractProvider(tx, contract.ID)
	if err != nil {
		tx.Rollback()
		err = errors.Wrapf(err, "error getting provider for contract %s", contract.Code)
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Unable to get 3rd-party contract provider", nil)
	}
	updContract := `update contracts set
		version = version + 1,
		updated_at = now() at time zone 'utc',
		updated_by_user_id = :updated_by_user_id,
		product_variant_id = :product_variant_id,
		product_variant_name = :product_variant_name,
		product_variant_display_name = :product_variant_display_name,
		product_name = :product_name,
		plan_id = :plan_id,
		plan_cost = :plan_cost,
		price = :price,
		plan_name = :plan_name,
		plan_code = :plan_code,
		plan_mileage = :plan_mileage,
		plan_duration = :plan_duration,
		maintenance_visits = :maintenance_visits,
		maintenance_visit_value = :maintenance_visit_value,
		effective_date = :effective_date,
		effective_mileage = :effective_mileage,
		expiration_date = :expiration_date,
		expiration_mileage = :expiration_mileage,
		event = :event,
		event_notes = :event_notes,
		status = :status
		where id = :id
	`
	updStmt, err := tx.PrepareNamedContext(ctx, updContract)
	if err != nil {
		tx.Rollback()
		err = errors.Wrap(err, "could not prepare update contracts")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Unable to prepare to update contract", nil)
	}
	defer func() { updStmt.Close() }()
	_, err = updStmt.ExecContext(ctx, contract)
	if err != nil {
		tx.Rollback()
		err = errors.Wrap(err, "could not update contract")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Unable to update contract", nil)
	}

	deletedOptions := intArrayMissingItems(currentOptions, payload.OptionIDs)
	if len(deletedOptions) > 0 {
		delQ := "delete from contract_options where contract_id = ? and option_id in (?)"
		delQ, args, err := sqlx.In(delQ, contract.ID, deletedOptions)
		if err != nil {
			tx.Rollback()
			err = errors.Wrap(err, "could not prepare delete options query")
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Unable to prepare to delete contract options", nil)
		}
		delQ = db.Get().Rebind(delQ)
		_, err = tx.ExecContext(ctx, delQ, args...)
		if err != nil {
			tx.Rollback()
			err = errors.Wrapf(err, "could not deleted options (%v) for contract %s", deletedOptions, contract.Code)
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Unable to delete contract options", nil)
		}
	}

	addedOptions := intArrayMissingItems(payload.OptionIDs, currentOptions)
	if len(addedOptions) > 0 {
		addQ := `insert into contract_options (created_at, contract_id, name, code, cost, option_id, rate_bucket_id)
			select now() at time zone 'utc', ?, o.name, o.code, o.cost, o.id, o.rate_bucket_id
			from options o where o.id in (?)`
		addQ, args, err := sqlx.In(addQ, contract.ID, addedOptions)
		if err != nil {
			tx.Rollback()
			err = errors.Wrap(err, "could not prepare add options query")
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Unable to prepare to add contract options", nil)
		}
		addQ = db.Get().Rebind(addQ)
		_, err = tx.ExecContext(ctx, addQ, args...)
		if err != nil {
			tx.Rollback()
			err = errors.Wrapf(err, "could not add options (%v) for contract %s", addedOptions, contract.Code)
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Unable to add contract options", nil)
		}
	}

	deletedSurcharges := intArrayMissingItems(currentSurcharges, payload.SurchargeIDs)
	if len(deletedSurcharges) > 0 {
		delQ := "delete from contract_surcharges where contract_id = ? and surcharge_id in (?)"
		delQ, args, err := sqlx.In(delQ, contract.ID, deletedSurcharges)
		if err != nil {
			tx.Rollback()
			err = errors.Wrap(err, "could not prepare delete surcharges query")
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Unable to prepare to delete contract surcharges", nil)
		}
		delQ = db.Get().Rebind(delQ)
		_, err = tx.ExecContext(ctx, delQ, args...)
		if err != nil {
			tx.Rollback()
			err = errors.Wrapf(err, "could not deleted surcharges (%v) for contract %s", deletedSurcharges, contract.Code)
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Unable to delete contract surcharges", nil)
		}
	}

	addedSurcharges := intArrayMissingItems(payload.SurchargeIDs, currentSurcharges)
	if len(addedSurcharges) > 0 {
		addQ := `insert into contract_surcharges (created_at, contract_id, name, code, cost, surcharge_id, rate_bucket_id)
			select now() at time zone 'utc', ?, s.name, s.code, s.cost, s.id, s.rate_bucket_id
			from surcharges s where s.id in (?)`
		addQ, args, err := sqlx.In(addQ, contract.ID, addedSurcharges)
		if err != nil {
			tx.Rollback()
			err = errors.Wrap(err, "could not prepare add surcharges query")
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Unable to prepare to add contract surcharges", nil)
		}
		addQ = db.Get().Rebind(addQ)
		_, err = tx.ExecContext(ctx, addQ, args...)
		if err != nil {
			tx.Rollback()
			err = errors.Wrapf(err, "could not add surcharges (%v) for contract %s", addedSurcharges, contract.Code)
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Unable to add contract surcharges", nil)
		}
	}

	newAdjustments := []int64{}
	for _, a := range qpa.Adjustments {
		newAdjustments = append(newAdjustments, int64(a.AdjustmentID))
	}
	deletedAdjustments := intArrayMissingItems(currentAdjustments, newAdjustments)
	if len(deletedAdjustments) > 0 {
		delQ := "delete from contract_adjustments where contract_id = ? and adjustment_id in (?)"
		delQ, args, err := sqlx.In(delQ, contract.ID, deletedAdjustments)
		if err != nil {
			tx.Rollback()
			err = errors.Wrap(err, "could not prepare delete adjustments query")
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Unable to prepare to remove contract adjustments", nil)
		}
		delQ = db.Get().Rebind(delQ)
		_, err = tx.ExecContext(ctx, delQ, args...)
		if err != nil {
			tx.Rollback()
			err = errors.Wrapf(err, "could not deleted adjustments (%v) for contract %s", deletedAdjustments, contract.Code)
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Unable to remove contract adjustments", nil)
		}
	}

	contractEdited := false
	originalData := make([]string, 0)

	if historicalData.ProductVariantDisplayName != contract.ProductVariantDisplayName {
		originalProduct := "Product - " + historicalData.ProductVariantDisplayName
		originalData = append(originalData, originalProduct)
		contractEdited = true
	}

	if historicalData.PlanName != contract.PlanName {
		originalPlan := "Plan - " + historicalData.PlanName
		originalData = append(originalData, originalPlan)
		contractEdited = true
	}

	if !historicalData.EffectiveDate.Equal(contract.EffectiveDate) {
		originalEffectiveDate := "Effective Date - " + historicalData.EffectiveDate.Format("01/02/2006")
		originalData = append(originalData, originalEffectiveDate)
		contractEdited = true
	}

	if historicalData.EffectiveMileage != contract.EffectiveMileage {
		originalEffectiveMileage := "Effective Mileage - " + strconv.Itoa(historicalData.EffectiveMileage)
		originalData = append(originalData, originalEffectiveMileage)
		contractEdited = true
	}

	if !historicalData.TotalCost.Equal(payload.ExpectedTotalCost) {
		originalCost := "Original Cost - $" + historicalData.TotalCost.String()
		originalData = append(originalData, originalCost)
		contractEdited = true
	}

	type delOptions struct {
		Name null.String     `db:"name"`
		Cost decimal.Decimal `db:"cost"`
	}
	options := []delOptions{}
	listOfOptions := ""
	if len(deletedOptions) > 0 {
		selQ := "select name, cost from contract_options where contract_id = ? and option_id in (?)"
		selQ, args, err := sqlx.In(selQ, contract.ID, deletedOptions)
		if err != nil {
			tx.Rollback()
			err = errors.Wrap(err, "could not prepare select options query")
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Unable to prepare to select contract options", nil)
		}
		selQ = db.Get().Rebind(selQ)
		err = db.Get().SelectContext(ctx, &options, selQ, args...)
		if err != nil {
			tx.Rollback()
			err = errors.Wrapf(err, "could not fetch options (%v) for contract %s", deletedOptions, contract.Code)
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Unable to fetch contract options", nil)
		}
		listOfOptions = "Options - "
		for _, opt := range options {
			listOfOptions = listOfOptions + opt.Name.String + ":$" + opt.Cost.String() + ", "
		}
		listOfOptions = strings.TrimSuffix(listOfOptions, ", ")
		originalData = append(originalData, listOfOptions)
		contractEdited = true
	}

	for _, val := range originalData {
		addEventQ := `insert into contract_events(created_at,created_by_user_id,created_by_name,contract_id,description)values(
		now() at time zone 'utc', $1, $2, $3, $4)`
		_, err = tx.ExecContext(ctx, addEventQ, user.ID, user.FullName(), contract.ID, contract.Event+": "+val)
		if err != nil {
			_ = tx.Rollback()
			err = errors.Wrap(err, "could not prepare add event query")
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Unable to prepare to add contract events", nil)
		}
	}

	if !contractEdited {
		addEventQ := `insert into contract_events(created_at,created_by_user_id,created_by_name,contract_id,description)values(
	now() at time zone 'utc', $1, $2, $3, $4)`
		_, err = tx.ExecContext(ctx, addEventQ, user.ID, user.FullName(), contract.ID, contract.Event)
		if err != nil {
			_ = tx.Rollback()
			err = errors.Wrap(err, "could not prepare add event query")
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Unable to prepare to add contract events", nil)
		}
	}

	addQ := `insert into contract_adjustments (
		created_at, contract_id, rate_bucket_id, is_invoiceable, cost, adjustment_id
	) values (
		now() at time zone 'utc', $1, $2, $3, $4, $5
	)`
	updateQ := `update contract_adjustments set
		created_at = now() at time zone 'utc',
		cost = $1,
		is_invoiceable = $2,
		rate_bucket_id = $3,
		quote_adjustment_id = null
		where adjustment_id = $4 and contract_id = $5`
	for _, adj := range qpa.Adjustments {
		// Add new adjustments
		if !intSliceContains(currentAdjustments, int64(adj.AdjustmentID)) {
			addQ = db.Get().Rebind(addQ)
			_, err = tx.ExecContext(ctx, addQ, contract.ID, adj.RateBucketID, adj.IsInvoiceable, adj.Cost, adj.AdjustmentID)
			if err != nil {
				_ = tx.Rollback()
				err = errors.Wrap(err, "error inserting contract adjustment")
				handlers.ReportError(req, err)
				return http.StatusInternalServerError, handlers.ErrorMessage("Unable to add contract adjustments", nil)
			}

		} else {
			for _, oldAdj := range contract.Adjustments {
				// Update modified adjustments
				if oldAdj.AdjustmentID.Int64 == int64(adj.AdjustmentID) && (!oldAdj.Cost.Equals(adj.Cost) ||
					oldAdj.IsInvoiceable != adj.IsInvoiceable ||
					oldAdj.RateBucketID != adj.RateBucketID) {
					updateQ = db.Get().Rebind(updateQ)
					_, err = tx.ExecContext(ctx, updateQ, adj.Cost, adj.IsInvoiceable, adj.RateBucketID, adj.AdjustmentID, contract.ID)
					if err != nil {
						_ = tx.Rollback()
						err = errors.Wrap(err, "error updating contract adjustment")
						handlers.ReportError(req, err)
						return http.StatusInternalServerError, handlers.ErrorMessage("Unable to update contract adjustments", nil)
					}
				}
			}
		}
	}

	newContractCost, err := handlers.GetContractCost(tx, contract.ID)
	if err != nil {
		tx.Rollback()
		err = errors.Wrapf(err, "could not get new contract cost for contract %s", contract.Code)
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Unable to get contract cost", nil)
	}
	if !newContractCost.Equal(payload.ExpectedTotalCost) {
		tx.Rollback()
		err = errors.Errorf("unexpcted new value for cost. Expected %v, got %v", payload.ExpectedTotalCost, newContractCost)
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Unexpected value for new contract cost", nil)
	}

	tran := db.Transaction{}
	tran.ContractID = contract.ID
	tran.CreatedByUserID = user.ID
	tran.StoreID = contract.StoreID
	tran.VehicleYear = contract.VINRecord.Year
	tran.VehicleMake = contract.VINRecord.Make
	tran.VehicleModel = contract.VINRecord.Model
	tran.TransactionType = db.TranTypeContractChange
	tran.TransactionSubtype = null.StringFrom(db.TranSubtypeProduct)
	tran.Amount = newContractCost.Sub(originalContractCost)
	tran.ContractPrice = contract.Price
	err = db.CreateTransaction(ctx, tx, &tran)
	if err != nil {
		tx.Rollback()
		err = errors.Wrapf(err, "could not create transaction for contract %s", contract.Code)
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Unable to update contract. Unable to create contract transaction.", nil)
	}

	// Update in-memory copy of contract options, surcharges, and adjustments
	// so we can generate the new transaction breakouts
	contract.Options, err = db.GetContractOptions(ctx, tx, contract.ID)
	if err != nil {
		tx.Rollback()
		err = errors.Wrapf(err, "could not reload contract_options %v for contract %s", addedOptions, contract.Code)
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Unable reload contract options", nil)
	}
	contract.Surcharges, err = db.GetContractSurcharges(ctx, tx, contract.ID)
	if err != nil {
		tx.Rollback()
		err = errors.Wrapf(err, "could not reload contract_surcharges %v for contract %s", addedOptions, contract.Code)
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Unable reload contract surcharges", nil)
	}
	contract.Adjustments, err = db.GetContractAdjustments(ctx, tx, contract.ID)
	if err != nil {
		tx.Rollback()
		err = errors.Wrapf(err, "could not reload adjustments %v for contract %s", addedOptions, contract.Code)
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Unable reload contract adjustments", nil)
	}

	// Create transaction breakouts for the contract edit
	previousContract, err := db.GetContractWithDataByID(ctx, nil, contract.ID)
	if err != nil {
		tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "failed to get previous contract data"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Unable to update contract. Unable to get previous contract data.", nil)
	}
	currentContract, err := db.GetContractWithDataByID(ctx, tx, contract.ID)
	if err != nil {
		tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "failed to get previous contract data"))
		return http.StatusInternalServerError, handlers.ErrorMessage("Unable to update contract. Unable to get current contract data.", nil)
	}
	previousBreakouts := db.GetContractCostBreakouts(previousContract, tran.CreatedByUserID, tran.ID)
	currentBreakouts := db.GetContractCostBreakouts(currentContract, tran.CreatedByUserID, tran.ID)
	breakoutDiffs := db.SubTransactionBreakouts(previousBreakouts, currentBreakouts)
	for _, bo := range breakoutDiffs {
		err = db.InsertTransactionBreakout(ctx, tx, bo)
		if err != nil {
			tx.Rollback()
			msg := fmt.Sprintf("failed to save transaction breakout for contract %s", contract.Code)
			handlers.ReportError(req, errors.WithMessage(err, msg))
			return http.StatusInternalServerError, handlers.ErrorMessage("Unable to update contract. Unable to create transaction breakouts.", nil)
		}
	}

	newThirdPartyRemit, err := db.GetContractThirdPartyAmt(tx, contract.ID)
	if err != nil {
		tx.Rollback()
		err = errors.Wrapf(err, "could not get new 3rd party remit for contract %s", contract.Code)
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Unable to get 3rd-party remit cost", nil)
	}
	provider, err := db.GetContractProvider(tx, contract.ID)
	if err != nil {
		tx.Rollback()
		err = errors.Wrapf(err, "error getting provider for contract %s", contract.Code)
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Unable to get 3rd-party contract provider", nil)
	}

	if originalProvider.ID == provider.ID {
		var remittal db.TransactionRemittal
		remittal.TransactionID = tran.ID
		remittal.ProviderID = provider.ID
		remittal.Amount = newThirdPartyRemit.Sub(originalThirdPartyRemit)
		err = db.CreateTransactionRemittal(tx, contract.ContractWithData.Contract, &remittal)
		if err != nil {
			tx.Rollback()
			err = errors.Wrapf(err, "could not create transaction remittal for contract %s", contract.Code)
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Unable to create transaction remittal.", nil)
		}
	} else {
		// Refund the original provider
		// TODO: What if we don't send refunds to the original provider - e.g. Toyota
		var remittal db.TransactionRemittal
		remittal.TransactionID = tran.ID
		remittal.ProviderID = originalProvider.ID
		remittal.Amount = decimal.Zero.Sub(originalThirdPartyRemit)
		err = db.CreateTransactionRemittal(tx, contract.ContractWithData.Contract, &remittal)
		if err != nil {
			tx.Rollback()
			err = errors.Wrapf(err, "could not create original provider transaction remittal for contract %s", contract.Code)
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Unable to create transaction remittal.", nil)
		}

		// Charge the new provider
		remittal.ProviderID = provider.ID
		remittal.Amount = newThirdPartyRemit
		err = db.CreateTransactionRemittal(tx, contract.ContractWithData.Contract, &remittal)
		if err != nil {
			tx.Rollback()
			err = errors.Wrapf(err, "could not create new provider transaction remittal for contract %s", contract.Code)
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Unable to create transaction remittal.", nil)
		}

	}

	if isMODEdit(contract.ProductType.Code, contract.SaleType) {
		spiffRevoked := db.ContractSpiffIneligible{}
		spiffRevoked.CreatedByUserID = user.ID
		spiffRevoked.ContractID = contract.ID
		spiffRevoked.Reason = db.ReasonMODContractChanged

		insSpiffVoid := `insert into contract_spiff_ineligible
			(created_at, created_by_user_id, contract_id, reason)
			values (now() at time zone 'utc', :created_by_user_id, :contract_id, :reason)
			on conflict do nothing`
		insStmt, err := tx.PrepareNamedContext(ctx, insSpiffVoid)
		if err != nil {
			tx.Rollback()
			err = errors.Wrap(err, "could not prepare insert contract_spiff_ineligible")
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Unable to update contract to mark as ineligible for SPIFF.", nil)
		}
		defer func() { insStmt.Close() }()
		_, err = insStmt.ExecContext(ctx, spiffRevoked)
		if err != nil {
			tx.Rollback()
			err = errors.Wrap(err, "could not insert contract_spiff_ineligible")
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Unable to update contract to mark as ineligible for SPIFF.", nil)
		}
	}

	if contract.ProductType.Code == db.ProductTypeCodeService {
		stampedFile, err := generateContractEditEndorsement(&eeocd, &contract)
		if err != nil {
			err = errors.Wrap(err, "could not generate contract endorsement form")
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Unable to generate contract endorsement form", nil)
		}
		defer func() {
			_ = stampedFile.Close()
			_ = os.Remove(stampedFile.Name())
		}()
		s3FileNamePrefix := contract.Code
		contractAttachment, err := uploadContractEditEndorsement(ctx, contract.StoreID, contract.Store.Code, s3FileNamePrefix, stampedFile, user)
		if err != nil {
			err = errors.Wrap(err, "error uploading edit endorsement file")
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Error uploading edit endorsement", nil)
		}

		contractAttachment.ContractID = contract.ID

		_, err = db.CreateContractAttachment(tx, contractAttachment)
		if err != nil {
			tx.Rollback()
			err = errors.Wrap(err, "error updating contract attachments")
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage("Error creating attachment for edit endorsement", nil)
		}
	}
	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "could not commit contract edit transaction")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Unable to save contract changes. Possible issue with database.", nil)
	}

	return http.StatusOK, map[string]interface{}{
		"contract_code": contract.Code,
	}
}

// ContractEditEndorsement function creates endorsement pdf for contract with old and new coverage information
func ContractEditEndorsement(w http.ResponseWriter, req *http.Request, user db.CurrentUser) (int, map[string]interface{}) {
	contractCode := chi.URLParam(req, "code")
	var payload editEndorsementOriginalCoverageData

	query := req.FormValue("q")
	if query == "" {
		return http.StatusBadRequest, handlers.ErrorMessage("Bad request", nil)
	}
	err := json.Unmarshal([]byte(query), &payload)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage("Bad request", nil)
	}

	contract, err := handlers.LoadContractDetails(contractCode)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage("Contract not found", nil)
		}
		err = errors.Wrapf(err, "error loading contract %s", contractCode)
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Error loading contract", nil)
	}

	// generate contract endorsement for service contract only
	if contract.ProductType.Code != db.ProductTypeCodeService {
		return http.StatusBadRequest, handlers.ErrorMessage("Edit endorsement is not available for this product", nil)
	}

	stampedFile, err := generateContractEditEndorsement(&payload, &contract)
	if err != nil {
		err = errors.Wrap(err, "could not generate contract endorsement form")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage("Unable to generate contract endorsement form", nil)
	}
	defer func() {
		_ = stampedFile.Close()
		_ = os.Remove(stampedFile.Name())
	}()

	w.Header().Set("Content-Type", "application/pdf")
	w.Header().Set("Content-Disposition", fmt.Sprintf("inline; filename=\"contract_edit_endorsement_%s_.pdf\"", contract.Code))
	w.WriteHeader(http.StatusOK)
	_, err = io.Copy(w, stampedFile)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error writing stamped form to response."))
		return http.StatusInternalServerError, handlers.ErrorMessage("Error writing stamped form to response", nil)
	}

	return http.StatusOK, map[string]interface{}{
		"contract_code": contract.Code,
	}
}

// generateContractEditEndorsement is used to stamp contract edit endorsement
func generateContractEditEndorsement(eeocd *editEndorsementOriginalCoverageData, contract *handlers.ContractEditDetails) (*os.File, error) {

	stampedFile, err := ioutil.TempFile("", "temp-contract-edit-endorse")
	if err != nil {
		return nil, err
	}
	var deductible string
	for _, option := range contract.Options {
		if strings.Contains(strings.ToLower(option.Name.String), "deductible") {
			deductible = option.Name.String
			break
		}
	}
	customerName := contract.Customer.FirstName + " " + contract.Customer.LastName
	if contract.Customer.IsBusiness {
		customerName = customerName + " / " + contract.Customer.BusinessName
	}
	fields := map[string]string{
		"contract_number":  contract.Code,
		"customer_name":    customerName,
		"customer_phone":   contract.Customer.Phone,
		"customer_address": contract.Customer.Address,
		"customer_city":    contract.Customer.City,
		"customer_state":   contract.Customer.StateCode,
		"customer_zip":     contract.Customer.PostalCode,
		"dealer_name":      contract.Store.Name,
		"dealer_phone":     contract.Store.Phone,
		"dealer_address":   contract.Store.Address,
		"dealer_city":      contract.Store.City,
		"dealer_state":     contract.Store.StateCode,
		"dealer_zip":       contract.Store.PostalCode,

		"vin":   contract.VINRecord.VIN,
		"year":  strconv.Itoa(contract.VINRecord.Year),
		"make":  contract.VINRecord.Make,
		"model": contract.VINRecord.Model,

		"original_plan":             eeocd.ProductVariantDisplayName,
		"original_months_or_miles":  eeocd.PlanName,
		"original_expiration_miles": strconv.Itoa(eeocd.ExpirationMileage),
		"original_expiration_date":  eeocd.ExpirationDate.Format(htmlDateFormat),
		"original_deductible":       eeocd.Deductible,
		"updated_plan":              contract.ProductVariantDisplayName,
		"updated_months_or_miles":   contract.PlanName,
		"updated_expiration_miles":  strconv.Itoa(int(contract.ExpirationMileage.Int64)),
		"updated_expiration_date":   contract.ExpirationDate.Format(htmlDateFormat),
		"updated_deductible":        deductible,
	}

	err = pdftk.FillForm("files/VSC_endorsement_form.pdf", fields, stampedFile.Name())
	if err != nil {
		return nil, errors.Wrap(err, "error stamping contract edit endorsement")
	}
	return stampedFile, nil
}

// uploadContractEditEndorsement stores transfer endorsement file to s3 bucket
func uploadContractEditEndorsement(ctx context.Context, storeID int, storeCode, s3FileName string, file *os.File,
	user db.CurrentUser) (db.ContractAttachment, error) {
	txn := newrelic.FromContext(ctx)
	defer newrelic.StartSegment(txn, "Contract edit endorsement upload segment").End()
	ca := db.ContractAttachment{}
	ca.FileName = "Edit Endorsement.pdf"

	ca.CreatedByUserID = user.ID
	ca.S3Bucket = s3util.Bucket()
	ca.StoreID = storeID
	ca.S3FileName = fmt.Sprintf("contract-attachments/%s/%s_%s_endorsement.pdf", storeCode, s3FileName, randstr.StringN(6))
	ca.ContentType = "application/pdf"
	ca.Description = "Edit Endorsement"

	err := s3util.Put(txn, file, s3util.DefaultRegion, ca.S3Bucket, ca.S3FileName)
	if err != nil {
		return ca, errors.Wrap(err, "error in uploading contract edit endorsement to s3")
	}

	return ca, nil
}

func loadProductVariants(ctx context.Context, contract handlers.ContractEditDetails, editEffectiveMileage int) ([]productVariant, error) {
	rbMap, err := db.GetRateBucketMapByID(ctx)
	if err != nil {
		err = errors.WithMessage(err, "error getting rate bucket map")
		// Report the error but continue.  It is non-fatal.
		util.ReportError(ctx, err)
	}

	// Get possible product variants (and ratesheet) for the contract
	productVariants := []struct {
		ProductVariantID int `db:"product_variant_id"`
		RateSheetID      int `db:"rate_sheet_id"`
	}{}
	params := map[string]interface{}{
		"product_type_code": contract.ProductType.Code,
		"contract_date":     contract.Sale.ContractDate,
		"store_id":          contract.Store.ID,
	}
	pvQuery := `select pv.id product_variant_id, rs.id rate_sheet_id
	from
		product_variants pv
		join product_variants_stores pvs on pv.id = pvs.product_variant_id
		join products p on pv.product_id = p.id
		join product_types pt on pt.id = p.product_type_id
		join rate_sheets rs on rs.product_variant_id = pv.id
	where
		pvs.deleted_at is null
		and pt.code = :product_type_code
		and pvs.started_on <= :contract_date
		and (pvs.ended_on is null or pvs.ended_on >= :contract_date)
		and pv.started_on <= :contract_date
		and (pv.ended_on is null or pv.ended_on >= :contract_date)
		and pvs.store_id = :store_id
		and rs.id in (
			-- Find the active rate sheet for the product variant
			-- This is more complicated than it should be because deactivated is not set
			-- for a lot of rate sheets that have had newer versions activated
			select acts.rate_sheet_id
			from rate_sheets rs
			join active_rate_sheets acts on acts.rate_sheet_id = rs.id
			where rs.product_variant_id = pv.id
				and acts.activated_on <= :contract_date
				and deactivated_at is null
			order by acts.activated_on desc, acts.created_at desc
			limit 1
		)
		order by p.position, rs.product_variant_id
	`
	stmt, err := db.Get().PrepareNamedContext(ctx, pvQuery)
	if err != nil {
		return nil, errors.Wrap(err, "could not prepare product_variants query")
	}
	defer func() { _ = stmt.Close() }()
	err = stmt.SelectContext(ctx, &productVariants, params)
	if err != nil {
		return nil, errors.Wrap(err, "could not query product_variants")
	}

	if contract.EffectiveMileage > contract.Sale.Odometer {
		contract.Sale.Odometer = contract.EffectiveMileage
	}

	if editEffectiveMileage > 0 {
		contract.Sale.Odometer = editEffectiveMileage
	}

	rateQuery, err := db.RateQueryData(contract.CreatedByUserID, contract.Contract, contract.Sale, contract.Customer)
	if err != nil {
		return nil, errors.Wrap(err, "error creating a rate query")
	}

	filteredProductVariants := []productVariant{}
	for _, pv := range productVariants {
		variant, err := loadProductVariantDetails(
			ctx,
			contract,
			pv.ProductVariantID,
			pv.RateSheetID,
			rateQuery,
			rbMap,
		)
		if err != nil {
			return nil, errors.Wrap(err, "error loading product variant details")
		}
		if variant != nil {
			filteredProductVariants = append(filteredProductVariants, *variant)
		}
	}

	return filteredProductVariants, nil
}

func loadProductVariantDetails(
	ctx context.Context,
	contract handlers.ContractEditDetails,
	pvID int,
	rateSheetID int,
	rateQuery db.RateQuery,
	rbMap map[int]db.RateBucket,
) (*productVariant, error) {
	// Get Vehicle Classification based on ratesheet and contract date
	classificationList, err := db.FindClassificationList(rateSheetID, contract.Sale.ContractDate)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get classification list for rate sheet id %d with contract date %v", rateSheetID, contract.Sale.ContractDate)
	}

	var classification *db.Classification
	if classificationList != nil {
		classification, err = db.FindClassification(classificationList.ID, contract.VINRecord, contract.Sale.IsNew)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to get classification for rate sheet id %v with contract date %v", rateSheetID, contract.Sale.ContractDate)
		}
	}

	pv := productVariant{RateSheetID: rateSheetID}
	query := `select pv.*, coalesce(dppv.display_name,'') dealer_pv_display_name
	from product_variants pv
	left join dealer_platform_product_variants dppv
		on (dppv.product_variant_id = pv.id and dppv.dealer_platform_id = $1)
	where pv.id = $2`
	err = db.Get().Unsafe().GetContext(ctx, &pv, query, contract.DealerPlatformID, pvID)
	if err != nil {
		return nil, errors.Wrap(err, "error loading product variant")
	}

	rateQuery.HasGAPLicense = false
	plans, err := db.FindPlans(rateSheetID, rateQuery, classification)
	if err != nil && err != sql.ErrNoRows {
		return nil, errors.Wrap(err, "error during FindPlans")
	}
	if len(plans) < 1 {
		return nil, nil
	}

	rqParams := rateQuery.GetProductRulesParams()
	rules, err := db.FindProductRules(rateSheetID, rateQuery.ContractDate)
	if err != nil {
		return nil, errors.Wrap(err, "error during FindProductRules")
	}
	adjustments, err := db.FindAdjustments(rateSheetID, rateQuery)
	if err != nil {
		return nil, errors.Wrap(err, "error during FindAdjustments")
	}

	variantPlans := make([]variantPlan, len(plans))
	for i, plan := range plans {
		variantPlans[i], err = planWithAdjustments(
			ctx,
			plan,
			rules,
			rqParams,
			adjustments,
			contract.EffectiveMileage,
			contract.ProductTypeCode,
			rbMap,
		)
		if err != nil {
			return nil, errors.Wrap(err, "error getting plans with adjustments")
		}
	}
	pv.Plans = sortPlans(filterPlans(contract, variantPlans), contract.ProductType.Code)
	// Skip Product Variants that do not have any plans
	if len(pv.Plans) == 0 {
		return nil, nil
	}

	options, err := db.FindOptions(rateSheetID, rateQuery, classification)
	if err != nil {
		return nil, errors.Wrap(err, "error during FindOptions")
	}
	variantOptions := make([]variantOption, len(options))
	for i, option := range options {
		quoteOption := db.NewQuoteOption(option)
		_, err = quoteOption.ProcessRules(rules, rqParams)
		if err != nil {
			return nil, errors.Wrap(err, "one or more errors processing rules on option")
		}
		variantOptions[i] = newVariantOption(quoteOption)
	}
	pv.Options = filterOptions(variantOptions)

	// If more than one quote option then we need to group them based on the duration if its set
	var finalOptions []variantOption

	if len(pv.Options) > 1 {
		var firstOptionIndex int
		var optionGroup []variantOption
		for index, v := range pv.Options {
			if index == 0 {
				// If first element then check if then add it to final options if its only one element
				// or if its different than next element
				if len(pv.Options) == 1 || pv.Options[index+1].Name != v.Name {
					finalOptions = append(finalOptions, v)
				}
				continue
			}

			// Get previous element to compate if current element belongs to same option group
			quoteOption := pv.Options[index-1]
			if quoteOption.Name != v.Name {
				// If current and previous options are different then we check if there is optiongroup with same name
				// if option group exists then we add option group to root option and then set current option as root for next group
				if len(optionGroup) > 0 {
					filteredOption := pv.Options[firstOptionIndex]
					filteredOption.QuoteOptionGroup = optionGroup
					finalOptions = append(finalOptions, filteredOption)
					optionGroup = make([]variantOption, 0)
					firstOptionIndex = index
				}

				// If its last option then add it to final option group
				// Or if its next option and current dont have same name then add it to the final option group
				if len(pv.Options) == index+1 || pv.Options[index+1].Name != v.Name {
					finalOptions = append(finalOptions, v)
				}

				// We will continue with new root option group index
				continue
			}

			// If our option group name is same then just add to the group and check
			// If we need to reset the root option
			if firstOptionIndex > index-1 {
				firstOptionIndex = index - 1
			}

			if v.PlanDuration.Valid {
				optionGroup = append(optionGroup, v)
			}
		}
	}
	if len(finalOptions) > 0 {
		pv.Options = finalOptions
	}

	surcharges, err := db.FindSurcharges(rateSheetID, rateQuery, classification)
	if err != nil {
		return nil, errors.Wrap(err, "error during FindSurcharges")
	}
	variantSurcharges := make([]variantSurcharge, len(surcharges))
	for i, surcharge := range surcharges {
		variantSurcharge := db.NewQuoteSurcharge(surcharge)
		_, err = variantSurcharge.ProcessRules(rules, rqParams)
		if err != nil {
			return nil, errors.Wrap(err, "one or more errors processing rules on surcharge")
		}
		variantSurcharges[i] = newVariantSurcharge(variantSurcharge)
	}
	pv.Surcharges = filterSurcharges(variantSurcharges)

	return &pv, nil
}

func planWithAdjustments(
	ctx context.Context,
	plan db.Plan,
	rules []db.ProductRule,
	rqParams map[string]interface{},
	adjustments []db.Adjustment,
	mileage int,
	productTypeCode string,
	rbMap map[int]db.RateBucket,
) (variantPlan, error) {

	qpa, err := db.NewQuotePlanWithAdjustments(plan, adjustments, mileage, productTypeCode, rbMap)
	if err != nil {
		return variantPlan{}, errors.Wrapf(err, "error during NewQuotePlanWithAdjustments on Plan (%d)", plan.ID)
	}

	_, err = qpa.ProcessRules(rules, rqParams)
	if err != nil {
		err = errors.Wrapf(err, "one or more errors processing rules on Plan (%d)", plan.ID)
		return variantPlan{}, err
	}

	totalCost := qpa.Cost
	for _, a := range qpa.Adjustments {
		if a.IsInvoiceable {
			totalCost = totalCost.Add(a.Cost)
		}
	}

	return variantPlan{
		ID:                      qpa.PlanID,
		Name:                    qpa.Name,
		TotalCost:               totalCost,
		Tags:                    qpa.Tags,
		FilteredByProductRuleID: null.Int{NullInt64: qpa.FilteredByProductRuleID},
		Duration:                qpa.Duration,
		Mileage:                 null.Int{NullInt64: qpa.Mileage},
		OdometerMin:             null.Int{NullInt64: plan.OdometerMin},
		OdometerMax:             null.Int{NullInt64: plan.OdometerMax},
	}, nil
}

func sortPlans(plans []variantPlan, productType string) []variantPlan {
	if productType != "MNT" {
		return plans
	}

	return reversePlans(plans)
}

func reversePlans(p []variantPlan) []variantPlan {
	pCopy := make([]variantPlan, len(p))
	copy(pCopy, p)
	for i, j := 0, len(pCopy)-1; i < j; i, j = i+1, j-1 {
		pCopy[i], pCopy[j] = pCopy[j], pCopy[i]
	}
	return pCopy
}

func filterPlans(contract handlers.ContractEditDetails, p []variantPlan) []variantPlan {
	// Always show plans with lower terms.
	// This may or may not be a temporary workaround for some of the issues from TS-2888
	allowLesserTerms := true

	// Keep old logic of only allowing terms to be lowered for MOD edits in case TCA
	// Decides they want to go back to this behavior
	// Plan terms can only be lowered for MOD edits
	// allowLesserTerms := false
	// if isMODEdit(contract.ProductType.Code, contract.SaleType) {
	// 	allowLesserTerms = true
	// }

	allowPlan := func(
		plan variantPlan, allowLesserTerms bool,
		originalDuration int, originalMileage null.Int,
	) bool {
		return allowLesserTerms ||
			(originalDuration <= plan.Duration && originalMileage.Int64 <= plan.Mileage.Int64)
	}

	var filtered []variantPlan
	for _, po := range p {
		if !po.FilteredByProductRuleID.Valid && allowPlan(po, allowLesserTerms, contract.PlanDuration, contract.PlanMileage) {
			filtered = append(filtered, po)
		}
	}
	return filtered
}

func filterOptions(o []variantOption) []variantOption {
	var filtered []variantOption
	for _, opt := range o {
		if !opt.FilteredByProductRuleID.Valid {
			filtered = append(filtered, opt)
		}
	}
	return filtered
}

func filterSurcharges(s []variantSurcharge) []variantSurcharge {
	var filtered []variantSurcharge
	for _, opt := range s {
		if !opt.FilteredByProductRuleID.Valid {
			filtered = append(filtered, opt)
		}
	}
	return filtered
}

func isMODEdit(productTypeCode string, saleType string) bool {
	if productTypeCode == "MNT" && saleType == dms.SaleTypeServiceRO {
		return true
	}
	return false
}

func intSliceContains(slice []int64, value int64) bool {
	for _, v := range slice {
		if v == value {
			return true
		}
	}
	return false
}

// intArrayMissingItems returns a slice of ints which are in a, but not in b
func intArrayMissingItems(a []int64, b []int64) []int64 {
	missing := []int64{}
	for _, v := range a {
		if !intSliceContains(b, v) {
			missing = append(missing, v)
		}
	}

	return missing
}

// Adapted from https://golangr.com/difference-between-two-dates/
func dateDiff(a, b time.Time) (month, day int) {
	if a.Location() != b.Location() {
		b = b.In(a.Location())
	}
	if a.After(b) {
		a, b = b, a
	}
	y1, M1, d1 := a.Date()
	y2, M2, d2 := b.Date()

	h1, m1, s1 := a.Clock()
	h2, m2, s2 := b.Clock()

	year := int(y2 - y1)
	month = int(M2 - M1)
	day = int(d2 - d1)
	hour := int(h2 - h1)
	min := int(m2 - m1)
	sec := int(s2 - s1)

	// Normalize negative values
	if sec < 0 {
		sec += 60
		min--
	}
	if min < 0 {
		min += 60
		hour--
	}
	if hour < 0 {
		hour += 24
		day--
	}
	if day < 0 {
		// days in month:
		t := time.Date(y1, M1, 32, 0, 0, 0, 0, time.UTC)
		day += 32 - t.Day()
		month--
	}
	if month < 0 {
		month += 12
		year--
	}

	month += year * 12

	return
}
